{"https://github.com/0x-jerry/comfyui-rembg": {"stars": 0, "last_update": "2025-04-07 09:23:31", "author_account_age_days": 3577}, "https://github.com/0xRavenBlack/ComfyUI-OOP": {"stars": 7, "last_update": "2025-03-02 11:59:14", "author_account_age_days": 1747}, "https://github.com/0xbitches/ComfyUI-LCM": {"stars": 257, "last_update": "2023-11-11 21:24:33", "author_account_age_days": 900}, "https://github.com/1038lab/ComfyUI-EdgeTTS": {"stars": 46, "last_update": "2025-06-20 18:14:14", "author_account_age_days": 820}, "https://github.com/1038lab/ComfyUI-JoyCaption": {"stars": 21, "last_update": "2025-06-12 21:15:17", "author_account_age_days": 820}, "https://github.com/1038lab/ComfyUI-LBM": {"stars": 48, "last_update": "2025-05-27 17:37:31", "author_account_age_days": 820}, "https://github.com/1038lab/ComfyUI-MegaTTS": {"stars": 42, "last_update": "2025-06-19 19:12:51", "author_account_age_days": 820}, "https://github.com/1038lab/ComfyUI-OmniGen": {"stars": 277, "last_update": "2025-04-18 18:33:34", "author_account_age_days": 820}, "https://github.com/1038lab/ComfyUI-Pollinations": {"stars": 34, "last_update": "2025-06-19 19:48:05", "author_account_age_days": 820}, "https://github.com/1038lab/ComfyUI-RMBG": {"stars": 1096, "last_update": "2025-06-01 21:28:57", "author_account_age_days": 820}, "https://github.com/1038lab/ComfyUI-ReduxFineTune": {"stars": 53, "last_update": "2025-06-21 19:10:36", "author_account_age_days": 820}, "https://github.com/1038lab/ComfyUI-SparkTTS": {"stars": 105, "last_update": "2025-04-15 19:28:39", "author_account_age_days": 820}, "https://github.com/1038lab/ComfyUI-WildPromptor": {"stars": 37, "last_update": "2025-04-18 18:54:52", "author_account_age_days": 820}, "https://github.com/111496583yzy/comfyui-PuzzleCrack-Effect": {"stars": 3, "last_update": "2025-01-13 10:15:44", "author_account_age_days": 2250}, "https://github.com/11cafe/comfyui-workspace-manager": {"stars": 1313, "last_update": "2025-04-16 14:02:54", "author_account_age_days": 573}, "https://github.com/11dogzi/CYBERPUNK-STYLE-DIY": {"stars": 72, "last_update": "2025-06-20 07:49:50", "author_account_age_days": 493}, "https://github.com/11dogzi/ComfUI-EGAdapterMadAssistant": {"stars": 38, "last_update": "2024-08-02 05:24:19", "author_account_age_days": 493}, "https://github.com/11dogzi/Comfyui-ergouzi-Nodes": {"stars": 83, "last_update": "2024-08-23 12:04:09", "author_account_age_days": 493}, "https://github.com/11dogzi/Comfyui-ergouzi-kaiguan": {"stars": 67, "last_update": "2025-06-22 14:48:37", "author_account_age_days": 493}, "https://github.com/11dogzi/Comfyui-ergouzi-samplers": {"stars": 26, "last_update": "2024-06-28 05:28:05", "author_account_age_days": 493}, "https://github.com/1hew/ComfyUI-1hewNodes": {"stars": 3, "last_update": "2025-06-25 09:27:10", "author_account_age_days": 815}, "https://github.com/1mckw/Comfyui-Gelbooru": {"stars": 4, "last_update": "2025-04-06 14:11:25", "author_account_age_days": 1058}, "https://github.com/1zhangyy1/comfyui-vidu-nodes": {"stars": 8, "last_update": "2025-03-21 12:25:22", "author_account_age_days": 827}, "https://github.com/2frames/ComfyUI-AQnodes": {"stars": 1, "last_update": "2025-06-08 12:51:07", "author_account_age_days": 358}, "https://github.com/2kpr/ComfyUI-PMRF": {"stars": 205, "last_update": "2024-10-11 00:11:40", "author_account_age_days": 1287}, "https://github.com/2kpr/ComfyUI-UltraPixel": {"stars": 225, "last_update": "2024-07-27 14:52:10", "author_account_age_days": 1287}, "https://github.com/311-code/ComfyUI-MagicClip_Strength": {"stars": 2, "last_update": "2024-09-22 12:07:40", "author_account_age_days": 3140}, "https://github.com/***********/ComfyUI-GrsAI": {"stars": 7, "last_update": "2025-06-23 17:00:45", "author_account_age_days": 2372}, "https://github.com/42lux/ComfyUI-42lux": {"stars": 10, "last_update": "2024-12-19 10:21:03", "author_account_age_days": 4067}, "https://github.com/*********/ComfyUI-GPT4V-Image-Captioner": {"stars": 27, "last_update": "2025-04-06 02:06:59", "author_account_age_days": 782}, "https://github.com/45uee/ComfyUI-Color_Transfer": {"stars": 29, "last_update": "2025-05-12 22:12:06", "author_account_age_days": 2671}, "https://github.com/54rt1n/ComfyUI-DareMerge": {"stars": 89, "last_update": "2025-03-27 14:57:35", "author_account_age_days": 4419}, "https://github.com/5x00/ComfyUI-PiAPI-Faceswap": {"stars": 2, "last_update": "2025-01-12 14:49:09", "author_account_age_days": 1339}, "https://github.com/5x00/ComfyUI-VLM-Captions": {"stars": 6, "last_update": "2025-01-04 21:27:47", "author_account_age_days": 1339}, "https://github.com/6174/comflowy-nodes": {"stars": 14, "last_update": "2024-12-03 13:31:04", "author_account_age_days": 4487}, "https://github.com/*********/ComfyUI-3D-MeshTool": {"stars": 23, "last_update": "2024-10-18 09:59:54", "author_account_age_days": 2381}, "https://github.com/*********/ComfyUI-WJNodes": {"stars": 11, "last_update": "2025-06-16 03:44:19", "author_account_age_days": 2381}, "https://github.com/*********/ComfyUI_MaskGCT": {"stars": 27, "last_update": "2025-03-05 09:15:32", "author_account_age_days": 2381}, "https://github.com/80sVectorz/ComfyUI-Static-Primitives": {"stars": 11, "last_update": "2025-03-14 11:42:07", "author_account_age_days": 1839}, "https://github.com/834t/ComfyUI_834t_scene_composer": {"stars": 1, "last_update": "2025-06-23 10:55:47", "author_account_age_days": 565}, "https://github.com/852wa/ComfyUI-AAP": {"stars": 9, "last_update": "2025-01-29 13:21:59", "author_account_age_days": 730}, "https://github.com/852wa/ComfyUI-ColorshiftColor": {"stars": 48, "last_update": "2025-02-01 12:17:38", "author_account_age_days": 730}, "https://github.com/A043-studios/ComfyUI-ASDF-Pixel-Sort-Nodes": {"stars": 3, "last_update": "2025-06-12 12:51:33", "author_account_age_days": 1017}, "https://github.com/A043-studios/Comfyui-ascii-generator": {"stars": 0, "last_update": "2025-06-25 14:55:32", "author_account_age_days": 1017}, "https://github.com/A043-studios/comfyui-deforum-x-flux-nodes": {"stars": 0, "last_update": "2025-06-10 14:28:27", "author_account_age_days": 1017}, "https://github.com/A043-studios/comfyui-pixel3dmm": {"stars": 3, "last_update": "2025-06-10 08:11:51", "author_account_age_days": 1017}, "https://github.com/A4P7J1N7M05OT/ComfyUI-AutoColorGimp": {"stars": 1, "last_update": "2024-05-23 00:26:10", "author_account_age_days": 841}, "https://github.com/A4P7J1N7M05OT/ComfyUI-PixelOE-Wrapper": {"stars": 11, "last_update": "2025-01-21 22:26:11", "author_account_age_days": 841}, "https://github.com/AARG-FAN/Image-Vector-for-ComfyUI": {"stars": 137, "last_update": "2024-06-23 14:56:16", "author_account_age_days": 867}, "https://github.com/AEmotionStudio/ComfyUI-ChristmasTheme": {"stars": 40, "last_update": "2025-06-03 13:01:58", "author_account_age_days": 470}, "https://github.com/AEmotionStudio/ComfyUI-DiscordSend": {"stars": 9, "last_update": "2025-06-03 13:03:23", "author_account_age_days": 470}, "https://github.com/AEmotionStudio/ComfyUI-EnhancedLinksandNodes": {"stars": 38, "last_update": "2025-06-03 13:02:36", "author_account_age_days": 470}, "https://github.com/AEmotionStudio/ComfyUI-MagnifyGlass": {"stars": 12, "last_update": "2025-06-13 19:26:31", "author_account_age_days": 470}, "https://github.com/AEmotionStudio/ComfyUI-ShaderNoiseKSampler": {"stars": 46, "last_update": "2025-06-20 08:52:02", "author_account_age_days": 470}, "https://github.com/AI2lab/comfyUI-siliconflow-api-2lab": {"stars": 7, "last_update": "2024-08-01 15:13:33", "author_account_age_days": 560}, "https://github.com/AIDC-AI/ComfyUI-Copilot": {"stars": 1892, "last_update": "2025-06-26 11:41:59", "author_account_age_days": 378}, "https://github.com/AIExplorer25/ComfyUI_AutoDownloadModels": {"stars": 14, "last_update": "2025-04-05 22:05:47", "author_account_age_days": 3834}, "https://github.com/AIExplorer25/ComfyUI_ChatGptHelper": {"stars": 0, "last_update": "2025-05-18 19:11:40", "author_account_age_days": 3834}, "https://github.com/AIExplorer25/ComfyUI_ImageCaptioner": {"stars": 0, "last_update": "2025-06-07 19:49:36", "author_account_age_days": 3834}, "https://github.com/AIFSH/AniTalker-ComfyUI": {"stars": 5, "last_update": "2024-08-06 03:08:44", "author_account_age_days": 599}, "https://github.com/AIFSH/ComfyUI-3d-photo-inpainting": {"stars": 14, "last_update": "2024-06-19 13:59:49", "author_account_age_days": 599}, "https://github.com/AIFSH/ComfyUI-AuraSR": {"stars": 22, "last_update": "2024-06-27 14:00:16", "author_account_age_days": 599}, "https://github.com/AIFSH/ComfyUI-DiffSynth-Studio": {"stars": 84, "last_update": "2024-08-05 08:48:03", "author_account_age_days": 599}, "https://github.com/AIFSH/ComfyUI-FishSpeech": {"stars": 39, "last_update": "2024-05-23 01:18:49", "author_account_age_days": 599}, "https://github.com/AIFSH/ComfyUI-GPT_SoVITS": {"stars": 236, "last_update": "2024-08-09 22:00:45", "author_account_age_days": 599}, "https://github.com/AIFSH/ComfyUI-Hallo": {"stars": 306, "last_update": "2024-06-24 06:43:23", "author_account_age_days": 599}, "https://github.com/AIFSH/ComfyUI-I2V-Adapter": {"stars": 22, "last_update": "2024-07-02 01:59:49", "author_account_age_days": 599}, "https://github.com/AIFSH/ComfyUI-IP_LAP": {"stars": 34, "last_update": "2024-06-14 07:05:39", "author_account_age_days": 599}, "https://github.com/AIFSH/ComfyUI-Live2DViewer": {"stars": 8, "last_update": "2024-06-14 07:04:49", "author_account_age_days": 599}, "https://github.com/AIFSH/ComfyUI-MARS5-TTS": {"stars": 29, "last_update": "2024-07-02 02:00:28", "author_account_age_days": 599}, "https://github.com/AIFSH/ComfyUI-MimicBrush": {"stars": 113, "last_update": "2024-06-17 22:26:53", "author_account_age_days": 599}, "https://github.com/AIFSH/ComfyUI-MimicMotion": {"stars": 374, "last_update": "2024-08-06 06:21:16", "author_account_age_days": 599}, "https://github.com/AIFSH/ComfyUI-MuseTalk_FSH": {"stars": 21, "last_update": "2024-06-14 07:05:19", "author_account_age_days": 599}, "https://github.com/AIFSH/ComfyUI-RVC": {"stars": 24, "last_update": "2024-06-14 07:05:25", "author_account_age_days": 599}, "https://github.com/AIFSH/ComfyUI-UVR5": {"stars": 95, "last_update": "2024-06-20 07:31:20", "author_account_age_days": 599}, "https://github.com/AIFSH/ComfyUI-UniAnimate": {"stars": 39, "last_update": "2024-06-30 09:20:25", "author_account_age_days": 599}, "https://github.com/AIFSH/ComfyUI-WhisperX": {"stars": 50, "last_update": "2025-04-01 00:14:44", "author_account_age_days": 599}, "https://github.com/AIFSH/ComfyUI-XTTS": {"stars": 59, "last_update": "2024-06-24 09:45:59", "author_account_age_days": 599}, "https://github.com/AIFSH/ComfyUI_V-Express": {"stars": 87, "last_update": "2024-06-23 09:54:57", "author_account_age_days": 599}, "https://github.com/AIFSH/CosyVoice-ComfyUI": {"stars": 264, "last_update": "2024-09-10 22:21:37", "author_account_age_days": 599}, "https://github.com/AIFSH/DHLive-ComfyUI": {"stars": 24, "last_update": "2024-11-14 01:45:45", "author_account_age_days": 599}, "https://github.com/AIFSH/DiffMorpher-ComfyUI": {"stars": 16, "last_update": "2024-07-17 01:24:59", "author_account_age_days": 599}, "https://github.com/AIFSH/DiffSynth-ComfyUI": {"stars": 0, "last_update": "2024-09-07 12:23:07", "author_account_age_days": 599}, "https://github.com/AIFSH/EchoMimicV2-ComfyUI": {"stars": 57, "last_update": "2024-12-08 08:53:21", "author_account_age_days": 599}, "https://github.com/AIFSH/EzAudio-ComfyUI": {"stars": 9, "last_update": "2024-10-08 05:22:46", "author_account_age_days": 599}, "https://github.com/AIFSH/F5-TTS-ComfyUI": {"stars": 36, "last_update": "2024-11-14 01:43:03", "author_account_age_days": 599}, "https://github.com/AIFSH/FancyVideo-ComfyUI": {"stars": 36, "last_update": "2024-10-12 07:21:51", "author_account_age_days": 599}, "https://github.com/AIFSH/FireRedTTS-ComfyUI": {"stars": 12, "last_update": "2024-10-24 01:18:51", "author_account_age_days": 599}, "https://github.com/AIFSH/GSTTS-ComfyUI": {"stars": 40, "last_update": "2024-08-25 03:23:24", "author_account_age_days": 599}, "https://github.com/AIFSH/HivisionIDPhotos-ComfyUI": {"stars": 149, "last_update": "2024-09-16 14:16:06", "author_account_age_days": 599}, "https://github.com/AIFSH/IMAGDressing-ComfyUI": {"stars": 61, "last_update": "2024-11-14 01:44:02", "author_account_age_days": 599}, "https://github.com/AIFSH/JoyHallo-ComfyUI": {"stars": 8, "last_update": "2024-11-14 01:44:39", "author_account_age_days": 599}, "https://github.com/AIFSH/MaskGCT-ComfyUI": {"stars": 61, "last_update": "2024-11-14 01:40:15", "author_account_age_days": 599}, "https://github.com/AIFSH/MiniMates-ComfyUI": {"stars": 29, "last_update": "2024-11-14 01:36:30", "author_account_age_days": 599}, "https://github.com/AIFSH/OmniGen-ComfyUI": {"stars": 209, "last_update": "2024-11-14 01:37:33", "author_account_age_days": 599}, "https://github.com/AIFSH/PyramidFlow-ComfyUI": {"stars": 14, "last_update": "2024-10-10 13:59:16", "author_account_age_days": 599}, "https://github.com/AIFSH/RealisDance-ComfyUI": {"stars": 49, "last_update": "2024-09-13 14:38:59", "author_account_age_days": 599}, "https://github.com/AIFSH/SemiChat-ComfyUI": {"stars": 11, "last_update": "2025-02-19 23:21:48", "author_account_age_days": 599}, "https://github.com/AIFSH/SenseVoice-ComfyUI": {"stars": 14, "last_update": "2024-07-16 06:41:25", "author_account_age_days": 599}, "https://github.com/AIFSH/StyleShot-ComfyUI": {"stars": 4, "last_update": "2024-08-17 00:25:29", "author_account_age_days": 599}, "https://github.com/AIFSH/VideoSys-ComfyUI": {"stars": 6, "last_update": "2024-09-01 09:11:57", "author_account_age_days": 599}, "https://github.com/AIFSH/ViewCrafter-ComfyUI": {"stars": 8, "last_update": "2024-09-19 11:11:25", "author_account_age_days": 599}, "https://github.com/AIFSH/VocalSeparation-ComfyUI": {"stars": 18, "last_update": "2024-10-24 07:16:37", "author_account_age_days": 599}, "https://github.com/AIGCTeam/ComfyUI_kkTranslator_nodes": {"stars": 7, "last_update": "2024-09-13 07:34:18", "author_account_age_days": 578}, "https://github.com/AIGODLIKE/AIGODLIKE-COMFYUI-TRANSLATION": {"stars": 2271, "last_update": "2025-03-24 00:01:12", "author_account_age_days": 869}, "https://github.com/AIGODLIKE/AIGODLIKE-ComfyUI-Studio": {"stars": 341, "last_update": "2025-01-06 11:31:20", "author_account_age_days": 869}, "https://github.com/AIGODLIKE/ComfyUI-CUP": {"stars": 48, "last_update": "2025-05-12 07:53:11", "author_account_age_days": 869}, "https://github.com/AIGODLIKE/ComfyUI-ToonCrafter": {"stars": 366, "last_update": "2024-07-17 02:28:49", "author_account_age_days": 869}, "https://github.com/AIPOQUE/ComfyUI-APQNodes": {"stars": 102, "last_update": "2024-11-21 08:56:49", "author_account_age_days": 251}, "https://github.com/AIToldMeTo/comfyui-cache-cleaner": {"stars": 6, "last_update": "2025-05-29 10:44:45", "author_account_age_days": 197}, "https://github.com/AIWarper/ComfyUI-NormalCrafterWrapper": {"stars": 61, "last_update": "2025-05-15 17:06:29", "author_account_age_days": 245}, "https://github.com/AIWarper/ComfyUI-WarperNodes": {"stars": 10, "last_update": "2025-06-25 02:11:15", "author_account_age_days": 245}, "https://github.com/AInseven/ComfyUI-fastblend": {"stars": 218, "last_update": "2024-11-22 03:32:25", "author_account_age_days": 2051}, "https://github.com/AIrjen/OneButtonPrompt": {"stars": 992, "last_update": "2025-03-28 09:17:10", "author_account_age_days": 811}, "https://github.com/AJO-reading/ComfyUI-AjoNodes": {"stars": 7, "last_update": "2025-06-16 08:10:10", "author_account_age_days": 217}, "https://github.com/AKharytonchyk/ComfyUI-telegram-bot-node": {"stars": 0, "last_update": "2025-06-23 22:03:39", "author_account_age_days": 3250}, "https://github.com/ALatentPlace/ComfyUI_yanc": {"stars": 63, "last_update": "2025-01-22 14:44:17", "author_account_age_days": 1828}, "https://github.com/ALatentPlace/YANC_LMStudio": {"stars": 10, "last_update": "2025-04-30 14:57:13", "author_account_age_days": 1828}, "https://github.com/APZmedia/APZmedia-comfy-together-lora": {"stars": 0, "last_update": "2025-02-15 13:14:17", "author_account_age_days": 2845}, "https://github.com/APZmedia/APZmedia-comfyui-fast-image-save": {"stars": 4, "last_update": "2025-04-21 19:22:43", "author_account_age_days": 2845}, "https://github.com/APZmedia/ComfyUI-APZmedia-cleanName-from-string": {"stars": 7, "last_update": "2025-04-21 19:22:10", "author_account_age_days": 2845}, "https://github.com/ARZUMATA/ComfyUI-ARZUMATA": {"stars": 4, "last_update": "2025-04-08 08:00:15", "author_account_age_days": 2135}, "https://github.com/ARZUMATA/ComfyUI-ARZUMATA-PixelIt": {"stars": 1, "last_update": "2025-06-04 13:11:52", "author_account_age_days": 2135}, "https://github.com/ARZUMATA/ComfyUI-ARZUMATA-Qwen2": {"stars": 1, "last_update": "2025-06-04 13:10:22", "author_account_age_days": 2135}, "https://github.com/Aaron-CHM/ComfyUI-z-a1111-sd-webui-DanTagGen": {"stars": 4, "last_update": "2024-07-17 03:55:26", "author_account_age_days": 1894}, "https://github.com/AbdullahAlfaraj/Comfy-Photoshop-SD": {"stars": 294, "last_update": "2024-06-14 07:04:37", "author_account_age_days": 4036}, "https://github.com/AbyssBadger0/ComfyUI_BadgerTools": {"stars": 8, "last_update": "2024-11-12 11:10:16", "author_account_age_days": 857}, "https://github.com/AbyssBadger0/ComfyUI_Kolors_awesome_prompts": {"stars": 5, "last_update": "2024-08-29 15:19:06", "author_account_age_days": 857}, "https://github.com/Acly/comfyui-inpaint-nodes": {"stars": 985, "last_update": "2025-03-31 09:53:40", "author_account_age_days": 4173}, "https://github.com/Acly/comfyui-tooling-nodes": {"stars": 511, "last_update": "2025-06-15 10:32:50", "author_account_age_days": 4173}, "https://github.com/AconexOfficial/ComfyUI_GOAT_Nodes": {"stars": 9, "last_update": "2025-05-14 08:38:12", "author_account_age_days": 1848}, "https://github.com/Aerse/ComfyUI-Seed-Nodes": {"stars": 5, "last_update": "2025-06-10 08:19:10", "author_account_age_days": 3745}, "https://github.com/AgencyMind/ComfyUI-GPU-Preprocessor-Wrapper": {"stars": 2, "last_update": "2025-06-13 08:15:25", "author_account_age_days": 228}, "https://github.com/AgencyMind/ComfyUI-Satori": {"stars": 0, "last_update": "2025-06-27 01:29:00", "author_account_age_days": 228}, "https://github.com/AhBumm/ComfyUI_BillBum_APIset_Nodes": {"stars": 10, "last_update": "2025-06-25 05:15:21", "author_account_age_days": 1177}, "https://github.com/AiMiDi/ComfyUI-Aimidi-nodes": {"stars": 0, "last_update": "2024-06-20 17:26:02", "author_account_age_days": 1644}, "https://github.com/AkashKarnatak/ComfyUI_faishme": {"stars": 0, "last_update": "2025-03-10 20:04:22", "author_account_age_days": 2121}, "https://github.com/Aksaz/comfyui-seamless-clone": {"stars": 8, "last_update": "2025-05-20 07:08:24", "author_account_age_days": 250}, "https://github.com/AlekPet/ComfyUI_Custom_Nodes_AlekPet": {"stars": 1256, "last_update": "2025-06-26 20:25:22", "author_account_age_days": 3068}, "https://github.com/Alexankharin/camera-comfyUI": {"stars": 8, "last_update": "2025-06-21 21:59:12", "author_account_age_days": 2488}, "https://github.com/Aljnk/ComfyUI-JNK-Tiny-Nodes": {"stars": 1, "last_update": "2025-06-04 07:12:02", "author_account_age_days": 3770}, "https://github.com/Altair200333/ComfyUI_Flux_1.1_PRO": {"stars": 0, "last_update": "2025-03-23 19:29:25", "author_account_age_days": 3077}, "https://github.com/Alvaroeai/ComfyUI-Text2Json": {"stars": 1, "last_update": "2024-11-26 16:40:31", "author_account_age_days": 4069}, "https://github.com/Amorano/Jovi_Capture": {"stars": 5, "last_update": "2025-05-31 18:38:22", "author_account_age_days": 5580}, "https://github.com/Amorano/Jovi_Colorizer": {"stars": 6, "last_update": "2025-05-22 20:00:19", "author_account_age_days": 5580}, "https://github.com/Amorano/Jovi_GLSL": {"stars": 16, "last_update": "2025-06-18 02:55:59", "author_account_age_days": 5580}, "https://github.com/Amorano/Jovi_MIDI": {"stars": 6, "last_update": "2025-05-05 04:11:06", "author_account_age_days": 5580}, "https://github.com/Amorano/Jovi_Measure": {"stars": 2, "last_update": "2025-05-05 04:10:36", "author_account_age_days": 5580}, "https://github.com/Amorano/Jovi_Spout": {"stars": 6, "last_update": "2025-05-29 17:34:42", "author_account_age_days": 5580}, "https://github.com/Amorano/Jovimetrix": {"stars": 357, "last_update": "2025-06-18 16:48:38", "author_account_age_days": 5580}, "https://github.com/Andro-Meta/ComfyUI-Ovis2": {"stars": 5, "last_update": "2025-03-24 04:27:56", "author_account_age_days": 639}, "https://github.com/AngelCookies/ComfyUI-Seed-Tracker": {"stars": 0, "last_update": "2025-06-23 23:56:50", "author_account_age_days": 1189}, "https://github.com/Anibaaal/ComfyUI-UX-Nodes": {"stars": 2, "last_update": "2025-01-23 13:35:49", "author_account_age_days": 3745}, "https://github.com/AonekoSS/ComfyUI-LoRA-Tuner": {"stars": 9, "last_update": "2025-03-27 17:07:38", "author_account_age_days": 4460}, "https://github.com/AonekoSS/ComfyUI-SimpleCounter": {"stars": 1, "last_update": "2025-03-27 17:08:39", "author_account_age_days": 4460}, "https://github.com/ArcherFMY/Diffusion360_ComfyUI": {"stars": 44, "last_update": "2025-03-17 06:08:17", "author_account_age_days": 3757}, "https://github.com/ArdeniusAI/ComfyUI-Ardenius": {"stars": 5, "last_update": "2024-11-24 09:57:46", "author_account_age_days": 495}, "https://github.com/Arkanun/ReadCSV_ComfyUI": {"stars": 0, "last_update": "2025-02-05 23:06:48", "author_account_age_days": 3317}, "https://github.com/ArtBot2023/CharacterFaceSwap": {"stars": 90, "last_update": "2024-05-22 20:53:09", "author_account_age_days": 661}, "https://github.com/ArtHommage/HommageTools": {"stars": 2, "last_update": "2025-05-20 20:40:25", "author_account_age_days": 900}, "https://github.com/ArtsticH/ComfyUI_EasyKitHT_NodeAlignPro": {"stars": 11, "last_update": "2025-05-01 01:49:07", "author_account_age_days": 456}, "https://github.com/AshMartian/ComfyUI-DirGir": {"stars": 25, "last_update": "2025-05-04 03:34:19", "author_account_age_days": 4954}, "https://github.com/AstroCorp/ComfyUI-AstroCorp-Nodes": {"stars": 0, "last_update": "2025-06-20 12:27:42", "author_account_age_days": 3208}, "https://github.com/AuroBit/ComfyUI-AnimateAnyone-reproduction": {"stars": 37, "last_update": "2024-06-14 09:03:24", "author_account_age_days": 758}, "https://github.com/AuroBit/ComfyUI-OOTDiffusion": {"stars": 461, "last_update": "2024-07-12 03:49:27", "author_account_age_days": 758}, "https://github.com/AustinMroz/ComfyUI-DynamicOversampling": {"stars": 0, "last_update": "2024-06-14 07:06:51", "author_account_age_days": 4442}, "https://github.com/AustinMroz/ComfyUI-MinCache": {"stars": 2, "last_update": "2024-12-25 18:52:07", "author_account_age_days": 4442}, "https://github.com/AustinMroz/ComfyUI-SpliceTools": {"stars": 6, "last_update": "2024-06-14 07:07:21", "author_account_age_days": 4442}, "https://github.com/AustinMroz/ComfyUI-WorkflowCheckpointing": {"stars": 11, "last_update": "2024-10-17 19:59:40", "author_account_age_days": 4442}, "https://github.com/Auttasak-L/ComfyUI-ImageCropper": {"stars": 1, "last_update": "2024-05-23 05:04:53", "author_account_age_days": 3017}, "https://github.com/BAIS1C/ComfyUI_RSS_Feed_Reader": {"stars": 4, "last_update": "2025-04-24 14:09:18", "author_account_age_days": 857}, "https://github.com/BIMer-99/ComfyUI_FishSpeech_EX": {"stars": 7, "last_update": "2024-12-21 11:35:08", "author_account_age_days": 1600}, "https://github.com/BIMer-99/Comfyui_Hunyuan3D_EX": {"stars": 7, "last_update": "2024-12-09 17:50:23", "author_account_age_days": 1600}, "https://github.com/BNP1111/comfyui_flux_corrector": {"stars": 4, "last_update": "2025-04-25 16:47:45", "author_account_age_days": 863}, "https://github.com/BXYMartin/ComfyUI-InstantIDUtils": {"stars": 3, "last_update": "2024-05-23 00:08:50", "author_account_age_days": 2811}, "https://github.com/BZcreativ/ComfyUI-FLUX-TOGETHER-API": {"stars": 3, "last_update": "2024-11-02 14:45:28", "author_account_age_days": 3605}, "https://github.com/BadCafeCode/masquerade-nodes-comfyui": {"stars": 426, "last_update": "2024-06-19 04:16:54", "author_account_age_days": 798}, "https://github.com/BahaC/ComfyUI-ZonosTTS": {"stars": 18, "last_update": "2025-02-19 06:28:38", "author_account_age_days": 1673}, "https://github.com/Beinsezii/bsz-cui-extras": {"stars": 24, "last_update": "2024-05-22 20:46:45", "author_account_age_days": 2594}, "https://github.com/Bellzs/ComfyUI-LoRA-Assistant": {"stars": 15, "last_update": "2025-01-27 09:47:46", "author_account_age_days": 3374}, "https://github.com/BenNarum/ComfyUI_CAS": {"stars": 3, "last_update": "2024-07-13 12:00:40", "author_account_age_days": 3440}, "https://github.com/BenNarum/SigmaWaveFormNode": {"stars": 5, "last_update": "2024-06-20 15:20:35", "author_account_age_days": 3440}, "https://github.com/BennyKok/comfyui-deploy": {"stars": 1362, "last_update": "2025-06-27 16:41:07", "author_account_age_days": 3364}, "https://github.com/BetaDoggo/ComfyUI-Cloud-APIs": {"stars": 36, "last_update": "2025-05-01 06:24:47", "author_account_age_days": 1166}, "https://github.com/BetaDoggo/ComfyUI-FastSDCPU": {"stars": 9, "last_update": "2024-09-16 05:34:01", "author_account_age_days": 1166}, "https://github.com/BetaDoggo/ComfyUI-Gatcha-Embedding": {"stars": 1, "last_update": "2024-08-28 00:24:01", "author_account_age_days": 1166}, "https://github.com/BetaDoggo/ComfyUI-VideoPlayer": {"stars": 17, "last_update": "2024-08-05 04:45:12", "author_account_age_days": 1166}, "https://github.com/BetaDoggo/ComfyUI-WDV-Nodes": {"stars": 1, "last_update": "2024-08-01 07:59:10", "author_account_age_days": 1166}, "https://github.com/BetaDoggo/ComfyUI-YetAnotherSafetyChecker": {"stars": 5, "last_update": "2024-07-19 18:11:11", "author_account_age_days": 1166}, "https://github.com/Big-Idea-Technology/ComfyUI-Book-Tools": {"stars": 26, "last_update": "2025-04-21 15:40:34", "author_account_age_days": 1240}, "https://github.com/Big-Idea-Technology/ComfyUI_LLM_Node": {"stars": 66, "last_update": "2025-04-19 11:58:55", "author_account_age_days": 1240}, "https://github.com/BigStationW/ComfyUi-RescaleCFGAdvanced": {"stars": 25, "last_update": "2025-05-07 18:10:18", "author_account_age_days": 53}, "https://github.com/BigWhiteFly/ComfyUI-ImageConcat": {"stars": 0, "last_update": "2025-05-21 01:16:27", "author_account_age_days": 2708}, "https://github.com/Billius-AI/ComfyUI-Path-Helper": {"stars": 18, "last_update": "2024-05-22 23:25:08", "author_account_age_days": 503}, "https://github.com/Bin-sam/DynamicPose-ComfyUI": {"stars": 5, "last_update": "2024-09-11 12:09:11", "author_account_age_days": 302}, "https://github.com/Black-Lioness/ComfyUI-PromptUtils": {"stars": 2, "last_update": "2024-11-22 03:05:11", "author_account_age_days": 1223}, "https://github.com/BlackVortexAI/ComfyUI-BVortexNodes": {"stars": 2, "last_update": "2024-10-23 09:19:54", "author_account_age_days": 324}, "https://github.com/BlakeOne/ComfyUI-CustomScheduler": {"stars": 17, "last_update": "2024-05-23 00:23:56", "author_account_age_days": 2901}, "https://github.com/BlakeOne/ComfyUI-NodePresets": {"stars": 12, "last_update": "2024-05-23 00:24:07", "author_account_age_days": 2901}, "https://github.com/BlakeOne/ComfyUI-NodeReset": {"stars": 3, "last_update": "2024-05-23 00:24:18", "author_account_age_days": 2901}, "https://github.com/BlakeOne/ComfyUI-SchedulerMixer": {"stars": 10, "last_update": "2024-05-23 00:23:44", "author_account_age_days": 2901}, "https://github.com/BlenderNeko/ComfyUI_ADV_CLIP_emb": {"stars": 389, "last_update": "2024-08-07 15:13:31", "author_account_age_days": 846}, "https://github.com/BlenderNeko/ComfyUI_Cutoff": {"stars": 388, "last_update": "2024-05-22 15:01:45", "author_account_age_days": 846}, "https://github.com/BlenderNeko/ComfyUI_Noise": {"stars": 298, "last_update": "2024-06-10 16:38:48", "author_account_age_days": 846}, "https://github.com/BlenderNeko/ComfyUI_SeeCoder": {"stars": 38, "last_update": "2024-05-22 14:57:04", "author_account_age_days": 846}, "https://github.com/BlenderNeko/ComfyUI_TiledKSampler": {"stars": 382, "last_update": "2024-05-22 14:56:49", "author_account_age_days": 846}, "https://github.com/Blonicx/ComfyUI-X-Rework": {"stars": 1, "last_update": "2025-05-07 17:02:20", "author_account_age_days": 1091}, "https://github.com/BlueprintCoding/ComfyUI_AIDocsClinicalTools": {"stars": 4, "last_update": "2025-02-22 17:07:39", "author_account_age_days": 812}, "https://github.com/BobRandomNumber/ComfyUI-DiaTTS": {"stars": 8, "last_update": "2025-06-02 03:02:19", "author_account_age_days": 211}, "https://github.com/BobsBlazed/Bobs-Lora-Loader": {"stars": 1, "last_update": "2025-06-27 02:18:59", "author_account_age_days": 2581}, "https://github.com/BobsBlazed/Bobs_Latent_Optimizer": {"stars": 38, "last_update": "2025-06-04 03:00:27", "author_account_age_days": 2581}, "https://github.com/BoyuanJiang/FitDiT-ComfyUI": {"stars": 97, "last_update": "2025-01-21 12:09:05", "author_account_age_days": 3449}, "https://github.com/Bria-AI/ComfyUI-BRIA-API": {"stars": 43, "last_update": "2025-06-16 13:24:17", "author_account_age_days": 1846}, "https://github.com/BuffMcBigHuge/ComfyUI-Zonos": {"stars": 71, "last_update": "2025-04-29 21:48:07", "author_account_age_days": 3280}, "https://github.com/Burgstall-labs/ComfyUI-BETA-Cropnodes": {"stars": 4, "last_update": "2025-06-26 11:35:33", "author_account_age_days": 158}, "https://github.com/Burgstall-labs/ComfyUI-BETA-Helpernodes": {"stars": 4, "last_update": "2025-06-26 11:35:33", "author_account_age_days": 158}, "https://github.com/Burgstall-labs/ComfyUI-BS-Textchop": {"stars": 0, "last_update": "2025-04-05 07:45:54", "author_account_age_days": 158}, "https://github.com/Burgstall-labs/ComfyUI-BS_Kokoro-onnx": {"stars": 36, "last_update": "2025-01-19 19:05:24", "author_account_age_days": 158}, "https://github.com/CC-BryanOttho/ComfyUI_API_Manager": {"stars": 23, "last_update": "2024-06-14 07:13:34", "author_account_age_days": 853}, "https://github.com/CC-SUN6/ccsun_node": {"stars": 0, "last_update": "2025-02-12 07:58:41", "author_account_age_days": 736}, "https://github.com/CHAOSEA/ComfyUI_FaceAlignPaste": {"stars": 12, "last_update": "2025-03-27 13:34:40", "author_account_age_days": 333}, "https://github.com/CY-CHENYUE/ComfyUI-FramePack-HY": {"stars": 18, "last_update": "2025-05-08 09:38:09", "author_account_age_days": 566}, "https://github.com/CY-CHENYUE/ComfyUI-Free-GPU": {"stars": 10, "last_update": "2025-02-16 16:30:36", "author_account_age_days": 566}, "https://github.com/CY-CHENYUE/ComfyUI-GPT-API": {"stars": 70, "last_update": "2025-04-17 09:51:35", "author_account_age_days": 566}, "https://github.com/CY-CHENYUE/ComfyUI-Gemini-API": {"stars": 217, "last_update": "2025-05-08 05:52:02", "author_account_age_days": 566}, "https://github.com/CY-CHENYUE/ComfyUI-InpaintEasy": {"stars": 74, "last_update": "2025-01-24 16:09:46", "author_account_age_days": 566}, "https://github.com/CY-CHENYUE/ComfyUI-Janus-Pro": {"stars": 608, "last_update": "2025-01-30 08:08:20", "author_account_age_days": 566}, "https://github.com/CY-CHENYUE/ComfyUI-MiniCPM-Plus": {"stars": 23, "last_update": "2024-10-09 06:56:04", "author_account_age_days": 566}, "https://github.com/CY-CHENYUE/ComfyUI-MiniCPM-o": {"stars": 34, "last_update": "2025-02-16 18:52:28", "author_account_age_days": 566}, "https://github.com/CY-CHENYUE/ComfyUI-Molmo": {"stars": 130, "last_update": "2024-10-14 15:06:36", "author_account_age_days": 566}, "https://github.com/CY-CHENYUE/ComfyUI-OmniGenX": {"stars": 6, "last_update": "2025-01-24 16:13:13", "author_account_age_days": 566}, "https://github.com/CY-CHENYUE/ComfyUI-Redux-Prompt": {"stars": 89, "last_update": "2025-01-24 15:43:29", "author_account_age_days": 566}, "https://github.com/CYBERLOOM-INC/ComfyUI-nodes-hnmr": {"stars": 9, "last_update": "2024-05-22 17:55:41", "author_account_age_days": 617}, "https://github.com/CavinHuang/comfyui-nodes-docs": {"stars": 235, "last_update": "2025-03-26 02:12:29", "author_account_age_days": 3094}, "https://github.com/Chan-0312/ComfyUI-EasyDeforum": {"stars": 11, "last_update": "2024-05-22 23:22:14", "author_account_age_days": 2234}, "https://github.com/Chan-0312/ComfyUI-IPAnimate": {"stars": 73, "last_update": "2024-05-22 23:22:03", "author_account_age_days": 2234}, "https://github.com/Chan-0312/ComfyUI-Prompt-Preview": {"stars": 34, "last_update": "2024-06-14 09:01:37", "author_account_age_days": 2234}, "https://github.com/Chaoses-Ib/ComfyUI_Ib_CustomNodes": {"stars": 38, "last_update": "2025-02-08 13:11:30", "author_account_age_days": 2247}, "https://github.com/Charlweed/image_transceiver": {"stars": 2, "last_update": "2025-01-06 19:22:50", "author_account_age_days": 5465}, "https://github.com/Charonartist/Comfyui_gemini_tts_node": {"stars": 0, "last_update": "2025-05-26 01:17:59", "author_account_age_days": 362}, "https://github.com/Charonartist/comfyui-auto-lora-v2": {"stars": 0, "last_update": "2025-06-17 15:00:30", "author_account_age_days": 362}, "https://github.com/ChenDarYen/ComfyUI-NAG": {"stars": 42, "last_update": "2025-06-25 23:23:43", "author_account_age_days": 2269}, "https://github.com/ChenDarYen/ComfyUI-TimestepShiftModel": {"stars": 8, "last_update": "2025-01-07 18:22:10", "author_account_age_days": 2269}, "https://github.com/Chengym2023/ComfyUI-DeepSeek_Online": {"stars": 0, "last_update": "2025-04-07 01:09:05", "author_account_age_days": 767}, "https://github.com/ChrisColeTech/ComfyUI-Elegant-Resource-Monitor": {"stars": 13, "last_update": "2024-09-23 21:48:27", "author_account_age_days": 2780}, "https://github.com/ChrisColeTech/ComfyUI-Line-counter": {"stars": 2, "last_update": "2025-03-12 00:07:25", "author_account_age_days": 2780}, "https://github.com/Chrisvenator/ComfyUI-Painting-by-colors-generator": {"stars": 0, "last_update": "2025-06-03 07:56:17", "author_account_age_days": 2096}, "https://github.com/ClownsharkBatwing/RES4LYF": {"stars": 272, "last_update": "2025-06-27 02:26:51", "author_account_age_days": 397}, "https://github.com/Clybius/ComfyUI-ClybsChromaNodes": {"stars": 8, "last_update": "2025-06-18 17:09:18", "author_account_age_days": 2102}, "https://github.com/Clybius/ComfyUI-Extra-Samplers": {"stars": 86, "last_update": "2024-11-15 17:21:45", "author_account_age_days": 2102}, "https://github.com/Clybius/ComfyUI-Latent-Modifiers": {"stars": 80, "last_update": "2024-06-14 09:02:44", "author_account_age_days": 2102}, "https://github.com/CoiiChan/ComfyUI-Depth-Visualization-Advanced": {"stars": 4, "last_update": "2025-06-17 03:43:27", "author_account_age_days": 2265}, "https://github.com/CoiiChan/ComfyUI-FuncAsTexture-CoiiNode": {"stars": 1, "last_update": "2025-06-24 03:34:32", "author_account_age_days": 2265}, "https://github.com/CoiiChan/comfyui-every-person-seg-coii": {"stars": 3, "last_update": "2025-06-20 08:51:56", "author_account_age_days": 2265}, "https://github.com/ComfyAssets/ComfyUI-KikoStats": {"stars": 0, "last_update": "2025-06-21 15:03:38", "author_account_age_days": 33}, "https://github.com/ComfyAssets/ComfyUI-KikoTools": {"stars": 0, "last_update": "2025-06-20 15:03:50", "author_account_age_days": 33}, "https://github.com/ComfyAssets/ComfyUI_PromptManager": {"stars": 22, "last_update": "2025-06-19 23:56:57", "author_account_age_days": 33}, "https://github.com/ComfyAssets/ComfyUI_Selectors": {"stars": 0, "last_update": "2025-06-13 16:13:05", "author_account_age_days": 33}, "https://github.com/ComfyUI-JH/ComfyUI-JH-Misc-Nodes": {"stars": 1, "last_update": "2024-12-28 19:44:14", "author_account_age_days": 190}, "https://github.com/ComfyUI-JH/ComfyUI-JH-XMP-Metadata-Nodes": {"stars": 2, "last_update": "2024-12-31 21:44:05", "author_account_age_days": 190}, "https://github.com/ComplexRobot/ComfyUI-Simple-VFI": {"stars": 0, "last_update": "2025-03-31 15:37:25", "author_account_age_days": 4791}, "https://github.com/Conor-Collins/ComfyUI-CoCoTools_IO": {"stars": 45, "last_update": "2025-06-17 22:17:57", "author_account_age_days": 541}, "https://github.com/CosmicLaca/ComfyUI_Primere_Nodes": {"stars": 124, "last_update": "2025-06-14 14:59:08", "author_account_age_days": 4023}, "https://github.com/CpreForEver/CFE_comfyui": {"stars": 0, "last_update": "2024-12-09 01:38:42", "author_account_age_days": 320}, "https://github.com/Creeper-MZ/comfyui_nai_api": {"stars": 0, "last_update": "2024-10-02 21:30:26", "author_account_age_days": 1371}, "https://github.com/Creepybits/ComfyUI-Creepy_nodes": {"stars": 11, "last_update": "2025-06-26 07:45:13", "author_account_age_days": 1975}, "https://github.com/Cryptyox/anaglyphTool-Comfyui": {"stars": 7, "last_update": "2025-05-13 16:12:27", "author_account_age_days": 1295}, "https://github.com/Curt-Park/human-parser-comfyui-node-in-pure-python": {"stars": 3, "last_update": "2025-03-18 00:51:34", "author_account_age_days": 3553}, "https://github.com/CyanAutumn/ComfyUi_Random_Manage_Cyan": {"stars": 3, "last_update": "2024-12-19 10:54:08", "author_account_age_days": 1469}, "https://github.com/Cyber-BlackCat/ComfyUI-Image-Vector": {"stars": 2, "last_update": "2025-04-27 05:40:25", "author_account_age_days": 783}, "https://github.com/Cyber-BlackCat/ComfyUI-MoneyMaker": {"stars": 10, "last_update": "2025-06-27 10:04:22", "author_account_age_days": 783}, "https://github.com/Cyber-BlackCat/ComfyUI_Auto_Caption": {"stars": 13, "last_update": "2025-05-29 02:14:55", "author_account_age_days": 783}, "https://github.com/Cyberschorsch/ComfyUI-checkpoint-config-loader": {"stars": 1, "last_update": "2024-07-31 13:54:16", "author_account_age_days": 5526}, "https://github.com/DJ-Tribefull/Comfyui_FOCUS_nodes": {"stars": 5, "last_update": "2025-02-02 00:46:30", "author_account_age_days": 156}, "https://github.com/Danand/ComfyUI-ComfyCouple": {"stars": 60, "last_update": "2024-08-10 22:24:01", "author_account_age_days": 4657}, "https://github.com/DanielHabib/ComfyUI-Voxels": {"stars": 4, "last_update": "2024-09-16 15:41:02", "author_account_age_days": 3952}, "https://github.com/Danteday/ComfyUI-NoteManager": {"stars": 10, "last_update": "2025-04-20 19:52:58", "author_account_age_days": 2684}, "https://github.com/DareFail/ComfyUI-Roboflow": {"stars": 34, "last_update": "2024-09-25 18:30:43", "author_account_age_days": 4955}, "https://github.com/DarioFT/ComfyUI-VideoDirCombiner": {"stars": 5, "last_update": "2025-03-08 13:58:12", "author_account_age_days": 3845}, "https://github.com/DataCTE/prompt_injection": {"stars": 91, "last_update": "2024-06-21 12:56:43", "author_account_age_days": 1146}, "https://github.com/Dayuppy/ComfyUI-DiscordWebhook": {"stars": 3, "last_update": "2024-10-12 05:12:07", "author_account_age_days": 4584}, "https://github.com/De-Zoomer/ComfyUI-DeZoomer-Nodes": {"stars": 7, "last_update": "2025-06-20 16:45:43", "author_account_age_days": 1222}, "https://github.com/DeJoker/pipeline-parallel-comfy": {"stars": 3, "last_update": "2024-07-29 06:59:37", "author_account_age_days": 3359}, "https://github.com/DebugPadawan/DebugPadawans-ComfyUI-Essentials": {"stars": 0, "last_update": "2025-06-21 12:46:56", "author_account_age_days": 168}, "https://github.com/Deep-Neko/ComfyUI_ascii_art": {"stars": 1, "last_update": "2025-02-24 13:07:36", "author_account_age_days": 122}, "https://github.com/Derfuu/Derfuu_ComfyUI_ModdedNodes": {"stars": 414, "last_update": "2024-06-22 02:12:19", "author_account_age_days": 2143}, "https://github.com/DesertPixelAi/ComfyUI-Desert-Pixel-Nodes": {"stars": 16, "last_update": "2025-06-22 10:33:31", "author_account_age_days": 513}, "https://github.com/DiaoDaiaChan/ComfyUI_API_Request": {"stars": 3, "last_update": "2025-06-02 14:54:47", "author_account_age_days": 852}, "https://github.com/DiffusionWave/PickResolution_DiffusionWave": {"stars": 0, "last_update": "2025-05-19 23:16:22", "author_account_age_days": 89}, "https://github.com/DigitalIO/ComfyUI-stable-wildcards": {"stars": 25, "last_update": "2025-03-17 17:53:33", "author_account_age_days": 4407}, "https://github.com/DimaChaichan/LAizypainter-Exporter-ComfyUI": {"stars": 6, "last_update": "2024-05-22 23:14:06", "author_account_age_days": 3441}, "https://github.com/Diohim/ComfyUI-Unusual-Tools": {"stars": 0, "last_update": "2025-03-17 12:47:19", "author_account_age_days": 147}, "https://github.com/Dobidop/ComfyStereo": {"stars": 23, "last_update": "2025-03-23 18:45:54", "author_account_age_days": 1828}, "https://github.com/DoctorDiffusion/ComfyUI-BEN": {"stars": 41, "last_update": "2024-12-15 18:19:01", "author_account_age_days": 710}, "https://github.com/DoctorDiffusion/ComfyUI-MediaMixer": {"stars": 19, "last_update": "2024-12-05 03:05:44", "author_account_age_days": 710}, "https://github.com/DoctorDiffusion/ComfyUI-Schedulizer": {"stars": 6, "last_update": "2024-11-30 03:13:29", "author_account_age_days": 710}, "https://github.com/DoctorDiffusion/ComfyUI-SnakeOil": {"stars": 4, "last_update": "2024-12-31 00:59:19", "author_account_age_days": 710}, "https://github.com/DoctorDiffusion/ComfyUI-basic-pitch": {"stars": 1, "last_update": "2024-12-25 19:07:11", "author_account_age_days": 710}, "https://github.com/Dontdrunk/ComfyUI-DD-Nodes": {"stars": 58, "last_update": "2025-06-24 11:10:27", "author_account_age_days": 3271}, "https://github.com/Dontdrunk/ComfyUI-DD-Translation": {"stars": 238, "last_update": "2025-06-27 05:31:11", "author_account_age_days": 3271}, "https://github.com/DrJKL/ComfyUI-Anchors": {"stars": 6, "last_update": "2024-06-20 18:23:00", "author_account_age_days": 5363}, "https://github.com/DrMWeigand/ComfyUI-StereoVision": {"stars": 9, "last_update": "2025-02-04 14:24:46", "author_account_age_days": 1400}, "https://github.com/DrMWeigand/ComfyUI_ColorImageDetection": {"stars": 3, "last_update": "2024-07-15 13:21:10", "author_account_age_days": 1400}, "https://github.com/DrStone71/ComfyUI-Prompt-Translator": {"stars": 0, "last_update": "2025-06-17 00:22:24", "author_account_age_days": 313}, "https://github.com/DraconicDragon/ComfyUI-RyuuNoodles": {"stars": 3, "last_update": "2025-06-25 03:59:19", "author_account_age_days": 1741}, "https://github.com/DraconicDragon/ComfyUI-Venice-API": {"stars": 5, "last_update": "2025-06-16 18:58:57", "author_account_age_days": 1741}, "https://github.com/DragonDiffusionbyBoyo/BoyoSupercoolWrapper": {"stars": 4, "last_update": "2025-06-01 16:44:46", "author_account_age_days": 179}, "https://github.com/DragonDiffusionbyBoyo/Boyonodes": {"stars": 2, "last_update": "2025-05-19 22:59:06", "author_account_age_days": 179}, "https://github.com/Duanyll/duanyll_nodepack": {"stars": 0, "last_update": "2025-03-12 08:41:14", "author_account_age_days": 3100}, "https://github.com/Eagle-CN/ComfyUI-Addoor": {"stars": 50, "last_update": "2025-04-25 01:03:58", "author_account_age_days": 2994}, "https://github.com/Easymode-ai/ComfyUI-BPT": {"stars": 8, "last_update": "2025-02-28 00:32:37", "author_account_age_days": 1640}, "https://github.com/Easymode-ai/ComfyUI-ShadowR": {"stars": 10, "last_update": "2025-02-21 20:53:27", "author_account_age_days": 1640}, "https://github.com/EeroHeikkinen/ComfyUI-eesahesNodes": {"stars": 70, "last_update": "2024-09-01 11:43:02", "author_account_age_days": 5085}, "https://github.com/Elaine-chennn/comfyui-overlay-media": {"stars": 0, "last_update": "2024-10-09 11:07:46", "author_account_age_days": 1511}, "https://github.com/Electrofried/ComfyUI-OpenAINode": {"stars": 28, "last_update": "2024-06-14 09:01:22", "author_account_age_days": 2988}, "https://github.com/EllangoK/ComfyUI-post-processing-nodes": {"stars": 224, "last_update": "2025-01-20 07:16:46", "author_account_age_days": 3146}, "https://github.com/EmAySee/ComfyUI_EmAySee_CustomNodes": {"stars": 1, "last_update": "2025-05-07 19:52:32", "author_account_age_days": 1956}, "https://github.com/EnragedAntelope/ComfyUI-ConstrainResolution": {"stars": 5, "last_update": "2025-03-30 13:06:11", "author_account_age_days": 337}, "https://github.com/EnragedAntelope/ComfyUI-Doubutsu-Describer": {"stars": 10, "last_update": "2025-03-30 13:06:28", "author_account_age_days": 337}, "https://github.com/EnragedAntelope/ComfyUI-EACloudNodes": {"stars": 6, "last_update": "2025-04-22 00:44:56", "author_account_age_days": 337}, "https://github.com/EnragedAntelope/comfyui-relight": {"stars": 73, "last_update": "2025-05-16 16:06:28", "author_account_age_days": 337}, "https://github.com/Erehr/ComfyUI-EreNodes": {"stars": 23, "last_update": "2025-06-23 06:46:10", "author_account_age_days": 3638}, "https://github.com/EvilBT/ComfyUI_SLK_joy_caption_two": {"stars": 574, "last_update": "2025-06-18 23:00:26", "author_account_age_days": 3967}, "https://github.com/Excidos/ComfyUI-Documents": {"stars": 54, "last_update": "2024-07-11 20:15:21", "author_account_age_days": 375}, "https://github.com/Excidos/ComfyUI-Lumina-Next-SFT-DiffusersWrapper": {"stars": 17, "last_update": "2024-07-30 10:27:07", "author_account_age_days": 375}, "https://github.com/ExponentialML/ComfyUI_ModelScopeT2V": {"stars": 27, "last_update": "2024-05-23 00:12:17", "author_account_age_days": 1992}, "https://github.com/ExponentialML/ComfyUI_Native_DynamiCrafter": {"stars": 112, "last_update": "2024-06-08 02:33:02", "author_account_age_days": 1992}, "https://github.com/ExponentialML/ComfyUI_VisualStylePrompting": {"stars": 301, "last_update": "2024-05-23 00:12:41", "author_account_age_days": 1992}, "https://github.com/ExterminanzHS/Gecco-Discord-Autosend": {"stars": 1, "last_update": "2024-09-05 12:33:30", "author_account_age_days": 3576}, "https://github.com/Extraltodeus/ComfyUI-AutomaticCFG": {"stars": 414, "last_update": "2024-09-10 17:44:50", "author_account_age_days": 3518}, "https://github.com/Extraltodeus/DistanceSampler": {"stars": 35, "last_update": "2025-06-19 22:54:08", "author_account_age_days": 3518}, "https://github.com/Extraltodeus/LoadLoraWithTags": {"stars": 76, "last_update": "2025-02-25 18:12:40", "author_account_age_days": 3518}, "https://github.com/Extraltodeus/Negative-attention-for-ComfyUI-": {"stars": 9, "last_update": "2025-03-20 15:10:24", "author_account_age_days": 3518}, "https://github.com/Extraltodeus/Skimmed_CFG": {"stars": 195, "last_update": "2024-10-25 20:59:10", "author_account_age_days": 3518}, "https://github.com/Extraltodeus/Stable-Diffusion-temperature-settings": {"stars": 43, "last_update": "2024-07-10 00:27:51", "author_account_age_days": 3518}, "https://github.com/Extraltodeus/Uncond-Zero-for-ComfyUI": {"stars": 48, "last_update": "2024-07-10 00:27:36", "author_account_age_days": 3518}, "https://github.com/Extraltodeus/Vector_Sculptor_ComfyUI": {"stars": 121, "last_update": "2024-08-28 05:29:07", "author_account_age_days": 3518}, "https://github.com/Extraltodeus/noise_latent_perlinpinpin": {"stars": 33, "last_update": "2024-08-13 14:19:11", "author_account_age_days": 3518}, "https://github.com/Extraltodeus/pre_cfg_comfy_nodes_for_ComfyUI": {"stars": 47, "last_update": "2025-05-24 07:36:22", "author_account_age_days": 3518}, "https://github.com/Extraltodeus/sigmas_tools_and_the_golden_scheduler": {"stars": 82, "last_update": "2024-12-13 00:18:40", "author_account_age_days": 3518}, "https://github.com/FaberVS/MultiModel": {"stars": 1, "last_update": "2025-05-06 14:27:08", "author_account_age_days": 2138}, "https://github.com/Fannovel16/ComfyUI-Frame-Interpolation": {"stars": 734, "last_update": "2025-04-30 11:32:27", "author_account_age_days": 3500}, "https://github.com/Fannovel16/ComfyUI-MagickWand": {"stars": 113, "last_update": "2025-03-31 10:26:14", "author_account_age_days": 3500}, "https://github.com/Fannovel16/ComfyUI-MotionDiff": {"stars": 201, "last_update": "2024-08-01 01:01:53", "author_account_age_days": 3500}, "https://github.com/Fannovel16/ComfyUI-Video-Matting": {"stars": 208, "last_update": "2024-08-14 01:28:50", "author_account_age_days": 3500}, "https://github.com/Fannovel16/comfyui_controlnet_aux": {"stars": 3130, "last_update": "2025-06-20 08:57:29", "author_account_age_days": 3500}, "https://github.com/Fantaxico/ComfyUI-GCP-Storage": {"stars": 3, "last_update": "2024-06-14 09:05:52", "author_account_age_days": 904}, "https://github.com/Feidorian/feidorian-ComfyNodes": {"stars": 5, "last_update": "2024-06-20 11:31:37", "author_account_age_days": 3123}, "https://github.com/FewBox/fewbox-outfit-comfyui": {"stars": 0, "last_update": "2025-04-27 01:02:28", "author_account_age_days": 2982}, "https://github.com/Fictiverse/ComfyUI_Fictiverse": {"stars": 14, "last_update": "2024-12-02 16:48:03", "author_account_age_days": 1040}, "https://github.com/Fihade/IC-Light-ComfyUI-Node": {"stars": 8, "last_update": "2024-07-02 03:47:17", "author_account_age_days": 3115}, "https://github.com/FinetunersAI/ComfyUI_Finetuners_Suite": {"stars": 2, "last_update": "2025-01-30 08:30:13", "author_account_age_days": 388}, "https://github.com/FizzleDorf/ComfyUI-AIT": {"stars": 52, "last_update": "2024-06-22 03:13:05", "author_account_age_days": 2348}, "https://github.com/FizzleDorf/ComfyUI_FizzNodes": {"stars": 445, "last_update": "2024-10-29 01:51:46", "author_account_age_days": 2348}, "https://github.com/Flow-two/ComfyUI-WanStartEndFramesNative": {"stars": 77, "last_update": "2025-03-28 04:58:45", "author_account_age_days": 1875}, "https://github.com/FlyingFireCo/tiled_ksampler": {"stars": 86, "last_update": "2024-05-22 23:15:17", "author_account_age_days": 1001}, "https://github.com/ForeignGods/ComfyUI-Mana-Nodes": {"stars": 234, "last_update": "2024-05-29 18:29:05", "author_account_age_days": 1612}, "https://github.com/Franck-Demongin/NX_HuggingFace_Flux": {"stars": 4, "last_update": "2024-08-14 02:17:21", "author_account_age_days": 2139}, "https://github.com/Franck-Demongin/NX_PromptStyler": {"stars": 9, "last_update": "2024-05-22 23:25:21", "author_account_age_days": 2139}, "https://github.com/Franck-Demongin/NX_Translator": {"stars": 1, "last_update": "2024-08-14 02:17:01", "author_account_age_days": 2139}, "https://github.com/FredBill1/comfyui-fb-utils": {"stars": 1, "last_update": "2025-03-14 08:09:14", "author_account_age_days": 2685}, "https://github.com/FunnyFinger/ComfyUi-RadarWeightNode": {"stars": 1, "last_update": "2025-04-22 09:12:55", "author_account_age_days": 943}, "https://github.com/FunnyFinger/Dynamic_Sliders_stack": {"stars": 2, "last_update": "2025-04-22 10:00:31", "author_account_age_days": 943}, "https://github.com/FuouM/ComfyUI-EbSynth": {"stars": 91, "last_update": "2025-03-30 06:30:52", "author_account_age_days": 2049}, "https://github.com/FuouM/ComfyUI-FirstOrderMM": {"stars": 5, "last_update": "2025-03-27 12:22:31", "author_account_age_days": 2049}, "https://github.com/FuouM/ComfyUI-MatAnyone": {"stars": 9, "last_update": "2025-03-24 03:43:48", "author_account_age_days": 2049}, "https://github.com/FuouM/ComfyUI-StyleTransferPlus": {"stars": 11, "last_update": "2025-03-27 12:15:58", "author_account_age_days": 2049}, "https://github.com/FuouM/FM_nodes": {"stars": 5, "last_update": "2025-03-27 12:16:55", "author_account_age_days": 2049}, "https://github.com/Fuwuffyi/ComfyUI-VisualArea-Nodes": {"stars": 71, "last_update": "2024-11-05 17:00:49", "author_account_age_days": 1531}, "https://github.com/G-370/ComfyUI-SD3-Powerlab": {"stars": 20, "last_update": "2024-06-22 19:17:18", "author_account_age_days": 1887}, "https://github.com/GACLove/ComfyUI-Lightx2vWrapper": {"stars": 5, "last_update": "2025-06-27 04:26:43", "author_account_age_days": 3985}, "https://github.com/GHOSTLXH/ComfyUI-Counternodes": {"stars": 10, "last_update": "2025-02-20 12:58:43", "author_account_age_days": 2536}, "https://github.com/GTSuya-Studio/ComfyUI-Gtsuya-Nodes": {"stars": 12, "last_update": "2024-05-22 21:31:52", "author_account_age_days": 2925}, "https://github.com/GadzoinksOfficial/comfyui_gprompts": {"stars": 0, "last_update": "2025-05-16 05:25:09", "author_account_age_days": 524}, "https://github.com/GadzoinksOfficial/gadzoinks_ComfyUI": {"stars": 0, "last_update": "2025-05-12 09:51:17", "author_account_age_days": 524}, "https://github.com/GamingDaveUk/daves_nodes": {"stars": 0, "last_update": "2025-02-22 06:22:19", "author_account_age_days": 799}, "https://github.com/Gary-yeh/ComfyUI-WebPrompter": {"stars": 0, "last_update": "2025-06-26 08:24:16", "author_account_age_days": 855}, "https://github.com/Gary-yeh/comfyui-super-captioner": {"stars": 0, "last_update": "2025-06-26 03:47:55", "author_account_age_days": 855}, "https://github.com/GavChap/ComfyUI-SD3LatentSelectRes": {"stars": 13, "last_update": "2025-03-07 14:22:14", "author_account_age_days": 4936}, "https://github.com/GeekyGhost/ComfyUI-Geeky-Kokoro-TTS": {"stars": 31, "last_update": "2025-03-21 11:44:13", "author_account_age_days": 1038}, "https://github.com/GeekyGhost/ComfyUI-GeekyRemB": {"stars": 45, "last_update": "2025-04-09 05:27:46", "author_account_age_days": 1038}, "https://github.com/GentlemanHu/ComfyUI-SunoAI": {"stars": 19, "last_update": "2024-12-17 11:46:33", "author_account_age_days": 2751}, "https://github.com/GiusTex/ComfyUI-DiffusersImageOutpaint": {"stars": 86, "last_update": "2025-05-20 10:59:38", "author_account_age_days": 1034}, "https://github.com/Goktug/comfyui-saveimage-plus": {"stars": 12, "last_update": "2024-11-13 06:03:10", "author_account_age_days": 5300}, "https://github.com/Goshe-nite/comfyui-gps-supplements": {"stars": 2, "last_update": "2025-05-14 20:52:22", "author_account_age_days": 1028}, "https://github.com/Gourieff/ComfyUI-ReActor": {"stars": 624, "last_update": "2025-05-26 16:30:58", "author_account_age_days": 1488}, "https://github.com/GraftingRayman/ComfyUI-PuLID-Flux-GR": {"stars": 55, "last_update": "2025-02-24 07:15:35", "author_account_age_days": 531}, "https://github.com/GraftingRayman/ComfyUI_GraftingRayman": {"stars": 61, "last_update": "2025-04-22 06:50:24", "author_account_age_days": 531}, "https://github.com/GraftingRayman/ComfyUI_QueueTube": {"stars": 0, "last_update": "2025-01-08 20:59:13", "author_account_age_days": 531}, "https://github.com/GrailGreg/images_base64": {"stars": 1, "last_update": "2025-05-13 07:12:00", "author_account_age_days": 113}, "https://github.com/GreenLandisaLie/AuraSR-ComfyUI": {"stars": 186, "last_update": "2024-09-04 10:58:03", "author_account_age_days": 1565}, "https://github.com/GrenKain/PixelArt-Processing-Nodes-for-ComfyUI": {"stars": 7, "last_update": "2024-09-06 11:37:05", "author_account_age_days": 2778}, "https://github.com/GroxicTinch/EasyUI-ComfyUI": {"stars": 5, "last_update": "2025-05-16 07:54:32", "author_account_age_days": 3313}, "https://github.com/GrvBdgr/comfyui-negativewildcardsprocessor": {"stars": 1, "last_update": "2024-11-15 19:46:39", "author_account_age_days": 241}, "https://github.com/Gue-e/ComfyUI-PanoCard": {"stars": 11, "last_update": "2025-06-23 08:57:03", "author_account_age_days": 2451}, "https://github.com/Guillaume-Fgt/ComfyUI_StableCascadeLatentRatio": {"stars": 3, "last_update": "2024-06-14 08:59:42", "author_account_age_days": 1848}, "https://github.com/HAL41/ComfyUI-aichemy-nodes": {"stars": 4, "last_update": "2024-05-22 23:10:19", "author_account_age_days": 3214}, "https://github.com/HECer/ComfyUI-FilePathCreator": {"stars": 7, "last_update": "2025-04-17 16:32:12", "author_account_age_days": 3348}, "https://github.com/HJH-AILab/ComfyUI_CosyVoice2": {"stars": 3, "last_update": "2025-05-21 08:36:14", "author_account_age_days": 136}, "https://github.com/HJH-AILab/ComfyUI_StableAnimator": {"stars": 16, "last_update": "2025-04-24 02:45:32", "author_account_age_days": 136}, "https://github.com/HM-RunningHub/ComfyUI_RH_APICall": {"stars": 64, "last_update": "2025-05-04 16:35:02", "author_account_age_days": 196}, "https://github.com/HM-RunningHub/ComfyUI_RH_FramePack": {"stars": 179, "last_update": "2025-05-05 18:32:28", "author_account_age_days": 196}, "https://github.com/HM-RunningHub/ComfyUI_RH_OminiControl": {"stars": 136, "last_update": "2024-12-20 08:41:09", "author_account_age_days": 196}, "https://github.com/HM-RunningHub/ComfyUI_RH_Step1XEdit": {"stars": 24, "last_update": "2025-04-30 17:12:58", "author_account_age_days": 196}, "https://github.com/HM-RunningHub/ComfyUI_RH_UNO": {"stars": 51, "last_update": "2025-04-15 17:12:25", "author_account_age_days": 196}, "https://github.com/Haiper-ai/ComfyUI-HaiperAI-API": {"stars": 13, "last_update": "2024-12-06 18:08:50", "author_account_age_days": 1368}, "https://github.com/HannibalP/comfyui-HannibalPack": {"stars": 1, "last_update": "2025-03-11 23:36:33", "author_account_age_days": 2972}, "https://github.com/Haoming02/comfyui-clear-screen": {"stars": 1, "last_update": "2025-03-14 06:47:03", "author_account_age_days": 1699}, "https://github.com/Haoming02/comfyui-diffusion-cg": {"stars": 99, "last_update": "2024-10-12 13:39:00", "author_account_age_days": 1699}, "https://github.com/Haoming02/comfyui-floodgate": {"stars": 31, "last_update": "2025-03-14 06:46:50", "author_account_age_days": 1699}, "https://github.com/Haoming02/comfyui-menu-anchor": {"stars": 3, "last_update": "2024-10-19 11:42:51", "author_account_age_days": 1699}, "https://github.com/Haoming02/comfyui-node-beautify": {"stars": 8, "last_update": "2025-03-14 06:46:56", "author_account_age_days": 1699}, "https://github.com/Haoming02/comfyui-old-photo-restoration": {"stars": 47, "last_update": "2025-05-14 05:36:27", "author_account_age_days": 1699}, "https://github.com/Haoming02/comfyui-prompt-format": {"stars": 34, "last_update": "2024-09-20 04:29:03", "author_account_age_days": 1699}, "https://github.com/Haoming02/comfyui-resharpen": {"stars": 48, "last_update": "2024-08-20 05:21:20", "author_account_age_days": 1699}, "https://github.com/Haoming02/comfyui-tab-handler": {"stars": 4, "last_update": "2024-09-09 09:20:58", "author_account_age_days": 1699}, "https://github.com/HavocsCall/comfyui_HavocsCall_Custom_Nodes": {"stars": 3, "last_update": "2025-06-07 18:56:34", "author_account_age_days": 2282}, "https://github.com/HaydenReeve/ComfyUI-Better-Strings": {"stars": 2, "last_update": "2025-03-27 12:41:28", "author_account_age_days": 2610}, "https://github.com/HeadshotPro/ComfyUI-HeadshotPro": {"stars": 1, "last_update": "2024-08-14 04:00:34", "author_account_age_days": 722}, "https://github.com/HebelHuber/comfyui-enhanced-save-node": {"stars": 2, "last_update": "2024-06-14 08:59:28", "author_account_age_days": 2692}, "https://github.com/HellerCommaA/ComfyUI-VideoResolutions": {"stars": 1, "last_update": "2025-03-28 14:51:23", "author_account_age_days": 4695}, "https://github.com/Hellfiredragon/comfyui-image-manipulation": {"stars": 0, "last_update": "2025-02-17 23:25:53", "author_account_age_days": 2102}, "https://github.com/HelloVision/ComfyUI_HelloMeme": {"stars": 365, "last_update": "2025-06-27 09:47:01", "author_account_age_days": 281}, "https://github.com/Hellrunner2k/ComfyUI-HellrunnersMagicalNodes": {"stars": 2, "last_update": "2025-05-21 02:17:36", "author_account_age_days": 3453}, "https://github.com/Hiero207/ComfyUI-Hiero-Nodes": {"stars": 6, "last_update": "2024-08-14 01:25:26", "author_account_age_days": 2067}, "https://github.com/HighDoping/ComfyUI_ASSSSA": {"stars": 0, "last_update": "2025-06-21 02:05:12", "author_account_age_days": 2538}, "https://github.com/Holasyb918/Ghost2_Comfyui": {"stars": 3, "last_update": "2025-03-14 02:41:21", "author_account_age_days": 1003}, "https://github.com/Hopping-Mad-Games/ComfyUI_LiteLLM": {"stars": 5, "last_update": "2025-06-25 23:22:58", "author_account_age_days": 557}, "https://github.com/HowToSD/ComfyUI-Data-Analysis": {"stars": 16, "last_update": "2025-06-11 04:28:54", "author_account_age_days": 542}, "https://github.com/HowToSD/ComfyUI-Pt-Wrapper": {"stars": 6, "last_update": "2025-06-11 04:48:46", "author_account_age_days": 542}, "https://github.com/Hullabalo/ComfyUI-Loop": {"stars": 8, "last_update": "2025-05-01 15:26:44", "author_account_age_days": 978}, "https://github.com/IDGallagher/ComfyUI-IG-Motion-I2V": {"stars": 38, "last_update": "2024-09-30 10:38:22", "author_account_age_days": 5849}, "https://github.com/IDGallagher/ComfyUI-IG-Nodes": {"stars": 2, "last_update": "2025-05-13 08:37:57", "author_account_age_days": 5849}, "https://github.com/IDGallagher/MotionVideoSearch": {"stars": 12, "last_update": "2025-01-13 09:37:08", "author_account_age_days": 5849}, "https://github.com/IIs-fanta/ComfyUI-FANTA-GameBox": {"stars": 2, "last_update": "2025-06-04 09:43:26", "author_account_age_days": 691}, "https://github.com/INuBq8/ComfyUI-NotificationBridge": {"stars": 0, "last_update": "2025-06-09 04:11:29", "author_account_age_days": 249}, "https://github.com/ITurchenko/ComfyUI-SizeFromArray": {"stars": 0, "last_update": "2024-08-01 08:45:43", "author_account_age_days": 4077}, "https://github.com/IamCreateAI/Ruyi-Models": {"stars": 521, "last_update": "2025-01-20 12:21:40", "author_account_age_days": 199}, "https://github.com/IcelandicCenterArtificialIntelligence/ComfyUI-SamplerSchedulerMetricsTester": {"stars": 1, "last_update": "2025-05-19 15:04:38", "author_account_age_days": 396}, "https://github.com/Iemand005/ComfyUI-Touch-Gestures": {"stars": 3, "last_update": "2025-02-03 00:25:14", "author_account_age_days": 1866}, "https://github.com/Iemand005/ComfyUI-Touchpad-Gestures": {"stars": 2, "last_update": "2025-02-03 00:21:47", "author_account_age_days": 1866}, "https://github.com/IgalOgonov/ComfyUI_Simple_String_Repository": {"stars": 3, "last_update": "2024-12-28 20:21:22", "author_account_age_days": 2597}, "https://github.com/ImagineerNL/ComfyUI-IMGNR-Utils": {"stars": 0, "last_update": "2025-05-05 21:36:48", "author_account_age_days": 1920}, "https://github.com/ImagineerNL/ComfyUI-ToSVG-Potracer": {"stars": 10, "last_update": "2025-05-08 21:56:04", "author_account_age_days": 1920}, "https://github.com/Immac/ComfyUI-CoreVideoMocks": {"stars": 1, "last_update": "2025-03-17 20:21:25", "author_account_age_days": 4546}, "https://github.com/ImmortalPie/ComfyUI-PonySwitch": {"stars": 10, "last_update": "2025-03-27 12:49:04", "author_account_age_days": 4191}, "https://github.com/InceptionsAI/ComfyUI-RunComfy-Helper": {"stars": 2, "last_update": "2025-05-06 04:03:58", "author_account_age_days": 897}, "https://github.com/InstantStudioAI/ComfyUI-InstantStudio": {"stars": 4, "last_update": "2025-03-25 06:19:54", "author_account_age_days": 197}, "https://github.com/Intersection98/ComfyUI_MX_post_processing-nodes": {"stars": 13, "last_update": "2024-05-23 01:12:46", "author_account_age_days": 2998}, "https://github.com/Inzaniak/comfyui-ranbooru": {"stars": 19, "last_update": "2024-05-22 23:12:23", "author_account_age_days": 4278}, "https://github.com/Irsalistic/comfyui-dam-object-extractor": {"stars": 5, "last_update": "2025-05-13 11:10:44", "author_account_age_days": 687}, "https://github.com/IsItDanOrAi/ComfyUI-Stereopsis": {"stars": 9, "last_update": "2024-09-21 21:39:11", "author_account_age_days": 480}, "https://github.com/Isi-dev/ComfyUI-Animation_Nodes_and_Workflows": {"stars": 28, "last_update": "2025-06-11 15:26:03", "author_account_age_days": 1460}, "https://github.com/Isi-dev/ComfyUI-Img2DrawingAssistants": {"stars": 18, "last_update": "2024-12-15 10:03:55", "author_account_age_days": 1460}, "https://github.com/Isi-dev/ComfyUI-Img2PaintingAssistant": {"stars": 11, "last_update": "2024-12-15 11:00:51", "author_account_age_days": 1460}, "https://github.com/Isi-dev/ComfyUI-UniAnimate-W": {"stars": 176, "last_update": "2025-03-11 10:32:39", "author_account_age_days": 1460}, "https://github.com/Isi-dev/ComfyUI_Animation_Nodes_and_Workflows": {"stars": 28, "last_update": "2025-06-11 15:26:03", "author_account_age_days": 1460}, "https://github.com/Isulion/ComfyUI_Isulion": {"stars": 38, "last_update": "2025-05-03 12:21:05", "author_account_age_days": 725}, "https://github.com/IuvenisSapiens/ComfyUI_MiniCPM-V-2_6-int4": {"stars": 184, "last_update": "2025-04-02 16:32:54", "author_account_age_days": 779}, "https://github.com/IuvenisSapiens/ComfyUI_Qwen2-Audio-7B-Instruct-Int4": {"stars": 10, "last_update": "2025-04-02 16:35:52", "author_account_age_days": 779}, "https://github.com/IuvenisSapiens/ComfyUI_Qwen2-VL-Instruct": {"stars": 98, "last_update": "2025-04-02 16:22:22", "author_account_age_days": 779}, "https://github.com/JEONG-JIWOO/ComfyUI_Eugene_Nodes": {"stars": 2, "last_update": "2025-01-27 19:09:46", "author_account_age_days": 2934}, "https://github.com/JPS-GER/ComfyUI_JPS-Nodes": {"stars": 75, "last_update": "2024-05-22 20:39:14", "author_account_age_days": 684}, "https://github.com/JPrevots/ComfyUI-PhyCV": {"stars": 1, "last_update": "2025-02-21 11:36:11", "author_account_age_days": 927}, "https://github.com/JTriggerFish/ComfyLatentTools": {"stars": 2, "last_update": "2025-05-06 21:07:17", "author_account_age_days": 4366}, "https://github.com/JackEllie/ComfyUI_AI_Assistant": {"stars": 24, "last_update": "2024-09-05 03:42:14", "author_account_age_days": 939}, "https://github.com/Jacky-MYQ/comfyui-DataCleaning": {"stars": 5, "last_update": "2025-05-10 12:26:38", "author_account_age_days": 710}, "https://github.com/Jacky-MYQ/comfyui-rgb2cmyk": {"stars": 2, "last_update": "2025-04-28 02:05:19", "author_account_age_days": 710}, "https://github.com/Jaminanim/ComfyUI-Random-Int-Divisor-Node": {"stars": 0, "last_update": "2025-01-07 06:50:58", "author_account_age_days": 1929}, "https://github.com/Jannchie/ComfyUI-J": {"stars": 99, "last_update": "2025-04-07 09:03:24", "author_account_age_days": 2921}, "https://github.com/Jannled/owl-vit-comfyui": {"stars": 0, "last_update": "2025-05-20 00:41:53", "author_account_age_days": 4046}, "https://github.com/JaredTherriault/ComfyUI-JNodes": {"stars": 69, "last_update": "2025-06-14 20:09:47", "author_account_age_days": 3940}, "https://github.com/Jash-Vora/ComfyUI-GarmentDiT": {"stars": 3, "last_update": "2025-01-04 08:22:14", "author_account_age_days": 778}, "https://github.com/JcandZero/ComfyUI_GLM4Node": {"stars": 26, "last_update": "2024-05-22 23:12:46", "author_account_age_days": 1058}, "https://github.com/Jcd1230/rembg-comfyui-node": {"stars": 168, "last_update": "2024-05-22 17:58:34", "author_account_age_days": 5254}, "https://github.com/JerryOrbachJr/ComfyUI-RandomSize": {"stars": 5, "last_update": "2024-08-25 18:35:55", "author_account_age_days": 524}, "https://github.com/JettHu/ComfyUI-TCD": {"stars": 131, "last_update": "2024-07-31 13:50:21", "author_account_age_days": 2726}, "https://github.com/JettHu/ComfyUI_TGate": {"stars": 93, "last_update": "2024-09-24 02:15:59", "author_account_age_days": 2726}, "https://github.com/JiSenHua/ComfyUI-TD": {"stars": 62, "last_update": "2025-04-20 16:24:26", "author_account_age_days": 1111}, "https://github.com/Jint8888/Comfyui_JTnodes": {"stars": 1, "last_update": "2025-04-22 16:23:53", "author_account_age_days": 418}, "https://github.com/JoeNavark/comfyui_custom_sigma_editor": {"stars": 7, "last_update": "2025-05-11 18:00:22", "author_account_age_days": 1216}, "https://github.com/JohanK66/ComfyUI-WebhookImage": {"stars": 1, "last_update": "2025-03-10 19:38:53", "author_account_age_days": 118}, "https://github.com/JohnDoeSmithee/ComfyUI-SoX-Mixdown": {"stars": 1, "last_update": "2025-01-26 22:42:52", "author_account_age_days": 151}, "https://github.com/Jokimbe/ComfyUI-DrawThings-gRPC": {"stars": 9, "last_update": "2025-06-27 01:24:07", "author_account_age_days": 4748}, "https://github.com/Jonseed/ComfyUI-Detail-Daemon": {"stars": 748, "last_update": "2025-03-14 16:47:41", "author_account_age_days": 2548}, "https://github.com/Jordach/comfy-plasma": {"stars": 75, "last_update": "2024-05-22 18:08:28", "author_account_age_days": 4881}, "https://github.com/JosefKuchar/ComfyUI-AdvancedTiling": {"stars": 12, "last_update": "2024-08-02 15:16:12", "author_account_age_days": 3723}, "https://github.com/JosephThomasParker/ComfyUI-DrawThingsWrapper": {"stars": 30, "last_update": "2025-02-04 21:14:38", "author_account_age_days": 3538}, "https://github.com/Julian-adv/WildDivide": {"stars": 17, "last_update": "2025-05-27 13:24:07", "author_account_age_days": 706}, "https://github.com/JustLateNightAI/KeywordImageBlocker": {"stars": 0, "last_update": "2025-05-07 17:25:44", "author_account_age_days": 248}, "https://github.com/JustinMatters/comfyUI-JMNodes": {"stars": 0, "last_update": "2025-01-04 14:57:58", "author_account_age_days": 3139}, "https://github.com/KAVVATARE/ComfyUI-Light-N-Color": {"stars": 1, "last_update": "2025-03-02 16:56:41", "author_account_age_days": 4498}, "https://github.com/KAVVATARE/ComfyUI_RightEyeDisparity": {"stars": 2, "last_update": "2025-05-23 19:32:04", "author_account_age_days": 4498}, "https://github.com/KERRY-YUAN/ComfyUI_Float_Animator": {"stars": 3, "last_update": "2025-06-16 06:49:23", "author_account_age_days": 1621}, "https://github.com/KERRY-YUAN/ComfyUI_Simple_Executor": {"stars": 2, "last_update": "2025-04-09 03:25:32", "author_account_age_days": 1621}, "https://github.com/KERRY-YUAN/ComfyUI_Spark_TTS": {"stars": 2, "last_update": "2025-06-10 06:16:34", "author_account_age_days": 1621}, "https://github.com/KLL535/ComfyUI_PNGInfo_Sidebar": {"stars": 17, "last_update": "2025-02-16 13:11:48", "author_account_age_days": 217}, "https://github.com/KLL535/ComfyUI_SimpleButcher": {"stars": 7, "last_update": "2025-03-09 21:53:41", "author_account_age_days": 217}, "https://github.com/Kangkang625/ComfyUI-paint-by-example": {"stars": 16, "last_update": "2024-05-22 22:20:27", "author_account_age_days": 1282}, "https://github.com/Kartel-ai/ComfyUI-8iPlayer": {"stars": 48, "last_update": "2025-06-20 07:54:00", "author_account_age_days": 57}, "https://github.com/Kayarte/AudioDriven-Latent-Space-Tools-for-ComfyUI": {"stars": 3, "last_update": "2025-06-15 22:39:14", "author_account_age_days": 421}, "https://github.com/Kesin11/ComfyUI-list-filter": {"stars": 0, "last_update": "2025-03-28 04:00:03", "author_account_age_days": 4915}, "https://github.com/KewkLW/ComfyUI-kewky_tools": {"stars": 8, "last_update": "2025-05-11 21:55:10", "author_account_age_days": 2055}, "https://github.com/Kidev/ComfyUI-Fisheye-effects": {"stars": 16, "last_update": "2025-04-03 19:00:30", "author_account_age_days": 4969}, "https://github.com/Kinglord/ComfyUI_LoRA_Sidebar": {"stars": 82, "last_update": "2025-04-27 08:48:53", "author_account_age_days": 5259}, "https://github.com/Kinglord/ComfyUI_Prompt_Gallery": {"stars": 54, "last_update": "2024-09-24 21:58:55", "author_account_age_days": 5259}, "https://github.com/Kinglord/ComfyUI_Slider_Sidebar": {"stars": 40, "last_update": "2024-09-26 02:40:30", "author_account_age_days": 5259}, "https://github.com/KohakuBlueleaf/z-tipo-extension": {"stars": 481, "last_update": "2025-06-10 16:57:54", "author_account_age_days": 1996}, "https://github.com/Koishi-Star/Euler-Smea-Dyn-Sampler": {"stars": 209, "last_update": "2024-09-01 03:57:22", "author_account_age_days": 1854}, "https://github.com/Koishi-Star/Pyramid_Noise_For_Inference": {"stars": 6, "last_update": "2024-09-27 17:58:43", "author_account_age_days": 1854}, "https://github.com/KoreTeknology/ComfyUI-Nai-Production-Nodes-Pack": {"stars": 10, "last_update": "2024-11-24 15:55:30", "author_account_age_days": 3558}, "https://github.com/KoreTeknology/ComfyUI-Universal-Styler": {"stars": 62, "last_update": "2025-03-01 05:37:40", "author_account_age_days": 3558}, "https://github.com/Kosinkadink/ComfyUI-Advanced-ControlNet": {"stars": 817, "last_update": "2025-03-05 03:01:28", "author_account_age_days": 4085}, "https://github.com/Kosinkadink/ComfyUI-AnimateDiff-Evolved": {"stars": 3187, "last_update": "2025-04-09 21:22:11", "author_account_age_days": 4085}, "https://github.com/Kosinkadink/ComfyUI-VideoHelperSuite": {"stars": 1068, "last_update": "2025-04-26 20:27:20", "author_account_age_days": 4085}, "https://github.com/Koushakur/ComfyUI-DenoiseChooser": {"stars": 4, "last_update": "2025-03-14 09:52:02", "author_account_age_days": 1489}, "https://github.com/KunmyonChoi/ComfyUI_S3_direct": {"stars": 0, "last_update": "2025-01-07 01:22:23", "author_account_age_days": 5935}, "https://github.com/Kurdknight/Kurdknight_comfycheck": {"stars": 4, "last_update": "2025-01-15 16:47:23", "author_account_age_days": 879}, "https://github.com/KwaiVGI/ComfyUI-KLingAI-API": {"stars": 130, "last_update": "2025-05-06 06:25:51", "author_account_age_days": 426}, "https://github.com/Ky11le/draw_tools": {"stars": 0, "last_update": "2025-05-14 05:35:47", "author_account_age_days": 846}, "https://github.com/Ky11le/ygo_tools": {"stars": 0, "last_update": "2025-05-14 05:35:47", "author_account_age_days": 846}, "https://github.com/KytraScript/ComfyUI_KytraWebhookHTTP": {"stars": 5, "last_update": "2024-05-23 00:21:43", "author_account_age_days": 2148}, "https://github.com/KytraScript/ComfyUI_MatAnyone_Kytra": {"stars": 125, "last_update": "2025-03-16 18:58:58", "author_account_age_days": 2148}, "https://github.com/LAOGOU-666/ComfyUI-LG_HotReload": {"stars": 200, "last_update": "2025-06-21 16:06:56", "author_account_age_days": 463}, "https://github.com/LAOGOU-666/ComfyUI_LG_FFT": {"stars": 8, "last_update": "2024-10-10 04:45:57", "author_account_age_days": 463}, "https://github.com/LAOGOU-666/Comfyui-LG_GroupExecutor": {"stars": 143, "last_update": "2025-05-31 17:36:04", "author_account_age_days": 463}, "https://github.com/LAOGOU-666/Comfyui-LG_Relight": {"stars": 185, "last_update": "2025-06-16 13:28:22", "author_account_age_days": 463}, "https://github.com/LAOGOU-666/Comfyui-Memory_Cleanup": {"stars": 137, "last_update": "2025-04-09 16:45:10", "author_account_age_days": 463}, "https://github.com/LAOGOU-666/Comfyui_LG_Tools": {"stars": 143, "last_update": "2025-06-27 08:05:54", "author_account_age_days": 463}, "https://github.com/LEv145/images-grid-comfy-plugin": {"stars": 184, "last_update": "2024-05-30 17:54:32", "author_account_age_days": 2576}, "https://github.com/LKbaba/ComfyUI-TuZi-Flux-Kontext": {"stars": 14, "last_update": "2025-06-19 03:37:53", "author_account_age_days": 2856}, "https://github.com/LaVie024/comfyui-lopi999-nodes": {"stars": 3, "last_update": "2025-06-25 23:41:44", "author_account_age_days": 1925}, "https://github.com/LamEmil/ComfyUI_ASCIIArtNode": {"stars": 1, "last_update": "2025-05-18 09:22:38", "author_account_age_days": 269}, "https://github.com/LargeModGames/comfyui-smart-lora-downloader": {"stars": 1, "last_update": "2025-06-12 08:40:31", "author_account_age_days": 1499}, "https://github.com/LarryJane491/Image-Captioning-in-ComfyUI": {"stars": 65, "last_update": "2024-06-06 20:45:43", "author_account_age_days": 532}, "https://github.com/LarryJane491/Lora-Training-in-Comfy": {"stars": 478, "last_update": "2024-08-05 11:32:30", "author_account_age_days": 532}, "https://github.com/LatentRat/comfy_remote_run": {"stars": 6, "last_update": "2024-09-08 04:06:09", "author_account_age_days": 1112}, "https://github.com/LatentSpaceDirective/ComfyUI-Texturaizer": {"stars": 15, "last_update": "2025-01-19 14:21:04", "author_account_age_days": 227}, "https://github.com/Laurent2916/comfyui-piq": {"stars": 0, "last_update": "2025-03-17 13:50:16", "author_account_age_days": 3235}, "https://github.com/Layer-norm/comfyui-lama-remover": {"stars": 129, "last_update": "2024-08-03 04:18:39", "author_account_age_days": 700}, "https://github.com/Legorobotdude/ComfyUI-VariationLab": {"stars": 1, "last_update": "2025-03-02 04:59:28", "author_account_age_days": 3737}, "https://github.com/Lerc/canvas_tab": {"stars": 189, "last_update": "2024-05-22 20:48:45", "author_account_age_days": 5736}, "https://github.com/LevelPixel/ComfyUI-LevelPixel": {"stars": 11, "last_update": "2025-06-22 02:10:16", "author_account_age_days": 351}, "https://github.com/LevelPixel/ComfyUI-LevelPixel-Advanced": {"stars": 5, "last_update": "2025-06-09 13:45:18", "author_account_age_days": 351}, "https://github.com/Lhyejin/ComfyUI-Fill-Image-for-Outpainting": {"stars": 9, "last_update": "2024-08-26 00:40:09", "author_account_age_days": 2970}, "https://github.com/LiJT/ComfyUI-Gemini-Prompt-Generator-JT": {"stars": 4, "last_update": "2025-04-06 12:33:08", "author_account_age_days": 3769}, "https://github.com/Light-x02/ComfyUI-FluxSettingsNode": {"stars": 6, "last_update": "2025-04-28 21:45:01", "author_account_age_days": 1145}, "https://github.com/Light-x02/ComfyUI-Image-Metadata-Nodes": {"stars": 6, "last_update": "2025-05-01 18:14:59", "author_account_age_days": 1145}, "https://github.com/LightSketch-ai/ComfyUI-LivePortraitNode": {"stars": 2, "last_update": "2024-07-17 01:24:53", "author_account_age_days": 352}, "https://github.com/Lightricks/ComfyUI-LTXVideo": {"stars": 2097, "last_update": "2025-06-20 14:01:36", "author_account_age_days": 4558}, "https://github.com/Limbicnation/ComfyUI-TransparencyBackgroundRemover": {"stars": 5, "last_update": "2025-06-04 00:28:38", "author_account_age_days": 4197}, "https://github.com/Limbicnation/ComfyUIDepthEstimation": {"stars": 13, "last_update": "2025-05-14 22:31:24", "author_account_age_days": 4197}, "https://github.com/Limbicnation/ComfyUI_FaceDetectionNode": {"stars": 1, "last_update": "2025-06-23 01:32:50", "author_account_age_days": 4197}, "https://github.com/Limitex/ComfyUI-Calculation": {"stars": 0, "last_update": "2024-05-22 22:18:40", "author_account_age_days": 1643}, "https://github.com/Limitex/ComfyUI-Diffusers": {"stars": 170, "last_update": "2025-03-10 19:04:32", "author_account_age_days": 1643}, "https://github.com/Ling-APE/ComfyUI-PixelResolutionCalculator": {"stars": 8, "last_update": "2025-06-02 02:45:04", "author_account_age_days": 755}, "https://github.com/LingSss9/comfyui-merge": {"stars": 7, "last_update": "2025-06-01 17:27:49", "author_account_age_days": 629}, "https://github.com/Loewen-Hob/rembg-comfyui-node-better": {"stars": 64, "last_update": "2025-04-07 09:09:34", "author_account_age_days": 839}, "https://github.com/LonicaMewinsky/ComfyUI-MakeFrame": {"stars": 29, "last_update": "2024-05-22 21:29:02", "author_account_age_days": 1344}, "https://github.com/LonicaMewinsky/ComfyUI-RawSaver": {"stars": 3, "last_update": "2024-05-22 21:31:28", "author_account_age_days": 1344}, "https://github.com/LoveEatCandy/COMFYUI-ReplacePartOfImage": {"stars": 0, "last_update": "2025-06-17 05:53:17", "author_account_age_days": 2809}, "https://github.com/LucipherDev/ComfyUI-AniDoc": {"stars": 53, "last_update": "2025-03-28 18:39:10", "author_account_age_days": 1864}, "https://github.com/LucipherDev/ComfyUI-Golden-Noise": {"stars": 23, "last_update": "2025-03-28 18:38:24", "author_account_age_days": 1864}, "https://github.com/LucipherDev/ComfyUI-TangoFlux": {"stars": 93, "last_update": "2025-03-28 18:39:16", "author_account_age_days": 1864}, "https://github.com/Ludobico/ComfyUI-ScenarioPrompt": {"stars": 17, "last_update": "2025-03-12 09:07:07", "author_account_age_days": 1401}, "https://github.com/LyazS/comfyui-anime-seg": {"stars": 10, "last_update": "2024-05-22 23:21:49", "author_account_age_days": 3225}, "https://github.com/LyazS/comfyui-nettools": {"stars": 5, "last_update": "2024-09-23 12:52:44", "author_account_age_days": 3225}, "https://github.com/M1kep/ComfyLiterals": {"stars": 53, "last_update": "2024-05-22 20:31:38", "author_account_age_days": 4626}, "https://github.com/M1kep/ComfyUI-KepOpenAI": {"stars": 30, "last_update": "2024-08-20 16:33:57", "author_account_age_days": 4626}, "https://github.com/M1kep/ComfyUI-OtherVAEs": {"stars": 2, "last_update": "2024-05-22 20:33:41", "author_account_age_days": 4626}, "https://github.com/M1kep/Comfy_KepKitchenSink": {"stars": 0, "last_update": "2024-05-22 20:33:29", "author_account_age_days": 4626}, "https://github.com/M1kep/Comfy_KepListStuff": {"stars": 45, "last_update": "2024-06-22 00:51:28", "author_account_age_days": 4626}, "https://github.com/M1kep/Comfy_KepMatteAnything": {"stars": 11, "last_update": "2024-05-22 20:33:16", "author_account_age_days": 4626}, "https://github.com/M1kep/KepPromptLang": {"stars": 6, "last_update": "2024-05-22 20:32:56", "author_account_age_days": 4626}, "https://github.com/MDMAchine/ComfyUI_MD_Nodes": {"stars": 3, "last_update": "2025-06-18 03:28:39", "author_account_age_days": 2018}, "https://github.com/MNeMoNiCuZ/ComfyUI-mnemic-nodes": {"stars": 62, "last_update": "2025-06-07 15:59:38", "author_account_age_days": 1973}, "https://github.com/Makeezi/ComfyUI-promptLAB": {"stars": 0, "last_update": "2024-05-23 01:24:51", "author_account_age_days": 2150}, "https://github.com/MakkiShizu/ComfyUI-Prompt-Wildcards": {"stars": 7, "last_update": "2025-06-14 10:33:54", "author_account_age_days": 681}, "https://github.com/MakkiShizu/ComfyUI-Qwen2_5-VL": {"stars": 3, "last_update": "2025-05-30 16:50:42", "author_account_age_days": 681}, "https://github.com/MakkiShizu/comfyui_reimgsize": {"stars": 5, "last_update": "2025-04-27 15:34:57", "author_account_age_days": 681}, "https://github.com/Mamaaaamooooo/batchImg-rembg-ComfyUI-nodes": {"stars": 28, "last_update": "2024-06-14 10:24:17", "author_account_age_days": 750}, "https://github.com/ManglerFTW/ComfyI2I": {"stars": 174, "last_update": "2024-06-14 11:01:01", "author_account_age_days": 1024}, "https://github.com/MaraScott/ComfyUI_MaraScott_Nodes": {"stars": 151, "last_update": "2025-04-26 23:44:45", "author_account_age_days": 5338}, "https://github.com/MarcusNyne/m9-prompts-comfyui": {"stars": 1, "last_update": "2024-08-24 16:56:53", "author_account_age_days": 1791}, "https://github.com/MariusKM/ComfyUI-BadmanNodes": {"stars": 2, "last_update": "2024-12-30 15:36:09", "author_account_age_days": 2599}, "https://github.com/MarkoCa1/ComfyUI-Text": {"stars": 7, "last_update": "2024-12-16 09:48:49", "author_account_age_days": 2001}, "https://github.com/MarkoCa1/ComfyUI_Segment_Mask": {"stars": 22, "last_update": "2024-05-23 00:15:51", "author_account_age_days": 2001}, "https://github.com/Marksusu/ComfyUI_MTCLIPEncode": {"stars": 7, "last_update": "2025-05-07 13:56:23", "author_account_age_days": 1076}, "https://github.com/MaruPelkar/comfyui-conditioning-resizer": {"stars": 1, "last_update": "2025-04-21 20:57:33", "author_account_age_days": 3948}, "https://github.com/Mason-McGough/ComfyUI-Mosaica": {"stars": 6, "last_update": "2024-08-26 20:42:35", "author_account_age_days": 3573}, "https://github.com/Mattabyte/ComfyUI-SecureApiCall": {"stars": 0, "last_update": "2025-03-07 23:55:55", "author_account_age_days": 1975}, "https://github.com/Maxed-Out-99/ComfyUI-MaxedOut": {"stars": 2, "last_update": "2025-06-20 17:31:54", "author_account_age_days": 48}, "https://github.com/McKlinton2/comfyui-mcklinton-pack": {"stars": 1, "last_update": "2025-05-31 18:41:13", "author_account_age_days": 1146}, "https://github.com/Mcmillian/ComfyUI-SimpleToolsNodes": {"stars": 0, "last_update": "2024-09-29 14:18:23", "author_account_age_days": 3264}, "https://github.com/MeeeyoAI/ComfyUI_StringOps": {"stars": 78, "last_update": "2025-06-04 17:47:55", "author_account_age_days": 124}, "https://github.com/Meettya/ComfyUI-OneForOne": {"stars": 2, "last_update": "2025-01-07 22:49:30", "author_account_age_days": 5699}, "https://github.com/MetaGLM/ComfyUI-ZhipuAI-Platform": {"stars": 5, "last_update": "2024-09-16 16:11:59", "author_account_age_days": 647}, "https://github.com/MicheleGuidi/ComfyUI-Contextual-SAM2": {"stars": 5, "last_update": "2025-05-01 16:09:43", "author_account_age_days": 1616}, "https://github.com/MiddleKD/ComfyUI-denoise-mask-scheduler": {"stars": 6, "last_update": "2024-11-07 12:35:00", "author_account_age_days": 898}, "https://github.com/MiddleKD/ComfyUI-mem-safe-wrapper": {"stars": 4, "last_update": "2024-08-01 06:47:24", "author_account_age_days": 898}, "https://github.com/MiddleKD/ComfyUI-productfix": {"stars": 11, "last_update": "2025-05-12 05:00:24", "author_account_age_days": 898}, "https://github.com/MieMieeeee/ComfyUI-CaptionThis": {"stars": 57, "last_update": "2025-04-22 05:54:42", "author_account_age_days": 1924}, "https://github.com/MieMieeeee/ComfyUI-MieNodes": {"stars": 49, "last_update": "2025-04-17 07:37:04", "author_account_age_days": 1924}, "https://github.com/MieMieeeee/ComfyUI-MinioConnector": {"stars": 3, "last_update": "2025-03-21 09:09:09", "author_account_age_days": 1924}, "https://github.com/MijnSpam/ComfyUI_SwapAndScale": {"stars": 0, "last_update": "2025-05-31 09:27:10", "author_account_age_days": 916}, "https://github.com/MijnSpam/UploadToPushOver": {"stars": 1, "last_update": "2025-05-31 09:32:38", "author_account_age_days": 916}, "https://github.com/MilitantHitchhiker/MilitantHitchhiker-SwitchbladePack": {"stars": 3, "last_update": "2024-10-06 07:46:05", "author_account_age_days": 428}, "https://github.com/Mintbeer96/ComfyUI-KerasOCR": {"stars": 3, "last_update": "2024-07-24 16:39:41", "author_account_age_days": 3545}, "https://github.com/MinusZoneAI/ComfyUI-CogVideoX-MZ": {"stars": 105, "last_update": "2024-10-30 10:52:42", "author_account_age_days": 434}, "https://github.com/MinusZoneAI/ComfyUI-Flux1Quantize-MZ": {"stars": 11, "last_update": "2024-08-14 04:01:10", "author_account_age_days": 434}, "https://github.com/MinusZoneAI/ComfyUI-FluxExt-MZ": {"stars": 311, "last_update": "2024-08-16 18:57:07", "author_account_age_days": 434}, "https://github.com/MinusZoneAI/ComfyUI-Kolors-MZ": {"stars": 590, "last_update": "2025-03-31 02:51:00", "author_account_age_days": 434}, "https://github.com/MinusZoneAI/ComfyUI-Prompt-MZ": {"stars": 115, "last_update": "2025-03-14 06:36:29", "author_account_age_days": 434}, "https://github.com/MinusZoneAI/ComfyUI-StylizePhoto-MZ": {"stars": 18, "last_update": "2024-05-23 01:13:32", "author_account_age_days": 434}, "https://github.com/MinusZoneAI/ComfyUI-TrainTools-MZ": {"stars": 58, "last_update": "2025-02-24 06:08:49", "author_account_age_days": 434}, "https://github.com/Miosp/ComfyUI-FBCNN": {"stars": 26, "last_update": "2025-02-24 20:53:32", "author_account_age_days": 2884}, "https://github.com/MitoshiroPJ/comfyui_slothful_attention": {"stars": 7, "last_update": "2024-05-22 22:09:15", "author_account_age_days": 4336}, "https://github.com/Miyuutsu/comfyui-save-vpred": {"stars": 4, "last_update": "2024-12-15 22:29:47", "author_account_age_days": 3292}, "https://github.com/MohammadAboulEla/ComfyUI-iTools": {"stars": 159, "last_update": "2025-05-08 14:47:04", "author_account_age_days": 1395}, "https://github.com/MokkaBoss1/ComfyUI_Mokkaboss1": {"stars": 16, "last_update": "2025-06-08 11:06:37", "author_account_age_days": 747}, "https://github.com/MontagenAI/ComfyUI-Montagen": {"stars": 21, "last_update": "2025-05-29 10:43:37", "author_account_age_days": 197}, "https://github.com/MoonGoblinDev/Civicomfy": {"stars": 34, "last_update": "2025-06-10 15:38:45", "author_account_age_days": 3170}, "https://github.com/MoonHugo/ComfyUI-BAGEL-Hugo": {"stars": 3, "last_update": "2025-06-04 08:48:11", "author_account_age_days": 297}, "https://github.com/MoonHugo/ComfyUI-BiRefNet-Hugo": {"stars": 305, "last_update": "2025-05-25 15:37:49", "author_account_age_days": 297}, "https://github.com/MoonHugo/ComfyUI-FFmpeg": {"stars": 83, "last_update": "2025-06-04 07:30:29", "author_account_age_days": 297}, "https://github.com/MoonHugo/ComfyUI-StableAudioOpen": {"stars": 27, "last_update": "2024-10-18 04:12:04", "author_account_age_days": 297}, "https://github.com/Moooonet/ComfyUI-Align": {"stars": 138, "last_update": "2025-05-12 09:40:03", "author_account_age_days": 355}, "https://github.com/MrForExample/ComfyUI-3D-Pack": {"stars": 3201, "last_update": "2025-06-19 11:10:43", "author_account_age_days": 1929}, "https://github.com/MrForExample/ComfyUI-AnimateAnyone-Evolved": {"stars": 547, "last_update": "2024-06-14 12:02:47", "author_account_age_days": 1929}, "https://github.com/MrSamSeen/ComfyUI_SSBeforeAfterNode": {"stars": 1, "last_update": "2025-05-25 01:55:29", "author_account_age_days": 3959}, "https://github.com/MrSamSeen/ComfyUI_SSStereoscope": {"stars": 25, "last_update": "2025-04-27 11:44:51", "author_account_age_days": 3959}, "https://github.com/Munkyfoot/ComfyUI-TextOverlay": {"stars": 34, "last_update": "2025-06-07 04:46:39", "author_account_age_days": 3413}, "https://github.com/MuziekMagie/ComfyUI-Matchering": {"stars": 48, "last_update": "2024-07-23 14:39:52", "author_account_age_days": 340}, "https://github.com/MzMaXaM/ComfyUi-MzMaXaM": {"stars": 10, "last_update": "2025-06-01 16:34:55", "author_account_age_days": 2107}, "https://github.com/N3rd00d/ComfyUI-Paint3D-Nodes": {"stars": 70, "last_update": "2024-08-19 12:52:20", "author_account_age_days": 445}, "https://github.com/NMWave/ComfyUI-Nader-Tagging": {"stars": 2, "last_update": "2025-04-09 01:07:33", "author_account_age_days": 947}, "https://github.com/NVIDIAGameWorks/ComfyUI-RTX-Remix": {"stars": 41, "last_update": "2025-06-04 21:48:12", "author_account_age_days": 4049}, "https://github.com/NakamuraShippo/ComfyUI-NS-ManySliders": {"stars": 4, "last_update": "2024-11-03 02:48:52", "author_account_age_days": 730}, "https://github.com/NakamuraShippo/ComfyUI-NS-PromptList": {"stars": 8, "last_update": "2025-06-21 08:50:06", "author_account_age_days": 730}, "https://github.com/NeoDroleDeGueule/comfyui-image-mixer": {"stars": 0, "last_update": "2025-06-11 16:54:15", "author_account_age_days": 491}, "https://github.com/NeoGriever/ComfyUI-NeoGriever": {"stars": 2, "last_update": "2024-12-12 02:55:40", "author_account_age_days": 4656}, "https://github.com/NeonLightning/neonllama": {"stars": 1, "last_update": "2025-06-20 22:49:39", "author_account_age_days": 4584}, "https://github.com/Nestorchik/NStor-ComfyUI-Translation": {"stars": 2, "last_update": "2024-06-14 10:25:32", "author_account_age_days": 1693}, "https://github.com/NeuralSamurAI/ComfyUI-Dimensional-Latent-Perlin": {"stars": 34, "last_update": "2024-08-06 19:59:25", "author_account_age_days": 462}, "https://github.com/NeuralSamurAI/ComfyUI-FluxPseudoNegativePrompt": {"stars": 7, "last_update": "2024-08-14 02:16:43", "author_account_age_days": 462}, "https://github.com/NeuralSamurAI/ComfyUI-PromptJSON": {"stars": 2, "last_update": "2024-08-11 18:10:36", "author_account_age_days": 462}, "https://github.com/NeuralSamurAI/Comfyui-Superprompt-Unofficial": {"stars": 68, "last_update": "2024-05-23 00:22:08", "author_account_age_days": 462}, "https://github.com/Nevysha/ComfyUI-nevysha-top-menu": {"stars": 5, "last_update": "2024-05-23 00:17:31", "author_account_age_days": 895}, "https://github.com/NguynHungNguyen/Segment-Bedroom-Interior": {"stars": 7, "last_update": "2024-10-17 13:22:19", "author_account_age_days": 1026}, "https://github.com/NicholasMcCarthy/ComfyUI_TravelSuite": {"stars": 15, "last_update": "2024-05-22 20:34:46", "author_account_age_days": 5518}, "https://github.com/Nikosis/ComfyUI-Nikosis-Nodes": {"stars": 1, "last_update": "2025-04-10 00:32:13", "author_account_age_days": 2644}, "https://github.com/Nikosis/ComfyUI-Nikosis-Preprocessors": {"stars": 2, "last_update": "2025-04-08 12:28:17", "author_account_age_days": 2644}, "https://github.com/NimaNzrii/comfyui-photoshop": {"stars": 1129, "last_update": "2025-05-17 18:02:00", "author_account_age_days": 613}, "https://github.com/NimaNzrii/comfyui-popup_preview": {"stars": 35, "last_update": "2024-05-22 22:12:04", "author_account_age_days": 613}, "https://github.com/Niutonian/ComfyUi-NoodleWebcam": {"stars": 32, "last_update": "2024-05-22 21:30:40", "author_account_age_days": 1410}, "https://github.com/Njbx/ComfyUI-LTX13B-Blockswap": {"stars": 6, "last_update": "2025-05-07 18:47:45", "author_account_age_days": 1571}, "https://github.com/Nlar/ComfyUI_CartoonSegmentation": {"stars": 16, "last_update": "2024-05-22 23:15:37", "author_account_age_days": 4199}, "https://github.com/Nojahhh/ComfyUI_GLM4_Wrapper": {"stars": 18, "last_update": "2025-05-08 10:35:48", "author_account_age_days": 3188}, "https://github.com/NotHarroweD/Harronode": {"stars": 5, "last_update": "2024-05-22 22:18:29", "author_account_age_days": 2352}, "https://github.com/Nourepide/ComfyUI-Allor": {"stars": 260, "last_update": "2024-05-22 18:11:17", "author_account_age_days": 3214}, "https://github.com/Nuked88/ComfyUI-N-Nodes": {"stars": 222, "last_update": "2024-08-15 21:07:32", "author_account_age_days": 4848}, "https://github.com/Nuked88/ComfyUI-N-Sidebar": {"stars": 558, "last_update": "2025-06-04 18:29:26", "author_account_age_days": 4848}, "https://github.com/NyaamZ/ComfyUI-ImageGallery-ED": {"stars": 6, "last_update": "2025-06-27 12:15:32", "author_account_age_days": 2485}, "https://github.com/NyaamZ/efficiency-nodes-ED": {"stars": 26, "last_update": "2025-06-27 12:15:14", "author_account_age_days": 2485}, "https://github.com/Off-Live/ComfyUI-off-suite": {"stars": 0, "last_update": "2024-06-14 12:02:25", "author_account_age_days": 1540}, "https://github.com/OgreLemonSoup/ComfyUI-Load-Image-Gallery": {"stars": 34, "last_update": "2025-06-07 02:47:12", "author_account_age_days": 321}, "https://github.com/OliverCrosby/Comfyui-Minimap": {"stars": 97, "last_update": "2024-08-24 14:10:43", "author_account_age_days": 2494}, "https://github.com/OpalSky-AI/OpalSky_Nodes": {"stars": 2, "last_update": "2024-10-27 20:13:40", "author_account_age_days": 2104}, "https://github.com/OpenArt-AI/ComfyUI-Assistant": {"stars": 21, "last_update": "2024-05-22 22:16:57", "author_account_age_days": 1143}, "https://github.com/OuticNZ/ComfyUI-Simple-Of-Complex": {"stars": 0, "last_update": "2024-08-14 04:00:49", "author_account_age_days": 2901}, "https://github.com/PCMonsterx/ComfyUI-CSV-Loader": {"stars": 16, "last_update": "2025-03-14 12:21:40", "author_account_age_days": 2033}, "https://github.com/Pablerdo/ComfyUI-MultiCutAndDrag": {"stars": 3, "last_update": "2025-03-22 01:25:55", "author_account_age_days": 3168}, "https://github.com/Pablerdo/ComfyUI-ResizeZeptaPayload": {"stars": 1, "last_update": "2025-03-29 00:39:01", "author_account_age_days": 3168}, "https://github.com/Pablerdo/ComfyUI-StableVirtualCameraWrapper": {"stars": 1, "last_update": "2025-04-19 09:40:38", "author_account_age_days": 3168}, "https://github.com/Pablerdo/ComfyUI-ZeptaframePromptMerger": {"stars": 1, "last_update": "2025-03-21 17:42:55", "author_account_age_days": 3168}, "https://github.com/PanicTitan/ComfyUI-Fooocus-V2-Expansion": {"stars": 8, "last_update": "2025-05-09 22:51:17", "author_account_age_days": 1853}, "https://github.com/PanicTitan/ComfyUI-Gallery": {"stars": 31, "last_update": "2025-06-24 02:36:08", "author_account_age_days": 1853}, "https://github.com/Parameshvadivel/ComfyUI-SVGview": {"stars": 1, "last_update": "2024-07-31 13:40:33", "author_account_age_days": 3196}, "https://github.com/ParisNeo/lollms_nodes_suite": {"stars": 11, "last_update": "2025-03-12 07:36:41", "author_account_age_days": 5138}, "https://github.com/ParmanBabra/ComfyUI-Malefish-Custom-Scripts": {"stars": 0, "last_update": "2024-05-22 21:26:35", "author_account_age_days": 4011}, "https://github.com/PauldeLavallaz/comfyui_claude_prompt_generator": {"stars": 0, "last_update": "2025-03-18 17:38:28", "author_account_age_days": 2211}, "https://github.com/PenguinTeo/Comfyui-TextEditor-Penguin": {"stars": 1, "last_update": "2025-06-04 14:38:13", "author_account_age_days": 342}, "https://github.com/Pfaeff/pfaeff-comfyui": {"stars": 22, "last_update": "2024-05-22 18:21:10", "author_account_age_days": 3576}, "https://github.com/Phando/ComfyUI-PhandoNodes": {"stars": 0, "last_update": "2024-09-05 16:12:24", "author_account_age_days": 5594}, "https://github.com/Pheat-AI/Remade_nodes": {"stars": 3, "last_update": "2024-10-18 00:04:58", "author_account_age_days": 402}, "https://github.com/PiggyDance/ComfyUI_OpenCV": {"stars": 0, "last_update": "2025-03-24 10:02:49", "author_account_age_days": 2754}, "https://github.com/Pigidiy/ComfyUI-LikeSpiderAI-SaveMP3": {"stars": 0, "last_update": "2025-06-01 16:35:20", "author_account_age_days": 257}, "https://github.com/Pigidiy/ComfyUI-LikeSpiderAI-UI": {"stars": 0, "last_update": "2025-06-05 19:20:04", "author_account_age_days": 257}, "https://github.com/PixelFunAI/ComfyUI_PixelFun": {"stars": 3, "last_update": "2025-01-20 05:44:54", "author_account_age_days": 158}, "https://github.com/PixelML/ComfyUI-PixelML-CustomNodes": {"stars": 0, "last_update": "2025-01-20 06:40:21", "author_account_age_days": 478}, "https://github.com/PnthrLeo/comfyUI-PL-data-tools": {"stars": 1, "last_update": "2024-12-03 13:39:28", "author_account_age_days": 2920}, "https://github.com/Poseidon-fan/ComfyUI-RabbitMQ-Publisher": {"stars": 2, "last_update": "2024-11-07 08:59:23", "author_account_age_days": 950}, "https://github.com/Positliver/comfyui-zegr": {"stars": 1, "last_update": "2025-01-26 11:51:59", "author_account_age_days": 3759}, "https://github.com/PowerHouseMan/ComfyUI-AdvancedLivePortrait": {"stars": 2422, "last_update": "2024-08-21 06:14:24", "author_account_age_days": 331}, "https://github.com/PressWagon/ComfyUI-StringsAndThings": {"stars": 2, "last_update": "2025-05-18 12:01:37", "author_account_age_days": 192}, "https://github.com/ProGamerGov/ComfyUI_preview360panorama": {"stars": 55, "last_update": "2025-05-25 19:26:43", "author_account_age_days": 3809}, "https://github.com/ProGamerGov/ComfyUI_pytorch360convert": {"stars": 12, "last_update": "2025-02-27 20:23:27", "author_account_age_days": 3809}, "https://github.com/PrunaAI/ComfyUI_pruna": {"stars": 61, "last_update": "2025-06-06 09:08:03", "author_account_age_days": 1016}, "https://github.com/Pseudotools/Pseudocomfy": {"stars": 1, "last_update": "2025-06-09 04:22:06", "author_account_age_days": 638}, "https://github.com/Q-Bug4/Comfyui-Qb-DateNodes": {"stars": 1, "last_update": "2024-11-03 01:52:39", "author_account_age_days": 2302}, "https://github.com/Q-Bug4/Comfyui-Simple-Json-Node": {"stars": 5, "last_update": "2025-03-27 12:51:03", "author_account_age_days": 2302}, "https://github.com/Q-Bug4/comfyui-qbug-batch": {"stars": 2, "last_update": "2025-04-13 03:05:36", "author_account_age_days": 2302}, "https://github.com/QaisMalkawi/ComfyUI-QaisHelper": {"stars": 2, "last_update": "2024-05-23 20:29:30", "author_account_age_days": 1619}, "https://github.com/QijiTec/ComfyUI-RED-UNO": {"stars": 25, "last_update": "2025-04-21 01:07:24", "author_account_age_days": 831}, "https://github.com/R5-Revo/llm-node-comfyui": {"stars": 6, "last_update": "2025-05-24 03:55:35", "author_account_age_days": 166}, "https://github.com/Raapys/ComfyUI-LatentGC_Aggressive": {"stars": 4, "last_update": "2024-08-12 15:55:42", "author_account_age_days": 4300}, "https://github.com/Ravenmelt/ComfyUI-Rodin": {"stars": 25, "last_update": "2025-05-07 13:29:25", "author_account_age_days": 2443}, "https://github.com/Raykosan/ComfyUI_RS-SaturationNode": {"stars": 8, "last_update": "2025-04-12 10:38:46", "author_account_age_days": 1747}, "https://github.com/Raykosan/ComfyUI_RaykoStudio": {"stars": 7, "last_update": "2025-04-12 10:21:00", "author_account_age_days": 1747}, "https://github.com/RaymondProduction/comfyui-zerna-pack": {"stars": 0, "last_update": "2025-03-26 16:10:15", "author_account_age_days": 3293}, "https://github.com/ReBeating/ComfyUI-Artist-Selector": {"stars": 1, "last_update": "2025-02-10 15:39:41", "author_account_age_days": 1745}, "https://github.com/Reithan/negative_rejection_steering": {"stars": 7, "last_update": "2025-04-14 05:14:35", "author_account_age_days": 4072}, "https://github.com/RenderRift/ComfyUI-RenderRiftNodes": {"stars": 7, "last_update": "2024-05-22 22:16:41", "author_account_age_days": 555}, "https://github.com/RhizoNymph/ComfyUI-CLIPSlider": {"stars": 9, "last_update": "2024-09-07 19:47:02", "author_account_age_days": 1535}, "https://github.com/RhizoNymph/ComfyUI-ColorWheel": {"stars": 1, "last_update": "2024-10-13 06:26:51", "author_account_age_days": 1535}, "https://github.com/RhizoNymph/ComfyUI-Latte": {"stars": 3, "last_update": "2024-08-11 07:25:04", "author_account_age_days": 1535}, "https://github.com/RiceRound/ComfyUI_CryptoCat": {"stars": 89, "last_update": "2025-06-19 04:14:31", "author_account_age_days": 272}, "https://github.com/RiceRound/ComfyUI_RiceRound": {"stars": 17, "last_update": "2025-03-18 07:31:16", "author_account_age_days": 272}, "https://github.com/Rinsanga1/comfyui-florence2xy": {"stars": 0, "last_update": "2025-06-25 05:42:46", "author_account_age_days": 519}, "https://github.com/RodrigoSKohl/ComfyUI-Panoramic-ImgStitcher": {"stars": 6, "last_update": "2025-06-09 23:34:07", "author_account_age_days": 1116}, "https://github.com/RodrigoSKohl/InteriorDesign-for-ComfyUI": {"stars": 8, "last_update": "2025-05-14 04:26:55", "author_account_age_days": 1116}, "https://github.com/RodrigoSKohl/comfyui-tryoff-anyone": {"stars": 21, "last_update": "2025-04-14 03:36:22", "author_account_age_days": 1116}, "https://github.com/RomanKuschanow/ComfyUI-Advanced-Latent-Control": {"stars": 21, "last_update": "2025-03-27 17:57:44", "author_account_age_days": 1750}, "https://github.com/Ron-Digital/ComfyUI-SceneGenerator": {"stars": 3, "last_update": "2024-06-28 19:36:30", "author_account_age_days": 1297}, "https://github.com/Runware/ComfyUI-Runware": {"stars": 92, "last_update": "2025-06-18 12:05:09", "author_account_age_days": 567}, "https://github.com/Ryuukeisyou/ComfyUI-SyncTalk": {"stars": 39, "last_update": "2024-09-12 11:54:59", "author_account_age_days": 2781}, "https://github.com/Ryuukeisyou/comfyui_face_parsing": {"stars": 168, "last_update": "2025-02-18 09:22:52", "author_account_age_days": 2781}, "https://github.com/Ryuukeisyou/comfyui_io_helpers": {"stars": 1, "last_update": "2024-07-13 13:10:10", "author_account_age_days": 2781}, "https://github.com/S4MUEL-404/ComfyUI-S4Tool-Image-Overlay": {"stars": 0, "last_update": "2025-03-10 14:16:35", "author_account_age_days": 3454}, "https://github.com/SEkINVR/ComfyUI-SaveAs": {"stars": 6, "last_update": "2024-08-19 01:06:16", "author_account_age_days": 1025}, "https://github.com/SKBv0/ComfyUI_SKBundle": {"stars": 39, "last_update": "2025-06-26 21:46:14", "author_account_age_days": 1923}, "https://github.com/SLAPaper/ComfyUI-Image-Selector": {"stars": 96, "last_update": "2025-03-16 12:13:46", "author_account_age_days": 4066}, "https://github.com/SLAPaper/StableDiffusion-dpmpp_2m_alt-Sampler": {"stars": 12, "last_update": "2025-03-16 12:13:59", "author_account_age_days": 4066}, "https://github.com/SOELexicon/ComfyUI-LexMSDBNodes": {"stars": 4, "last_update": "2025-03-12 00:17:50", "author_account_age_days": 4452}, "https://github.com/SOELexicon/ComfyUI-LexTools": {"stars": 30, "last_update": "2025-03-28 10:50:35", "author_account_age_days": 4452}, "https://github.com/SS-snap/ComfyUI-Ad_scheduler": {"stars": 6, "last_update": "2025-04-25 04:53:31", "author_account_age_days": 668}, "https://github.com/SS-snap/ComfyUI-LBW_flux": {"stars": 4, "last_update": "2025-04-25 04:47:47", "author_account_age_days": 668}, "https://github.com/SS-snap/ComfyUI-Snap_Processing": {"stars": 61, "last_update": "2025-04-25 04:54:44", "author_account_age_days": 668}, "https://github.com/SS-snap/Comfyui_SSsnap_pose-Remapping": {"stars": 21, "last_update": "2025-06-18 07:25:25", "author_account_age_days": 668}, "https://github.com/SXQBW/ComfyUI-Qwen": {"stars": 6, "last_update": "2025-05-26 05:01:41", "author_account_age_days": 3159}, "https://github.com/SXQBW/ComfyUI-Qwen-Omni": {"stars": 20, "last_update": "2025-06-08 07:53:11", "author_account_age_days": 3159}, "https://github.com/SXQBW/ComfyUI-Qwen-VL": {"stars": 7, "last_update": "2025-05-26 06:11:20", "author_account_age_days": 3159}, "https://github.com/SamKhoze/ComfyUI-DeepFuze": {"stars": 408, "last_update": "2024-11-22 19:28:20", "author_account_age_days": 1811}, "https://github.com/SamTyurenkov/comfyui_chatgpt": {"stars": 0, "last_update": "2025-06-16 12:18:20", "author_account_age_days": 3329}, "https://github.com/San4itos/ComfyUI-Save-Images-as-Video": {"stars": 1, "last_update": "2025-05-18 12:37:15", "author_account_age_days": 1948}, "https://github.com/SanDiegoDude/ComfyUI-DeepStereo": {"stars": 2, "last_update": "2025-05-26 22:46:39", "author_account_age_days": 999}, "https://github.com/SanDiegoDude/ComfyUI-Kontext-API": {"stars": 7, "last_update": "2025-06-18 16:41:48", "author_account_age_days": 999}, "https://github.com/SanDiegoDude/ComfyUI-SaveAudioMP3": {"stars": 3, "last_update": "2025-05-07 23:48:49", "author_account_age_days": 999}, "https://github.com/Santodan/santodan-custom-nodes-comfyui": {"stars": 0, "last_update": "2025-06-11 11:43:21", "author_account_age_days": 3062}, "https://github.com/SayanoAI/Comfy-RVC": {"stars": 22, "last_update": "2024-10-09 04:08:31", "author_account_age_days": 2972}, "https://github.com/Sayene/comfyui-base64-to-image-size": {"stars": 0, "last_update": "2025-05-15 12:33:33", "author_account_age_days": 4048}, "https://github.com/Scholar01/ComfyUI-Keyframe": {"stars": 16, "last_update": "2025-01-22 04:09:29", "author_account_age_days": 3564}, "https://github.com/Scorpinaus/ComfyUI-DiffusersLoader": {"stars": 16, "last_update": "2024-08-26 14:51:47", "author_account_age_days": 1478}, "https://github.com/ScreamingHawk/comfyui-ollama-prompt-encode": {"stars": 11, "last_update": "2024-11-29 21:51:05", "author_account_age_days": 4874}, "https://github.com/SeaArtLab/ComfyUI-Long-CLIP": {"stars": 151, "last_update": "2025-03-08 04:16:32", "author_account_age_days": 445}, "https://github.com/SeanScripts/ComfyUI-PixtralLlamaMolmoVision": {"stars": 75, "last_update": "2025-01-31 09:01:23", "author_account_age_days": 1888}, "https://github.com/SeanScripts/ComfyUI-Unload-Model": {"stars": 50, "last_update": "2025-06-13 04:22:23", "author_account_age_days": 1888}, "https://github.com/SeargeDP/ComfyUI_Searge_LLM": {"stars": 105, "last_update": "2024-09-04 09:04:18", "author_account_age_days": 4540}, "https://github.com/SeargeDP/SeargeSDXL": {"stars": 847, "last_update": "2024-05-22 00:28:26", "author_account_age_days": 4540}, "https://github.com/Seedsa/Fooocus_Nodes": {"stars": 97, "last_update": "2025-01-08 07:57:28", "author_account_age_days": 2976}, "https://github.com/Sekiun/ComfyUI-WebpToPNGSequence": {"stars": 3, "last_update": "2025-04-15 12:40:47", "author_account_age_days": 1839}, "https://github.com/Semper-Sursum/HF-Flux-ComfyUI": {"stars": 2, "last_update": "2025-03-29 17:35:11", "author_account_age_days": 157}, "https://github.com/ServiceStack/comfy-asset-downloader": {"stars": 7, "last_update": "2025-05-08 16:21:02", "author_account_age_days": 5253}, "https://github.com/Shadetail/ComfyUI_Eagleshadow": {"stars": 4, "last_update": "2025-03-08 20:09:28", "author_account_age_days": 3766}, "https://github.com/Shakker-Labs/ComfyUI-IPAdapter-Flux": {"stars": 405, "last_update": "2025-06-22 08:50:25", "author_account_age_days": 216}, "https://github.com/Shannooty/ComfyUI-Timer-Nodes": {"stars": 3, "last_update": "2024-12-17 09:20:49", "author_account_age_days": 1664}, "https://github.com/SherryXieYuchen/ComfyUI-Image-Inpainting": {"stars": 4, "last_update": "2024-07-03 03:39:49", "author_account_age_days": 483}, "https://github.com/Shiba-2-shiba/ComfyUI-Magcache-for-SDXL": {"stars": 2, "last_update": "2025-06-24 03:54:19", "author_account_age_days": 754}, "https://github.com/Shiba-2-shiba/ComfyUI_DiffusionModel_fp8_converter": {"stars": 23, "last_update": "2025-02-18 07:36:09", "author_account_age_days": 754}, "https://github.com/Shiba-2-shiba/ComfyUI_FreeU_V2_timestepadd": {"stars": 0, "last_update": "2025-03-02 00:15:45", "author_account_age_days": 754}, "https://github.com/Shiba-2-shiba/comfyui-color-ascii-art-node": {"stars": 3, "last_update": "2025-06-13 08:51:55", "author_account_age_days": 754}, "https://github.com/Shibiko-AI/ShibikoAI-ComfyUI-Tools": {"stars": 10, "last_update": "2025-04-23 04:49:00", "author_account_age_days": 770}, "https://github.com/ShinChven/sc-comfy-nodes": {"stars": 1, "last_update": "2025-05-21 03:07:18", "author_account_age_days": 4537}, "https://github.com/ShmuelRonen/ComfyUI-Apply_Style_Model_Adjust": {"stars": 9, "last_update": "2024-11-23 03:57:20", "author_account_age_days": 1573}, "https://github.com/ShmuelRonen/ComfyUI-AstralAnimator": {"stars": 18, "last_update": "2024-07-18 12:41:22", "author_account_age_days": 1573}, "https://github.com/ShmuelRonen/ComfyUI-Audio_Quality_Enhancer": {"stars": 10, "last_update": "2025-05-11 20:53:31", "author_account_age_days": 1573}, "https://github.com/ShmuelRonen/ComfyUI-CohernetVideoSampler": {"stars": 17, "last_update": "2024-12-23 10:54:08", "author_account_age_days": 1573}, "https://github.com/ShmuelRonen/ComfyUI-DeepSeek_R1-Chat": {"stars": 19, "last_update": "2025-01-27 17:14:24", "author_account_age_days": 1573}, "https://github.com/ShmuelRonen/ComfyUI-EmptyHunyuanLatent": {"stars": 8, "last_update": "2024-12-29 05:30:57", "author_account_age_days": 1573}, "https://github.com/ShmuelRonen/ComfyUI-FramePackWrapper_Plus": {"stars": 94, "last_update": "2025-05-19 21:10:06", "author_account_age_days": 1573}, "https://github.com/ShmuelRonen/ComfyUI-FreeMemory": {"stars": 108, "last_update": "2025-03-20 11:25:12", "author_account_age_days": 1573}, "https://github.com/ShmuelRonen/ComfyUI-FreeVC_wrapper": {"stars": 63, "last_update": "2025-04-03 13:49:04", "author_account_age_days": 1573}, "https://github.com/ShmuelRonen/ComfyUI-Gemini_Flash_2.0_Exp": {"stars": 302, "last_update": "2025-04-22 17:30:51", "author_account_age_days": 1573}, "https://github.com/ShmuelRonen/ComfyUI-Gemini_TTS": {"stars": 14, "last_update": "2025-05-23 14:21:58", "author_account_age_days": 1573}, "https://github.com/ShmuelRonen/ComfyUI-HunyuanVideoSamplerSave": {"stars": 19, "last_update": "2025-02-05 19:26:18", "author_account_age_days": 1573}, "https://github.com/ShmuelRonen/ComfyUI-HunyuanVideoStyler": {"stars": 43, "last_update": "2024-12-31 19:19:42", "author_account_age_days": 1573}, "https://github.com/ShmuelRonen/ComfyUI-ImageMotionGuider": {"stars": 42, "last_update": "2024-12-27 11:19:59", "author_account_age_days": 1573}, "https://github.com/ShmuelRonen/ComfyUI-Janus_pro_vision": {"stars": 26, "last_update": "2025-03-20 11:20:56", "author_account_age_days": 1573}, "https://github.com/ShmuelRonen/ComfyUI-JoyHallo_wrapper": {"stars": 8, "last_update": "2025-03-20 11:24:21", "author_account_age_days": 1573}, "https://github.com/ShmuelRonen/ComfyUI-LatentSyncWrapper": {"stars": 832, "last_update": "2025-06-14 12:30:27", "author_account_age_days": 1573}, "https://github.com/ShmuelRonen/ComfyUI-Orpheus-TTS": {"stars": 4, "last_update": "2025-05-03 22:06:22", "author_account_age_days": 1573}, "https://github.com/ShmuelRonen/ComfyUI-PS_Flatten_Image": {"stars": 6, "last_update": "2025-04-02 10:58:27", "author_account_age_days": 1573}, "https://github.com/ShmuelRonen/ComfyUI-PixArt_XL": {"stars": 2, "last_update": "2025-03-20 11:23:20", "author_account_age_days": 1573}, "https://github.com/ShmuelRonen/ComfyUI-SVDResizer": {"stars": 3, "last_update": "2025-03-09 04:33:26", "author_account_age_days": 1573}, "https://github.com/ShmuelRonen/ComfyUI-Veo2-Experimental": {"stars": 26, "last_update": "2025-04-12 04:25:55", "author_account_age_days": 1573}, "https://github.com/ShmuelRonen/ComfyUI-VideoUpscale_WithModel": {"stars": 65, "last_update": "2025-05-02 20:13:08", "author_account_age_days": 1573}, "https://github.com/ShmuelRonen/ComfyUI-WanVideoKsampler": {"stars": 34, "last_update": "2025-02-27 13:48:05", "author_account_age_days": 1573}, "https://github.com/ShmuelRonen/ComfyUI_ChatterBox_Voice": {"stars": 14, "last_update": "2025-06-04 18:50:40", "author_account_age_days": 1573}, "https://github.com/ShmuelRonen/ComfyUI_Flux_1.1_RAW_API": {"stars": 58, "last_update": "2025-03-20 11:21:27", "author_account_age_days": 1573}, "https://github.com/ShmuelRonen/ComfyUI_Gemini_Flash": {"stars": 31, "last_update": "2025-03-20 04:42:59", "author_account_age_days": 1573}, "https://github.com/ShmuelRonen/ComfyUI_Hedra": {"stars": 2, "last_update": "2025-05-04 16:41:02", "author_account_age_days": 1573}, "https://github.com/ShmuelRonen/ComfyUI_pixtral_large": {"stars": 15, "last_update": "2025-01-08 10:59:35", "author_account_age_days": 1573}, "https://github.com/ShmuelRonen/ComfyUI_pixtral_vision": {"stars": 16, "last_update": "2024-11-20 12:58:30", "author_account_age_days": 1573}, "https://github.com/ShmuelRonen/ComfyUI_wav2lip": {"stars": 136, "last_update": "2024-09-18 13:17:42", "author_account_age_days": 1573}, "https://github.com/ShmuelRonen/DJ_VideoAudioMixer": {"stars": 1, "last_update": "2025-04-04 16:06:49", "author_account_age_days": 1573}, "https://github.com/ShmuelRonen/FluxKontextCreator": {"stars": 17, "last_update": "2025-06-10 17:07:05", "author_account_age_days": 1573}, "https://github.com/ShmuelRonen/comfyui-openai_fm": {"stars": 2, "last_update": "2025-04-03 14:25:24", "author_account_age_days": 1573}, "https://github.com/ShmuelRonen/google_moogle": {"stars": 5, "last_update": "2025-03-27 19:59:35", "author_account_age_days": 1573}, "https://github.com/Shraknard/ComfyUI-Remover": {"stars": 5, "last_update": "2024-07-24 08:42:48", "author_account_age_days": 2685}, "https://github.com/ShunL12324/comfy-portal-endpoint": {"stars": 0, "last_update": "2025-05-17 05:43:21", "author_account_age_days": 2905}, "https://github.com/Siberpone/lazy-pony-prompter": {"stars": 40, "last_update": "2025-03-28 05:54:45", "author_account_age_days": 821}, "https://github.com/Siempreflaco/ComfyUI-NCNodes": {"stars": 0, "last_update": "2025-05-02 20:04:43", "author_account_age_days": 1023}, "https://github.com/Sieyalixnet/ComfyUI_Textarea_Loaders": {"stars": 3, "last_update": "2024-08-30 01:19:54", "author_account_age_days": 2051}, "https://github.com/SignalCha1n/comfyui-ComfySnap": {"stars": 1, "last_update": "2025-04-27 15:24:25", "author_account_age_days": 77}, "https://github.com/SijieMei/ComfyUI-promptHistory": {"stars": 0, "last_update": "2025-03-24 03:32:05", "author_account_age_days": 2279}, "https://github.com/Sinphaltimus/comfyui_fedcoms_node_pack": {"stars": 0, "last_update": "2025-05-10 15:54:59", "author_account_age_days": 2646}, "https://github.com/SipherAGI/comfyui-animatediff": {"stars": 736, "last_update": "2024-05-22 18:16:43", "author_account_age_days": 773}, "https://github.com/SirWillance/FoW_Suite_LIGHT": {"stars": 2, "last_update": "2025-04-15 08:48:46", "author_account_age_days": 145}, "https://github.com/SlackinJack/asyncdiff_comfyui": {"stars": 0, "last_update": "2025-04-03 03:17:56", "author_account_age_days": 2543}, "https://github.com/SlackinJack/distrifuser_comfyui": {"stars": 0, "last_update": "2025-04-03 03:18:17", "author_account_age_days": 2543}, "https://github.com/SleeeepyZhou/ComfyUI-CNtranslator": {"stars": 5, "last_update": "2025-03-29 04:35:17", "author_account_age_days": 1565}, "https://github.com/Slickytail/ComfyUI-InstantX-IPAdapter-SD3": {"stars": 60, "last_update": "2025-03-27 12:47:27", "author_account_age_days": 3929}, "https://github.com/Slickytail/ComfyUI-RegionalAdaptiveSampling": {"stars": 19, "last_update": "2025-04-07 09:20:23", "author_account_age_days": 3929}, "https://github.com/Smirnov75/ComfyUI-mxToolkit": {"stars": 231, "last_update": "2025-05-07 11:44:27", "author_account_age_days": 1880}, "https://github.com/Smuzzies/comfyui_meme_maker": {"stars": 1, "last_update": "2024-07-05 22:01:41", "author_account_age_days": 1060}, "https://github.com/SoftMeng/ComfyUI-DeepCache-Fix": {"stars": 13, "last_update": "2024-07-25 13:09:00", "author_account_age_days": 3892}, "https://github.com/SoftMeng/ComfyUI-PIL": {"stars": 6, "last_update": "2024-10-13 10:02:17", "author_account_age_days": 3892}, "https://github.com/SoftMeng/ComfyUI_ImageToText": {"stars": 14, "last_update": "2024-06-14 08:08:36", "author_account_age_days": 3892}, "https://github.com/SoftMeng/ComfyUI_Mexx_Poster": {"stars": 25, "last_update": "2024-06-14 07:06:27", "author_account_age_days": 3892}, "https://github.com/SoftMeng/ComfyUI_Mexx_Styler": {"stars": 23, "last_update": "2024-06-14 07:09:03", "author_account_age_days": 3892}, "https://github.com/SongGuo11/ComfyUI-SaveAnything-SG11": {"stars": 0, "last_update": "2025-03-18 08:59:39", "author_account_age_days": 204}, "https://github.com/Sorcerio/MBM-Music-Visualizer": {"stars": 23, "last_update": "2024-05-23 01:09:18", "author_account_age_days": 4615}, "https://github.com/SozeInc/ComfyUI-Mobile": {"stars": 0, "last_update": "2024-08-22 03:12:11", "author_account_age_days": 448}, "https://github.com/SozeInc/ComfyUI_Soze": {"stars": 5, "last_update": "2025-06-14 17:26:59", "author_account_age_days": 448}, "https://github.com/SparknightLLC/ComfyUI-ConditionalInterrupt": {"stars": 3, "last_update": "2025-04-15 20:36:37", "author_account_age_days": 320}, "https://github.com/SparknightLLC/ComfyUI-GPENO": {"stars": 65, "last_update": "2025-04-15 20:29:05", "author_account_age_days": 320}, "https://github.com/SparknightLLC/ComfyUI-ImageAutosize": {"stars": 0, "last_update": "2025-05-23 19:44:54", "author_account_age_days": 320}, "https://github.com/SparknightLLC/ComfyUI-ImageAutotone": {"stars": 13, "last_update": "2025-04-15 20:35:55", "author_account_age_days": 320}, "https://github.com/SparknightLLC/ComfyUI-LatentClamp": {"stars": 2, "last_update": "2025-04-15 20:36:15", "author_account_age_days": 320}, "https://github.com/SparknightLLC/ComfyUI-MaskArbiter": {"stars": 4, "last_update": "2025-04-15 20:35:34", "author_account_age_days": 320}, "https://github.com/SparknightLLC/ComfyUI-WeightedRandomChoice": {"stars": 0, "last_update": "2025-04-22 00:31:50", "author_account_age_days": 320}, "https://github.com/SpenserCai/ComfyUI-FunAudioLLM": {"stars": 82, "last_update": "2024-11-27 09:22:05", "author_account_age_days": 3083}, "https://github.com/SshunWang/ComfyUI_CosyVoice": {"stars": 12, "last_update": "2025-02-05 23:48:10", "author_account_age_days": 2309}, "https://github.com/Stability-AI/ComfyUI-SAI_API": {"stars": 61, "last_update": "2025-03-04 12:11:12", "author_account_age_days": 1211}, "https://github.com/Stability-AI/stability-ComfyUI-nodes": {"stars": 230, "last_update": "2024-05-22 15:30:47", "author_account_age_days": 1211}, "https://github.com/StableLlama/ComfyUI-basic_data_handling": {"stars": 6, "last_update": "2025-06-15 19:50:31", "author_account_age_days": 546}, "https://github.com/StarAsh042/ComfyUI_RollingArtist": {"stars": 0, "last_update": "2025-05-05 21:26:43", "author_account_age_days": 3376}, "https://github.com/StarMagicAI/comfyui_tagger": {"stars": 5, "last_update": "2024-09-03 02:01:59", "author_account_age_days": 3914}, "https://github.com/Starnodes2024/ComfyUI_StarNodes": {"stars": 40, "last_update": "2025-06-14 07:54:01", "author_account_age_days": 370}, "https://github.com/StartHua/ComfyUI_OOTDiffusion_CXH": {"stars": 122, "last_update": "2024-06-14 08:12:12", "author_account_age_days": 3203}, "https://github.com/StartHua/ComfyUI_PCDMs": {"stars": 7, "last_update": "2024-05-22 23:21:14", "author_account_age_days": 3203}, "https://github.com/StartHua/ComfyUI_Seg_VITON": {"stars": 215, "last_update": "2024-05-22 23:20:17", "author_account_age_days": 3203}, "https://github.com/StartHua/Comfyui_CXH_DeepLX": {"stars": 8, "last_update": "2024-09-21 02:38:08", "author_account_age_days": 3203}, "https://github.com/StartHua/Comfyui_CXH_FluxLoraMerge": {"stars": 24, "last_update": "2024-12-26 06:56:07", "author_account_age_days": 3203}, "https://github.com/StartHua/Comfyui_CXH_Phi_3.5": {"stars": 16, "last_update": "2024-08-22 04:45:39", "author_account_age_days": 3203}, "https://github.com/StartHua/Comfyui_Gemini2": {"stars": 16, "last_update": "2024-12-12 09:42:42", "author_account_age_days": 3203}, "https://github.com/StartHua/Comfyui_joytag": {"stars": 55, "last_update": "2024-05-22 23:20:28", "author_account_age_days": 3203}, "https://github.com/StartHua/Comfyui_segformer_b2_clothes": {"stars": 89, "last_update": "2024-07-24 14:45:58", "author_account_age_days": 3203}, "https://github.com/Steudio/ComfyUI_Steudio": {"stars": 68, "last_update": "2025-05-22 23:05:15", "author_account_age_days": 504}, "https://github.com/Style-Mosaic/dino-x-comfyui-node": {"stars": 1, "last_update": "2025-01-28 21:40:18", "author_account_age_days": 235}, "https://github.com/SuperBeastsAI/ComfyUI-SuperBeasts": {"stars": 165, "last_update": "2024-07-31 02:48:34", "author_account_age_days": 457}, "https://github.com/SuperMasterBlasterLaser/ComfyUI_YOLO_Classifiers": {"stars": 1, "last_update": "2025-03-29 13:16:05", "author_account_age_days": 3946}, "https://github.com/Suzie1/ComfyUI_Comfyroll_CustomNodes": {"stars": 918, "last_update": "2024-07-24 11:16:13", "author_account_age_days": 2518}, "https://github.com/Sxela/ComfyWarp": {"stars": 47, "last_update": "2025-04-01 22:18:02", "author_account_age_days": 3740}, "https://github.com/SykkoAtHome/ComfyUI_FaceProcessor": {"stars": 8, "last_update": "2025-06-15 16:24:45", "author_account_age_days": 763}, "https://github.com/T-Ph525/ComfyUI-Underage-Filter": {"stars": 0, "last_update": "2025-05-30 12:10:57", "author_account_age_days": 1303}, "https://github.com/TFL-TFL/ComfyUI_Text_Translation": {"stars": 61, "last_update": "2025-05-25 02:27:01", "author_account_age_days": 1909}, "https://github.com/THtianhao/ComfyUI-FaceChain": {"stars": 135, "last_update": "2025-04-28 07:00:45", "author_account_age_days": 4059}, "https://github.com/THtianhao/ComfyUI-Portrait-Maker": {"stars": 194, "last_update": "2024-05-22 21:18:05", "author_account_age_days": 4059}, "https://github.com/TJ16th/comfyUI_TJ_NormalLighting": {"stars": 149, "last_update": "2024-05-23 00:25:37", "author_account_age_days": 2903}, "https://github.com/TKRLAB/ComfyUI_Prompt_List_JSON": {"stars": 1, "last_update": "2024-12-23 05:26:14", "author_account_age_days": 487}, "https://github.com/TMElyralab/Comfyui-MusePose": {"stars": 409, "last_update": "2024-07-31 06:21:52", "author_account_age_days": 465}, "https://github.com/TRI3D-LC/ComfyUI-MiroBoard": {"stars": 4, "last_update": "2024-11-21 07:15:20", "author_account_age_days": 807}, "https://github.com/TRI3D-LC/tri3d-comfyui-nodes": {"stars": 27, "last_update": "2025-06-19 08:44:57", "author_account_age_days": 807}, "https://github.com/TTPlanetPig/Comfyui_Hunyuan3D": {"stars": 28, "last_update": "2024-11-10 16:59:42", "author_account_age_days": 571}, "https://github.com/TTPlanetPig/Comfyui_JC2": {"stars": 201, "last_update": "2025-05-21 16:25:36", "author_account_age_days": 571}, "https://github.com/TTPlanetPig/Comfyui_Object_Detect_QWen_VL": {"stars": 92, "last_update": "2025-06-24 15:16:20", "author_account_age_days": 571}, "https://github.com/TTPlanetPig/Comfyui_Object_Migration": {"stars": 743, "last_update": "2024-11-20 16:51:57", "author_account_age_days": 571}, "https://github.com/TTPlanetPig/Comfyui_TTP_CN_Preprocessor": {"stars": 29, "last_update": "2024-08-21 17:52:56", "author_account_age_days": 571}, "https://github.com/TTPlanetPig/Comfyui_TTP_Toolset": {"stars": 691, "last_update": "2025-06-15 11:21:35", "author_account_age_days": 571}, "https://github.com/TTPlanetPig/TTP_Comfyui_FramePack_SE": {"stars": 43, "last_update": "2025-04-25 11:36:15", "author_account_age_days": 571}, "https://github.com/TW-CUI/TW-CUI-Util": {"stars": 1, "last_update": "2024-08-14 01:49:13", "author_account_age_days": 399}, "https://github.com/TZOOTZ/ComfyUI-TZOOTZ_VHS": {"stars": 2, "last_update": "2025-06-04 10:19:49", "author_account_age_days": 3417}, "https://github.com/TaiTair/comfyui-simswap": {"stars": 14, "last_update": "2024-07-31 18:28:38", "author_account_age_days": 3935}, "https://github.com/Taithrah/ComfyUI_Fens_Simple_Nodes": {"stars": 0, "last_update": "2025-06-20 22:03:36", "author_account_age_days": 4874}, "https://github.com/Taremin/comfyui-keep-multiple-tabs": {"stars": 5, "last_update": "2025-02-25 15:53:35", "author_account_age_days": 2583}, "https://github.com/Taremin/comfyui-prompt-config": {"stars": 0, "last_update": "2025-02-28 03:53:16", "author_account_age_days": 2583}, "https://github.com/Taremin/comfyui-prompt-extranetworks": {"stars": 7, "last_update": "2025-03-04 07:49:21", "author_account_age_days": 2583}, "https://github.com/Taremin/comfyui-string-tools": {"stars": 1, "last_update": "2025-02-26 13:22:39", "author_account_age_days": 2583}, "https://github.com/Taremin/webui-monaco-prompt": {"stars": 27, "last_update": "2025-03-06 08:57:58", "author_account_age_days": 2583}, "https://github.com/TashaSkyUp/EternalKernelPytorchNodes": {"stars": 1, "last_update": "2025-06-22 19:16:21", "author_account_age_days": 3532}, "https://github.com/TeaCrab/ComfyUI-TeaNodes": {"stars": 5, "last_update": "2024-05-22 20:44:05", "author_account_age_days": 3586}, "https://github.com/TechnoByteJS/ComfyUI-TechNodes": {"stars": 14, "last_update": "2024-09-20 23:26:02", "author_account_age_days": 2044}, "https://github.com/TemryL/ComfyS3": {"stars": 47, "last_update": "2024-11-05 14:56:04", "author_account_age_days": 1237}, "https://github.com/TemryL/ComfyUI-IDM-VTON": {"stars": 525, "last_update": "2024-08-20 02:44:02", "author_account_age_days": 1237}, "https://github.com/Temult/TWanSigmaGraph": {"stars": 8, "last_update": "2025-04-17 09:39:00", "author_account_age_days": 633}, "https://github.com/TencentQQGYLab/ComfyUI-ELLA": {"stars": 380, "last_update": "2024-08-16 11:21:10", "author_account_age_days": 466}, "https://github.com/Tenney95/ComfyUI-NodeAligner": {"stars": 129, "last_update": "2025-05-09 07:48:08", "author_account_age_days": 294}, "https://github.com/Tensor-Art/ComfyUI_TENSOR_ART": {"stars": 9, "last_update": "2025-04-02 08:31:41", "author_account_age_days": 767}, "https://github.com/TensorKaze/ComfyUI-TkNodes": {"stars": 0, "last_update": "2025-05-26 01:36:34", "author_account_age_days": 104}, "https://github.com/TheBarret/ZSuite": {"stars": 9, "last_update": "2024-08-10 13:31:03", "author_account_age_days": 3081}, "https://github.com/TheBill2001/ComfyUI-Save-Image-Caption": {"stars": 8, "last_update": "2025-04-04 12:21:18", "author_account_age_days": 1810}, "https://github.com/TheBill2001/comfyui-upscale-by-model": {"stars": 9, "last_update": "2024-06-18 17:57:06", "author_account_age_days": 1810}, "https://github.com/TheLustriVA/ComfyUI-Image-Size-Tools": {"stars": 0, "last_update": "2025-06-21 15:09:46", "author_account_age_days": 1447}, "https://github.com/TheMistoAI/ComfyUI-Anyline": {"stars": 467, "last_update": "2024-08-30 09:50:34", "author_account_age_days": 533}, "https://github.com/TheWhykiki/Whykiki-ComfyUIToolset": {"stars": 0, "last_update": "2025-03-02 22:17:54", "author_account_age_days": 3685}, "https://github.com/ThepExcel/aiangelgallery-comfyui": {"stars": 2, "last_update": "2025-01-15 07:53:09", "author_account_age_days": 1929}, "https://github.com/ThereforeGames/ComfyUI-Unprompted": {"stars": 10, "last_update": "2024-11-13 20:46:08", "author_account_age_days": 1303}, "https://github.com/TiamaTiramisu/risutools": {"stars": 1, "last_update": "2025-04-20 22:51:50", "author_account_age_days": 368}, "https://github.com/TinyTerra/ComfyUI_tinyterraNodes": {"stars": 530, "last_update": "2025-03-14 08:21:19", "author_account_age_days": 989}, "https://github.com/Tlant/ComfyUI-OllamaPromptsGeneratorTlant": {"stars": 2, "last_update": "2025-06-20 16:23:14", "author_account_age_days": 3037}, "https://github.com/ToTheBeginning/ComfyUI-DreamO": {"stars": 142, "last_update": "2025-06-24 14:59:29", "author_account_age_days": 3757}, "https://github.com/Tr1dae/ComfyUI-Dequality": {"stars": 0, "last_update": "2025-02-13 16:41:59", "author_account_age_days": 906}, "https://github.com/Trgtuan10/ComfyUI_YoloSegment_Mask": {"stars": 1, "last_update": "2024-09-26 01:46:02", "author_account_age_days": 940}, "https://github.com/TripleHeadedMonkey/ComfyUI_MileHighStyler": {"stars": 55, "last_update": "2025-06-18 09:32:23", "author_account_age_days": 1229}, "https://github.com/Tropfchen/ComfyUI-Embedding_Picker": {"stars": 44, "last_update": "2024-08-26 16:33:49", "author_account_age_days": 4222}, "https://github.com/Tropfchen/ComfyUI-yaResolutionSelector": {"stars": 14, "last_update": "2024-11-10 20:44:23", "author_account_age_days": 4222}, "https://github.com/TrophiHunter/ComfyUI_Photography_Nodes": {"stars": 2, "last_update": "2025-05-22 07:41:32", "author_account_age_days": 1086}, "https://github.com/Trung0246/ComfyUI-0246": {"stars": 127, "last_update": "2025-03-15 03:39:33", "author_account_age_days": 3748}, "https://github.com/Ttl/ComfyUi_NNLatentUpscale": {"stars": 241, "last_update": "2024-12-01 16:34:24", "author_account_age_days": 5290}, "https://github.com/TylerZoro/SD3-Scaling": {"stars": 1, "last_update": "2024-06-15 16:59:22", "author_account_age_days": 1654}, "https://github.com/Umikaze-job/select_folder_path_easy": {"stars": 6, "last_update": "2024-05-22 21:30:13", "author_account_age_days": 587}, "https://github.com/VAST-AI-Research/ComfyUI-Tripo": {"stars": 284, "last_update": "2025-06-26 07:50:36", "author_account_age_days": 610}, "https://github.com/VK/vk-nodes": {"stars": 0, "last_update": "2025-05-07 19:59:57", "author_account_age_days": 5713}, "https://github.com/Vaibhavs10/ComfyUI-DDUF": {"stars": 5, "last_update": "2025-01-03 15:10:44", "author_account_age_days": 3348}, "https://github.com/VangengLab/ComfyUI-LivePortrait_v2": {"stars": 6, "last_update": "2024-11-09 08:00:22", "author_account_age_days": 647}, "https://github.com/VangengLab/ComfyUI-LivePortrait_v3": {"stars": 22, "last_update": "2024-11-09 07:59:42", "author_account_age_days": 647}, "https://github.com/Vaporbook/ComfyUI-SaveImage-PP": {"stars": 0, "last_update": "2025-05-08 15:04:17", "author_account_age_days": 5206}, "https://github.com/VertexAnomaly/ComfyUI_ImageSentinel": {"stars": 1, "last_update": "2025-04-04 13:50:16", "author_account_age_days": 1023}, "https://github.com/VertexStudio/roblox-comfyui-nodes": {"stars": 0, "last_update": "2024-10-08 16:35:54", "author_account_age_days": 3347}, "https://github.com/VikramxD/VEnhancer-ComfyUI-Wrapper": {"stars": 12, "last_update": "2025-01-14 07:35:00", "author_account_age_days": 1724}, "https://github.com/Visionatrix/ComfyUI-Gemini": {"stars": 7, "last_update": "2025-06-26 14:45:14", "author_account_age_days": 484}, "https://github.com/Visionatrix/ComfyUI-RemoteVAE": {"stars": 2, "last_update": "2025-03-12 05:57:35", "author_account_age_days": 485}, "https://github.com/Visionatrix/ComfyUI-Visionatrix": {"stars": 1, "last_update": "2025-06-22 07:38:06", "author_account_age_days": 485}, "https://github.com/VrchStudio/comfyui-web-viewer": {"stars": 219, "last_update": "2025-06-18 01:35:41", "author_account_age_days": 1288}, "https://github.com/VykosX/ControlFlowUtils": {"stars": 118, "last_update": "2024-12-09 17:24:48", "author_account_age_days": 2278}, "https://github.com/WASasquatch/ComfyUI_Preset_Merger": {"stars": 33, "last_update": "2025-03-27 14:52:46", "author_account_age_days": 4994}, "https://github.com/WASasquatch/FreeU_Advanced": {"stars": 120, "last_update": "2024-10-27 01:49:14", "author_account_age_days": 4994}, "https://github.com/WASasquatch/PPF_Noise_ComfyUI": {"stars": 24, "last_update": "2024-06-14 10:27:23", "author_account_age_days": 4994}, "https://github.com/WASasquatch/PowerNoiseSuite": {"stars": 77, "last_update": "2024-07-31 13:48:33", "author_account_age_days": 4994}, "https://github.com/WASasquatch/WAS_Extras": {"stars": 34, "last_update": "2024-06-17 04:08:37", "author_account_age_days": 4994}, "https://github.com/WUYUDING2583/ComfyUI-Save-Image-Callback": {"stars": 2, "last_update": "2025-01-21 08:19:52", "author_account_age_days": 2579}, "https://github.com/WX-NPS1598/ComfyUI-Auto_Crop_By_NPS": {"stars": 5, "last_update": "2024-07-30 04:43:14", "author_account_age_days": 344}, "https://github.com/WaddingtonHoldings/ComfyUI-InstaSD": {"stars": 3, "last_update": "2025-06-27 22:18:33", "author_account_age_days": 991}, "https://github.com/WainWong/ComfyUI-Loop-image": {"stars": 34, "last_update": "2025-03-28 03:09:27", "author_account_age_days": 2990}, "https://github.com/Wakfull33/ComfyUI-SaveImageCivitAI": {"stars": 1, "last_update": "2024-10-29 11:03:23", "author_account_age_days": 3328}, "https://github.com/WangPengxing/ComfyUI_WPX_Node": {"stars": 0, "last_update": "2025-01-20 08:31:55", "author_account_age_days": 694}, "https://github.com/WarpedAnimation/ComfyUI-WarpedToolset": {"stars": 2, "last_update": "2025-06-01 05:35:02", "author_account_age_days": 108}, "https://github.com/WaveSpeedAI/wavespeed-comfyui": {"stars": 14, "last_update": "2025-06-26 03:49:36", "author_account_age_days": 156}, "https://github.com/WebDev9000/WebDev9000-Nodes": {"stars": 1, "last_update": "2024-06-14 10:28:22", "author_account_age_days": 4120}, "https://github.com/Wenaka2004/ComfyUI-TagClassifier": {"stars": 23, "last_update": "2025-01-31 04:28:34", "author_account_age_days": 906}, "https://github.com/Wicloz/ComfyUI-Simply-Nodes": {"stars": 1, "last_update": "2025-01-05 01:44:38", "author_account_age_days": 4012}, "https://github.com/X-School-Academy/X-FluxAgent": {"stars": 26, "last_update": "2025-06-05 08:28:11", "author_account_age_days": 86}, "https://github.com/X-T-E-R/ComfyUI-EasyCivitai-XTNodes": {"stars": 44, "last_update": "2024-09-04 11:37:04", "author_account_age_days": 1531}, "https://github.com/XLabs-AI/x-flux-comfyui": {"stars": 1578, "last_update": "2024-10-30 12:51:21", "author_account_age_days": 326}, "https://github.com/XWAVEart/comfyui-xwave-xlitch-nodes": {"stars": 1, "last_update": "2025-06-04 20:33:17", "author_account_age_days": 604}, "https://github.com/XchanBik/ComfyUI_SimpleBridgeNode": {"stars": 0, "last_update": "2025-05-15 22:10:43", "author_account_age_days": 49}, "https://github.com/Xclbr7/ComfyUI-Merlin": {"stars": 29, "last_update": "2024-09-02 19:36:05", "author_account_age_days": 305}, "https://github.com/Xiangyu-CAS/HandFixer": {"stars": 184, "last_update": "2025-02-10 02:02:01", "author_account_age_days": 3755}, "https://github.com/XieJunchen/comfyUI_LLM": {"stars": 2, "last_update": "2025-06-07 08:34:02", "author_account_age_days": 2141}, "https://github.com/Xkipper/ComfyUI_SkipperNodes": {"stars": 0, "last_update": "2025-04-26 20:13:45", "author_account_age_days": 3847}, "https://github.com/XmYx/deforum-comfy-nodes": {"stars": 190, "last_update": "2025-05-26 19:50:55", "author_account_age_days": 2974}, "https://github.com/Xyem/Xycuno-Oobabooga": {"stars": 4, "last_update": "2024-05-23 00:14:14", "author_account_age_days": 4693}, "https://github.com/YMC-GitHub/comfyui_node_ymc_effect_shatter": {"stars": 0, "last_update": "2025-04-12 15:00:21", "author_account_age_days": 3063}, "https://github.com/YMC-GitHub/ymc-node-as-x-type": {"stars": 0, "last_update": "2025-06-06 12:23:11", "author_account_age_days": 3063}, "https://github.com/YMC-GitHub/ymc-node-suite-comfyui": {"stars": 20, "last_update": "2025-06-09 08:07:23", "author_account_age_days": 3063}, "https://github.com/YMC-GitHub/ymc_node_joy": {"stars": 0, "last_update": "2025-06-19 07:24:28", "author_account_age_days": 3063}, "https://github.com/YOUR-WORST-TACO/ComfyUI-TacoNodes": {"stars": 15, "last_update": "2024-05-22 20:48:23", "author_account_age_days": 4124}, "https://github.com/YRIKKA/ComfyUI-InferenceTimeScaling": {"stars": 20, "last_update": "2025-02-27 21:13:18", "author_account_age_days": 367}, "https://github.com/Yahweasel/ComfyUI-MinDalle": {"stars": 0, "last_update": "2025-05-26 20:42:34", "author_account_age_days": 3017}, "https://github.com/Yanick112/ComfyUI-ToSVG": {"stars": 198, "last_update": "2025-06-20 14:10:06", "author_account_age_days": 1182}, "https://github.com/YaroslavIv/comfyui_swd": {"stars": 2, "last_update": "2025-06-23 04:10:43", "author_account_age_days": 1944}, "https://github.com/YarvixPA/ComfyUI-NeuralMedia": {"stars": 4, "last_update": "2025-06-26 08:25:44", "author_account_age_days": 575}, "https://github.com/YinBailiang/MergeBlockWeighted_fo_ComfyUI": {"stars": 16, "last_update": "2025-01-03 03:58:20", "author_account_age_days": 1154}, "https://github.com/Yuan-ManX/ComfyUI-AniSora": {"stars": 23, "last_update": "2025-05-27 04:11:59", "author_account_age_days": 1807}, "https://github.com/Yuan-ManX/ComfyUI-AudioX": {"stars": 10, "last_update": "2025-05-27 04:14:59", "author_account_age_days": 1807}, "https://github.com/Yuan-ManX/ComfyUI-Bagel": {"stars": 29, "last_update": "2025-05-28 03:00:53", "author_account_age_days": 1807}, "https://github.com/Yuan-ManX/ComfyUI-ChatterboxTTS": {"stars": 8, "last_update": "2025-05-30 08:13:06", "author_account_age_days": 1807}, "https://github.com/Yuan-ManX/ComfyUI-Cobra": {"stars": 5, "last_update": "2025-04-18 02:06:26", "author_account_age_days": 1807}, "https://github.com/Yuan-ManX/ComfyUI-Dia": {"stars": 3, "last_update": "2025-04-24 06:58:05", "author_account_age_days": 1807}, "https://github.com/Yuan-ManX/ComfyUI-Direct3D-S2": {"stars": 4, "last_update": "2025-06-10 03:24:25", "author_account_age_days": 1807}, "https://github.com/Yuan-ManX/ComfyUI-HiDream-I1": {"stars": 8, "last_update": "2025-04-14 02:56:22", "author_account_age_days": 1807}, "https://github.com/Yuan-ManX/ComfyUI-Hunyuan3D-2.1": {"stars": 12, "last_update": "2025-06-16 07:03:54", "author_account_age_days": 1807}, "https://github.com/Yuan-ManX/ComfyUI-HunyuanPortrait": {"stars": 7, "last_update": "2025-05-28 09:47:34", "author_account_age_days": 1807}, "https://github.com/Yuan-ManX/ComfyUI-HunyuanVideo-Avatar": {"stars": 24, "last_update": "2025-05-29 07:49:15", "author_account_age_days": 1807}, "https://github.com/Yuan-ManX/ComfyUI-Kimi-VL": {"stars": 1, "last_update": "2025-04-17 06:55:14", "author_account_age_days": 1807}, "https://github.com/Yuan-ManX/ComfyUI-LLaMA-Mesh": {"stars": 5, "last_update": "2024-11-29 09:52:04", "author_account_age_days": 1807}, "https://github.com/Yuan-ManX/ComfyUI-LayerAnimate": {"stars": 4, "last_update": "2025-04-01 03:16:53", "author_account_age_days": 1807}, "https://github.com/Yuan-ManX/ComfyUI-LiveCC": {"stars": 4, "last_update": "2025-05-27 04:14:30", "author_account_age_days": 1807}, "https://github.com/Yuan-ManX/ComfyUI-Matrix-Game": {"stars": 3, "last_update": "2025-05-13 08:05:00", "author_account_age_days": 1807}, "https://github.com/Yuan-ManX/ComfyUI-MoviiGen": {"stars": 9, "last_update": "2025-05-27 04:12:30", "author_account_age_days": 1807}, "https://github.com/Yuan-ManX/ComfyUI-Multiverse": {"stars": 1, "last_update": "2025-05-09 06:51:35", "author_account_age_days": 1807}, "https://github.com/Yuan-ManX/ComfyUI-Muyan-TTS": {"stars": 2, "last_update": "2025-05-08 08:21:24", "author_account_age_days": 1807}, "https://github.com/Yuan-ManX/ComfyUI-OmniGen2": {"stars": 95, "last_update": "2025-06-26 02:46:07", "author_account_age_days": 1808}, "https://github.com/Yuan-ManX/ComfyUI-OrpheusTTS": {"stars": 6, "last_update": "2025-03-24 02:47:23", "author_account_age_days": 1807}, "https://github.com/Yuan-ManX/ComfyUI-PhotoDoodle": {"stars": 3, "last_update": "2025-02-28 03:47:54", "author_account_age_days": 1807}, "https://github.com/Yuan-ManX/ComfyUI-PosterCraft": {"stars": 5, "last_update": "2025-06-26 10:00:28", "author_account_age_days": 1808}, "https://github.com/Yuan-ManX/ComfyUI-SkyReels-A2": {"stars": 26, "last_update": "2025-05-27 04:14:03", "author_account_age_days": 1807}, "https://github.com/Yuan-ManX/ComfyUI-SoundHub": {"stars": 1, "last_update": "2024-11-27 08:00:48", "author_account_age_days": 1807}, "https://github.com/Yuan-ManX/ComfyUI-Step1X-3D": {"stars": 12, "last_update": "2025-05-16 02:36:06", "author_account_age_days": 1807}, "https://github.com/Yuan-ManX/ComfyUI-Step1X-Edit": {"stars": 11, "last_update": "2025-04-29 07:36:52", "author_account_age_days": 1807}, "https://github.com/Yuan-ManX/ComfyUI-StyleStudio": {"stars": 4, "last_update": "2025-03-10 09:38:08", "author_account_age_days": 1807}, "https://github.com/Yuan-ManX/ComfyUI-UNO": {"stars": 10, "last_update": "2025-04-11 07:37:33", "author_account_age_days": 1807}, "https://github.com/Yuan-ManX/ComfyUI-Vui": {"stars": 3, "last_update": "2025-06-12 03:55:32", "author_account_age_days": 1807}, "https://github.com/ZHO-ZHO-ZHO/ComfyUI-APISR": {"stars": 376, "last_update": "2024-05-22 14:14:46", "author_account_age_days": 708}, "https://github.com/ZHO-ZHO-ZHO/ComfyUI-Animated-optical-illusions": {"stars": 21, "last_update": "2024-06-14 07:06:15", "author_account_age_days": 708}, "https://github.com/ZHO-ZHO-ZHO/ComfyUI-ArtGallery": {"stars": 509, "last_update": "2024-06-12 04:40:50", "author_account_age_days": 708}, "https://github.com/ZHO-ZHO-ZHO/ComfyUI-BRIA_AI-RMBG": {"stars": 806, "last_update": "2024-05-22 14:14:18", "author_account_age_days": 708}, "https://github.com/ZHO-ZHO-ZHO/ComfyUI-DeepSeek-JanusPro": {"stars": 103, "last_update": "2025-02-21 09:45:54", "author_account_age_days": 708}, "https://github.com/ZHO-ZHO-ZHO/ComfyUI-DepthFM": {"stars": 74, "last_update": "2024-05-22 14:14:03", "author_account_age_days": 708}, "https://github.com/ZHO-ZHO-ZHO/ComfyUI-Gemini": {"stars": 762, "last_update": "2024-05-22 14:15:11", "author_account_age_days": 708}, "https://github.com/ZHO-ZHO-ZHO/ComfyUI-InstantID": {"stars": 1418, "last_update": "2024-05-22 13:57:55", "author_account_age_days": 708}, "https://github.com/ZHO-ZHO-ZHO/ComfyUI-Phi-3-mini": {"stars": 205, "last_update": "2024-06-30 08:41:40", "author_account_age_days": 708}, "https://github.com/ZHO-ZHO-ZHO/ComfyUI-PhotoMaker-ZHO": {"stars": 816, "last_update": "2024-05-22 14:13:49", "author_account_age_days": 708}, "https://github.com/ZHO-ZHO-ZHO/ComfyUI-PixArt-alpha-Diffusers": {"stars": 50, "last_update": "2024-05-22 13:40:58", "author_account_age_days": 708}, "https://github.com/ZHO-ZHO-ZHO/ComfyUI-Q-Align": {"stars": 5, "last_update": "2024-05-22 14:15:52", "author_account_age_days": 708}, "https://github.com/ZHO-ZHO-ZHO/ComfyUI-Qwen-VL-API": {"stars": 207, "last_update": "2024-05-22 14:14:57", "author_account_age_days": 708}, "https://github.com/ZHO-ZHO-ZHO/ComfyUI-SVD-ZHO": {"stars": 107, "last_update": "2024-05-22 13:40:44", "author_account_age_days": 708}, "https://github.com/ZHO-ZHO-ZHO/ComfyUI-SegMoE": {"stars": 79, "last_update": "2024-05-22 13:41:14", "author_account_age_days": 708}, "https://github.com/ZHO-ZHO-ZHO/ComfyUI-Text_Image-Composite": {"stars": 110, "last_update": "2024-05-31 12:03:55", "author_account_age_days": 708}, "https://github.com/ZHO-ZHO-ZHO/ComfyUI-UltraEdit-ZHO": {"stars": 147, "last_update": "2024-07-11 14:59:07", "author_account_age_days": 708}, "https://github.com/ZHO-ZHO-ZHO/ComfyUI-YoloWorld-EfficientSAM": {"stars": 744, "last_update": "2024-05-22 13:01:07", "author_account_age_days": 708}, "https://github.com/ZHO-ZHO-ZHO/comfyui-portrait-master-zh-cn": {"stars": 1746, "last_update": "2024-06-14 09:00:04", "author_account_age_days": 708}, "https://github.com/ZZXYWQ/ComfyUI-ZZXYWQ": {"stars": 23, "last_update": "2024-07-19 06:38:39", "author_account_age_days": 1641}, "https://github.com/Zachary116699/ComfyUI-LoadImageWithMetaDataEx": {"stars": 1, "last_update": "2025-06-10 07:10:28", "author_account_age_days": 1728}, "https://github.com/ZaneA/ComfyUI-ImageReward": {"stars": 31, "last_update": "2025-02-24 19:55:45", "author_account_age_days": 5908}, "https://github.com/Zar4X/ComfyUI-Batch-Process": {"stars": 2, "last_update": "2025-06-25 14:40:23", "author_account_age_days": 798}, "https://github.com/Zar4X/ComfyUI-Image-Resizing": {"stars": 0, "last_update": "2025-06-25 14:43:35", "author_account_age_days": 798}, "https://github.com/Zch6111/AI_Text_Comfyui": {"stars": 1, "last_update": "2025-06-05 03:22:47", "author_account_age_days": 440}, "https://github.com/ZeDarkAdam/ComfyUI-Embeddings-Tools": {"stars": 2, "last_update": "2024-06-23 19:19:40", "author_account_age_days": 1562}, "https://github.com/Zehong-Ma/ComfyUI-MagCache": {"stars": 143, "last_update": "2025-06-21 03:29:12", "author_account_age_days": 1570}, "https://github.com/Zeks/comfyui-rapidfire": {"stars": 0, "last_update": "2025-01-14 18:28:43", "author_account_age_days": 4988}, "https://github.com/Zuellni/ComfyUI-Custom-Nodes": {"stars": 44, "last_update": "2023-09-19 12:11:26", "author_account_age_days": 891}, "https://github.com/Zuellni/ComfyUI-ExLlama-Nodes": {"stars": 118, "last_update": "2024-12-06 14:22:11", "author_account_age_days": 891}, "https://github.com/Zuellni/ComfyUI-PickScore-Nodes": {"stars": 37, "last_update": "2024-09-08 09:17:04", "author_account_age_days": 891}, "https://github.com/a-und-b/ComfyUI_Delay": {"stars": 4, "last_update": "2025-01-10 11:20:35", "author_account_age_days": 807}, "https://github.com/a-und-b/ComfyUI_IC-Light-v2_fal": {"stars": 36, "last_update": "2025-05-05 08:34:47", "author_account_age_days": 807}, "https://github.com/a-und-b/ComfyUI_JSON_Helper": {"stars": 3, "last_update": "2025-01-09 15:54:55", "author_account_age_days": 807}, "https://github.com/a-und-b/ComfyUI_LoRA_from_URL": {"stars": 3, "last_update": "2025-01-16 13:40:26", "author_account_age_days": 807}, "https://github.com/a-und-b/ComfyUI_MaskAreaCondition": {"stars": 2, "last_update": "2025-04-28 08:23:36", "author_account_age_days": 807}, "https://github.com/a1lazydog/ComfyUI-AudioScheduler": {"stars": 103, "last_update": "2024-08-08 03:04:19", "author_account_age_days": 5170}, "https://github.com/abdozmantar/ComfyUI-DeepExtract": {"stars": 37, "last_update": "2025-04-26 15:13:57", "author_account_age_days": 520}, "https://github.com/aburahamu/ComfyUI-IsNiceParts": {"stars": 3, "last_update": "2024-06-14 12:01:40", "author_account_age_days": 441}, "https://github.com/aburahamu/ComfyUI-RequestsPoster": {"stars": 2, "last_update": "2024-06-14 13:59:24", "author_account_age_days": 441}, "https://github.com/abyz22/image_control": {"stars": 16, "last_update": "2024-08-31 08:39:44", "author_account_age_days": 531}, "https://github.com/acorderob/sd-webui-prompt-postprocessor": {"stars": 36, "last_update": "2025-05-31 10:32:11", "author_account_age_days": 4196}, "https://github.com/adbrasi/ComfyUI-TrashNodes-DownloadHuggingface": {"stars": 6, "last_update": "2024-05-22 23:24:45", "author_account_age_days": 1072}, "https://github.com/adieyal/comfyui-dynamicprompts": {"stars": 320, "last_update": "2024-07-09 14:21:09", "author_account_age_days": 5385}, "https://github.com/adigayung/ComfyUI-Translator": {"stars": 10, "last_update": "2024-09-09 03:36:52", "author_account_age_days": 581}, "https://github.com/adriflex/ComfyUI_Blender_Texdiff": {"stars": 2, "last_update": "2024-05-22 23:14:18", "author_account_age_days": 2590}, "https://github.com/aegis72/aegisflow_utility_nodes": {"stars": 31, "last_update": "2024-10-03 11:11:39", "author_account_age_days": 952}, "https://github.com/aegis72/comfyui-styles-all": {"stars": 52, "last_update": "2024-05-22 22:10:41", "author_account_age_days": 952}, "https://github.com/agilly1989/ComfyUI_agilly1989_motorway": {"stars": 7, "last_update": "2025-05-02 22:23:32", "author_account_age_days": 2252}, "https://github.com/ahernandezmiro/ComfyUI-GCP_Storage_tools": {"stars": 2, "last_update": "2025-01-03 18:48:03", "author_account_age_days": 4363}, "https://github.com/ai-liam/comfyui-liam": {"stars": 2, "last_update": "2024-06-22 03:27:52", "author_account_age_days": 1889}, "https://github.com/ai-liam/comfyui_liam_util": {"stars": 1, "last_update": "2024-05-22 22:21:23", "author_account_age_days": 1889}, "https://github.com/ai-shizuka/ComfyUI-tbox": {"stars": 15, "last_update": "2025-04-22 10:21:03", "author_account_age_days": 380}, "https://github.com/aiaiaikkk/ComfyUI-Curve": {"stars": 103, "last_update": "2025-06-26 21:59:17", "author_account_age_days": 271}, "https://github.com/aianimation55/ComfyUI-FatLabels": {"stars": 5, "last_update": "2024-05-22 21:26:01", "author_account_age_days": 642}, "https://github.com/aiartvn/A2V_Multi_Image_Composite": {"stars": 1, "last_update": "2025-02-02 04:14:06", "author_account_age_days": 152}, "https://github.com/aicoder-max/Pillar_For_ComfyUI": {"stars": 2, "last_update": "2025-06-05 09:40:09", "author_account_age_days": 37}, "https://github.com/aicuai/aicu-comfyui-stability-ai-api": {"stars": 1, "last_update": "2025-02-21 13:31:14", "author_account_age_days": 710}, "https://github.com/aidec/Comfyui_TextBatch_aidec": {"stars": 8, "last_update": "2025-04-09 20:26:38", "author_account_age_days": 4270}, "https://github.com/aidenli/ComfyUI_NYJY": {"stars": 126, "last_update": "2025-06-16 06:06:12", "author_account_age_days": 4916}, "https://github.com/aigc-apps/EasyAnimate": {"stars": 2171, "last_update": "2025-03-06 11:41:28", "author_account_age_days": 686}, "https://github.com/aigc-apps/VideoX-Fun": {"stars": 1140, "last_update": "2025-06-26 08:17:03", "author_account_age_days": 686}, "https://github.com/aimerib/ComfyUI_HigherBitDepthSaveImage": {"stars": 2, "last_update": "2024-09-14 03:03:01", "author_account_age_days": 3039}, "https://github.com/ainewsto/Comfyui-chatgpt-api": {"stars": 50, "last_update": "2025-04-30 04:08:25", "author_account_age_days": 1021}, "https://github.com/ainewsto/Comfyui-google-veo2-api": {"stars": 5, "last_update": "2025-05-06 06:43:48", "author_account_age_days": 1021}, "https://github.com/ainewsto/Comfyui_Comfly_v2": {"stars": 40, "last_update": "2025-06-19 05:59:08", "author_account_age_days": 1021}, "https://github.com/ainewsto/comfyui-labs-google": {"stars": 80, "last_update": "2025-06-18 02:12:39", "author_account_age_days": 1021}, "https://github.com/aisabervisionlab/ComfyUI_merge_ASVL": {"stars": 2, "last_update": "2024-07-31 13:39:36", "author_account_age_days": 357}, "https://github.com/ajbergh/comfyui-ethnicity_hairstyle_clip_encoder": {"stars": 2, "last_update": "2025-02-28 22:07:11", "author_account_age_days": 2150}, "https://github.com/akatz-ai/ComfyUI-AKatz-Nodes": {"stars": 25, "last_update": "2025-04-05 00:47:00", "author_account_age_days": 403}, "https://github.com/akatz-ai/ComfyUI-Basic-Math": {"stars": 0, "last_update": "2025-06-18 01:49:05", "author_account_age_days": 403}, "https://github.com/akatz-ai/ComfyUI-DepthCrafter-Nodes": {"stars": 226, "last_update": "2025-05-05 04:23:55", "author_account_age_days": 403}, "https://github.com/akatz-ai/ComfyUI-Depthflow-Nodes": {"stars": 284, "last_update": "2025-06-24 22:27:31", "author_account_age_days": 403}, "https://github.com/akatz-ai/ComfyUI-X-Portrait-Nodes": {"stars": 85, "last_update": "2025-04-20 05:29:13", "author_account_age_days": 403}, "https://github.com/akierson/ComfyUI-textnodes": {"stars": 0, "last_update": "2024-10-20 20:12:15", "author_account_age_days": 2702}, "https://github.com/akierson/comfyui-colornodes": {"stars": 1, "last_update": "2024-10-20 20:14:09", "author_account_age_days": 2702}, "https://github.com/akspa0/ComfyUI-FapMixPlus": {"stars": 1, "last_update": "2024-11-11 02:59:10", "author_account_age_days": 499}, "https://github.com/al-swaiti/All-IN-ONE-style": {"stars": 5, "last_update": "2024-07-30 05:59:49", "author_account_age_days": 1298}, "https://github.com/al-swaiti/ComfyUI-CascadeResolutions": {"stars": 5, "last_update": "2024-07-31 13:48:47", "author_account_age_days": 1298}, "https://github.com/al-swaiti/ComfyUI-OllamaGemini": {"stars": 102, "last_update": "2025-05-17 14:07:02", "author_account_age_days": 1298}, "https://github.com/alanhuang67/ComfyUI-FAI-Node": {"stars": 14, "last_update": "2024-08-02 03:35:41", "author_account_age_days": 3858}, "https://github.com/alastor-666-1933/caching_to_not_waste": {"stars": 5, "last_update": "2025-05-29 19:57:12", "author_account_age_days": 4417}, "https://github.com/aleolidev/comfy_kaizen_package": {"stars": 0, "last_update": "2025-06-21 11:10:52", "author_account_age_days": 2945}, "https://github.com/alessandroperilli/APW_Nodes": {"stars": 4, "last_update": "2025-06-19 14:34:41", "author_account_age_days": 3876}, "https://github.com/alessandroperilli/apw_nodes": {"stars": 4, "last_update": "2025-06-19 14:34:41", "author_account_age_days": 3876}, "https://github.com/alessandrozonta/ComfyUI-CenterNode": {"stars": 7, "last_update": "2024-11-14 12:20:40", "author_account_age_days": 1565}, "https://github.com/alessandrozonta/ComfyUI-Layers": {"stars": 51, "last_update": "2024-07-31 13:46:32", "author_account_age_days": 1565}, "https://github.com/alessandrozonta/ComfyUI-OpenPose": {"stars": 26, "last_update": "2024-07-31 13:51:14", "author_account_age_days": 1565}, "https://github.com/alessandrozonta/ComfyUI-PoseDirection": {"stars": 1, "last_update": "2025-06-03 10:12:26", "author_account_age_days": 1565}, "https://github.com/alessandrozonta/Comfyui-LoopLoader": {"stars": 2, "last_update": "2025-02-21 13:28:39", "author_account_age_days": 1565}, "https://github.com/alexcong/ComfyUI_QwenVL": {"stars": 70, "last_update": "2025-06-07 21:58:57", "author_account_age_days": 3955}, "https://github.com/alexgenovese/ComfyUI-UNO-Flux": {"stars": 0, "last_update": "2025-06-16 17:27:27", "author_account_age_days": 5383}, "https://github.com/alexgenovese/ComfyUI_HF_Servelress_Inference": {"stars": 12, "last_update": "2024-09-01 13:04:48", "author_account_age_days": 5383}, "https://github.com/alexisrolland/ComfyUI-Phi": {"stars": 9, "last_update": "2025-06-02 16:03:13", "author_account_age_days": 3654}, "https://github.com/alexopus/ComfyUI-Image-Saver": {"stars": 102, "last_update": "2025-06-15 13:48:54", "author_account_age_days": 3048}, "https://github.com/ali-vilab/ACE_plus": {"stars": 1196, "last_update": "2025-04-21 06:36:02", "author_account_age_days": 856}, "https://github.com/ali1234/comfyui-job-iterator": {"stars": 116, "last_update": "2024-11-16 07:51:07", "author_account_age_days": 5228}, "https://github.com/alisson-anjos/ComfyUI-Ollama-Describer": {"stars": 72, "last_update": "2025-06-09 05:43:18", "author_account_age_days": 969}, "https://github.com/alpertunga-bile/image-caption-comfyui": {"stars": 9, "last_update": "2025-05-21 20:09:00", "author_account_age_days": 1641}, "https://github.com/alpertunga-bile/prompt-generator-comfyui": {"stars": 102, "last_update": "2025-05-21 20:05:48", "author_account_age_days": 1641}, "https://github.com/alsritter/asymmetric-tiling-comfyui": {"stars": 17, "last_update": "2024-05-22 20:43:07", "author_account_age_days": 2362}, "https://github.com/alt-key-project/comfyui-dream-project": {"stars": 100, "last_update": "2025-02-16 14:45:43", "author_account_age_days": 1028}, "https://github.com/alt-key-project/comfyui-dream-video-batches": {"stars": 72, "last_update": "2025-02-23 10:28:40", "author_account_age_days": 1028}, "https://github.com/an90ray/ComfyUI_RErouter_CustomNodes": {"stars": 0, "last_update": "2024-05-22 22:21:00", "author_account_age_days": 557}, "https://github.com/andersxa/comfyui-PromptAttention": {"stars": 22, "last_update": "2024-06-20 11:09:25", "author_account_age_days": 3321}, "https://github.com/andygill/comfyui-sunflower-nodes": {"stars": 1, "last_update": "2025-01-02 04:23:22", "author_account_age_days": 6160}, "https://github.com/angeloshredder/StableCascadeResizer": {"stars": 2, "last_update": "2024-05-23 00:12:55", "author_account_age_days": 2197}, "https://github.com/angree/ComfyUI-Q_GLB_Material_Modifier": {"stars": 1, "last_update": "2025-05-30 22:51:59", "author_account_age_days": 3089}, "https://github.com/angree/ComfyUI-Q_find-mask-size": {"stars": 0, "last_update": "2025-05-30 22:53:04", "author_account_age_days": 3089}, "https://github.com/anhkhoatranle30/Handy-Nodes-ComfyUI": {"stars": 1, "last_update": "2025-03-27 14:09:26", "author_account_age_days": 2118}, "https://github.com/antrobot1234/antrobots-comfyUI-nodepack": {"stars": 23, "last_update": "2025-04-02 21:40:49", "author_account_age_days": 3231}, "https://github.com/arcum42/ComfyUI_SageUtils": {"stars": 6, "last_update": "2025-06-25 21:51:09", "author_account_age_days": 6116}, "https://github.com/aria1th/ComfyUI-LogicUtils": {"stars": 53, "last_update": "2025-05-22 16:16:37", "author_account_age_days": 2713}, "https://github.com/asaddi/ComfyUI-YALLM-node": {"stars": 4, "last_update": "2025-03-27 14:39:38", "author_account_age_days": 3902}, "https://github.com/asaddi/YALLM-LlamaVision": {"stars": 5, "last_update": "2025-03-27 14:42:04", "author_account_age_days": 3902}, "https://github.com/asagi4/ComfyUI-Adaptive-Guidance": {"stars": 59, "last_update": "2025-05-03 18:12:38", "author_account_age_days": 809}, "https://github.com/asagi4/ComfyUI-CADS": {"stars": 42, "last_update": "2025-06-23 17:58:56", "author_account_age_days": 809}, "https://github.com/asagi4/ComfyUI-NPNet": {"stars": 17, "last_update": "2024-12-10 17:20:10", "author_account_age_days": 809}, "https://github.com/asagi4/comfyui-prompt-control": {"stars": 300, "last_update": "2025-06-21 10:17:42", "author_account_age_days": 809}, "https://github.com/asagi4/comfyui-utility-nodes": {"stars": 8, "last_update": "2025-01-30 23:01:52", "author_account_age_days": 809}, "https://github.com/asdrabael/Hunyuan-Multi-Lora-Loader": {"stars": 4, "last_update": "2025-02-09 02:50:51", "author_account_age_days": 382}, "https://github.com/asutermo/ComfyUI-Flux-TryOff": {"stars": 42, "last_update": "2025-03-10 21:05:14", "author_account_age_days": 5279}, "https://github.com/aszc-dev/ComfyUI-CoreMLSuite": {"stars": 160, "last_update": "2025-04-01 21:45:31", "author_account_age_days": 3095}, "https://github.com/atluslin/comfyui_arcane_style_trans": {"stars": 0, "last_update": "2025-03-14 01:25:41", "author_account_age_days": 3394}, "https://github.com/atmaranto/ComfyUI-SaveAsScript": {"stars": 144, "last_update": "2024-10-09 08:44:54", "author_account_age_days": 2717}, "https://github.com/attashe/ComfyUI-FluxRegionAttention": {"stars": 131, "last_update": "2025-03-02 16:37:39", "author_account_age_days": 3991}, "https://github.com/audioscavenger/ComfyUI-Thumbnails": {"stars": 30, "last_update": "2025-01-06 23:41:08", "author_account_age_days": 4489}, "https://github.com/audioscavenger/save-image-extended-comfyui": {"stars": 98, "last_update": "2025-06-02 02:01:15", "author_account_age_days": 4489}, "https://github.com/austinbrown34/ComfyUI-IO-Helpers": {"stars": 1, "last_update": "2025-02-13 14:29:22", "author_account_age_days": 4465}, "https://github.com/avatechai/avatar-graph-comfyui": {"stars": 263, "last_update": "2024-05-22 21:14:14", "author_account_age_days": 1222}, "https://github.com/avenstack/ComfyUI-AV-FunASR": {"stars": 9, "last_update": "2025-06-13 05:53:11", "author_account_age_days": 71}, "https://github.com/avenstack/ComfyUI-AV-LatentSync": {"stars": 2, "last_update": "2025-05-28 14:27:42", "author_account_age_days": 71}, "https://github.com/avenstack/ComfyUI-AV-MegaTTS3": {"stars": 0, "last_update": "2025-05-25 13:35:03", "author_account_age_days": 71}, "https://github.com/avocadori/ComfyUI-load-image-prompt-lora": {"stars": 0, "last_update": "2025-06-02 20:35:37", "author_account_age_days": 441}, "https://github.com/aws-samples/comfyui-llm-node-for-amazon-bedrock": {"stars": 26, "last_update": "2025-03-07 08:09:46", "author_account_age_days": 3927}, "https://github.com/azazeal04/Azazeal_Anime_Characters_ComfyUI": {"stars": 1, "last_update": "2025-06-21 18:26:40", "author_account_age_days": 786}, "https://github.com/azure-dragon-ai/ComfyUI-ClipScore-Nodes": {"stars": 4, "last_update": "2024-05-22 23:16:28", "author_account_age_days": 669}, "https://github.com/azure-dragon-ai/ComfyUI-HPSv2-Nodes": {"stars": 6, "last_update": "2025-05-11 05:18:07", "author_account_age_days": 669}, "https://github.com/babe-and-spencer-enterprises/base-comfyui-node": {"stars": 3, "last_update": "2025-06-23 16:12:19", "author_account_age_days": 45}, "https://github.com/bablueza/ComfyUI-Vaja-Ai4thai": {"stars": 0, "last_update": "2025-04-23 04:14:55", "author_account_age_days": 2112}, "https://github.com/babydjac/comfyui-grok-prompts": {"stars": 1, "last_update": "2025-06-06 09:47:19", "author_account_age_days": 773}, "https://github.com/babydjac/comfyui-smart-scaler": {"stars": 0, "last_update": "2025-05-16 07:28:19", "author_account_age_days": 773}, "https://github.com/badayvedat/ComfyUI-fal-Connector": {"stars": 42, "last_update": "2025-06-20 15:50:56", "author_account_age_days": 2139}, "https://github.com/badjeff/comfyui_lora_tag_loader": {"stars": 83, "last_update": "2024-05-22 20:40:03", "author_account_age_days": 5731}, "https://github.com/badxprogramm/ComfyUI-GradientBlur": {"stars": 1, "last_update": "2025-04-10 03:47:51", "author_account_age_days": 639}, "https://github.com/baicai99/ComfyUI-FrameSkipping": {"stars": 10, "last_update": "2025-06-23 02:50:12", "author_account_age_days": 1199}, "https://github.com/bananasss00/ComfyUI-SP-Nodes": {"stars": 14, "last_update": "2025-02-22 18:17:31", "author_account_age_days": 2902}, "https://github.com/bananasss00/ComfyUI-flux_fill_patcher": {"stars": 7, "last_update": "2024-11-25 20:04:20", "author_account_age_days": 2901}, "https://github.com/banodoco/steerable-motion": {"stars": 907, "last_update": "2025-06-27 15:56:48", "author_account_age_days": 770}, "https://github.com/banqingyuan/ComfyUI-text-replace": {"stars": 0, "last_update": "2024-09-22 16:14:22", "author_account_age_days": 2667}, "https://github.com/bartly/Comfyui_babel_removebg_api": {"stars": 6, "last_update": "2024-10-14 00:48:34", "author_account_age_days": 4495}, "https://github.com/bash-j/mikey_nodes": {"stars": 155, "last_update": "2025-03-22 01:52:20", "author_account_age_days": 4555}, "https://github.com/bbtaivi/ComfyUI-Aiv-Param": {"stars": 1, "last_update": "2025-02-16 03:01:20", "author_account_age_days": 821}, "https://github.com/bear2b/comfyui-argo-nodes": {"stars": 0, "last_update": "2025-01-16 11:11:38", "author_account_age_days": 3332}, "https://github.com/bedovyy/ComfyUI_NAIDGenerator": {"stars": 67, "last_update": "2025-05-30 14:03:50", "author_account_age_days": 731}, "https://github.com/bemoregt/ComfyUI_CustomNode_Image2Spectrum": {"stars": 1, "last_update": "2025-03-28 12:13:20", "author_account_age_days": 3327}, "https://github.com/benda1989/CosyVoice2_ComfyUI": {"stars": 19, "last_update": "2025-04-16 08:39:57", "author_account_age_days": 2480}, "https://github.com/benda1989/Sonic_ComfyUI": {"stars": 3, "last_update": "2025-02-24 10:04:56", "author_account_age_days": 2480}, "https://github.com/benjamin-bertram/Comfyui_OIDN_Denoiser": {"stars": 0, "last_update": "2025-06-12 22:37:05", "author_account_age_days": 2134}, "https://github.com/benjiyaya/ComfyUI-HunyuanVideoImagesGuider": {"stars": 29, "last_update": "2025-01-14 10:42:44", "author_account_age_days": 480}, "https://github.com/benjiyaya/ComfyUI-KokoroTTS": {"stars": 56, "last_update": "2025-03-18 20:13:52", "author_account_age_days": 480}, "https://github.com/bentoml/comfy-pack": {"stars": 161, "last_update": "2025-06-23 08:13:37", "author_account_age_days": 2278}, "https://github.com/big-mon/ComfyUI-ResolutionPresets": {"stars": 1, "last_update": "2025-04-12 17:05:21", "author_account_age_days": 3043}, "https://github.com/bikiam/ComfyUI_WhisperSRT": {"stars": 0, "last_update": "2025-06-01 13:56:23", "author_account_age_days": 517}, "https://github.com/bilal-arikan/ComfyUI_TextAssets": {"stars": 2, "last_update": "2024-05-22 23:23:50", "author_account_age_days": 3876}, "https://github.com/billwuhao/ComfyUI_ACE-Step": {"stars": 147, "last_update": "2025-05-28 08:39:13", "author_account_age_days": 2303}, "https://github.com/billwuhao/ComfyUI_AudioTools": {"stars": 50, "last_update": "2025-06-03 17:11:26", "author_account_age_days": 2303}, "https://github.com/billwuhao/ComfyUI_CSM": {"stars": 5, "last_update": "2025-06-02 14:00:17", "author_account_age_days": 2303}, "https://github.com/billwuhao/ComfyUI_DiffRhythm": {"stars": 110, "last_update": "2025-05-30 12:12:57", "author_account_age_days": 2303}, "https://github.com/billwuhao/ComfyUI_EraX-WoW-Turbo": {"stars": 14, "last_update": "2025-05-23 09:41:43", "author_account_age_days": 2303}, "https://github.com/billwuhao/ComfyUI_IndexTTS": {"stars": 75, "last_update": "2025-06-02 13:54:19", "author_account_age_days": 2303}, "https://github.com/billwuhao/ComfyUI_KokoroTTS_MW": {"stars": 24, "last_update": "2025-06-02 14:03:36", "author_account_age_days": 2303}, "https://github.com/billwuhao/ComfyUI_MegaTTS3": {"stars": 102, "last_update": "2025-06-11 01:01:40", "author_account_age_days": 2303}, "https://github.com/billwuhao/ComfyUI_NotaGen": {"stars": 48, "last_update": "2025-06-06 02:58:28", "author_account_age_days": 2303}, "https://github.com/billwuhao/ComfyUI_OneButtonPrompt": {"stars": 20, "last_update": "2025-05-06 01:07:39", "author_account_age_days": 2303}, "https://github.com/billwuhao/ComfyUI_OuteTTS": {"stars": 9, "last_update": "2025-06-11 06:14:07", "author_account_age_days": 2303}, "https://github.com/billwuhao/ComfyUI_PortraitTools": {"stars": 15, "last_update": "2025-06-15 13:31:45", "author_account_age_days": 2303}, "https://github.com/billwuhao/ComfyUI_SOME": {"stars": 5, "last_update": "2025-06-10 08:08:17", "author_account_age_days": 2303}, "https://github.com/billwuhao/ComfyUI_SparkTTS": {"stars": 44, "last_update": "2025-05-23 09:45:08", "author_account_age_days": 2303}, "https://github.com/billwuhao/ComfyUI_StepAudioTTS": {"stars": 127, "last_update": "2025-05-23 09:45:26", "author_account_age_days": 2303}, "https://github.com/billwuhao/ComfyUI_gemmax": {"stars": 21, "last_update": "2025-05-30 12:17:42", "author_account_age_days": 2303}, "https://github.com/billwuhao/ComfyUI_parakeet-tdt": {"stars": 3, "last_update": "2025-06-15 13:24:58", "author_account_age_days": 2303}, "https://github.com/billwuhao/Comfyui_HeyGem": {"stars": 74, "last_update": "2025-06-02 14:14:35", "author_account_age_days": 2303}, "https://github.com/bitaffinity/ComfyUI_HF_Inference": {"stars": 4, "last_update": "2024-06-14 10:23:29", "author_account_age_days": 425}, "https://github.com/black-forest-labs/bfl-comfy-nodes": {"stars": 78, "last_update": "2025-02-07 22:13:26", "author_account_age_days": 464}, "https://github.com/blackcodetavern/ComfyUI-Benripack": {"stars": 2, "last_update": "2024-09-07 09:06:00", "author_account_age_days": 3242}, "https://github.com/blepping/ComfyUI-ApplyResAdapterUnet": {"stars": 31, "last_update": "2025-02-27 16:14:46", "author_account_age_days": 522}, "https://github.com/blepping/ComfyUI-bleh": {"stars": 106, "last_update": "2025-06-27 20:46:15", "author_account_age_days": 522}, "https://github.com/blepping/ComfyUI-sonar": {"stars": 42, "last_update": "2025-06-27 20:55:23", "author_account_age_days": 522}, "https://github.com/blepping/comfyui_jankdiffusehigh": {"stars": 35, "last_update": "2025-05-06 10:28:37", "author_account_age_days": 522}, "https://github.com/blepping/comfyui_jankhidiffusion": {"stars": 124, "last_update": "2025-05-06 10:34:03", "author_account_age_days": 522}, "https://github.com/blepping/comfyui_overly_complicated_sampling": {"stars": 26, "last_update": "2025-06-27 20:54:42", "author_account_age_days": 522}, "https://github.com/blib-la/blibla-comfyui-extensions": {"stars": 173, "last_update": "2025-04-29 06:49:03", "author_account_age_days": 642}, "https://github.com/blob8/ComfyUI_sloppy-comic": {"stars": 8, "last_update": "2024-09-20 18:53:34", "author_account_age_days": 436}, "https://github.com/blovett80/ComfyUI-PixelDojo": {"stars": 0, "last_update": "2025-03-27 10:30:18", "author_account_age_days": 570}, "https://github.com/blueraincoatli/comfyUI_SillyNodes": {"stars": 3, "last_update": "2025-01-17 09:17:48", "author_account_age_days": 681}, "https://github.com/bluevisor/ComfyUI_PS_Blend_Node": {"stars": 2, "last_update": "2025-03-31 08:48:48", "author_account_age_days": 4935}, "https://github.com/bmad4ever/ComfyUI-Bmad-DirtyUndoRedo": {"stars": 47, "last_update": "2024-05-22 18:11:51", "author_account_age_days": 3905}, "https://github.com/bmad4ever/comfyui_ab_samplercustom": {"stars": 9, "last_update": "2024-09-17 20:18:46", "author_account_age_days": 3905}, "https://github.com/bmad4ever/comfyui_lists_cartesian_product": {"stars": 4, "last_update": "2025-03-17 14:49:40", "author_account_age_days": 3905}, "https://github.com/bmad4ever/comfyui_quilting": {"stars": 10, "last_update": "2025-03-17 14:50:15", "author_account_age_days": 3905}, "https://github.com/bmad4ever/comfyui_wfc_like": {"stars": 5, "last_update": "2025-03-17 14:51:47", "author_account_age_days": 3905}, "https://github.com/bobmagicii/comfykit-custom-nodes": {"stars": 1, "last_update": "2024-08-22 22:28:30", "author_account_age_days": 5113}, "https://github.com/bollerdominik/ComfyUI-load-lora-from-url": {"stars": 0, "last_update": "2025-06-05 16:21:55", "author_account_age_days": 3781}, "https://github.com/bombax-xiaoice/ComfyUI-Allegro": {"stars": 4, "last_update": "2025-05-13 04:00:11", "author_account_age_days": 276}, "https://github.com/bombax-xiaoice/ComfyUI-DisPose": {"stars": 0, "last_update": "2025-03-03 06:49:40", "author_account_age_days": 276}, "https://github.com/bombax-xiaoice/ComfyUI-MagicDance": {"stars": 2, "last_update": "2024-12-26 04:43:40", "author_account_age_days": 276}, "https://github.com/bombax-xiaoice/ComfyUI-Open-Sora-I2V": {"stars": 1, "last_update": "2025-01-21 07:58:50", "author_account_age_days": 276}, "https://github.com/bombax-xiaoice/ComfyUI-OpenSoraPlan": {"stars": 1, "last_update": "2025-01-22 05:38:11", "author_account_age_days": 276}, "https://github.com/bombless/comfyUI-RememberingUtils": {"stars": 0, "last_update": "2024-12-25 01:31:05", "author_account_age_days": 4926}, "https://github.com/bongsang/ComfyUI-Bongsang": {"stars": 0, "last_update": "2025-01-05 05:42:30", "author_account_age_days": 3971}, "https://github.com/boredofnames/ComfyUI-ntfy": {"stars": 0, "last_update": "2025-03-28 00:54:54", "author_account_age_days": 4466}, "https://github.com/boricuapab/ComfyUI-Bori-JsonSetGetConverter": {"stars": 0, "last_update": "2025-06-02 06:52:21", "author_account_age_days": 1937}, "https://github.com/bradsec/ComfyUI_ResolutionSelector": {"stars": 12, "last_update": "2024-07-07 12:15:49", "author_account_age_days": 4024}, "https://github.com/bradsec/ComfyUI_StringEssentials": {"stars": 14, "last_update": "2025-06-09 06:17:09", "author_account_age_days": 4023}, "https://github.com/braintacles/braintacles-comfyui-nodes": {"stars": 1, "last_update": "2024-07-31 15:01:52", "author_account_age_days": 799}, "https://github.com/brantje/ComfyUI-api-tools": {"stars": 3, "last_update": "2025-05-28 11:37:29", "author_account_age_days": 4612}, "https://github.com/brantje/ComfyUI_MagicQuill": {"stars": 3, "last_update": "2025-05-20 19:32:21", "author_account_age_days": 4612}, "https://github.com/brayevalerien/ComfyUI-resynthesizer": {"stars": 20, "last_update": "2025-02-19 10:33:17", "author_account_age_days": 1962}, "https://github.com/brianfitzgerald/style_aligned_comfy": {"stars": 300, "last_update": "2025-03-24 20:04:44", "author_account_age_days": 4608}, "https://github.com/bronkula/comfyui-fitsize": {"stars": 49, "last_update": "2024-05-22 21:32:34", "author_account_age_days": 5523}, "https://github.com/bruefire/ComfyUI-SeqImageLoader": {"stars": 42, "last_update": "2025-06-22 19:09:54", "author_account_age_days": 2734}, "https://github.com/budihartono/comfyui-aspect-ratio-presets": {"stars": 1, "last_update": "2025-06-12 10:55:46", "author_account_age_days": 5054}, "https://github.com/budihartono/comfyui_otonx_nodes": {"stars": 1, "last_update": "2024-07-31 16:01:47", "author_account_age_days": 5054}, "https://github.com/bugltd/ComfyLab-Pack": {"stars": 5, "last_update": "2025-05-13 17:35:50", "author_account_age_days": 167}, "https://github.com/burnsbert/ComfyUI-EBU-LMStudio": {"stars": 13, "last_update": "2025-06-08 01:10:11", "author_account_age_days": 5013}, "https://github.com/burnsbert/ComfyUI-EBU-PromptHelper": {"stars": 1, "last_update": "2025-06-08 01:11:52", "author_account_age_days": 5013}, "https://github.com/burnsbert/ComfyUI-EBU-Workflow": {"stars": 0, "last_update": "2025-06-08 02:44:44", "author_account_age_days": 5013}, "https://github.com/bvhari/ComfyUI_CFGStar": {"stars": 1, "last_update": "2025-04-10 17:53:08", "author_account_age_days": 1543}, "https://github.com/bvhari/ComfyUI_ImageProcessing": {"stars": 22, "last_update": "2025-03-30 18:55:42", "author_account_age_days": 1543}, "https://github.com/bvhari/ComfyUI_PerpCFG": {"stars": 1, "last_update": "2025-03-30 18:53:54", "author_account_age_days": 1543}, "https://github.com/bvhari/ComfyUI_PerpWeight": {"stars": 12, "last_update": "2025-03-30 18:55:52", "author_account_age_days": 1543}, "https://github.com/bvhari/ComfyUI_SUNoise": {"stars": 13, "last_update": "2025-03-30 18:55:16", "author_account_age_days": 1543}, "https://github.com/bytedance/ComfyUI-HyperLoRA": {"stars": 349, "last_update": "2025-06-25 08:47:51", "author_account_age_days": 4455}, "https://github.com/bytedance/ComfyUI_InfiniteYou": {"stars": 188, "last_update": "2025-05-23 23:21:52", "author_account_age_days": 4455}, "https://github.com/bytedance/comfyui-lumi-batcher": {"stars": 163, "last_update": "2025-06-26 06:06:39", "author_account_age_days": 4455}, "https://github.com/c0ffymachyne/ComfyUI_BeatByte": {"stars": 5, "last_update": "2025-04-03 03:08:15", "author_account_age_days": 4883}, "https://github.com/c0ffymachyne/ComfyUI_SignalProcessing": {"stars": 11, "last_update": "2025-05-14 01:41:00", "author_account_age_days": 4883}, "https://github.com/cake-ml/tiny-sana-preview": {"stars": 2, "last_update": "2025-02-08 00:36:49", "author_account_age_days": 249}, "https://github.com/calcuis/gguf": {"stars": 66, "last_update": "2025-06-20 00:59:37", "author_account_age_days": 1015}, "https://github.com/caleboleary/ComfyUI-Arc2Face": {"stars": 44, "last_update": "2024-09-02 23:00:00", "author_account_age_days": 3671}, "https://github.com/caleboleary/Comfyui-calbenodes": {"stars": 1, "last_update": "2024-09-16 19:27:58", "author_account_age_days": 3671}, "https://github.com/camenduru/ComfyUI-TostAI": {"stars": 1, "last_update": "2024-08-22 04:04:06", "author_account_age_days": 2137}, "https://github.com/cardenluo/ComfyUI-Apt_Preset": {"stars": 22, "last_update": "2025-06-21 03:25:27", "author_account_age_days": 771}, "https://github.com/casterpollux/MiniMax-bmo": {"stars": 31, "last_update": "2025-06-24 19:22:18", "author_account_age_days": 41}, "https://github.com/catboxanon/comfyui_stealth_pnginfo": {"stars": 3, "last_update": "2025-04-09 03:39:29", "author_account_age_days": 899}, "https://github.com/cathodeDreams/comfyui-azul-scripts": {"stars": 0, "last_update": "2025-04-30 17:03:38", "author_account_age_days": 836}, "https://github.com/cdb-boop/ComfyUI-Bringing-Old-Photos-Back-to-Life": {"stars": 461, "last_update": "2024-09-12 06:55:50", "author_account_age_days": 1573}, "https://github.com/cdb-boop/comfyui-image-round": {"stars": 10, "last_update": "2025-05-10 13:32:13", "author_account_age_days": 1573}, "https://github.com/cdxOo/comfyui-text-node-with-comments": {"stars": 2, "last_update": "2024-08-03 00:54:38", "author_account_age_days": 3664}, "https://github.com/celoron/ComfyUI-VisualQueryTemplate": {"stars": 12, "last_update": "2025-04-01 20:35:56", "author_account_age_days": 5380}, "https://github.com/celsojr2013/comfyui_jamworks_client": {"stars": 0, "last_update": "2024-06-23 12:35:44", "author_account_age_days": 3767}, "https://github.com/celsojr2013/comfyui_simpletools": {"stars": 2, "last_update": "2024-06-22 11:35:40", "author_account_age_days": 3767}, "https://github.com/cenzijing/ComfyUI-Markmap": {"stars": 1, "last_update": "2025-01-04 21:00:08", "author_account_age_days": 1832}, "https://github.com/cerspense/ComfyUI_cspnodes": {"stars": 33, "last_update": "2024-12-17 04:07:09", "author_account_age_days": 3050}, "https://github.com/ceruleandeep/ComfyUI-LLaVA-Captioner": {"stars": 133, "last_update": "2024-08-03 16:22:31", "author_account_age_days": 1521}, "https://github.com/cganimitta/ComfyUI_CGAnimittaTools": {"stars": 41, "last_update": "2025-04-11 05:29:55", "author_account_age_days": 943}, "https://github.com/chakib-belgaid/ComfyUI-autosize": {"stars": 0, "last_update": "2024-06-14 07:13:20", "author_account_age_days": 4204}, "https://github.com/chakib-belgaid/Comfyui_Prompt_styler": {"stars": 0, "last_update": "2024-07-01 12:40:52", "author_account_age_days": 4204}, "https://github.com/chandlergis/ComfyUI-IMG_Query": {"stars": 1, "last_update": "2024-05-23 01:25:57", "author_account_age_days": 724}, "https://github.com/chandlergis/ComfyUI_EmojiOverlay": {"stars": 0, "last_update": "2024-06-14 09:05:03", "author_account_age_days": 724}, "https://github.com/changwook987/ComfyUI-Small-Utility": {"stars": 0, "last_update": "2025-01-25 17:18:32", "author_account_age_days": 1561}, "https://github.com/chaojie/ComfyUI-AniPortrait": {"stars": 252, "last_update": "2024-05-22 22:26:03", "author_account_age_days": 5202}, "https://github.com/chaojie/ComfyUI-CameraCtrl-Wrapper": {"stars": 21, "last_update": "2024-06-14 09:07:23", "author_account_age_days": 5202}, "https://github.com/chaojie/ComfyUI-Champ": {"stars": 24, "last_update": "2024-05-22 22:26:47", "author_account_age_days": 5202}, "https://github.com/chaojie/ComfyUI-DragAnything": {"stars": 70, "last_update": "2024-06-14 10:23:53", "author_account_age_days": 5202}, "https://github.com/chaojie/ComfyUI-DragNUWA": {"stars": 407, "last_update": "2024-06-14 10:25:01", "author_account_age_days": 5202}, "https://github.com/chaojie/ComfyUI-DynamiCrafter": {"stars": 129, "last_update": "2024-06-14 10:23:59", "author_account_age_days": 5202}, "https://github.com/chaojie/ComfyUI-EasyAnimate": {"stars": 54, "last_update": "2024-05-22 22:24:00", "author_account_age_days": 5202}, "https://github.com/chaojie/ComfyUI-Gemma": {"stars": 6, "last_update": "2024-05-22 22:27:47", "author_account_age_days": 5202}, "https://github.com/chaojie/ComfyUI-I2VGEN-XL": {"stars": 28, "last_update": "2024-06-14 09:06:10", "author_account_age_days": 5202}, "https://github.com/chaojie/ComfyUI-Img2Img-Turbo": {"stars": 36, "last_update": "2024-05-22 22:26:30", "author_account_age_days": 5202}, "https://github.com/chaojie/ComfyUI-LaVIT": {"stars": 12, "last_update": "2024-06-14 10:27:44", "author_account_age_days": 5202}, "https://github.com/chaojie/ComfyUI-LightGlue": {"stars": 48, "last_update": "2024-01-20 16:53:51", "author_account_age_days": 5202}, "https://github.com/chaojie/ComfyUI-Moore-AnimateAnyone": {"stars": 213, "last_update": "2024-06-10 20:16:06", "author_account_age_days": 5202}, "https://github.com/chaojie/ComfyUI-Motion-Vector-Extractor": {"stars": 1, "last_update": "2024-06-14 10:26:15", "author_account_age_days": 5202}, "https://github.com/chaojie/ComfyUI-MotionCtrl": {"stars": 138, "last_update": "2024-06-14 10:26:02", "author_account_age_days": 5202}, "https://github.com/chaojie/ComfyUI-MotionCtrl-SVD": {"stars": 86, "last_update": "2024-06-14 10:26:30", "author_account_age_days": 5202}, "https://github.com/chaojie/ComfyUI-MuseTalk": {"stars": 263, "last_update": "2024-05-22 22:25:07", "author_account_age_days": 5202}, "https://github.com/chaojie/ComfyUI-MuseV": {"stars": 159, "last_update": "2024-05-22 22:25:31", "author_account_age_days": 5202}, "https://github.com/chaojie/ComfyUI-Open-Sora": {"stars": 103, "last_update": "2024-07-19 05:13:25", "author_account_age_days": 5202}, "https://github.com/chaojie/ComfyUI-Open-Sora-Plan": {"stars": 50, "last_update": "2024-05-29 16:15:10", "author_account_age_days": 5202}, "https://github.com/chaojie/ComfyUI-Panda3d": {"stars": 16, "last_update": "2024-06-14 10:28:47", "author_account_age_days": 5202}, "https://github.com/chaojie/ComfyUI-Pymunk": {"stars": 16, "last_update": "2024-06-14 12:02:32", "author_account_age_days": 5202}, "https://github.com/chaojie/ComfyUI-RAFT": {"stars": 26, "last_update": "2024-06-14 11:02:00", "author_account_age_days": 5202}, "https://github.com/chaojie/ComfyUI-SimDA": {"stars": 13, "last_update": "2024-06-14 12:02:39", "author_account_age_days": 5202}, "https://github.com/chaojie/ComfyUI-Trajectory": {"stars": 6, "last_update": "2024-05-22 22:27:12", "author_account_age_days": 5202}, "https://github.com/chaojie/ComfyUI-Video-Editing-X-Attention": {"stars": 17, "last_update": "2024-06-14 10:28:16", "author_account_age_days": 5202}, "https://github.com/chaojie/ComfyUI-dust3r": {"stars": 22, "last_update": "2024-05-22 22:27:33", "author_account_age_days": 5202}, "https://github.com/chaojie/ComfyUI_StreamingT2V": {"stars": 36, "last_update": "2024-06-14 10:26:21", "author_account_age_days": 5202}, "https://github.com/chaosaiart/Chaosaiart-Nodes": {"stars": 103, "last_update": "2025-05-06 07:15:41", "author_account_age_days": 702}, "https://github.com/charlyad142/ComfyUI_bfl_api_pro_nodes": {"stars": 0, "last_update": "2025-05-07 19:34:13", "author_account_age_days": 2983}, "https://github.com/chaunceyyann/comfyui-image-processing-nodes": {"stars": 0, "last_update": "2025-06-20 02:01:53", "author_account_age_days": 4235}, "https://github.com/checkbins/checkbin-comfy": {"stars": 0, "last_update": "2025-01-31 18:05:33", "author_account_age_days": 251}, "https://github.com/chenbaiyujason/ComfyUI_StepFun": {"stars": 6, "last_update": "2024-12-05 14:45:27", "author_account_age_days": 2103}, "https://github.com/chenlongming/ComfyUI_Spectral": {"stars": 1, "last_update": "2025-02-22 17:20:35", "author_account_age_days": 3545}, "https://github.com/chenpipi0807/ComfyUI-Index-TTS": {"stars": 178, "last_update": "2025-06-25 14:30:23", "author_account_age_days": 654}, "https://github.com/chenpipi0807/ComfyUI_NSFW_Godie": {"stars": 2, "last_update": "2025-03-20 11:48:28", "author_account_age_days": 654}, "https://github.com/chenpipi0807/PIP_ArtisticWords": {"stars": 26, "last_update": "2025-03-21 07:29:20", "author_account_age_days": 654}, "https://github.com/chenpx976/ComfyUI-RunRunRun": {"stars": 0, "last_update": "2024-05-23 01:19:37", "author_account_age_days": 3783}, "https://github.com/cherninlab/logo-generator-comfyui": {"stars": 1, "last_update": "2024-12-22 15:45:31", "author_account_age_days": 464}, "https://github.com/chesnokovivan/ComfyUI-Novakid": {"stars": 0, "last_update": "2024-06-10 20:15:56", "author_account_age_days": 1898}, "https://github.com/chflame163/ComfyUI_CatVTON_Wrapper": {"stars": 338, "last_update": "2025-01-01 12:55:16", "author_account_age_days": 812}, "https://github.com/chflame163/ComfyUI_CogView4_Wrapper": {"stars": 51, "last_update": "2025-03-06 09:27:25", "author_account_age_days": 812}, "https://github.com/chflame163/ComfyUI_FaceSimilarity": {"stars": 33, "last_update": "2025-03-31 13:12:01", "author_account_age_days": 812}, "https://github.com/chflame163/ComfyUI_Janus_Wrapper": {"stars": 17, "last_update": "2025-03-12 02:00:43", "author_account_age_days": 812}, "https://github.com/chflame163/ComfyUI_LayerStyle": {"stars": 2360, "last_update": "2025-06-20 00:45:04", "author_account_age_days": 812}, "https://github.com/chflame163/ComfyUI_LayerStyle_Advance": {"stars": 363, "last_update": "2025-05-17 12:57:26", "author_account_age_days": 812}, "https://github.com/chflame163/ComfyUI_MSSpeech_TTS": {"stars": 29, "last_update": "2025-03-31 13:11:24", "author_account_age_days": 812}, "https://github.com/chflame163/ComfyUI_OmniGen_Wrapper": {"stars": 141, "last_update": "2025-03-12 01:58:47", "author_account_age_days": 812}, "https://github.com/chflame163/ComfyUI_WordCloud": {"stars": 108, "last_update": "2025-03-31 13:11:39", "author_account_age_days": 812}, "https://github.com/chibiace/ComfyUI-Chibi-Nodes": {"stars": 75, "last_update": "2025-03-18 11:13:16", "author_account_age_days": 3217}, "https://github.com/choey/Comfy-Topaz": {"stars": 191, "last_update": "2024-09-28 08:02:47", "author_account_age_days": 5891}, "https://github.com/chou18194766xx/comfyui-EncryptSave": {"stars": 4, "last_update": "2025-05-18 07:55:45", "author_account_age_days": 593}, "https://github.com/chou18194766xx/comfyui_EncryptPreview": {"stars": 2, "last_update": "2025-04-26 12:29:43", "author_account_age_days": 593}, "https://github.com/chri002/ComfyUI_depthMapOperation": {"stars": 10, "last_update": "2025-05-27 06:19:56", "author_account_age_days": 2087}, "https://github.com/chris-arsenault/ComfyUI-AharaNodes": {"stars": 0, "last_update": "2024-12-25 16:45:58", "author_account_age_days": 4341}, "https://github.com/chris-the-wiz/EmbeddingsCurveEditor_ComfyUI": {"stars": 7, "last_update": "2024-07-31 13:51:59", "author_account_age_days": 2165}, "https://github.com/chrisfreilich/virtuoso-nodes": {"stars": 87, "last_update": "2025-04-19 22:57:17", "author_account_age_days": 1100}, "https://github.com/chrisgoringe/cg-controller": {"stars": 71, "last_update": "2025-04-25 00:43:21", "author_account_age_days": 4433}, "https://github.com/chrisgoringe/cg-image-filter": {"stars": 63, "last_update": "2025-06-23 06:11:13", "author_account_age_days": 4433}, "https://github.com/chrisgoringe/cg-noisetools": {"stars": 17, "last_update": "2024-12-17 04:09:18", "author_account_age_days": 4433}, "https://github.com/chrisgoringe/cg-prompt-info": {"stars": 30, "last_update": "2024-05-22 21:07:33", "author_account_age_days": 4433}, "https://github.com/chrisgoringe/cg-use-everywhere": {"stars": 729, "last_update": "2025-06-26 02:09:05", "author_account_age_days": 4433}, "https://github.com/chrish-slingshot/CrasHUtils": {"stars": 13, "last_update": "2024-10-29 22:55:39", "author_account_age_days": 969}, "https://github.com/chrissy0/chris-comfyui-nodes": {"stars": 1, "last_update": "2024-09-17 16:09:35", "author_account_age_days": 2423}, "https://github.com/christian-byrne/audio-separation-nodes-comfyui": {"stars": 239, "last_update": "2025-06-16 04:01:47", "author_account_age_days": 1717}, "https://github.com/christian-byrne/claude-code-comfyui-nodes": {"stars": 2, "last_update": "2025-06-17 04:43:43", "author_account_age_days": 1717}, "https://github.com/christian-byrne/comfyui-default-values-manager": {"stars": 11, "last_update": "2024-07-28 20:52:51", "author_account_age_days": 1717}, "https://github.com/christian-byrne/comfyui-search-navigation": {"stars": 7, "last_update": "2024-06-26 04:41:12", "author_account_age_days": 1717}, "https://github.com/christian-byrne/img2colors-comfyui-node": {"stars": 13, "last_update": "2025-01-05 18:48:59", "author_account_age_days": 1717}, "https://github.com/christian-byrne/img2txt-comfyui-nodes": {"stars": 90, "last_update": "2025-03-14 10:38:33", "author_account_age_days": 1717}, "https://github.com/christian-byrne/size-match-compositing-nodes": {"stars": 5, "last_update": "2025-01-05 17:45:02", "author_account_age_days": 1717}, "https://github.com/christian-byrne/youtube-dl-comfyui": {"stars": 4, "last_update": "2024-10-01 16:32:14", "author_account_age_days": 1717}, "https://github.com/ciga2011/ComfyUI-MarkItDown": {"stars": 8, "last_update": "2025-02-27 20:16:01", "author_account_age_days": 4566}, "https://github.com/ciga2011/ComfyUI-Pollinations": {"stars": 3, "last_update": "2025-01-14 15:23:14", "author_account_age_days": 4566}, "https://github.com/ciga2011/ComfyUI-PromptOptimizer": {"stars": 7, "last_update": "2025-01-16 02:24:50", "author_account_age_days": 4566}, "https://github.com/ciri/comfyui-model-downloader": {"stars": 73, "last_update": "2025-03-24 14:53:09", "author_account_age_days": 5739}, "https://github.com/city96/ComfyUI-GGUF": {"stars": 2110, "last_update": "2025-06-14 23:07:45", "author_account_age_days": 866}, "https://github.com/city96/ComfyUI_ColorMod": {"stars": 94, "last_update": "2024-08-06 22:38:54", "author_account_age_days": 866}, "https://github.com/city96/ComfyUI_DiT": {"stars": 5, "last_update": "2024-08-06 22:44:33", "author_account_age_days": 866}, "https://github.com/city96/ComfyUI_ExtraModels": {"stars": 510, "last_update": "2024-12-17 06:44:05", "author_account_age_days": 866}, "https://github.com/city96/ComfyUI_NetDist": {"stars": 455, "last_update": "2024-05-22 18:05:10", "author_account_age_days": 866}, "https://github.com/city96/SD-Latent-Interposer": {"stars": 295, "last_update": "2024-08-06 22:01:47", "author_account_age_days": 866}, "https://github.com/city96/SD-Latent-Upscaler": {"stars": 159, "last_update": "2024-05-22 18:05:50", "author_account_age_days": 866}, "https://github.com/civen-cn/ComfyUI-PaddleOcr": {"stars": 7, "last_update": "2024-12-31 19:11:04", "author_account_age_days": 2873}, "https://github.com/civen-cn/ComfyUI-Whisper-Translator": {"stars": 6, "last_update": "2025-01-04 03:37:06", "author_account_age_days": 2872}, "https://github.com/civitai/civitai_comfy_nodes": {"stars": 146, "last_update": "2024-08-25 03:32:49", "author_account_age_days": 967}, "https://github.com/claussteinmassl/ComfyUI-CS-CustomNodes": {"stars": 1, "last_update": "2024-06-14 09:03:10", "author_account_age_days": 3069}, "https://github.com/cleanlii/comfyui-dalle-integration": {"stars": 1, "last_update": "2025-04-02 08:29:56", "author_account_age_days": 2470}, "https://github.com/clhui/ComfyUi-clh-Tool": {"stars": 6, "last_update": "2024-12-28 10:22:00", "author_account_age_days": 3197}, "https://github.com/clouddreamfly/ComfyUI-PromptWrapper": {"stars": 2, "last_update": "2025-06-02 16:16:12", "author_account_age_days": 1960}, "https://github.com/cloudkoala/comfyui-koala": {"stars": 0, "last_update": "2025-06-06 00:17:19", "author_account_age_days": 281}, "https://github.com/cluny85/ComfyUI-Scripting-Tools": {"stars": 0, "last_update": "2025-06-16 12:28:27", "author_account_age_days": 4913}, "https://github.com/cmdicely/simple_image_to_palette": {"stars": 0, "last_update": "2025-06-22 03:20:08", "author_account_age_days": 5852}, "https://github.com/cnbjjj/ComfyUI-Jtils": {"stars": 3, "last_update": "2025-05-10 23:25:19", "author_account_age_days": 542}, "https://github.com/codecringebinge/ComfyUI-Arrow-Key-Canvas-Navigation": {"stars": 2, "last_update": "2024-09-29 22:35:01", "author_account_age_days": 3166}, "https://github.com/codeprimate/ComfyUI-MaskContourProcessor": {"stars": 2, "last_update": "2024-12-16 06:53:08", "author_account_age_days": 6216}, "https://github.com/comfy-deploy/comfyui-llm-toolkit": {"stars": 19, "last_update": "2025-06-18 01:48:53", "author_account_age_days": 534}, "https://github.com/comfyanonymous/ComfyUI": {"stars": 80888, "last_update": "2025-06-26 22:41:51", "author_account_age_days": 917}, "https://github.com/comfyanonymous/ComfyUI_TensorRT": {"stars": 622, "last_update": "2024-10-10 00:23:55", "author_account_age_days": 917}, "https://github.com/comfyanonymous/ComfyUI_experiments": {"stars": 184, "last_update": "2024-05-22 15:29:49", "author_account_age_days": 917}, "https://github.com/concarne000/ConCarneNode": {"stars": 4, "last_update": "2024-05-22 22:10:18", "author_account_age_days": 2269}, "https://github.com/conquestace/ComfyUI-ImageUploader": {"stars": 2, "last_update": "2024-05-23 01:25:49", "author_account_age_days": 4977}, "https://github.com/coolzilj/ComfyUI-LJNodes": {"stars": 87, "last_update": "2024-06-15 01:57:32", "author_account_age_days": 5031}, "https://github.com/coolzilj/ComfyUI-Photopea": {"stars": 148, "last_update": "2024-06-14 08:10:57", "author_account_age_days": 5031}, "https://github.com/coreyryanhanson/ComfyQR": {"stars": 77, "last_update": "2025-01-26 16:25:19", "author_account_age_days": 3424}, "https://github.com/coreyryanhanson/ComfyQR-scanning-nodes": {"stars": 11, "last_update": "2025-01-26 16:26:36", "author_account_age_days": 3424}, "https://github.com/coulterj/comfyui-svg-visual-normalize": {"stars": 0, "last_update": "2025-05-26 11:45:44", "author_account_age_days": 3309}, "https://github.com/cozy-comfyui/cozy_comm": {"stars": 2, "last_update": "2025-04-03 17:02:54", "author_account_age_days": 434}, "https://github.com/cozymantis/cozy-utils-comfyui-nodes": {"stars": 5, "last_update": "2025-04-07 09:53:31", "author_account_age_days": 477}, "https://github.com/cozymantis/human-parser-comfyui-node": {"stars": 106, "last_update": "2025-04-19 14:09:03", "author_account_age_days": 477}, "https://github.com/cozymantis/pose-generator-comfyui-node": {"stars": 83, "last_update": "2025-04-07 09:53:17", "author_account_age_days": 477}, "https://github.com/cr7Por/ComfyUI_DepthFlow": {"stars": 5, "last_update": "2024-09-16 09:10:08", "author_account_age_days": 1493}, "https://github.com/craig-tanaka/comfyui_animeseg": {"stars": 0, "last_update": "2025-05-20 18:59:45", "author_account_age_days": 2977}, "https://github.com/crave33/RenesStuffDanbooruTagGet": {"stars": 0, "last_update": "2025-02-23 15:48:48", "author_account_age_days": 134}, "https://github.com/crystian/ComfyUI-Crystools": {"stars": 1246, "last_update": "2025-06-26 18:52:04", "author_account_age_days": 4485}, "https://github.com/crystian/ComfyUI-Crystools-save": {"stars": 45, "last_update": "2025-06-01 20:17:39", "author_account_age_days": 4485}, "https://github.com/cubiq/Block_Patcher_ComfyUI": {"stars": 83, "last_update": "2024-09-22 09:49:06", "author_account_age_days": 5379}, "https://github.com/cubiq/ComfyUI_FaceAnalysis": {"stars": 450, "last_update": "2025-05-20 05:18:36", "author_account_age_days": 5379}, "https://github.com/cubiq/ComfyUI_IPAdapter_plus": {"stars": 5257, "last_update": "2025-04-14 07:29:17", "author_account_age_days": 5379}, "https://github.com/cubiq/ComfyUI_InstantID": {"stars": 1649, "last_update": "2025-04-14 07:50:01", "author_account_age_days": 5379}, "https://github.com/cubiq/ComfyUI_essentials": {"stars": 875, "last_update": "2025-04-14 07:33:29", "author_account_age_days": 5379}, "https://github.com/cubiq/PuLID_ComfyUI": {"stars": 869, "last_update": "2025-04-14 07:47:23", "author_account_age_days": 5379}, "https://github.com/cuongloveit/comfy_http_request": {"stars": 5, "last_update": "2024-06-14 11:00:11", "author_account_age_days": 3620}, "https://github.com/curiousjp/ComfyUI-MaskBatchPermutations": {"stars": 5, "last_update": "2024-05-28 13:09:32", "author_account_age_days": 2298}, "https://github.com/cyberhirsch/seb_nodes": {"stars": 0, "last_update": "2025-06-12 18:30:24", "author_account_age_days": 2234}, "https://github.com/czcz1024/Comfyui-FaceCompare": {"stars": 0, "last_update": "2024-06-14 07:13:32", "author_account_age_days": 4602}, "https://github.com/da2el-ai/ComfyUI-d2-send-eagle": {"stars": 16, "last_update": "2025-03-10 14:31:22", "author_account_age_days": 758}, "https://github.com/da2el-ai/ComfyUI-d2-size-selector": {"stars": 4, "last_update": "2024-10-02 14:04:20", "author_account_age_days": 758}, "https://github.com/da2el-ai/ComfyUI-d2-steps": {"stars": 5, "last_update": "2024-10-02 14:03:14", "author_account_age_days": 758}, "https://github.com/da2el-ai/ComfyUI-d2-xyplot-utils": {"stars": 5, "last_update": "2024-10-02 14:00:58", "author_account_age_days": 758}, "https://github.com/da2el-ai/D2-PromptSelector-comfyUI": {"stars": 3, "last_update": "2025-04-05 03:00:34", "author_account_age_days": 758}, "https://github.com/da2el-ai/D2-SavePSD-ComfyUI": {"stars": 2, "last_update": "2025-04-08 15:28:06", "author_account_age_days": 758}, "https://github.com/da2el-ai/D2-nodes-ComfyUI": {"stars": 35, "last_update": "2025-06-21 17:06:13", "author_account_age_days": 758}, "https://github.com/dadoirie/ComfyUI_Dados_Nodes": {"stars": 1, "last_update": "2025-06-09 10:35:26", "author_account_age_days": 1958}, "https://github.com/dafeng012/comfyui-imgmake": {"stars": 14, "last_update": "2024-11-03 17:38:47", "author_account_age_days": 1044}, "https://github.com/dagthomas/comfyui_dagthomas": {"stars": 253, "last_update": "2025-04-23 14:00:14", "author_account_age_days": 4440}, "https://github.com/danger-electrodes/ComfyUI_Fawfluencer_Nodes": {"stars": 0, "last_update": "2025-04-15 10:31:38", "author_account_age_days": 758}, "https://github.com/daniabib/ComfyUI_ProPainter_Nodes": {"stars": 316, "last_update": "2024-12-22 13:50:25", "author_account_age_days": 2773}, "https://github.com/daniel-lewis-ab/ComfyUI-Llama": {"stars": 59, "last_update": "2024-06-29 19:55:42", "author_account_age_days": 3720}, "https://github.com/daniel-lewis-ab/ComfyUI-TTS": {"stars": 28, "last_update": "2024-06-14 08:09:49", "author_account_age_days": 3720}, "https://github.com/darkpixel/darkprompts": {"stars": 8, "last_update": "2025-06-09 16:39:38", "author_account_age_days": 5692}, "https://github.com/darth-veitcher/comfydv": {"stars": 1, "last_update": "2025-05-13 07:24:56", "author_account_age_days": 4797}, "https://github.com/daryltucker/ComfyUI-LoadFiles": {"stars": 2, "last_update": "2024-08-31 23:59:44", "author_account_age_days": 4833}, "https://github.com/dasilva333/ComfyUI_ContrastingColor": {"stars": 1, "last_update": "2025-02-22 04:49:59", "author_account_age_days": 5081}, "https://github.com/dasilva333/ComfyUI_MarkdownImage": {"stars": 0, "last_update": "2025-04-12 03:11:13", "author_account_age_days": 5081}, "https://github.com/dave-palt/comfyui_DSP_imagehelpers": {"stars": 0, "last_update": "2024-05-22 23:12:11", "author_account_age_days": 526}, "https://github.com/davidgressett/comfyui-systemlevel": {"stars": 0, "last_update": "2025-01-22 23:51:40", "author_account_age_days": 2998}, "https://github.com/daxcay/ComfyUI-DataSet": {"stars": 50, "last_update": "2025-03-01 05:24:50", "author_account_age_days": 463}, "https://github.com/daxcay/ComfyUI-JDCN": {"stars": 120, "last_update": "2025-04-14 09:20:22", "author_account_age_days": 463}, "https://github.com/daxcay/ComfyUI-NODEJS": {"stars": 14, "last_update": "2024-11-28 09:46:29", "author_account_age_days": 463}, "https://github.com/daxcay/ComfyUI-Nexus": {"stars": 86, "last_update": "2025-03-01 15:40:05", "author_account_age_days": 463}, "https://github.com/daxcay/ComfyUI-TG": {"stars": 19, "last_update": "2024-11-28 09:45:12", "author_account_age_days": 463}, "https://github.com/daxcay/ComfyUI-WA": {"stars": 48, "last_update": "2024-11-28 09:44:50", "author_account_age_days": 463}, "https://github.com/daxcay/ComfyUI-YouTubeVideoPlayer": {"stars": 5, "last_update": "2024-11-28 09:45:30", "author_account_age_days": 463}, "https://github.com/dchatel/comfyui_davcha": {"stars": 1, "last_update": "2025-04-03 06:39:42", "author_account_age_days": 4904}, "https://github.com/dchatel/comfyui_facetools": {"stars": 140, "last_update": "2025-05-18 12:30:50", "author_account_age_days": 4904}, "https://github.com/denfrost/Den_ComfyUI_Workflow": {"stars": 4, "last_update": "2025-05-07 07:15:01", "author_account_age_days": 3865}, "https://github.com/deroberon/StableZero123-comfyui": {"stars": 172, "last_update": "2024-05-22 22:09:53", "author_account_age_days": 5664}, "https://github.com/deroberon/demofusion-comfyui": {"stars": 88, "last_update": "2024-05-22 22:09:42", "author_account_age_days": 5664}, "https://github.com/dfghsdh/ComfyUI_FluxPromptGen": {"stars": 14, "last_update": "2024-09-23 07:51:56", "author_account_age_days": 278}, "https://github.com/dfl/comfyui-clip-with-break": {"stars": 13, "last_update": "2025-03-04 20:16:06", "author_account_age_days": 6343}, "https://github.com/dfl/comfyui-tcd-scheduler": {"stars": 83, "last_update": "2024-05-22 23:23:28", "author_account_age_days": 6343}, "https://github.com/diStyApps/ComfyUI-disty-Flow": {"stars": 548, "last_update": "2025-01-04 18:03:37", "author_account_age_days": 4573}, "https://github.com/diStyApps/ComfyUI_FrameMaker": {"stars": 22, "last_update": "2024-05-23 00:11:33", "author_account_age_days": 4573}, "https://github.com/dicksensei69/comfyui_loops": {"stars": 0, "last_update": "2025-05-03 22:22:55", "author_account_age_days": 1002}, "https://github.com/dicksondickson/ComfyUI-Dickson-Nodes": {"stars": 10, "last_update": "2024-09-18 04:30:33", "author_account_age_days": 4359}, "https://github.com/digitaljohn/comfyui-propost": {"stars": 179, "last_update": "2025-02-10 23:25:24", "author_account_age_days": 4886}, "https://github.com/dimtion/comfyui-raw-image": {"stars": 1, "last_update": "2025-03-31 00:25:41", "author_account_age_days": 4732}, "https://github.com/dimtoneff/ComfyUI-PixelArt-Detector": {"stars": 306, "last_update": "2025-04-01 15:43:07", "author_account_age_days": 3783}, "https://github.com/dionren/ComfyUI-Pro-Export-Tool": {"stars": 2, "last_update": "2024-10-11 08:26:18", "author_account_age_days": 4254}, "https://github.com/diontimmer/ComfyUI-Vextra-Nodes": {"stars": 77, "last_update": "2024-06-20 16:48:44", "author_account_age_days": 5139}, "https://github.com/discopixel-studio/comfyui-discopixel": {"stars": 12, "last_update": "2024-09-30 00:46:13", "author_account_age_days": 714}, "https://github.com/discus0434/comfyui-aesthetic-predictor-v2-5": {"stars": 13, "last_update": "2024-06-14 08:12:05", "author_account_age_days": 1838}, "https://github.com/discus0434/comfyui-caching-embeddings": {"stars": 2, "last_update": "2024-06-14 08:59:36", "author_account_age_days": 1838}, "https://github.com/discus0434/comfyui-flux-accelerator": {"stars": 136, "last_update": "2024-12-19 14:39:39", "author_account_age_days": 1838}, "https://github.com/djbielejeski/a-person-mask-generator": {"stars": 355, "last_update": "2025-03-14 11:19:45", "author_account_age_days": 4652}, "https://github.com/dmMaze/sketch2manga": {"stars": 41, "last_update": "2025-03-31 08:51:09", "author_account_age_days": 2218}, "https://github.com/dmarx/ComfyUI-AudioReactive": {"stars": 10, "last_update": "2024-05-22 22:12:53", "author_account_age_days": 4873}, "https://github.com/dmarx/ComfyUI-Keyframed": {"stars": 87, "last_update": "2024-07-01 01:41:23", "author_account_age_days": 4873}, "https://github.com/domenecmiralles/obobo_nodes": {"stars": 0, "last_update": "2025-06-12 22:44:13", "author_account_age_days": 974}, "https://github.com/dorpxam/ComfyUI-FramePack-F1-T2V": {"stars": 2, "last_update": "2025-05-29 06:33:54", "author_account_age_days": 627}, "https://github.com/dorpxam/ComfyUI-LTXVideoLoRA": {"stars": 16, "last_update": "2025-05-10 16:42:44", "author_account_age_days": 627}, "https://github.com/doubletwisted/ComfyUI-Deadline-Plugin": {"stars": 4, "last_update": "2025-05-25 22:47:23", "author_account_age_days": 958}, "https://github.com/drago87/ComfyUI_Dragos_Nodes": {"stars": 3, "last_update": "2024-05-22 21:32:15", "author_account_age_days": 4138}, "https://github.com/dreamhartley/ComfyUI_show_seed": {"stars": 1, "last_update": "2025-01-14 16:15:12", "author_account_age_days": 851}, "https://github.com/drmbt/comfyui-dreambait-nodes": {"stars": 4, "last_update": "2025-06-23 04:17:05", "author_account_age_days": 4146}, "https://github.com/drphero/comfyui_prompttester": {"stars": 1, "last_update": "2025-06-19 01:06:51", "author_account_age_days": 3580}, "https://github.com/drustan-hawk/primitive-types": {"stars": 6, "last_update": "2024-08-01 17:44:51", "author_account_age_days": 657}, "https://github.com/dseditor/ComfyUI-ListHelper": {"stars": 2, "last_update": "2025-06-22 10:21:59", "author_account_age_days": 1371}, "https://github.com/dseditor/ComfyUI-ScheduledTask": {"stars": 6, "last_update": "2025-06-19 16:07:41", "author_account_age_days": 1371}, "https://github.com/dseditor/ComfyUI-Thread": {"stars": 3, "last_update": "2025-06-17 02:38:00", "author_account_age_days": 1371}, "https://github.com/duchamps0305/comfyui-white-extractor": {"stars": 0, "last_update": "2025-01-23 08:09:12", "author_account_age_days": 989}, "https://github.com/ducido/ObjectFusion_ComfyUI_nodes": {"stars": 1, "last_update": "2025-05-02 08:31:46", "author_account_age_days": 897}, "https://github.com/dymokomi/comfyui_dygen": {"stars": 1, "last_update": "2024-11-28 20:08:13", "author_account_age_days": 940}, "https://github.com/dzqdzq/ComfyUI-crop-alpha": {"stars": 2, "last_update": "2025-02-17 14:46:11", "author_account_age_days": 3382}, "https://github.com/e-tier-newbie/ComfyUI-E-Tier-TextSaver": {"stars": 0, "last_update": "2025-06-06 21:59:50", "author_account_age_days": 49}, "https://github.com/e7mac/ComfyUI-ShadertoyGL": {"stars": 4, "last_update": "2024-06-20 14:52:42", "author_account_age_days": 5172}, "https://github.com/ealkanat/comfyui-easy-padding": {"stars": 17, "last_update": "2024-12-31 02:38:22", "author_account_age_days": 2831}, "https://github.com/eastoc/ComfyUI_SemanticSAM": {"stars": 4, "last_update": "2024-08-13 19:24:33", "author_account_age_days": 3086}, "https://github.com/edelvarden/ComfyUI-Display-Value": {"stars": 0, "last_update": "2025-05-25 23:02:40", "author_account_age_days": 2501}, "https://github.com/edelvarden/comfyui_image_metadata_extension": {"stars": 47, "last_update": "2025-06-19 18:38:45", "author_account_age_days": 2501}, "https://github.com/edenartlab/eden_comfy_pipelines": {"stars": 91, "last_update": "2025-06-16 14:34:11", "author_account_age_days": 641}, "https://github.com/edenartlab/sd-lora-trainer": {"stars": 54, "last_update": "2025-02-24 16:18:16", "author_account_age_days": 641}, "https://github.com/educator-art/ComfyUI-Load-DirectoryFiles": {"stars": 3, "last_update": "2025-04-22 08:51:32", "author_account_age_days": 570}, "https://github.com/emojiiii/ComfyUI_Emojiiii_Custom_Nodes": {"stars": 0, "last_update": "2024-09-03 06:55:04", "author_account_age_days": 2891}, "https://github.com/envy-ai/ComfyUI-ConDelta": {"stars": 200, "last_update": "2025-04-24 00:16:02", "author_account_age_days": 323}, "https://github.com/erosDiffusion/ComfyUI-enricos-nodes": {"stars": 460, "last_update": "2025-05-05 22:53:42", "author_account_age_days": 365}, "https://github.com/evanspearman/ComfyMath": {"stars": 128, "last_update": "2025-03-08 18:14:34", "author_account_age_days": 4607}, "https://github.com/excelwong/ComfyUI-PromptComposer": {"stars": 0, "last_update": "2025-04-30 10:33:43", "author_account_age_days": 3733}, "https://github.com/exdysa/comfyui-selector": {"stars": 4, "last_update": "2025-03-14 12:21:29", "author_account_age_days": 1364}, "https://github.com/exectails/comfyui-et_dynamicprompts": {"stars": 4, "last_update": "2024-11-29 22:37:19", "author_account_age_days": 4285}, "https://github.com/exectails/comfyui-et_infoutils": {"stars": 2, "last_update": "2024-11-29 17:27:49", "author_account_age_days": 4285}, "https://github.com/exectails/comfyui-et_stringutils": {"stars": 1, "last_update": "2024-11-26 20:26:14", "author_account_age_days": 4285}, "https://github.com/ez-af/ComfyUI-EZ-AF-Nodes": {"stars": 13, "last_update": "2025-06-25 06:54:14", "author_account_age_days": 302}, "https://github.com/fablestudio/ComfyUI-Showrunner-Utils": {"stars": 0, "last_update": "2025-06-04 04:34:09", "author_account_age_days": 2417}, "https://github.com/facok/ComfyUI-HunyuanVideoMultiLora": {"stars": 115, "last_update": "2025-05-13 18:35:00", "author_account_age_days": 826}, "https://github.com/facok/ComfyUI-TeaCacheHunyuanVideo": {"stars": 92, "last_update": "2025-04-05 05:24:59", "author_account_age_days": 826}, "https://github.com/fairy-root/ComfyUI-GLHF": {"stars": 4, "last_update": "2025-04-03 13:47:20", "author_account_age_days": 2302}, "https://github.com/fairy-root/ComfyUI-OpenAI-FM": {"stars": 30, "last_update": "2025-05-09 00:12:06", "author_account_age_days": 2302}, "https://github.com/fairy-root/ComfyUI-Show-Text": {"stars": 10, "last_update": "2025-04-08 14:21:57", "author_account_age_days": 2302}, "https://github.com/fairy-root/Flux-Prompt-Generator": {"stars": 208, "last_update": "2025-04-22 02:20:47", "author_account_age_days": 2302}, "https://github.com/fairy-root/comfyui-ollama-llms": {"stars": 17, "last_update": "2025-03-27 20:47:17", "author_account_age_days": 2302}, "https://github.com/fallingmeteorite/nsfw-image-check-comfyui": {"stars": 7, "last_update": "2025-06-02 03:59:25", "author_account_age_days": 1457}, "https://github.com/fashn-AI/ComfyUI-FASHN": {"stars": 25, "last_update": "2025-04-23 10:15:13", "author_account_age_days": 724}, "https://github.com/fat-tire/comfyui-unified-media-suite": {"stars": 4, "last_update": "2025-02-25 04:41:02", "author_account_age_days": 5304}, "https://github.com/fblissjr/ComfyUI-DatasetHelper": {"stars": 6, "last_update": "2025-01-27 18:58:33", "author_account_age_days": 3732}, "https://github.com/fblissjr/ComfyUI-EmbeddingPipelineAnalytics": {"stars": 2, "last_update": "2025-01-24 18:51:53", "author_account_age_days": 3732}, "https://github.com/fblissjr/ComfyUI-WanSeamlessFlow": {"stars": 4, "last_update": "2025-03-17 22:36:22", "author_account_age_days": 3732}, "https://github.com/fearnworks/ComfyUI_FearnworksNodes": {"stars": 19, "last_update": "2024-08-05 01:50:04", "author_account_age_days": 930}, "https://github.com/feixuetuba/Spleeter": {"stars": 0, "last_update": "2025-01-19 10:39:17", "author_account_age_days": 4330}, "https://github.com/felixszeto/ComfyUI-RequestNodes": {"stars": 86, "last_update": "2025-04-19 18:59:35", "author_account_age_days": 1417}, "https://github.com/fexli/fexli-util-node-comfyui": {"stars": 3, "last_update": "2025-06-18 06:07:31", "author_account_age_days": 1927}, "https://github.com/fexploit/ComfyUI-AutoLabel": {"stars": 7, "last_update": "2025-03-18 13:07:46", "author_account_age_days": 5412}, "https://github.com/fexploit/ComfyUI-AutoTrimBG": {"stars": 3, "last_update": "2025-03-10 12:59:42", "author_account_age_days": 5412}, "https://github.com/fexploit/ComfyUI-Classifier": {"stars": 1, "last_update": "2025-03-10 20:33:42", "author_account_age_days": 5412}, "https://github.com/filipemeneses/comfy_pixelization": {"stars": 63, "last_update": "2025-06-12 07:07:01", "author_account_age_days": 3845}, "https://github.com/filliptm/ComfyUI_FL-Trainer": {"stars": 166, "last_update": "2024-10-18 00:20:18", "author_account_age_days": 2104}, "https://github.com/filliptm/ComfyUI_Fill-ChatterBox": {"stars": 135, "last_update": "2025-06-25 01:45:29", "author_account_age_days": 2104}, "https://github.com/filliptm/ComfyUI_Fill-Nodes": {"stars": 414, "last_update": "2025-06-25 16:39:20", "author_account_age_days": 2104}, "https://github.com/finegrain-ai/comfyui-finegrain": {"stars": 12, "last_update": "2025-05-16 07:44:57", "author_account_age_days": 870}, "https://github.com/flamacore/ComfyUI-YouTubeUploader": {"stars": 1, "last_update": "2025-06-14 22:11:33", "author_account_age_days": 3676}, "https://github.com/florestefano1975/ComfyUI-Advanced-Sequence-Seed": {"stars": 1, "last_update": "2025-04-09 12:40:05", "author_account_age_days": 562}, "https://github.com/florestefano1975/ComfyUI-CogVideoX": {"stars": 15, "last_update": "2025-04-09 12:39:53", "author_account_age_days": 562}, "https://github.com/florestefano1975/ComfyUI-HiDiffusion": {"stars": 143, "last_update": "2025-04-09 12:40:58", "author_account_age_days": 562}, "https://github.com/florestefano1975/ComfyUI-StabilityAI-Suite": {"stars": 4, "last_update": "2025-04-09 12:40:36", "author_account_age_days": 562}, "https://github.com/florestefano1975/comfyui-portrait-master": {"stars": 1080, "last_update": "2025-06-24 09:25:13", "author_account_age_days": 562}, "https://github.com/florestefano1975/comfyui-prompt-composer": {"stars": 275, "last_update": "2025-04-27 15:00:00", "author_account_age_days": 562}, "https://github.com/flowtyone/ComfyUI-Flowty-CRM": {"stars": 155, "last_update": "2024-06-14 10:23:09", "author_account_age_days": 642}, "https://github.com/flowtyone/ComfyUI-Flowty-LDSR": {"stars": 244, "last_update": "2024-06-14 09:04:51", "author_account_age_days": 642}, "https://github.com/flowtyone/ComfyUI-Flowty-TripoSR": {"stars": 508, "last_update": "2024-06-16 00:53:22", "author_account_age_days": 642}, "https://github.com/fluffydiveX/ComfyUI-hvBlockswap": {"stars": 8, "last_update": "2025-03-30 03:30:40", "author_account_age_days": 200}, "https://github.com/flycarl/ComfyUI-Pixelate": {"stars": 1, "last_update": "2024-11-26 13:31:56", "author_account_age_days": 5227}, "https://github.com/flyingshutter/As_ComfyUI_CustomNodes": {"stars": 7, "last_update": "2025-05-23 17:29:13", "author_account_age_days": 3863}, "https://github.com/fmatray/ComfyUI_BattlemapGrid": {"stars": 0, "last_update": "2024-06-05 22:35:06", "author_account_age_days": 3990}, "https://github.com/fofr/ComfyUI-HyperSDXL1StepUnetScheduler": {"stars": 11, "last_update": "2024-06-20 11:51:50", "author_account_age_days": 5476}, "https://github.com/fofr/ComfyUI-Prompter-fofrAI": {"stars": 74, "last_update": "2025-02-10 16:39:49", "author_account_age_days": 5476}, "https://github.com/fofr/comfyui-basic-auth": {"stars": 1, "last_update": "2025-03-17 09:38:05", "author_account_age_days": 5476}, "https://github.com/fofr/comfyui-fofr-toolkit": {"stars": 4, "last_update": "2024-08-09 11:36:38", "author_account_age_days": 5476}, "https://github.com/forever22777/comfyui-self-guidance": {"stars": 10, "last_update": "2025-04-17 08:13:40", "author_account_age_days": 693}, "https://github.com/fotobudka-team/comfyui-ai-faces": {"stars": 2, "last_update": "2025-06-25 19:54:59", "author_account_age_days": 224}, "https://github.com/foxtrot-roger/comfyui-rf-nodes": {"stars": 2, "last_update": "2024-08-13 22:01:40", "author_account_age_days": 2689}, "https://github.com/fpgaminer/joycaption_comfyui": {"stars": 76, "last_update": "2025-05-15 23:30:13", "author_account_age_days": 4837}, "https://github.com/fplu/comfyui_lama_with_refiner": {"stars": 1, "last_update": "2025-06-22 16:38:15", "author_account_age_days": 2451}, "https://github.com/frankchieng/ComfyUI_Aniportrait": {"stars": 55, "last_update": "2024-09-13 10:41:16", "author_account_age_days": 809}, "https://github.com/frankchieng/ComfyUI_MagicClothing": {"stars": 573, "last_update": "2024-09-04 04:57:15", "author_account_age_days": 809}, "https://github.com/frankchieng/ComfyUI_llm_easyanimiate": {"stars": 12, "last_update": "2024-06-26 03:13:32", "author_account_age_days": 809}, "https://github.com/fredconex/ComfyUI-SongBloom": {"stars": 3, "last_update": "2025-06-25 15:00:17", "author_account_age_days": 1069}, "https://github.com/fredconex/ComfyUI-SoundFlow": {"stars": 39, "last_update": "2025-06-16 14:18:04", "author_account_age_days": 1069}, "https://github.com/fredconex/ComfyUI-SyncEdit": {"stars": 0, "last_update": "2025-06-16 21:52:36", "author_account_age_days": 1069}, "https://github.com/freelifehacker/ComfyUI-ImgMask2PNG": {"stars": 0, "last_update": "2024-08-28 08:32:23", "author_account_age_days": 2526}, "https://github.com/fsdymy1024/ComfyUI_fsdymy": {"stars": 9, "last_update": "2024-07-01 17:58:52", "author_account_age_days": 2574}, "https://github.com/fssorc/ComfyUI_FFT": {"stars": 13, "last_update": "2024-09-30 01:27:21", "author_account_age_days": 4955}, "https://github.com/fssorc/ComfyUI_FaceShaper": {"stars": 169, "last_update": "2024-09-20 06:15:46", "author_account_age_days": 4955}, "https://github.com/fssorc/ComfyUI_RopeWrapper": {"stars": 16, "last_update": "2025-01-07 04:55:59", "author_account_age_days": 4955}, "https://github.com/fssorc/ComfyUI_pose_inter": {"stars": 79, "last_update": "2025-05-27 07:05:00", "author_account_age_days": 4955}, "https://github.com/fuselayer/comfyui-mosaic-blur": {"stars": 1, "last_update": "2025-04-05 00:57:07", "author_account_age_days": 645}, "https://github.com/gabe-init/ComfyUI-11labs": {"stars": 2, "last_update": "2025-05-27 00:27:28", "author_account_age_days": 32}, "https://github.com/gabe-init/ComfyUI-Google-Image-Search": {"stars": 1, "last_update": "2025-05-27 00:54:00", "author_account_age_days": 32}, "https://github.com/gabe-init/ComfyUI-Openrouter_node": {"stars": 4, "last_update": "2025-06-13 01:42:28", "author_account_age_days": 32}, "https://github.com/gabe-init/ComfyUI-String-Similarity": {"stars": 0, "last_update": "2025-05-27 00:59:21", "author_account_age_days": 32}, "https://github.com/game4d/ComfyUI-BDsInfiniteYou": {"stars": 7, "last_update": "2025-04-01 03:12:04", "author_account_age_days": 4071}, "https://github.com/gasparuff/CustomSelector": {"stars": 1, "last_update": "2025-05-09 06:17:31", "author_account_age_days": 4361}, "https://github.com/gelasdev/ComfyUI-FLUX-BFL-API": {"stars": 43, "last_update": "2025-06-08 01:01:01", "author_account_age_days": 2349}, "https://github.com/gemell1/ComfyUI_GMIC": {"stars": 8, "last_update": "2024-05-22 21:28:51", "author_account_age_days": 2325}, "https://github.com/geocine/geocine-comfyui": {"stars": 0, "last_update": "2025-03-08 15:46:56", "author_account_age_days": 5320}, "https://github.com/ggarra13/ComfyUI-mrv2": {"stars": 4, "last_update": "2025-03-27 17:24:38", "author_account_age_days": 4227}, "https://github.com/giriss/comfy-image-saver": {"stars": 279, "last_update": "2024-05-22 20:40:55", "author_account_age_days": 4606}, "https://github.com/gisu/comfyui-foxpack": {"stars": 2, "last_update": "2024-08-20 06:43:22", "author_account_age_days": 5367}, "https://github.com/gitadmini/comfyui_extractstoryboards": {"stars": 1, "last_update": "2025-06-11 02:01:24", "author_account_age_days": 3409}, "https://github.com/githubYiheng/ComfyUI_Change_IMAGE_BOREDER": {"stars": 0, "last_update": "2024-05-23 01:20:09", "author_account_age_days": 4271}, "https://github.com/githubYiheng/ComfyUI_GetFileNameFromURL": {"stars": 1, "last_update": "2024-05-23 01:19:47", "author_account_age_days": 4271}, "https://github.com/githubYiheng/comfyui_kmeans_filter": {"stars": 0, "last_update": "2024-06-14 09:01:24", "author_account_age_days": 4271}, "https://github.com/githubYiheng/comfyui_meanshift_filter": {"stars": 0, "last_update": "2024-06-14 10:59:43", "author_account_age_days": 4271}, "https://github.com/githubYiheng/comfyui_private_postprocessor": {"stars": 1, "last_update": "2024-06-14 08:09:39", "author_account_age_days": 4271}, "https://github.com/gitmylo/ComfyUI-audio-nodes": {"stars": 9, "last_update": "2025-04-07 07:24:06", "author_account_age_days": 2675}, "https://github.com/glibsonoran/Plush-for-ComfyUI": {"stars": 175, "last_update": "2025-06-21 21:12:31", "author_account_age_days": 2866}, "https://github.com/glifxyz/ComfyUI-GlifNodes": {"stars": 55, "last_update": "2024-11-25 12:37:14", "author_account_age_days": 942}, "https://github.com/glowcone/comfyui-base64-to-image": {"stars": 16, "last_update": "2024-07-08 22:53:25", "author_account_age_days": 4114}, "https://github.com/glowcone/comfyui-string-converter": {"stars": 1, "last_update": "2024-07-31 13:40:48", "author_account_age_days": 4114}, "https://github.com/gmorks/ComfyUI-SendToDiscord": {"stars": 0, "last_update": "2025-01-29 08:10:54", "author_account_age_days": 2662}, "https://github.com/goburiin/nsfwrecog-comfyui": {"stars": 0, "last_update": "2024-08-14 02:17:15", "author_account_age_days": 322}, "https://github.com/godmt/ComfyUI-IP-Composer": {"stars": 5, "last_update": "2025-05-18 09:52:01", "author_account_age_days": 2098}, "https://github.com/godmt/ComfyUI-List-Utils": {"stars": 8, "last_update": "2025-05-26 21:41:06", "author_account_age_days": 2098}, "https://github.com/godspede/ComfyUI_Substring": {"stars": 0, "last_update": "2025-03-27 15:33:12", "author_account_age_days": 3481}, "https://github.com/gokayfem/ComfyUI-Depth-Visualization": {"stars": 64, "last_update": "2024-10-31 23:50:57", "author_account_age_days": 1426}, "https://github.com/gokayfem/ComfyUI-Dream-Interpreter": {"stars": 79, "last_update": "2024-07-31 16:11:04", "author_account_age_days": 1426}, "https://github.com/gokayfem/ComfyUI-Texture-Simple": {"stars": 51, "last_update": "2024-07-31 16:14:23", "author_account_age_days": 1426}, "https://github.com/gokayfem/ComfyUI-fal-API": {"stars": 135, "last_update": "2025-06-20 10:38:44", "author_account_age_days": 1426}, "https://github.com/gokayfem/ComfyUI_VLM_nodes": {"stars": 498, "last_update": "2025-02-13 10:37:34", "author_account_age_days": 1426}, "https://github.com/goldwins520/Comfyui_saveimg2webdav": {"stars": 0, "last_update": "2025-05-25 06:15:38", "author_account_age_days": 1943}, "https://github.com/gonzalu/ComfyUI_YFG_Comical": {"stars": 25, "last_update": "2025-05-03 20:30:02", "author_account_age_days": 2829}, "https://github.com/googincheng/ComfyUX": {"stars": 148, "last_update": "2024-08-22 09:47:17", "author_account_age_days": 3157}, "https://github.com/gorillaframeai/GF_nodes": {"stars": 27, "last_update": "2025-04-19 15:49:54", "author_account_age_days": 602}, "https://github.com/gorillaframeai/GF_translate": {"stars": 4, "last_update": "2025-02-04 19:26:53", "author_account_age_days": 601}, "https://github.com/greengerong/ComfyUI-JanusPro-PL": {"stars": 11, "last_update": "2025-02-08 03:32:59", "author_account_age_days": 4636}, "https://github.com/greengerong/ComfyUI-Lumina-Video": {"stars": 7, "last_update": "2025-02-23 03:01:18", "author_account_age_days": 4636}, "https://github.com/gremlation/ComfyUI-ImageLabel": {"stars": 4, "last_update": "2025-04-03 09:49:57", "author_account_age_days": 190}, "https://github.com/gremlation/ComfyUI-JMESPath": {"stars": 1, "last_update": "2025-04-03 09:50:11", "author_account_age_days": 190}, "https://github.com/gremlation/ComfyUI-TrackAndWheel": {"stars": 2, "last_update": "2025-04-03 09:50:20", "author_account_age_days": 190}, "https://github.com/gremlation/ComfyUI-ViewData": {"stars": 1, "last_update": "2025-04-03 09:50:28", "author_account_age_days": 190}, "https://github.com/gremlation/ComfyUI-jq": {"stars": 1, "last_update": "2025-04-03 09:50:39", "author_account_age_days": 190}, "https://github.com/griptape-ai/ComfyUI-Griptape": {"stars": 194, "last_update": "2025-05-29 03:24:58", "author_account_age_days": 889}, "https://github.com/gseth/ControlAltAI-Nodes": {"stars": 133, "last_update": "2025-06-05 04:21:56", "author_account_age_days": 4215}, "https://github.com/gt732/ComfyUI-DreamWaltz-G": {"stars": 2, "last_update": "2024-10-27 03:15:13", "author_account_age_days": 1524}, "https://github.com/guerreiro/comfyg-switch": {"stars": 3, "last_update": "2025-06-22 17:35:28", "author_account_age_days": 5408}, "https://github.com/guill/abracadabra-comfyui": {"stars": 1, "last_update": "2024-12-23 09:46:10", "author_account_age_days": 4561}, "https://github.com/guyaton/guy-nodes-comfyui": {"stars": 0, "last_update": "2024-10-02 13:15:26", "author_account_age_days": 270}, "https://github.com/hackkhai/ComfyUI-Image-Matting": {"stars": 18, "last_update": "2024-07-31 15:02:56", "author_account_age_days": 2219}, "https://github.com/hanoixan/ComfyUI-DataBeast": {"stars": 2, "last_update": "2024-11-05 17:47:30", "author_account_age_days": 5205}, "https://github.com/haohaocreates/ComfyUI-HH-Image-Selector": {"stars": 0, "last_update": "2024-07-28 21:08:27", "author_account_age_days": 474}, "https://github.com/hassan-sd/comfyui-image-prompt-loader": {"stars": 2, "last_update": "2025-06-11 21:10:47", "author_account_age_days": 937}, "https://github.com/havvk/ComfyUI_AIIA": {"stars": 7, "last_update": "2025-06-18 06:41:23", "author_account_age_days": 4006}, "https://github.com/hay86/ComfyUI_DDColor": {"stars": 7, "last_update": "2024-06-14 08:12:13", "author_account_age_days": 5035}, "https://github.com/hay86/ComfyUI_Dreamtalk": {"stars": 12, "last_update": "2024-08-15 03:37:37", "author_account_age_days": 5035}, "https://github.com/hay86/ComfyUI_Hallo": {"stars": 21, "last_update": "2024-07-30 09:55:03", "author_account_age_days": 5035}, "https://github.com/hay86/ComfyUI_LatentSync": {"stars": 16, "last_update": "2025-01-06 07:47:40", "author_account_age_days": 5035}, "https://github.com/hay86/ComfyUI_MiniCPM-V": {"stars": 39, "last_update": "2024-08-09 07:52:59", "author_account_age_days": 5035}, "https://github.com/hay86/ComfyUI_OpenVoice": {"stars": 18, "last_update": "2024-07-02 08:16:20", "author_account_age_days": 5035}, "https://github.com/hayd-zju/ICEdit-ComfyUI-official": {"stars": 191, "last_update": "2025-05-26 06:23:23", "author_account_age_days": 2285}, "https://github.com/hayde0096/Comfyui-EasySettingpipes": {"stars": 0, "last_update": "2025-05-31 03:51:08", "author_account_age_days": 3094}, "https://github.com/hayden-fr/ComfyUI-Model-Manager": {"stars": 131, "last_update": "2025-05-16 15:14:43", "author_account_age_days": 2305}, "https://github.com/hben35096/ComfyUI-ReplenishNodes": {"stars": 6, "last_update": "2025-05-24 17:06:41", "author_account_age_days": 716}, "https://github.com/hben35096/ComfyUI-ToolBox": {"stars": 6, "last_update": "2024-09-02 14:49:43", "author_account_age_days": 716}, "https://github.com/hekmon/comfyui-checkpoint-extract": {"stars": 0, "last_update": "2025-03-31 13:30:54", "author_account_age_days": 4531}, "https://github.com/hekmon/comfyui-openai-api": {"stars": 5, "last_update": "2025-04-08 09:40:51", "author_account_age_days": 4531}, "https://github.com/heshengtao/comfyui_LLM_party": {"stars": 1758, "last_update": "2025-06-22 08:42:11", "author_account_age_days": 3263}, "https://github.com/heshengtao/comfyui_LLM_schools": {"stars": 7, "last_update": "2024-08-24 15:08:14", "author_account_age_days": 3263}, "https://github.com/hexxacubic/ComfyUI-Prompt_Library": {"stars": 0, "last_update": "2025-06-06 23:30:44", "author_account_age_days": 35}, "https://github.com/hgabha/WWAA-CustomNodes": {"stars": 22, "last_update": "2025-05-11 09:11:45", "author_account_age_days": 523}, "https://github.com/hhhzzyang/Comfyui_Lama": {"stars": 54, "last_update": "2024-05-22 21:13:19", "author_account_age_days": 875}, "https://github.com/hieuck/ComfyUI-BiRefNet": {"stars": 1, "last_update": "2024-12-04 16:20:00", "author_account_age_days": 2893}, "https://github.com/hiforce/comfyui-hiforce-plugin": {"stars": 8, "last_update": "2024-06-14 08:13:24", "author_account_age_days": 2170}, "https://github.com/hinablue/ComfyUI_3dPoseEditor": {"stars": 201, "last_update": "2024-06-21 17:38:40", "author_account_age_days": 5480}, "https://github.com/hmwl/ComfyUI-TaskMonitor": {"stars": 4, "last_update": "2025-05-25 15:41:14", "author_account_age_days": 2981}, "https://github.com/hmwl/ComfyUI_zip": {"stars": 2, "last_update": "2025-05-25 16:21:35", "author_account_age_days": 2981}, "https://github.com/hnmr293/ComfyUI-latent-ops": {"stars": 2, "last_update": "2025-04-16 08:04:59", "author_account_age_days": 922}, "https://github.com/hnmr293/comfyui-savemem": {"stars": 0, "last_update": "2025-04-15 02:10:14", "author_account_age_days": 922}, "https://github.com/hodanajan/optimal-crop-resolution": {"stars": 1, "last_update": "2025-01-21 10:46:26", "author_account_age_days": 2697}, "https://github.com/hoveychen/ComfyUI-MusePose-Remaster": {"stars": 7, "last_update": "2024-10-22 09:40:04", "author_account_age_days": 5011}, "https://github.com/huagetai/ComfyUI-Gaffer": {"stars": 51, "last_update": "2024-06-19 00:58:38", "author_account_age_days": 5000}, "https://github.com/huagetai/ComfyUI_LightGradient": {"stars": 9, "last_update": "2024-05-23 01:21:27", "author_account_age_days": 5000}, "https://github.com/huanngzh/ComfyUI-MVAdapter": {"stars": 414, "last_update": "2025-06-26 07:01:15", "author_account_age_days": 1605}, "https://github.com/hubentu/ComfyUI-loras-loader": {"stars": 2, "last_update": "2025-06-04 19:02:35", "author_account_age_days": 3872}, "https://github.com/huchenlei/ComfyUI-IC-Light-Native": {"stars": 618, "last_update": "2025-02-25 16:35:36", "author_account_age_days": 3244}, "https://github.com/huchenlei/ComfyUI-layerdiffuse": {"stars": 1688, "last_update": "2025-02-25 16:35:50", "author_account_age_days": 3244}, "https://github.com/huchenlei/ComfyUI-openpose-editor": {"stars": 101, "last_update": "2024-07-31 13:44:16", "author_account_age_days": 3244}, "https://github.com/huchenlei/ComfyUI_DanTagGen": {"stars": 65, "last_update": "2024-08-01 01:42:14", "author_account_age_days": 3244}, "https://github.com/huchenlei/ComfyUI_densediffusion": {"stars": 134, "last_update": "2025-02-25 16:34:32", "author_account_age_days": 3244}, "https://github.com/huchenlei/ComfyUI_omost": {"stars": 445, "last_update": "2025-02-25 16:35:18", "author_account_age_days": 3244}, "https://github.com/hughescr/ComfyUI-OpenPose-Keypoint-Extractor": {"stars": 32, "last_update": "2025-04-08 18:48:00", "author_account_age_days": 6009}, "https://github.com/hugobb/FastGAN-ComfyUI-Node": {"stars": 1, "last_update": "2025-04-25 20:24:20", "author_account_age_days": 3203}, "https://github.com/huixingyun/ComfyUI-HX-Captioner": {"stars": 0, "last_update": "2025-01-25 06:48:18", "author_account_age_days": 203}, "https://github.com/huixingyun/ComfyUI-HX-Pimg": {"stars": 0, "last_update": "2025-03-04 09:30:50", "author_account_age_days": 203}, "https://github.com/humgate/simplecomfy": {"stars": 0, "last_update": "2024-06-14 08:58:21", "author_account_age_days": 1735}, "https://github.com/hunzmusic/ComfyUI-IG2MV": {"stars": 26, "last_update": "2025-05-09 10:46:42", "author_account_age_days": 96}, "https://github.com/hustille/ComfyUI_Fooocus_KSampler": {"stars": 62, "last_update": "2024-05-22 20:39:48", "author_account_age_days": 808}, "https://github.com/hustille/ComfyUI_hus_utils": {"stars": 5, "last_update": "2024-05-22 20:39:34", "author_account_age_days": 808}, "https://github.com/hvppycoding/comfyui-random-sampler-scheduler-steps": {"stars": 0, "last_update": "2025-06-04 15:41:16", "author_account_age_days": 973}, "https://github.com/hwhaocool/ComfyUI-Select-Any": {"stars": 2, "last_update": "2024-07-31 13:52:47", "author_account_age_days": 3256}, "https://github.com/hybskgks28275/ComfyUI-hybs-nodes": {"stars": 1, "last_update": "2025-05-28 07:15:21", "author_account_age_days": 1294}, "https://github.com/hyunamy/comfy-ui-on-complete-email-me": {"stars": 3, "last_update": "2025-04-10 01:38:49", "author_account_age_days": 3479}, "https://github.com/iDAPPA/ComfyUI-AMDGPUMonitor": {"stars": 2, "last_update": "2025-03-13 18:16:21", "author_account_age_days": 107}, "https://github.com/iFREEGROUP/comfyui-undistort": {"stars": 2, "last_update": "2024-06-14 08:59:52", "author_account_age_days": 1925}, "https://github.com/iSuneast/ComfyUI-WebhookNotifier": {"stars": 1, "last_update": "2025-04-06 03:53:13", "author_account_age_days": 4539}, "https://github.com/ialhabbal/OcclusionMask": {"stars": 20, "last_update": "2025-06-24 14:38:58", "author_account_age_days": 3387}, "https://github.com/iamandeepsandhu/ComfyUI-NSFW-Check": {"stars": 10, "last_update": "2024-11-26 07:32:18", "author_account_age_days": 2574}, "https://github.com/icesun963/ComfyUI_HFDownLoad": {"stars": 0, "last_update": "2024-07-18 12:13:23", "author_account_age_days": 4474}, "https://github.com/ichabodcole/ComfyUI-Ichis-Pack": {"stars": 2, "last_update": "2025-04-20 08:05:27", "author_account_age_days": 4763}, "https://github.com/idrirap/ComfyUI-Lora-Auto-Trigger-Words": {"stars": 206, "last_update": "2025-01-16 08:38:21", "author_account_age_days": 3418}, "https://github.com/iemesowum/ComfyUI_IsaacNodes": {"stars": 1, "last_update": "2025-03-27 13:28:10", "author_account_age_days": 5660}, "https://github.com/if-ai/ComfyUI-IF_AI_Dreamtalk": {"stars": 25, "last_update": "2025-03-14 13:19:03", "author_account_age_days": 3230}, "https://github.com/if-ai/ComfyUI-IF_AI_HFDownloaderNode": {"stars": 18, "last_update": "2025-03-09 09:21:13", "author_account_age_days": 3230}, "https://github.com/if-ai/ComfyUI-IF_AI_ParlerTTSNode": {"stars": 17, "last_update": "2025-03-14 13:27:47", "author_account_age_days": 3230}, "https://github.com/if-ai/ComfyUI-IF_AI_WishperSpeechNode": {"stars": 44, "last_update": "2025-03-09 09:17:01", "author_account_age_days": 3230}, "https://github.com/if-ai/ComfyUI-IF_AI_tools": {"stars": 655, "last_update": "2025-03-09 09:11:32", "author_account_age_days": 3230}, "https://github.com/if-ai/ComfyUI-IF_DatasetMkr": {"stars": 20, "last_update": "2025-03-17 08:14:01", "author_account_age_days": 3230}, "https://github.com/if-ai/ComfyUI-IF_Gemini": {"stars": 27, "last_update": "2025-04-14 06:27:01", "author_account_age_days": 3230}, "https://github.com/if-ai/ComfyUI-IF_LLM": {"stars": 124, "last_update": "2025-04-09 09:23:21", "author_account_age_days": 3230}, "https://github.com/if-ai/ComfyUI-IF_MemoAvatar": {"stars": 166, "last_update": "2025-03-09 09:28:07", "author_account_age_days": 3230}, "https://github.com/if-ai/ComfyUI-IF_Trellis": {"stars": 430, "last_update": "2025-03-09 09:31:12", "author_account_age_days": 3230}, "https://github.com/if-ai/ComfyUI-IF_VideoPrompts": {"stars": 47, "last_update": "2025-04-02 17:19:28", "author_account_age_days": 3230}, "https://github.com/if-ai/ComfyUI-WanResolutionSelector": {"stars": 0, "last_update": "2025-06-20 19:40:37", "author_account_age_days": 3230}, "https://github.com/if-ai/ComfyUI_IF_AI_LoadImages": {"stars": 9, "last_update": "2025-03-14 13:24:31", "author_account_age_days": 3230}, "https://github.com/ifmylove2011/comfyui-missed-tool": {"stars": 1, "last_update": "2025-04-10 09:15:08", "author_account_age_days": 3538}, "https://github.com/ihmily/ComfyUI-Light-Tool": {"stars": 11, "last_update": "2025-06-04 10:12:12", "author_account_age_days": 997}, "https://github.com/illuminatianon/comfyui-csvwildcards": {"stars": 0, "last_update": "2025-05-02 21:45:53", "author_account_age_days": 111}, "https://github.com/imb101/ComfyUI-FaceSwap": {"stars": 33, "last_update": "2024-05-22 18:22:29", "author_account_age_days": 1257}, "https://github.com/infinigence/ComfyUI_Model_Cache": {"stars": 8, "last_update": "2025-03-28 02:35:14", "author_account_age_days": 511}, "https://github.com/inflamously/comfyui-prompt-enhancer": {"stars": 0, "last_update": "2025-06-02 22:49:50", "author_account_age_days": 4148}, "https://github.com/injet-zhou/comfyui_extra_api": {"stars": 11, "last_update": "2025-06-06 02:35:34", "author_account_age_days": 2595}, "https://github.com/inventorado/ComfyUI_NNT": {"stars": 68, "last_update": "2025-01-08 17:22:46", "author_account_age_days": 3253}, "https://github.com/irreveloper/ComfyUI-DSD": {"stars": 39, "last_update": "2025-03-15 16:55:07", "author_account_age_days": 4085}, "https://github.com/iwanders/ComfyUI_nodes": {"stars": 1, "last_update": "2024-07-11 01:06:26", "author_account_age_days": 4794}, "https://github.com/jacklukai/ComfyUI_DeployCash": {"stars": 0, "last_update": "2025-04-25 09:46:49", "author_account_age_days": 351}, "https://github.com/jags111/ComfyUI_Jags_Audiotools": {"stars": 78, "last_update": "2025-03-20 16:23:33", "author_account_age_days": 4239}, "https://github.com/jags111/ComfyUI_Jags_VectorMagic": {"stars": 79, "last_update": "2025-04-02 08:46:34", "author_account_age_days": 4239}, "https://github.com/jags111/efficiency-nodes-comfyui": {"stars": 1280, "last_update": "2025-05-30 03:20:24", "author_account_age_days": 4239}, "https://github.com/jaimitoes/ComfyUI_Wan2_1_lora_trainer": {"stars": 16, "last_update": "2025-06-26 13:45:46", "author_account_age_days": 4127}, "https://github.com/jakechai/ComfyUI-JakeUpgrade": {"stars": 94, "last_update": "2025-06-22 02:42:41", "author_account_age_days": 1940}, "https://github.com/jamal-alkharrat/ComfyUI_rotate_image": {"stars": 2, "last_update": "2024-05-22 23:19:02", "author_account_age_days": 1345}, "https://github.com/jamesWalker55/comfyui-p2ldgan": {"stars": 17, "last_update": "2024-05-22 18:19:04", "author_account_age_days": 2895}, "https://github.com/jamesWalker55/comfyui-various": {"stars": 104, "last_update": "2025-02-27 11:01:51", "author_account_age_days": 2895}, "https://github.com/jammyfu/ComfyUI_PaintingCoderUtils": {"stars": 12, "last_update": "2025-02-26 05:03:05", "author_account_age_days": 4845}, "https://github.com/jasonjgardner/comfui-substance-designer-integration": {"stars": 1, "last_update": "2025-06-08 20:40:11", "author_account_age_days": 4746}, "https://github.com/jax-explorer/ComfyUI-InstantCharacter": {"stars": 181, "last_update": "2025-05-13 15:04:58", "author_account_age_days": 945}, "https://github.com/jax-explorer/ComfyUI-VideoBasic": {"stars": 16, "last_update": "2025-06-22 14:53:19", "author_account_age_days": 945}, "https://github.com/jax-explorer/ComfyUI-VideoBasicLatentSync": {"stars": 0, "last_update": "2025-04-07 10:07:44", "author_account_age_days": 945}, "https://github.com/jax-explorer/ComfyUI-easycontrol": {"stars": 187, "last_update": "2025-04-17 15:39:33", "author_account_age_days": 945}, "https://github.com/jax-explorer/comfyui-model-dynamic-loader": {"stars": 1, "last_update": "2025-06-01 07:58:16", "author_account_age_days": 945}, "https://github.com/jax-explorer/fast_video_comfyui": {"stars": 0, "last_update": "2024-05-23 01:17:35", "author_account_age_days": 945}, "https://github.com/jeffrey2212/ComfyUI-PonyCharacterPrompt": {"stars": 2, "last_update": "2024-10-26 05:38:07", "author_account_age_days": 4839}, "https://github.com/jeffy5/comfyui-faceless-node": {"stars": 52, "last_update": "2025-04-07 02:19:38", "author_account_age_days": 3299}, "https://github.com/jerome7562/ComfyUI-XenoFlow": {"stars": 4, "last_update": "2025-03-10 16:33:16", "author_account_age_days": 134}, "https://github.com/jerrylongyan/ComfyUI-My-Mask": {"stars": 2, "last_update": "2025-01-08 08:39:19", "author_account_age_days": 4299}, "https://github.com/jerrywap/ComfyUI_LoadImageFromHttpURL": {"stars": 1, "last_update": "2025-04-09 19:31:50", "author_account_age_days": 2701}, "https://github.com/jerrywap/ComfyUI_UploadToWebhookHTTP": {"stars": 1, "last_update": "2025-04-07 15:01:04", "author_account_age_days": 2701}, "https://github.com/jesenzhang/ComfyUI_StreamDiffusion": {"stars": 147, "last_update": "2025-03-18 04:47:24", "author_account_age_days": 4020}, "https://github.com/jhj0517/ComfyUI-Moondream-Gaze-Detection": {"stars": 54, "last_update": "2025-02-03 04:53:25", "author_account_age_days": 1266}, "https://github.com/jhj0517/ComfyUI-jhj-Kokoro-Onnx": {"stars": 4, "last_update": "2025-02-04 14:15:08", "author_account_age_days": 1266}, "https://github.com/jianzhichun/ComfyUI-Easyai": {"stars": 20, "last_update": "2024-10-27 03:29:53", "author_account_age_days": 3405}, "https://github.com/jiaqianjing/ComfyUI-MidjourneyHub": {"stars": 9, "last_update": "2025-06-19 01:29:05", "author_account_age_days": 3497}, "https://github.com/jiaxiangc/ComfyUI-ResAdapter": {"stars": 289, "last_update": "2024-05-23 00:22:23", "author_account_age_days": 1654}, "https://github.com/jinanlongen/ComfyUI-Prompt-Expander": {"stars": 0, "last_update": "2025-01-28 08:04:24", "author_account_age_days": 4944}, "https://github.com/jinchanz/ComfyUI-ADIC": {"stars": 1, "last_update": "2025-06-27 05:51:50", "author_account_age_days": 2433}, "https://github.com/jitcoder/lora-info": {"stars": 101, "last_update": "2025-05-15 07:25:46", "author_account_age_days": 4409}, "https://github.com/jjkramhoeft/ComfyUI-Jjk-Nodes": {"stars": 25, "last_update": "2024-05-22 20:44:56", "author_account_age_days": 4017}, "https://github.com/jkrauss82/ultools-comfyui": {"stars": 7, "last_update": "2025-04-09 20:17:27", "author_account_age_days": 4580}, "https://github.com/jmkl/ComfyUI-ricing": {"stars": 10, "last_update": "2024-10-16 15:38:08", "author_account_age_days": 4965}, "https://github.com/jn-jairo/jn_comfyui": {"stars": 5, "last_update": "2024-08-16 18:09:12", "author_account_age_days": 4352}, "https://github.com/jnxmx/ComfyUI_HuggingFace_Downloader": {"stars": 5, "last_update": "2025-04-27 12:08:27", "author_account_age_days": 706}, "https://github.com/joeriben/ai4artsed_comfyui": {"stars": 1, "last_update": "2025-06-09 21:38:55", "author_account_age_days": 4114}, "https://github.com/joeriben/ai4artsed_comfyui_nodes": {"stars": 0, "last_update": "2025-06-26 19:16:48", "author_account_age_days": 4115}, "https://github.com/john-mnz/ComfyUI-Inspyrenet-Rembg": {"stars": 584, "last_update": "2024-07-31 13:54:32", "author_account_age_days": 576}, "https://github.com/jojkaart/ComfyUI-sampler-lcm-alternative": {"stars": 134, "last_update": "2025-03-28 19:02:01", "author_account_age_days": 5166}, "https://github.com/jordoh/ComfyUI-Deepface": {"stars": 28, "last_update": "2025-05-27 18:09:06", "author_account_age_days": 5362}, "https://github.com/joreyaesh/comfyui_scroll_over_textarea": {"stars": 0, "last_update": "2025-03-09 18:58:09", "author_account_age_days": 4481}, "https://github.com/joreyaesh/comfyui_touchpad_scroll_controller.enableTouchpadScroll": {"stars": 0, "last_update": "2025-03-18 03:15:42", "author_account_age_days": 4481}, "https://github.com/jqy-yo/Comfyui-BBoxLowerMask2": {"stars": 0, "last_update": "2025-05-19 02:28:44", "author_account_age_days": 409}, "https://github.com/jroc22/ComfyUI-CSV-prompt-builder": {"stars": 10, "last_update": "2024-08-01 19:39:30", "author_account_age_days": 1057}, "https://github.com/jstit/comfyui_custom_node_image": {"stars": 0, "last_update": "2024-08-27 05:10:12", "author_account_age_days": 2232}, "https://github.com/jtrue/ComfyUI-JaRue": {"stars": 7, "last_update": "2024-06-14 09:01:12", "author_account_age_days": 4298}, "https://github.com/jtydhr88/ComfyUI-Hunyuan3D-1-wrapper": {"stars": 28, "last_update": "2024-11-13 11:50:46", "author_account_age_days": 5121}, "https://github.com/jtydhr88/ComfyUI-LayerDivider": {"stars": 95, "last_update": "2025-04-25 11:21:00", "author_account_age_days": 5121}, "https://github.com/jtydhr88/ComfyUI-Workflow-Encrypt": {"stars": 33, "last_update": "2024-07-31 13:45:53", "author_account_age_days": 5121}, "https://github.com/judian17/ComfyUI-Extract_Flux_Lora": {"stars": 14, "last_update": "2025-05-05 02:46:31", "author_account_age_days": 2213}, "https://github.com/judian17/ComfyUI-UniWorld-jd17": {"stars": 22, "last_update": "2025-06-10 14:12:16", "author_account_age_days": 2213}, "https://github.com/judian17/ComfyUI-joycaption-beta-one-GGUF": {"stars": 37, "last_update": "2025-05-26 02:28:33", "author_account_age_days": 2213}, "https://github.com/judian17/ComfyUI_ZIM": {"stars": 4, "last_update": "2025-05-14 11:32:06", "author_account_age_days": 2213}, "https://github.com/juehackr/comfyui_fk_server": {"stars": 409, "last_update": "2025-06-05 09:22:26", "author_account_age_days": 1480}, "https://github.com/jupo-ai/comfy-ex-tagcomplete": {"stars": 14, "last_update": "2025-05-24 07:58:32", "author_account_age_days": 117}, "https://github.com/jurdnf/ComfyUI-JurdnsIterativeNoiseKSampler": {"stars": 3, "last_update": "2025-06-23 03:58:50", "author_account_age_days": 34}, "https://github.com/jurdnf/ComfyUI-JurdnsModelSculptor": {"stars": 3, "last_update": "2025-06-23 14:03:22", "author_account_age_days": 34}, "https://github.com/jurdnisglobby/ComfyUI-Jurdns-Groq-Node": {"stars": 2, "last_update": "2025-01-18 06:20:23", "author_account_age_days": 279}, "https://github.com/justUmen/Bjornulf_custom_nodes": {"stars": 383, "last_update": "2025-06-11 12:32:38", "author_account_age_days": 3157}, "https://github.com/justin-vt/ComfyUI-brushstrokes": {"stars": 1, "last_update": "2025-03-05 18:27:37", "author_account_age_days": 3074}, "https://github.com/k-komarov/comfyui-bunny-cdn-storage": {"stars": 0, "last_update": "2024-08-31 20:59:08", "author_account_age_days": 3843}, "https://github.com/ka-puna/comfyui-yanc": {"stars": 9, "last_update": "2025-05-25 20:41:57", "author_account_age_days": 2575}, "https://github.com/kaanyalova/ComfyUI_ExtendedImageFormats": {"stars": 5, "last_update": "2025-01-25 10:57:38", "author_account_age_days": 1635}, "https://github.com/kadirnar/ComfyUI-Transformers": {"stars": 21, "last_update": "2024-06-22 22:44:39", "author_account_age_days": 2698}, "https://github.com/kadirnar/ComfyUI-YOLO": {"stars": 85, "last_update": "2025-05-30 13:26:43", "author_account_age_days": 2698}, "https://github.com/kael558/ComfyUI-GGUF-FantasyTalking": {"stars": 0, "last_update": "2025-06-18 02:19:28", "author_account_age_days": 3015}, "https://github.com/kaibioinfo/ComfyUI_AdvancedRefluxControl": {"stars": 592, "last_update": "2025-04-19 10:24:42", "author_account_age_days": 5049}, "https://github.com/kale4eat/ComfyUI-path-util": {"stars": 0, "last_update": "2024-05-25 05:44:11", "author_account_age_days": 1980}, "https://github.com/kale4eat/ComfyUI-speech-dataset-toolkit": {"stars": 18, "last_update": "2025-06-17 01:58:03", "author_account_age_days": 1980}, "https://github.com/kale4eat/ComfyUI-string-util": {"stars": 4, "last_update": "2024-05-23 00:24:40", "author_account_age_days": 1980}, "https://github.com/kale4eat/ComfyUI-text-file-util": {"stars": 0, "last_update": "2024-05-23 00:24:51", "author_account_age_days": 1980}, "https://github.com/kambara/ComfyUI-PromptPalette": {"stars": 2, "last_update": "2025-06-27 00:19:45", "author_account_age_days": 5902}, "https://github.com/kantsche/ComfyUI-MixMod": {"stars": 22, "last_update": "2025-05-08 17:40:10", "author_account_age_days": 4253}, "https://github.com/kappa54m/ComfyUI_Usability": {"stars": 0, "last_update": "2024-08-08 15:31:47", "author_account_age_days": 1879}, "https://github.com/karthikg-09/ComfyUI-Vton-Mask": {"stars": 0, "last_update": "2025-05-24 18:37:41", "author_account_age_days": 563}, "https://github.com/kasukanra/ComfyUI_StringToHex": {"stars": 1, "last_update": "2024-08-20 04:52:06", "author_account_age_days": 3030}, "https://github.com/katalist-ai/comfyUI-nsfw-detection": {"stars": 1, "last_update": "2024-05-23 01:23:32", "author_account_age_days": 1122}, "https://github.com/kazeyori/ComfyUI-QuickImageSequenceProcess": {"stars": 0, "last_update": "2025-04-05 12:52:40", "author_account_age_days": 1095}, "https://github.com/kealiu/ComfyUI-S3-Tools": {"stars": 7, "last_update": "2024-07-04 10:13:07", "author_account_age_days": 4496}, "https://github.com/kealiu/ComfyUI-Zero123-Porting": {"stars": 22, "last_update": "2024-08-22 07:07:57", "author_account_age_days": 4496}, "https://github.com/kealiu/ComfyUI-ZeroShot-MTrans": {"stars": 174, "last_update": "2024-07-04 10:12:32", "author_account_age_days": 4496}, "https://github.com/keit0728/ComfyUI-Image-Toolkit": {"stars": 1, "last_update": "2025-05-30 06:46:47", "author_account_age_days": 3356}, "https://github.com/keit0728/ComfyUI-keitNodes": {"stars": 3, "last_update": "2025-06-20 09:34:46", "author_account_age_days": 3356}, "https://github.com/kenjiqq/qq-nodes-comfyui": {"stars": 47, "last_update": "2025-06-24 22:38:01", "author_account_age_days": 5258}, "https://github.com/kevin314/ComfyUI-FastVideo": {"stars": 2, "last_update": "2025-05-25 10:25:28", "author_account_age_days": 2500}, "https://github.com/kevinmcmahondev/comfyui-kmcdev-image-filter-adjustments": {"stars": 0, "last_update": "2025-02-19 06:55:25", "author_account_age_days": 1121}, "https://github.com/kevinmcmahondev/comfyui-skin-tone-detector": {"stars": 2, "last_update": "2024-12-22 06:44:20", "author_account_age_days": 1121}, "https://github.com/kft334/Knodes": {"stars": 4, "last_update": "2024-06-14 08:12:06", "author_account_age_days": 1327}, "https://github.com/kijai/ComfyUI-ADMotionDirector": {"stars": 176, "last_update": "2024-11-07 07:20:23", "author_account_age_days": 2551}, "https://github.com/kijai/ComfyUI-APISR-KJ": {"stars": 68, "last_update": "2024-05-21 16:30:21", "author_account_age_days": 2551}, "https://github.com/kijai/ComfyUI-BrushNet-Wrapper": {"stars": 144, "last_update": "2024-06-20 12:15:16", "author_account_age_days": 2551}, "https://github.com/kijai/ComfyUI-CCSR": {"stars": 228, "last_update": "2024-06-28 11:13:33", "author_account_age_days": 2551}, "https://github.com/kijai/ComfyUI-CogVideoXWrapper": {"stars": 1503, "last_update": "2025-06-19 21:10:27", "author_account_age_days": 2551}, "https://github.com/kijai/ComfyUI-ControlNeXt-SVD": {"stars": 185, "last_update": "2024-08-15 08:26:15", "author_account_age_days": 2551}, "https://github.com/kijai/ComfyUI-DDColor": {"stars": 151, "last_update": "2024-05-21 16:04:26", "author_account_age_days": 2551}, "https://github.com/kijai/ComfyUI-DepthAnythingV2": {"stars": 329, "last_update": "2025-06-16 13:16:52", "author_account_age_days": 2551}, "https://github.com/kijai/ComfyUI-DiffusionLight": {"stars": 73, "last_update": "2024-05-21 16:16:52", "author_account_age_days": 2551}, "https://github.com/kijai/ComfyUI-DynamiCrafterWrapper": {"stars": 676, "last_update": "2025-06-02 11:49:00", "author_account_age_days": 2551}, "https://github.com/kijai/ComfyUI-ELLA-wrapper": {"stars": 116, "last_update": "2024-05-21 16:47:28", "author_account_age_days": 2551}, "https://github.com/kijai/ComfyUI-Florence2": {"stars": 1307, "last_update": "2025-05-21 14:46:50", "author_account_age_days": 2551}, "https://github.com/kijai/ComfyUI-FluxTrainer": {"stars": 933, "last_update": "2025-04-02 07:35:46", "author_account_age_days": 2551}, "https://github.com/kijai/ComfyUI-GIMM-VFI": {"stars": 324, "last_update": "2025-05-10 18:09:40", "author_account_age_days": 2551}, "https://github.com/kijai/ComfyUI-Geowizard": {"stars": 122, "last_update": "2024-12-16 19:33:54", "author_account_age_days": 2551}, "https://github.com/kijai/ComfyUI-HFRemoteVae": {"stars": 51, "last_update": "2025-03-01 18:22:59", "author_account_age_days": 2551}, "https://github.com/kijai/ComfyUI-HunyuanVideoWrapper": {"stars": 2489, "last_update": "2025-05-12 13:31:36", "author_account_age_days": 2551}, "https://github.com/kijai/ComfyUI-IC-Light": {"stars": 1052, "last_update": "2025-05-30 19:21:20", "author_account_age_days": 2551}, "https://github.com/kijai/ComfyUI-KJNodes": {"stars": 1488, "last_update": "2025-06-18 08:03:54", "author_account_age_days": 2551}, "https://github.com/kijai/ComfyUI-KwaiKolorsWrapper": {"stars": 595, "last_update": "2024-10-18 08:47:45", "author_account_age_days": 2551}, "https://github.com/kijai/ComfyUI-LBMWrapper": {"stars": 217, "last_update": "2025-05-14 09:25:13", "author_account_age_days": 2551}, "https://github.com/kijai/ComfyUI-LLaVA-OneVision": {"stars": 85, "last_update": "2024-08-25 14:04:22", "author_account_age_days": 2551}, "https://github.com/kijai/ComfyUI-LVCDWrapper": {"stars": 63, "last_update": "2024-09-30 11:49:12", "author_account_age_days": 2551}, "https://github.com/kijai/ComfyUI-LaVi-Bridge-Wrapper": {"stars": 22, "last_update": "2024-05-21 16:41:18", "author_account_age_days": 2551}, "https://github.com/kijai/ComfyUI-LivePortraitKJ": {"stars": 2003, "last_update": "2024-08-05 21:39:49", "author_account_age_days": 2551}, "https://github.com/kijai/ComfyUI-Lotus": {"stars": 137, "last_update": "2024-10-13 12:33:24", "author_account_age_days": 2551}, "https://github.com/kijai/ComfyUI-LuminaWrapper": {"stars": 195, "last_update": "2024-07-31 13:52:06", "author_account_age_days": 2551}, "https://github.com/kijai/ComfyUI-Marigold": {"stars": 538, "last_update": "2025-05-16 10:22:16", "author_account_age_days": 2551}, "https://github.com/kijai/ComfyUI-MimicMotionWrapper": {"stars": 489, "last_update": "2025-01-12 17:34:34", "author_account_age_days": 2551}, "https://github.com/kijai/ComfyUI-MoGe": {"stars": 49, "last_update": "2025-02-07 18:42:39", "author_account_age_days": 2551}, "https://github.com/kijai/ComfyUI-OpenDiTWrapper": {"stars": 43, "last_update": "2024-07-03 14:59:13", "author_account_age_days": 2551}, "https://github.com/kijai/ComfyUI-PyramidFlowWrapper": {"stars": 369, "last_update": "2024-11-15 13:28:18", "author_account_age_days": 2551}, "https://github.com/kijai/ComfyUI-SUPIR": {"stars": 1994, "last_update": "2025-06-07 09:17:40", "author_account_age_days": 2551}, "https://github.com/kijai/ComfyUI-SVD": {"stars": 163, "last_update": "2024-05-22 21:09:54", "author_account_age_days": 2551}, "https://github.com/kijai/ComfyUI-StableXWrapper": {"stars": 57, "last_update": "2025-01-31 11:59:01", "author_account_age_days": 2551}, "https://github.com/kijai/ComfyUI-depth-fm": {"stars": 75, "last_update": "2024-05-22 21:10:15", "author_account_age_days": 2551}, "https://github.com/kijai/ComfyUI-moondream": {"stars": 105, "last_update": "2024-08-12 16:30:11", "author_account_age_days": 2551}, "https://github.com/kijai/ComfyUI-segment-anything-2": {"stars": 966, "last_update": "2025-03-19 09:40:37", "author_account_age_days": 2551}, "https://github.com/kimara-ai/ComfyUI-Kimara-AI-Advanced-Watermarks": {"stars": 16, "last_update": "2025-04-03 17:22:59", "author_account_age_days": 224}, "https://github.com/kinfolk0117/ComfyUI_GradientDeepShrink": {"stars": 28, "last_update": "2024-05-22 21:25:13", "author_account_age_days": 837}, "https://github.com/kinfolk0117/ComfyUI_GridSwapper": {"stars": 29, "last_update": "2024-10-27 09:04:20", "author_account_age_days": 837}, "https://github.com/kinfolk0117/ComfyUI_Pilgram": {"stars": 7, "last_update": "2024-05-22 21:25:24", "author_account_age_days": 837}, "https://github.com/kinfolk0117/ComfyUI_SimpleTiles": {"stars": 55, "last_update": "2024-05-22 21:25:01", "author_account_age_days": 837}, "https://github.com/kk8bit/KayTool": {"stars": 151, "last_update": "2025-05-25 03:46:23", "author_account_age_days": 725}, "https://github.com/klinter007/klinter_nodes": {"stars": 18, "last_update": "2025-03-30 14:45:57", "author_account_age_days": 794}, "https://github.com/knuknX/ComfyUI-Image-Tools": {"stars": 3, "last_update": "2024-06-14 09:05:58", "author_account_age_days": 568}, "https://github.com/kohs100/comfyui-ppwc": {"stars": 0, "last_update": "2025-06-26 15:37:57", "author_account_age_days": 3259}, "https://github.com/kohya-ss/ControlNet-LLLite-ComfyUI": {"stars": 192, "last_update": "2024-05-22 20:44:44", "author_account_age_days": 2177}, "https://github.com/komojini/ComfyUI_SDXL_DreamBooth_LoRA_CustomNodes": {"stars": 3, "last_update": "2024-05-22 21:34:27", "author_account_age_days": 951}, "https://github.com/komojini/komojini-comfyui-nodes": {"stars": 75, "last_update": "2024-05-22 21:34:39", "author_account_age_days": 951}, "https://github.com/kostenickj/jk-comfyui-helpers": {"stars": 5, "last_update": "2024-12-19 10:22:42", "author_account_age_days": 3417}, "https://github.com/kpsss34/ComfyUI-kpsss34-Sana": {"stars": 0, "last_update": "2025-06-25 07:38:11", "author_account_age_days": 11}, "https://github.com/kraglik/prompt_collapse": {"stars": 5, "last_update": "2024-12-15 08:39:51", "author_account_age_days": 2825}, "https://github.com/krmahil/comfyui-hollow-preserve": {"stars": 1, "last_update": "2025-05-15 09:55:46", "author_account_age_days": 2646}, "https://github.com/kukuo6666/ComfyUI-Equirect": {"stars": 1, "last_update": "2025-03-29 18:28:47", "author_account_age_days": 1934}, "https://github.com/kungful/ComfyUI_to_webui": {"stars": 15, "last_update": "2025-05-30 20:08:56", "author_account_age_days": 1490}, "https://github.com/kunieone/ComfyUI_alkaid": {"stars": 0, "last_update": "2024-05-23 01:10:21", "author_account_age_days": 2888}, "https://github.com/kwaroran/abg-comfyui": {"stars": 25, "last_update": "2024-05-22 18:19:51", "author_account_age_days": 976}, "https://github.com/kycg/comfyui-Lora-auto-downloader": {"stars": 1, "last_update": "2024-11-08 19:57:23", "author_account_age_days": 1309}, "https://github.com/l-comm/WatermarkRemoval": {"stars": 4, "last_update": "2025-01-13 05:33:32", "author_account_age_days": 178}, "https://github.com/l20richo/ComfyUI-Azure-Blob-Storage": {"stars": 2, "last_update": "2024-06-22 16:53:47", "author_account_age_days": 1535}, "https://github.com/l3ony2k/comfyui-leon-nodes": {"stars": 0, "last_update": "2025-06-25 09:18:17", "author_account_age_days": 2025}, "https://github.com/laksjdjf/Batch-Condition-ComfyUI": {"stars": 7, "last_update": "2024-05-22 20:42:42", "author_account_age_days": 3199}, "https://github.com/laksjdjf/ComfyUI-Imatrix": {"stars": 3, "last_update": "2025-06-07 00:17:26", "author_account_age_days": 3199}, "https://github.com/laksjdjf/LCMSampler-ComfyUI": {"stars": 16, "last_update": "2024-05-22 20:42:17", "author_account_age_days": 3199}, "https://github.com/laksjdjf/LoRTnoC-ComfyUI": {"stars": 13, "last_update": "2024-05-22 20:42:29", "author_account_age_days": 3199}, "https://github.com/laksjdjf/cd-tuner_negpip-ComfyUI": {"stars": 23, "last_update": "2024-05-22 20:42:04", "author_account_age_days": 3199}, "https://github.com/laksjdjf/cgem156-ComfyUI": {"stars": 72, "last_update": "2025-04-30 14:52:29", "author_account_age_days": 3199}, "https://github.com/laksjdjf/pfg-ComfyUI": {"stars": 12, "last_update": "2024-05-22 20:41:41", "author_account_age_days": 3199}, "https://github.com/larsupb/LoRA-Merger-ComfyUI": {"stars": 46, "last_update": "2024-10-24 11:28:08", "author_account_age_days": 3444}, "https://github.com/latenightlabs/ComfyUI-LNL": {"stars": 26, "last_update": "2024-10-07 20:09:43", "author_account_age_days": 519}, "https://github.com/lazniak/Head-Orientation-Node-for-ComfyUI---by-PabloGFX": {"stars": 10, "last_update": "2024-09-25 15:02:14", "author_account_age_days": 2650}, "https://github.com/lazniak/LiquidTime-Interpolation": {"stars": 12, "last_update": "2025-04-03 11:42:12", "author_account_age_days": 2650}, "https://github.com/lazniak/comfyui-google-photos-loader": {"stars": 3, "last_update": "2025-04-03 11:46:29", "author_account_age_days": 2650}, "https://github.com/lc03lc/Comfyui_OmniConsistency": {"stars": 58, "last_update": "2025-06-01 02:56:02", "author_account_age_days": 1343}, "https://github.com/lceric/comfyui-gpt-image": {"stars": 8, "last_update": "2025-05-19 10:49:30", "author_account_age_days": 3077}, "https://github.com/lebrosoft/ComfyUI-VideoChatWrapper": {"stars": 2, "last_update": "2025-06-06 04:07:48", "author_account_age_days": 3884}, "https://github.com/leeguandong/ComfyUI_1Prompt1Story": {"stars": 5, "last_update": "2025-03-13 16:11:50", "author_account_age_days": 3162}, "https://github.com/leeguandong/ComfyUI_ChatGen": {"stars": 2, "last_update": "2025-03-13 16:24:46", "author_account_age_days": 3162}, "https://github.com/leeguandong/ComfyUI_Cogview4": {"stars": 2, "last_update": "2025-03-13 15:58:44", "author_account_age_days": 3162}, "https://github.com/leeguandong/ComfyUI_CompareModelWeights": {"stars": 3, "last_update": "2025-01-09 02:43:41", "author_account_age_days": 3162}, "https://github.com/leeguandong/ComfyUI_CrossImageAttention": {"stars": 3, "last_update": "2024-08-16 11:59:42", "author_account_age_days": 3162}, "https://github.com/leeguandong/ComfyUI_DeepSeekVL2": {"stars": 0, "last_update": "2025-03-13 16:32:16", "author_account_age_days": 3162}, "https://github.com/leeguandong/ComfyUI_FluxAttentionMask": {"stars": 4, "last_update": "2025-03-15 07:37:50", "author_account_age_days": 3162}, "https://github.com/leeguandong/ComfyUI_FluxClipWeight": {"stars": 3, "last_update": "2025-03-02 07:32:55", "author_account_age_days": 3162}, "https://github.com/leeguandong/ComfyUI_FluxCustomId": {"stars": 7, "last_update": "2025-01-06 01:12:44", "author_account_age_days": 3162}, "https://github.com/leeguandong/ComfyUI_FluxLayerDiffuse": {"stars": 15, "last_update": "2025-03-17 01:07:01", "author_account_age_days": 3162}, "https://github.com/leeguandong/ComfyUI_Gemma3": {"stars": 7, "last_update": "2025-03-25 14:45:01", "author_account_age_days": 3162}, "https://github.com/leeguandong/ComfyUI_InternVL2": {"stars": 13, "last_update": "2024-08-10 11:00:11", "author_account_age_days": 3162}, "https://github.com/leeguandong/ComfyUI_LLaSM": {"stars": 4, "last_update": "2024-08-10 10:58:17", "author_account_age_days": 3162}, "https://github.com/leeguandong/ComfyUI_M3Net": {"stars": 12, "last_update": "2024-08-16 00:03:21", "author_account_age_days": 3162}, "https://github.com/leeguandong/ComfyUI_MasaCtrl": {"stars": 3, "last_update": "2024-09-01 03:47:35", "author_account_age_days": 3162}, "https://github.com/leeguandong/ComfyUI_QWQ32B": {"stars": 2, "last_update": "2025-03-15 17:19:23", "author_account_age_days": 3162}, "https://github.com/leeguandong/ComfyUI_Style_Aligned": {"stars": 5, "last_update": "2024-08-16 11:59:33", "author_account_age_days": 3162}, "https://github.com/leeguandong/ComfyUI_VideoEditing": {"stars": 4, "last_update": "2024-08-14 16:59:49", "author_account_age_days": 3162}, "https://github.com/leeguandong/ComfyUI_VisualAttentionMap": {"stars": 8, "last_update": "2024-08-26 05:15:14", "author_account_age_days": 3162}, "https://github.com/leestuartx/ComfyUI-GG": {"stars": 2, "last_update": "2025-03-10 16:26:37", "author_account_age_days": 4147}, "https://github.com/lenskikh/ComfyUI-Prompt-Worker": {"stars": 11, "last_update": "2025-06-27 06:30:47", "author_account_age_days": 3865}, "https://github.com/leoleelxh/Comfy-Topaz-Photo": {"stars": 13, "last_update": "2025-05-24 05:47:40", "author_account_age_days": 4443}, "https://github.com/leoleelxh/ComfyUI-LLMs": {"stars": 47, "last_update": "2025-06-17 13:52:33", "author_account_age_days": 4443}, "https://github.com/leonardomiramondi/flux-context-comfyui": {"stars": 0, "last_update": "2025-06-25 10:18:42", "author_account_age_days": 787}, "https://github.com/lepiai/ComfyUI-Minitools": {"stars": 8, "last_update": "2025-05-24 16:11:50", "author_account_age_days": 2238}, "https://github.com/lerignoux/ComfyUI-PechaKucha": {"stars": 1, "last_update": "2025-05-12 15:42:19", "author_account_age_days": 4672}, "https://github.com/lgldlk/ComfyUI-PC-ding-dong": {"stars": 65, "last_update": "2024-12-27 03:25:38", "author_account_age_days": 2058}, "https://github.com/lgldlk/ComfyUI-PSD-Replace": {"stars": 4, "last_update": "2025-03-15 07:03:24", "author_account_age_days": 2058}, "https://github.com/liangt/comfyui-loadimagewithsubfolder": {"stars": 3, "last_update": "2025-03-27 16:49:42", "author_account_age_days": 4427}, "https://github.com/licyk/ComfyUI-HakuImg": {"stars": 8, "last_update": "2025-05-04 03:31:32", "author_account_age_days": 1636}, "https://github.com/licyk/ComfyUI-Restart-Sampler": {"stars": 10, "last_update": "2025-02-24 04:53:52", "author_account_age_days": 1636}, "https://github.com/licyk/ComfyUI-TCD-Sampler": {"stars": 4, "last_update": "2025-03-27 16:32:33", "author_account_age_days": 1636}, "https://github.com/lihaoyun6/ComfyUI-CSV-Random-Picker": {"stars": 1, "last_update": "2025-05-10 10:41:53", "author_account_age_days": 3478}, "https://github.com/lilly1987/ComfyUI_node_Lilly": {"stars": 55, "last_update": "2024-12-21 01:50:03", "author_account_age_days": 3278}, "https://github.com/lingha0h/comfyui_kj": {"stars": 6, "last_update": "2025-03-20 13:24:29", "author_account_age_days": 141}, "https://github.com/linjian-ufo/comfyui_deepseek_lj257_update": {"stars": 0, "last_update": "2025-06-17 11:26:32", "author_account_age_days": 465}, "https://github.com/linksluckytime/comfyui_snacknodes": {"stars": 0, "last_update": "2025-05-07 01:48:50", "author_account_age_days": 792}, "https://github.com/linshier/comfyui-remote-tools": {"stars": 4, "last_update": "2024-05-28 07:44:23", "author_account_age_days": 4160}, "https://github.com/lisaks/comfyui-panelforge": {"stars": 1, "last_update": "2025-04-29 00:25:00", "author_account_age_days": 1104}, "https://github.com/liuqianhonga/ComfyUI-Html2Image": {"stars": 9, "last_update": "2025-06-22 07:58:49", "author_account_age_days": 555}, "https://github.com/liuqianhonga/ComfyUI-Image-Compressor": {"stars": 18, "last_update": "2025-06-22 08:32:22", "author_account_age_days": 555}, "https://github.com/liuqianhonga/ComfyUI-QHNodes": {"stars": 3, "last_update": "2025-06-22 08:33:17", "author_account_age_days": 555}, "https://github.com/liuqianhonga/ComfyUI-String-Helper": {"stars": 7, "last_update": "2025-06-22 07:56:48", "author_account_age_days": 555}, "https://github.com/liushuchun/ComfyUI_Lora_List_With_Url_Loader": {"stars": 2, "last_update": "2024-09-26 12:38:32", "author_account_age_days": 4415}, "https://github.com/liusida/ComfyUI-AutoCropFaces": {"stars": 85, "last_update": "2024-08-12 17:38:17", "author_account_age_days": 3576}, "https://github.com/liusida/ComfyUI-B-LoRA": {"stars": 75, "last_update": "2024-06-18 03:17:46", "author_account_age_days": 3576}, "https://github.com/liusida/ComfyUI-Debug": {"stars": 11, "last_update": "2024-06-14 10:25:26", "author_account_age_days": 3576}, "https://github.com/liusida/ComfyUI-Login": {"stars": 171, "last_update": "2024-11-15 01:35:25", "author_account_age_days": 3576}, "https://github.com/liusida/ComfyUI-SD3-nodes": {"stars": 5, "last_update": "2024-06-14 13:01:41", "author_account_age_days": 3576}, "https://github.com/livepeer/ComfyUI-Stream-Pack": {"stars": 8, "last_update": "2025-05-07 12:46:57", "author_account_age_days": 3074}, "https://github.com/ljleb/comfy-mecha": {"stars": 79, "last_update": "2025-06-09 02:40:54", "author_account_age_days": 2832}, "https://github.com/lks-ai/ComfyUI-StableAudioSampler": {"stars": 252, "last_update": "2025-01-07 08:33:57", "author_account_age_days": 468}, "https://github.com/lks-ai/anynode": {"stars": 534, "last_update": "2024-07-07 03:45:48", "author_account_age_days": 468}, "https://github.com/lldacing/ComfyUI_BEN_ll": {"stars": 4, "last_update": "2025-05-22 07:01:42", "author_account_age_days": 2461}, "https://github.com/lldacing/ComfyUI_BiRefNet_ll": {"stars": 224, "last_update": "2025-06-01 16:39:20", "author_account_age_days": 2461}, "https://github.com/lldacing/ComfyUI_Patches_ll": {"stars": 107, "last_update": "2025-04-08 06:22:28", "author_account_age_days": 2461}, "https://github.com/lldacing/ComfyUI_PuLID_Flux_ll": {"stars": 373, "last_update": "2025-04-08 06:21:55", "author_account_age_days": 2461}, "https://github.com/lldacing/ComfyUI_StableDelight_ll": {"stars": 12, "last_update": "2025-04-08 06:22:43", "author_account_age_days": 2461}, "https://github.com/lldacing/ComfyUI_StableHair_ll": {"stars": 65, "last_update": "2025-03-31 09:16:21", "author_account_age_days": 2461}, "https://github.com/lldacing/comfyui-easyapi-nodes": {"stars": 77, "last_update": "2025-05-15 07:06:47", "author_account_age_days": 2461}, "https://github.com/lo-th/Comfyui_three_js": {"stars": 19, "last_update": "2025-06-12 08:18:17", "author_account_age_days": 4848}, "https://github.com/lodestone-rock/ComfyUI_FluxMod": {"stars": 108, "last_update": "2025-06-22 09:44:09", "author_account_age_days": 952}, "https://github.com/logtd/ComfyUI-4DHumans": {"stars": 7, "last_update": "2024-08-30 21:12:55", "author_account_age_days": 490}, "https://github.com/logtd/ComfyUI-APGScaling": {"stars": 29, "last_update": "2024-10-06 20:51:27", "author_account_age_days": 490}, "https://github.com/logtd/ComfyUI-DiLightNet": {"stars": 11, "last_update": "2024-10-06 03:48:15", "author_account_age_days": 490}, "https://github.com/logtd/ComfyUI-FLATTEN": {"stars": 109, "last_update": "2024-08-30 21:18:55", "author_account_age_days": 490}, "https://github.com/logtd/ComfyUI-Fluxtapoz": {"stars": 1344, "last_update": "2025-01-09 02:38:40", "author_account_age_days": 490}, "https://github.com/logtd/ComfyUI-InstanceDiffusion": {"stars": 180, "last_update": "2024-08-30 21:17:51", "author_account_age_days": 490}, "https://github.com/logtd/ComfyUI-InversedNoise": {"stars": 16, "last_update": "2024-05-22 00:10:18", "author_account_age_days": 490}, "https://github.com/logtd/ComfyUI-MochiEdit": {"stars": 294, "last_update": "2024-11-03 18:38:16", "author_account_age_days": 490}, "https://github.com/logtd/ComfyUI-MotionThiefExperiment": {"stars": 41, "last_update": "2024-08-30 21:19:48", "author_account_age_days": 490}, "https://github.com/logtd/ComfyUI-RAVE_ATTN": {"stars": 14, "last_update": "2024-05-22 00:20:03", "author_account_age_days": 490}, "https://github.com/logtd/ComfyUI-ReNoise": {"stars": 6, "last_update": "2024-09-01 22:17:49", "author_account_age_days": 490}, "https://github.com/logtd/ComfyUI-RefSampling": {"stars": 5, "last_update": "2024-09-11 20:56:01", "author_account_age_days": 490}, "https://github.com/logtd/ComfyUI-RefUNet": {"stars": 45, "last_update": "2024-08-30 21:20:20", "author_account_age_days": 490}, "https://github.com/logtd/ComfyUI-SEGAttention": {"stars": 38, "last_update": "2024-09-11 20:55:00", "author_account_age_days": 490}, "https://github.com/logtd/ComfyUI-SSREncoder": {"stars": 1, "last_update": "2024-08-24 23:33:09", "author_account_age_days": 490}, "https://github.com/logtd/ComfyUI-SeeCoder": {"stars": 0, "last_update": "2024-08-24 23:31:10", "author_account_age_days": 490}, "https://github.com/logtd/ComfyUI-TrackingNodes": {"stars": 19, "last_update": "2024-05-22 00:03:27", "author_account_age_days": 490}, "https://github.com/logtd/ComfyUI-ViewCrafter": {"stars": 12, "last_update": "2024-09-30 19:32:41", "author_account_age_days": 490}, "https://github.com/longgui0318/comfyui-common-util": {"stars": 1, "last_update": "2025-04-07 08:19:05", "author_account_age_days": 4533}, "https://github.com/longgui0318/comfyui-llm-assistant": {"stars": 7, "last_update": "2024-09-17 13:12:43", "author_account_age_days": 4533}, "https://github.com/longgui0318/comfyui-magic-clothing": {"stars": 77, "last_update": "2024-08-08 14:42:04", "author_account_age_days": 4533}, "https://github.com/longgui0318/comfyui-mask-util": {"stars": 7, "last_update": "2025-04-07 08:18:11", "author_account_age_days": 4533}, "https://github.com/lord-lethris/ComfyUI-RPG-Characters": {"stars": 1, "last_update": "2025-06-18 23:08:15", "author_account_age_days": 4792}, "https://github.com/lordgasmic/comfyui_save_image_with_options": {"stars": 0, "last_update": "2024-06-20 16:39:23", "author_account_age_days": 5135}, "https://github.com/lordgasmic/comfyui_wildcards": {"stars": 10, "last_update": "2024-06-20 16:52:14", "author_account_age_days": 5135}, "https://github.com/lquesada/ComfyUI-Inpaint-CropAndStitch": {"stars": 729, "last_update": "2025-05-10 07:46:49", "author_account_age_days": 4419}, "https://github.com/lquesada/ComfyUI-Interactive": {"stars": 38, "last_update": "2025-05-01 03:39:47", "author_account_age_days": 4419}, "https://github.com/lquesada/ComfyUI-Prompt-Combinator": {"stars": 38, "last_update": "2025-04-16 20:52:10", "author_account_age_days": 4419}, "https://github.com/lrzjason/ComfyUI-Watermark-Detection": {"stars": 19, "last_update": "2025-05-28 20:46:50", "author_account_age_days": 4031}, "https://github.com/lrzjason/Comfyui-In-Context-Lora-Utils": {"stars": 224, "last_update": "2025-04-03 09:09:43", "author_account_age_days": 4031}, "https://github.com/lrzjason/Comfyui-Kolors-Utils": {"stars": 17, "last_update": "2025-05-05 16:10:11", "author_account_age_days": 4031}, "https://github.com/lrzjason/Comfyui-ThinkRemover": {"stars": 4, "last_update": "2025-02-07 10:57:50", "author_account_age_days": 4031}, "https://github.com/ltdrdata/ComfyUI-Impact-Pack": {"stars": 2526, "last_update": "2025-06-19 03:41:19", "author_account_age_days": 830}, "https://github.com/ltdrdata/ComfyUI-Impact-Subpack": {"stars": 211, "last_update": "2025-06-17 13:31:07", "author_account_age_days": 830}, "https://github.com/ltdrdata/ComfyUI-Inspire-Pack": {"stars": 609, "last_update": "2025-06-17 13:06:13", "author_account_age_days": 830}, "https://github.com/ltdrdata/ComfyUI-Manager": {"stars": 10574, "last_update": "2025-06-27 22:55:23", "author_account_age_days": 443}, "https://github.com/ltdrdata/comfyui-connection-helper": {"stars": 27, "last_update": "2025-04-07 13:49:56", "author_account_age_days": 830}, "https://github.com/ltdrdata/was-node-suite-comfyui": {"stars": 35, "last_update": "2025-06-03 09:41:36", "author_account_age_days": 830}, "https://github.com/lthero-big/ComfyUI-GaussianShadingWatermark": {"stars": 5, "last_update": "2025-03-23 08:18:07", "author_account_age_days": 1761}, "https://github.com/luandev/ComfyUI-CrewAI": {"stars": 53, "last_update": "2025-01-17 18:06:27", "author_account_age_days": 4176}, "https://github.com/lucak5s/comfyui_gfpgan": {"stars": 1, "last_update": "2025-06-25 21:42:30", "author_account_age_days": 1049}, "https://github.com/lucianoambrosini/ComfyUI-ATk-Nodes": {"stars": 2, "last_update": "2025-06-25 05:04:14", "author_account_age_days": 3483}, "https://github.com/lujiazho/ComfyUI-CatvtonFluxWrapper": {"stars": 91, "last_update": "2024-12-02 22:10:41", "author_account_age_days": 1805}, "https://github.com/lum3on/ComfyUI-FrameUtilitys": {"stars": 1, "last_update": "2025-06-09 22:12:49", "author_account_age_days": 142}, "https://github.com/lum3on/ComfyUI-ModelQuantizer": {"stars": 34, "last_update": "2025-06-14 20:45:21", "author_account_age_days": 142}, "https://github.com/lum3on/ComfyUI-StableAudioX": {"stars": 19, "last_update": "2025-06-24 22:55:28", "author_account_age_days": 142}, "https://github.com/lum3on/comfyui_EdgeTAM": {"stars": 0, "last_update": "2025-06-16 10:42:14", "author_account_age_days": 142}, "https://github.com/lum3on/comfyui_LLM_Polymath": {"stars": 63, "last_update": "2025-06-12 22:24:36", "author_account_age_days": 142}, "https://github.com/lumalabs/ComfyUI-LumaAI-API": {"stars": 200, "last_update": "2025-03-31 22:54:28", "author_account_age_days": 1479}, "https://github.com/lunarring/bitalino_comfy": {"stars": 0, "last_update": "2025-02-21 09:03:54", "author_account_age_days": 1610}, "https://github.com/lxe/ComfyUI-OpenAI-Compat-LLM-Node": {"stars": 0, "last_update": "2025-05-28 05:39:55", "author_account_age_days": 4866}, "https://github.com/m-sokes/ComfyUI-Sokes-Nodes": {"stars": 2, "last_update": "2025-06-27 03:40:27", "author_account_age_days": 684}, "https://github.com/madtunebk/ComfyUI-ControlnetAux": {"stars": 15, "last_update": "2024-06-28 16:16:51", "author_account_age_days": 829}, "https://github.com/maepopi/Diffusers-in-ComfyUI": {"stars": 6, "last_update": "2025-03-28 07:29:38", "author_account_age_days": 2726}, "https://github.com/magekinnarus/ComfyUI-V-Prediction-Node": {"stars": 2, "last_update": "2025-02-04 08:29:24", "author_account_age_days": 1000}, "https://github.com/magic-eraser-org/ComfyUI-Unwatermark": {"stars": 1, "last_update": "2025-05-14 06:50:13", "author_account_age_days": 45}, "https://github.com/maludwig/basix_image_filters": {"stars": 5, "last_update": "2025-05-15 23:29:38", "author_account_age_days": 3874}, "https://github.com/mang01010/MangoNodePack": {"stars": 3, "last_update": "2025-05-21 21:53:18", "author_account_age_days": 118}, "https://github.com/mango-rgb/ComfyUI-Mango-Random-node": {"stars": 1, "last_update": "2025-01-21 11:31:10", "author_account_age_days": 771}, "https://github.com/mape/ComfyUI-mape-Helpers": {"stars": 180, "last_update": "2024-06-27 16:30:32", "author_account_age_days": 6122}, "https://github.com/maracman/ComfyUI-SubjectStyle-CSV": {"stars": 4, "last_update": "2024-06-24 13:53:39", "author_account_age_days": 1535}, "https://github.com/marawan206/ComfyUI-FaceCropper": {"stars": 10, "last_update": "2025-03-07 01:44:44", "author_account_age_days": 537}, "https://github.com/marcoc2/ComfyUI-AnotherUtils": {"stars": 1, "last_update": "2024-12-20 04:34:13", "author_account_age_days": 5551}, "https://github.com/marcoc2/ComfyUI_CogView4-6B_diffusers": {"stars": 2, "last_update": "2025-03-04 17:43:50", "author_account_age_days": 5551}, "https://github.com/marduk191/ComfyUI-Fluxpromptenhancer": {"stars": 106, "last_update": "2025-04-03 17:22:55", "author_account_age_days": 4783}, "https://github.com/marduk191/comfyui-marnodes": {"stars": 3, "last_update": "2025-03-27 13:26:45", "author_account_age_days": 4783}, "https://github.com/marhensa/sdxl-recommended-res-calc": {"stars": 87, "last_update": "2025-04-13 09:33:49", "author_account_age_days": 5143}, "https://github.com/marklieberman/ComfyUI-Liebs-Picker": {"stars": 2, "last_update": "2025-05-09 21:16:27", "author_account_age_days": 4173}, "https://github.com/marklieberman/ComfyUI-Liebs-Title": {"stars": 0, "last_update": "2025-05-12 23:32:28", "author_account_age_days": 4173}, "https://github.com/marklieberman/ComfyUI-Liebs-Toast": {"stars": 0, "last_update": "2025-05-12 23:34:55", "author_account_age_days": 4173}, "https://github.com/markuryy/ComfyUI-Flux-Prompt-Saver": {"stars": 12, "last_update": "2024-10-30 10:25:15", "author_account_age_days": 3246}, "https://github.com/markuryy/ComfyUI-Simple-Video-XY-Plot": {"stars": 3, "last_update": "2025-03-12 18:18:54", "author_account_age_days": 3246}, "https://github.com/markuryy/ComfyUI-SuperLoader": {"stars": 0, "last_update": "2025-03-12 18:23:22", "author_account_age_days": 3246}, "https://github.com/martijnat/comfyui-previewlatent": {"stars": 36, "last_update": "2024-05-22 21:28:39", "author_account_age_days": 3200}, "https://github.com/martin-rizzo/ComfyUI-TinyBreaker": {"stars": 35, "last_update": "2025-05-04 00:02:02", "author_account_age_days": 1965}, "https://github.com/massao000/ComfyUI_aspect_ratios": {"stars": 10, "last_update": "2024-05-22 22:23:10", "author_account_age_days": 1775}, "https://github.com/matan1905/ComfyUI-Serving-Toolkit": {"stars": 67, "last_update": "2025-05-01 10:03:33", "author_account_age_days": 3108}, "https://github.com/matorzhin/milan-nodes-comfyui": {"stars": 0, "last_update": "2025-06-05 16:37:41", "author_account_age_days": 3020}, "https://github.com/mattjohnpowell/comfyui-lmstudio-image-to-text-node": {"stars": 17, "last_update": "2025-06-22 11:06:40", "author_account_age_days": 4918}, "https://github.com/mav-rik/facerestore_cf": {"stars": 277, "last_update": "2025-06-21 08:02:17", "author_account_age_days": 3289}, "https://github.com/mbrostami/ComfyUI-HF": {"stars": 19, "last_update": "2024-05-27 21:45:33", "author_account_age_days": 4690}, "https://github.com/mbrostami/ComfyUI-TITrain": {"stars": 8, "last_update": "2025-03-14 17:39:11", "author_account_age_days": 4690}, "https://github.com/mcmonkeyprojects/sd-dynamic-thresholding": {"stars": 1209, "last_update": "2025-03-14 09:33:32", "author_account_age_days": 2470}, "https://github.com/meanin2/comfyui-MGnodes": {"stars": 2, "last_update": "2025-01-24 07:32:08", "author_account_age_days": 1022}, "https://github.com/meap158/ComfyUI-Background-Replacement": {"stars": 59, "last_update": "2025-01-06 23:45:28", "author_account_age_days": 3571}, "https://github.com/meap158/ComfyUI-GPU-temperature-protection": {"stars": 3, "last_update": "2024-05-22 20:43:21", "author_account_age_days": 3571}, "https://github.com/meap158/ComfyUI-Prompt-Expansion": {"stars": 77, "last_update": "2024-05-22 20:43:37", "author_account_age_days": 3571}, "https://github.com/mech-tools/comfyui-checkpoint-automatic-config": {"stars": 3, "last_update": "2024-09-05 14:23:29", "author_account_age_days": 4808}, "https://github.com/mediocreatmybest/ComfyUI-Transformers-Pipeline": {"stars": 4, "last_update": "2025-02-24 15:11:36", "author_account_age_days": 1569}, "https://github.com/melMass/comfy_mtb": {"stars": 577, "last_update": "2025-06-26 15:33:45", "author_account_age_days": 4113}, "https://github.com/melMass/comfy_oiio": {"stars": 4, "last_update": "2025-04-14 20:24:37", "author_account_age_days": 4113}, "https://github.com/mephisto83/petty-paint-comfyui-node": {"stars": 3, "last_update": "2024-10-23 22:23:03", "author_account_age_days": 4041}, "https://github.com/meshmesh-io/ComfyUI-MeshMesh": {"stars": 0, "last_update": "2024-05-23 00:10:09", "author_account_age_days": 597}, "https://github.com/meshmesh-io/mm-comfyui-loopback": {"stars": 1, "last_update": "2024-05-23 00:09:57", "author_account_age_days": 597}, "https://github.com/meshmesh-io/mm-comfyui-megamask": {"stars": 0, "last_update": "2024-05-23 00:09:47", "author_account_age_days": 597}, "https://github.com/metal3d/ComfyUI_Human_Parts": {"stars": 34, "last_update": "2025-03-07 08:14:46", "author_account_age_days": 5842}, "https://github.com/metal3d/ComfyUI_M3D_photo_effects": {"stars": 3, "last_update": "2025-03-11 12:09:55", "author_account_age_days": 5842}, "https://github.com/metncelik/comfyui_met_suite": {"stars": 2, "last_update": "2025-03-27 12:27:48", "author_account_age_days": 991}, "https://github.com/mfg637/ComfyUI-ScheduledGuider-Ext": {"stars": 2, "last_update": "2025-06-08 14:30:43", "author_account_age_days": 2686}, "https://github.com/mgfxer/ComfyUI-FrameFX": {"stars": 23, "last_update": "2024-07-20 13:58:46", "author_account_age_days": 370}, "https://github.com/miaoshouai/ComfyUI-Miaoshouai-Tagger": {"stars": 417, "last_update": "2025-04-26 02:32:18", "author_account_age_days": 836}, "https://github.com/michaelgold/ComfyUI-HF-Model-Downloader": {"stars": 2, "last_update": "2025-06-09 15:02:48", "author_account_age_days": 5724}, "https://github.com/microbote/ComfyUI-StyledCLIPTextEncode": {"stars": 2, "last_update": "2024-08-27 03:37:29", "author_account_age_days": 2376}, "https://github.com/mihaiiancu/ComfyUI_Inpaint": {"stars": 9, "last_update": "2024-05-22 18:19:38", "author_account_age_days": 3030}, "https://github.com/mikebilly/Transparent-background-comfyUI": {"stars": 2, "last_update": "2025-01-29 16:29:23", "author_account_age_days": 2932}, "https://github.com/mikkel/ComfyUI-text-overlay": {"stars": 57, "last_update": "2024-08-17 16:09:41", "author_account_age_days": 6285}, "https://github.com/mikkel/comfyui-mask-boundingbox": {"stars": 29, "last_update": "2024-05-22 21:26:23", "author_account_age_days": 6285}, "https://github.com/mingsky-ai/ComfyUI-MingNodes": {"stars": 400, "last_update": "2024-10-18 16:51:14", "author_account_age_days": 289}, "https://github.com/mira-6/comfyui-sasolver": {"stars": 3, "last_update": "2025-02-23 21:44:23", "author_account_age_days": 760}, "https://github.com/mirabarukaso/ComfyUI_Mira": {"stars": 130, "last_update": "2025-06-20 14:01:41", "author_account_age_days": 1589}, "https://github.com/misterjoessef/MLTask_ComfyUI": {"stars": 0, "last_update": "2024-08-17 16:45:24", "author_account_age_days": 1106}, "https://github.com/mit-han-lab/ComfyUI-nunchaku": {"stars": 1317, "last_update": "2025-06-18 20:11:50", "author_account_age_days": 2591}, "https://github.com/mittimi/ComfyUI_mittimiLoadPreset2": {"stars": 4, "last_update": "2025-06-13 16:52:53", "author_account_age_days": 4378}, "https://github.com/mittimi/ComfyUI_mittimiRecalculateSize": {"stars": 0, "last_update": "2024-09-07 07:43:41", "author_account_age_days": 4378}, "https://github.com/mittimi/ComfyUI_mittimiWidthHeight": {"stars": 1, "last_update": "2024-09-07 07:48:03", "author_account_age_days": 4378}, "https://github.com/mo230761/InsertAnything-ComfyUI-official": {"stars": 16, "last_update": "2025-06-04 13:23:00", "author_account_age_days": 1342}, "https://github.com/mobilehacker/ComfyUI_format-lora-stack": {"stars": 3, "last_update": "2025-04-04 19:45:39", "author_account_age_days": 4176}, "https://github.com/modelscope/comfyscope": {"stars": 4, "last_update": "2024-11-20 08:48:36", "author_account_age_days": 1068}, "https://github.com/modelscope/scepter": {"stars": 524, "last_update": "2025-04-03 06:00:15", "author_account_age_days": 1068}, "https://github.com/modusCell/ComfyUI-dimension-node-modusCell": {"stars": 1, "last_update": "2024-05-22 22:08:50", "author_account_age_days": 4972}, "https://github.com/mohseni-mr/ComfyUI-Mohseni-Kit": {"stars": 1, "last_update": "2025-02-17 07:14:46", "author_account_age_days": 1082}, "https://github.com/mohsensd1373/comfyui_wordpress": {"stars": 0, "last_update": "2025-05-08 02:25:36", "author_account_age_days": 4220}, "https://github.com/monkeyWie/ComfyUI-FormInput": {"stars": 0, "last_update": "2025-05-12 03:47:39", "author_account_age_days": 3646}, "https://github.com/moon7star9/ComfyUI_BiRefNet_Universal": {"stars": 19, "last_update": "2025-02-26 03:01:29", "author_account_age_days": 775}, "https://github.com/moose-lab/ComfyUI-GPT": {"stars": 4, "last_update": "2025-04-12 07:59:29", "author_account_age_days": 153}, "https://github.com/morgan55555/comfyui-lock-mode": {"stars": 0, "last_update": "2025-04-28 16:16:18", "author_account_age_days": 3552}, "https://github.com/morino-kumasan/comfyui-toml-prompt": {"stars": 0, "last_update": "2025-06-24 09:05:15", "author_account_age_days": 1692}, "https://github.com/motivated3/comfyui-shua-creator": {"stars": 6, "last_update": "2024-12-05 10:39:52", "author_account_age_days": 3173}, "https://github.com/moustafa-nasr/ComfyUI-SimpleLogger": {"stars": 4, "last_update": "2025-06-07 08:30:19", "author_account_age_days": 3839}, "https://github.com/moyi7712/ComfyUI_Seamless_Patten": {"stars": 17, "last_update": "2025-03-19 10:35:44", "author_account_age_days": 2669}, "https://github.com/mozman/ComfyUI_mozman_nodes": {"stars": 0, "last_update": "2024-05-22 22:13:32", "author_account_age_days": 4448}, "https://github.com/mr7thing/circle_pattern_processor": {"stars": 0, "last_update": "2025-03-02 19:24:26", "author_account_age_days": 504}, "https://github.com/mrchipset/ComfyUI-SaveImageS3": {"stars": 1, "last_update": "2025-04-07 00:27:45", "author_account_age_days": 2678}, "https://github.com/mrhan1993/ComfyUI-Fooocus": {"stars": 8, "last_update": "2025-01-15 15:18:07", "author_account_age_days": 2234}, "https://github.com/muhammederem/blip-comfyui": {"stars": 1, "last_update": "2025-05-25 14:11:04", "author_account_age_days": 2462}, "https://github.com/mullakhmetov/comfyui_dynamic_util_nodes": {"stars": 0, "last_update": "2024-07-15 14:13:58", "author_account_age_days": 4306}, "https://github.com/muxueChen/ComfyUI_NTCosyVoice": {"stars": 148, "last_update": "2025-05-20 13:36:56", "author_account_age_days": 3328}, "https://github.com/muzi12888/ComfyUI-PoseKeypoint-Mask": {"stars": 10, "last_update": "2025-03-15 00:23:20", "author_account_age_days": 3314}, "https://github.com/my-opencode/ComfyUI_IndustrialMagick": {"stars": 1, "last_update": "2024-07-31 14:04:26", "author_account_age_days": 1748}, "https://github.com/my-opencode/ComfyUI_KSamplerTimer": {"stars": 2, "last_update": "2024-07-31 14:13:17", "author_account_age_days": 1748}, "https://github.com/myshell-ai/ComfyUI-ShellAgent-Plugin": {"stars": 22, "last_update": "2025-05-22 06:54:44", "author_account_age_days": 837}, "https://github.com/n0neye/A3D-comfyui-integration": {"stars": 6, "last_update": "2025-04-28 03:54:34", "author_account_age_days": 1191}, "https://github.com/nagolinc/ComfyUI_FastVAEDecorder_SDXL": {"stars": 4, "last_update": "2024-07-19 14:46:14", "author_account_age_days": 4042}, "https://github.com/nagolinc/comfyui_openai_node": {"stars": 1, "last_update": "2024-06-15 15:59:07", "author_account_age_days": 4042}, "https://github.com/nako-nakoko/ComfyUI_Mel_Nodes": {"stars": 0, "last_update": "2025-04-26 22:48:50", "author_account_age_days": 83}, "https://github.com/narusas/Comfyui-Logic-Support": {"stars": 0, "last_update": "2025-05-30 04:44:16", "author_account_age_days": 5001}, "https://github.com/nat-chan/ComfyUI-graphToPrompt": {"stars": 2, "last_update": "2024-05-23 01:16:40", "author_account_age_days": 3361}, "https://github.com/nat-chan/comfyui-transceiver": {"stars": 5, "last_update": "2024-05-23 01:16:28", "author_account_age_days": 3361}, "https://github.com/nathannlu/ComfyUI-Cloud": {"stars": 199, "last_update": "2024-07-31 18:05:55", "author_account_age_days": 3094}, "https://github.com/nathannlu/ComfyUI-Pets": {"stars": 47, "last_update": "2024-06-14 11:00:42", "author_account_age_days": 3094}, "https://github.com/natto-maki/ComfyUI-NegiTools": {"stars": 31, "last_update": "2024-09-15 05:11:18", "author_account_age_days": 646}, "https://github.com/nchenevey1/comfyui-gimp-nodes": {"stars": 9, "last_update": "2024-10-26 09:11:34", "author_account_age_days": 1018}, "https://github.com/neggo/comfyui-sambanova": {"stars": 0, "last_update": "2025-05-15 01:49:53", "author_account_age_days": 4302}, "https://github.com/neocrz/comfyui-usetaesd": {"stars": 0, "last_update": "2025-06-14 18:58:39", "author_account_age_days": 1687}, "https://github.com/neph1/comfyui-smooth-step-lora-loader": {"stars": 6, "last_update": "2025-04-06 10:43:14", "author_account_age_days": 4020}, "https://github.com/neverbiasu/ComfyUI-BAGEL": {"stars": 162, "last_update": "2025-06-19 18:12:50", "author_account_age_days": 1387}, "https://github.com/neverbiasu/ComfyUI-ChatTTS": {"stars": 3, "last_update": "2025-05-12 08:15:13", "author_account_age_days": 1387}, "https://github.com/neverbiasu/ComfyUI-Dashscope": {"stars": 2, "last_update": "2025-04-05 02:19:36", "author_account_age_days": 1387}, "https://github.com/neverbiasu/ComfyUI-Image-Captioner": {"stars": 13, "last_update": "2025-05-12 16:09:03", "author_account_age_days": 1387}, "https://github.com/neverbiasu/ComfyUI-SAM2": {"stars": 185, "last_update": "2025-05-13 12:38:09", "author_account_age_days": 1387}, "https://github.com/neverbiasu/ComfyUI-StyleShot": {"stars": 12, "last_update": "2025-04-23 08:01:32", "author_account_age_days": 1387}, "https://github.com/newtextdoc1111/ComfyUI-Autocomplete-Plus": {"stars": 17, "last_update": "2025-06-14 08:51:48", "author_account_age_days": 103}, "https://github.com/ngosset/ComfyUI-ImageSimilarity": {"stars": 7, "last_update": "2025-01-18 18:17:50", "author_account_age_days": 4689}, "https://github.com/nicehero/comfyui-SegGPT": {"stars": 5, "last_update": "2024-08-26 06:05:35", "author_account_age_days": 4378}, "https://github.com/nickve28/ComfyUI-Nich-Utils": {"stars": 8, "last_update": "2025-06-19 10:15:26", "author_account_age_days": 4397}, "https://github.com/nicofdga/DZ-FaceDetailer": {"stars": 202, "last_update": "2024-06-17 10:00:30", "author_account_age_days": 1599}, "https://github.com/niknah/ComfyUI-F5-TTS": {"stars": 204, "last_update": "2025-06-13 12:27:03", "author_account_age_days": 5088}, "https://github.com/niknah/ComfyUI-Hunyuan-3D-2": {"stars": 52, "last_update": "2025-06-16 11:23:16", "author_account_age_days": 5088}, "https://github.com/niknah/ComfyUI-InfiniteYou": {"stars": 12, "last_update": "2025-04-16 08:44:22", "author_account_age_days": 5088}, "https://github.com/niknah/audio-general-ComfyUI": {"stars": 0, "last_update": "2025-05-28 02:51:53", "author_account_age_days": 5088}, "https://github.com/niknah/quick-connections": {"stars": 282, "last_update": "2025-03-27 22:16:29", "author_account_age_days": 5088}, "https://github.com/nilor-corp/nilor-nodes": {"stars": 3, "last_update": "2025-05-20 14:44:55", "author_account_age_days": 583}, "https://github.com/ningxiaoxiao/comfyui-NDI": {"stars": 59, "last_update": "2025-04-11 03:55:37", "author_account_age_days": 3356}, "https://github.com/nirbhay-faaya/ImgProcessing_ComfyUI": {"stars": 0, "last_update": "2024-07-31 08:34:48", "author_account_age_days": 704}, "https://github.com/nirex0/ComfyUI_pytorch_openpose": {"stars": 2, "last_update": "2024-06-14 12:01:07", "author_account_age_days": 3871}, "https://github.com/nisaruj/comfyui-daam": {"stars": 21, "last_update": "2025-06-08 12:41:49", "author_account_age_days": 3575}, "https://github.com/nisimjoseph/ComfyUI_OpenAI-Prompter": {"stars": 4, "last_update": "2025-01-18 19:57:31", "author_account_age_days": 4674}, "https://github.com/nkchocoai/ComfyUI-DanbooruPromptQuiz": {"stars": 0, "last_update": "2025-03-30 08:30:33", "author_account_age_days": 528}, "https://github.com/nkchocoai/ComfyUI-Dart": {"stars": 26, "last_update": "2025-03-30 08:19:01", "author_account_age_days": 528}, "https://github.com/nkchocoai/ComfyUI-PromptUtilities": {"stars": 19, "last_update": "2025-03-30 08:19:25", "author_account_age_days": 528}, "https://github.com/nkchocoai/ComfyUI-SaveImageWithMetaData": {"stars": 80, "last_update": "2025-03-30 08:19:20", "author_account_age_days": 528}, "https://github.com/nkchocoai/ComfyUI-SizeFromPresets": {"stars": 7, "last_update": "2025-03-30 08:19:30", "author_account_age_days": 528}, "https://github.com/nkchocoai/ComfyUI-TextOnSegs": {"stars": 12, "last_update": "2025-03-30 08:19:45", "author_account_age_days": 528}, "https://github.com/nobrainX2/comfyUI-customDia": {"stars": 13, "last_update": "2025-05-29 18:32:25", "author_account_age_days": 2154}, "https://github.com/noembryo/ComfyUI-noEmbryo": {"stars": 25, "last_update": "2025-05-11 19:04:36", "author_account_age_days": 3111}, "https://github.com/nofunstudio/Node_Fun_ComfyUI": {"stars": 2, "last_update": "2025-06-25 20:44:43", "author_account_age_days": 1580}, "https://github.com/nonnonstop/comfyui-faster-loading": {"stars": 10, "last_update": "2024-06-13 15:37:45", "author_account_age_days": 2490}, "https://github.com/northumber/ComfyUI-northTools": {"stars": 2, "last_update": "2025-05-22 18:08:04", "author_account_age_days": 3427}, "https://github.com/nosiu/comfyui-instantId-faceswap": {"stars": 237, "last_update": "2025-06-27 11:36:33", "author_account_age_days": 4273}, "https://github.com/nosiu/comfyui-text-randomizer": {"stars": 0, "last_update": "2025-03-03 01:40:12", "author_account_age_days": 4273}, "https://github.com/noxinias/ComfyUI_NoxinNodes": {"stars": 11, "last_update": "2024-05-22 21:24:24", "author_account_age_days": 2929}, "https://github.com/nsdtcloud3d/ComfyUI-3D-Convert": {"stars": 13, "last_update": "2024-12-23 07:46:17", "author_account_age_days": 442}, "https://github.com/ntc-ai/ComfyUI-DARE-LoRA-Merge": {"stars": 33, "last_update": "2024-05-22 22:22:14", "author_account_age_days": 2086}, "https://github.com/nuanarchy/ComfyUI-NuA-BIRD": {"stars": 8, "last_update": "2024-06-18 05:35:49", "author_account_age_days": 1460}, "https://github.com/nuanarchy/ComfyUI-NuA-FlashFace": {"stars": 24, "last_update": "2024-07-31 13:54:00", "author_account_age_days": 1460}, "https://github.com/nullquant/ComfyUI-BrushNet": {"stars": 888, "last_update": "2025-03-31 08:45:34", "author_account_age_days": 1545}, "https://github.com/numz/ComfyUI-FlowChain": {"stars": 151, "last_update": "2025-05-19 01:51:02", "author_account_age_days": 5152}, "https://github.com/numz/ComfyUI-SeedVR2_VideoUpscaler": {"stars": 86, "last_update": "2025-06-24 15:14:59", "author_account_age_days": 5151}, "https://github.com/numz/Comfyui-Orpheus": {"stars": 8, "last_update": "2025-04-16 19:20:21", "author_account_age_days": 5152}, "https://github.com/nux1111/ComfyUI_NetDist_Plus": {"stars": 31, "last_update": "2024-08-27 23:15:18", "author_account_age_days": 921}, "https://github.com/o-l-l-i/ComfyUI-Olm-CurveEditor": {"stars": 19, "last_update": "2025-06-24 17:41:52", "author_account_age_days": 2840}, "https://github.com/o-l-l-i/ComfyUI-Olm-Resolution-Picker": {"stars": 9, "last_update": "2025-06-24 18:05:33", "author_account_age_days": 2840}, "https://github.com/o-l-l-i/ComfyUI-OlmLUT": {"stars": 6, "last_update": "2025-06-09 18:55:46", "author_account_age_days": 2840}, "https://github.com/okgo4/ComfyUI-Mosaic-Mask": {"stars": 6, "last_update": "2025-04-03 09:41:53", "author_account_age_days": 3055}, "https://github.com/olduvai-jp/ComfyUI-CloudArchive": {"stars": 2, "last_update": "2025-04-15 07:18:38", "author_account_age_days": 1244}, "https://github.com/olduvai-jp/ComfyUI-HfLoader": {"stars": 4, "last_update": "2025-02-13 17:05:40", "author_account_age_days": 1244}, "https://github.com/oleksandr612/ComfyUI-Counter": {"stars": 0, "last_update": "2024-08-05 16:18:48", "author_account_age_days": 330}, "https://github.com/olivv-cs/ComfyUI-FunPack": {"stars": 1, "last_update": "2025-06-13 19:02:21", "author_account_age_days": 779}, "https://github.com/omar92/ComfyUI-QualityOfLifeSuit_Omar92": {"stars": 156, "last_update": "2024-09-10 14:16:30", "author_account_age_days": 4875}, "https://github.com/openvino-dev-samples/comfyui_openvino": {"stars": 4, "last_update": "2025-06-17 06:19:44", "author_account_age_days": 1373}, "https://github.com/opvelll/ComfyUI_TextListProduct": {"stars": 1, "last_update": "2024-10-30 16:00:09", "author_account_age_days": 1923}, "https://github.com/orange90/ComfyUI-Regex-Runner": {"stars": 3, "last_update": "2025-02-26 03:48:27", "author_account_age_days": 4464}, "https://github.com/orex2121/comfyui-OreX": {"stars": 5, "last_update": "2025-04-21 04:40:56", "author_account_age_days": 1874}, "https://github.com/orion4d/ComfyUI-Image-Effects": {"stars": 20, "last_update": "2025-05-28 00:37:16", "author_account_age_days": 949}, "https://github.com/orion4d/ComfyUI_extract_imag": {"stars": 0, "last_update": "2025-06-11 13:37:18", "author_account_age_days": 949}, "https://github.com/orion4d/ComfyUI_pdf_nodes": {"stars": 0, "last_update": "2025-06-10 15:51:53", "author_account_age_days": 949}, "https://github.com/orion4d/illusion_node": {"stars": 0, "last_update": "2025-06-22 08:57:01", "author_account_age_days": 949}, "https://github.com/orssorbit/ComfyUI-wanBlockswap": {"stars": 41, "last_update": "2025-03-19 12:56:23", "author_account_age_days": 3396}, "https://github.com/oshtz/ComfyUI-oshtz-nodes": {"stars": 5, "last_update": "2025-05-22 09:55:47", "author_account_age_days": 793}, "https://github.com/osi1880vr/prompt_quill_comfyui": {"stars": 18, "last_update": "2025-01-27 10:43:16", "author_account_age_days": 1445}, "https://github.com/ostris/ComfyUI-FlexTools": {"stars": 68, "last_update": "2025-04-21 23:12:58", "author_account_age_days": 2771}, "https://github.com/ostris/ostris_nodes_comfyui": {"stars": 30, "last_update": "2025-04-16 17:03:53", "author_account_age_days": 2771}, "https://github.com/otacoo/comfyui_otacoo": {"stars": 2, "last_update": "2025-06-05 23:09:32", "author_account_age_days": 64}, "https://github.com/ownimage/ComfyUI-ownimage": {"stars": 0, "last_update": "2024-05-22 22:22:37", "author_account_age_days": 3151}, "https://github.com/oxysoft/ComfyUI-gowiththeflow": {"stars": 3, "last_update": "2025-04-09 03:55:00", "author_account_age_days": 4479}, "https://github.com/oyvindg/ComfyUI-TrollSuite": {"stars": 4, "last_update": "2024-08-15 10:37:43", "author_account_age_days": 2698}, "https://github.com/oztrkoguz/ComfyUI_StoryCreator": {"stars": 29, "last_update": "2025-04-07 08:30:38", "author_account_age_days": 1210}, "https://github.com/p1atdev/comfyui-timm-backbone": {"stars": 1, "last_update": "2025-05-31 04:03:07", "author_account_age_days": 1982}, "https://github.com/palant/image-resize-comfyui": {"stars": 95, "last_update": "2024-01-18 20:59:55", "author_account_age_days": 5421}, "https://github.com/palant/integrated-nodes-comfyui": {"stars": 39, "last_update": "2023-12-27 22:52:00", "author_account_age_days": 5421}, "https://github.com/pamparamm/ComfyUI-ppm": {"stars": 196, "last_update": "2025-06-13 16:23:47", "author_account_age_days": 2499}, "https://github.com/pamparamm/ComfyUI-vectorscope-cc": {"stars": 19, "last_update": "2025-02-24 21:59:04", "author_account_age_days": 2499}, "https://github.com/pamparamm/sd-perturbed-attention": {"stars": 262, "last_update": "2025-06-24 00:09:09", "author_account_age_days": 2499}, "https://github.com/pants007/comfy-pants": {"stars": 2, "last_update": "2024-05-22 18:16:04", "author_account_age_days": 2685}, "https://github.com/papcorns/ComfyUI-Papcorns-Node-LoadImageFromUrl": {"stars": 1, "last_update": "2025-05-26 12:33:08", "author_account_age_days": 1880}, "https://github.com/pathway8-sudo/ComfyUI-Pathway-CutPNG-Node": {"stars": 0, "last_update": "2025-03-03 07:47:31", "author_account_age_days": 205}, "https://github.com/patriciogonzalezvivo/comfyui_glslnodes": {"stars": 220, "last_update": "2025-05-05 15:00:47", "author_account_age_days": 5448}, "https://github.com/paulo-coronado/comfy_clip_blip_node": {"stars": 29, "last_update": "2024-05-22 17:39:09", "author_account_age_days": 3055}, "https://github.com/pawelmal0101/ComfyUI-Webhook": {"stars": 0, "last_update": "2025-06-11 10:36:58", "author_account_age_days": 1029}, "https://github.com/pbpbpb2705/ComfyUI-LyraVSIH": {"stars": 0, "last_update": "2024-08-30 07:52:11", "author_account_age_days": 1598}, "https://github.com/penposs/ComfyUI_Gemini_Pro": {"stars": 7, "last_update": "2025-04-24 07:54:42", "author_account_age_days": 2125}, "https://github.com/penposs/Comfyui_wan_api": {"stars": 1, "last_update": "2025-04-02 16:02:44", "author_account_age_days": 2125}, "https://github.com/pharmapsychotic/comfy-cliption": {"stars": 52, "last_update": "2025-01-04 05:06:11", "author_account_age_days": 1282}, "https://github.com/phazei/ComfyUI-Prompt-Stash": {"stars": 14, "last_update": "2025-05-16 02:13:34", "author_account_age_days": 5377}, "https://github.com/philiprodriguez/ComfyUI-HunyuanImageLatentToVideoLatent": {"stars": 1, "last_update": "2025-01-12 16:43:09", "author_account_age_days": 3366}, "https://github.com/philipy1219/ComfyUI-TaylorSeer": {"stars": 26, "last_update": "2025-05-25 09:35:25", "author_account_age_days": 3626}, "https://github.com/philz1337x/ComfyUI-ClarityAI": {"stars": 182, "last_update": "2025-04-24 09:51:25", "author_account_age_days": 1032}, "https://github.com/phineas-pta/comfyui-auto-nodes-layout": {"stars": 46, "last_update": "2024-08-02 17:31:24", "author_account_age_days": 2657}, "https://github.com/phuvinh010701/ComfyUI-Nudenet": {"stars": 24, "last_update": "2025-05-01 01:46:07", "author_account_age_days": 2041}, "https://github.com/phyblas/paint-by-example_comfyui": {"stars": 9, "last_update": "2025-03-28 22:27:45", "author_account_age_days": 3421}, "https://github.com/pictorialink/ComfyUI-Custom-Node-Config": {"stars": 0, "last_update": "2025-06-13 02:46:08", "author_account_age_days": 43}, "https://github.com/pictorialink/ComfyUI-Text-Translation": {"stars": 2, "last_update": "2025-06-06 02:56:11", "author_account_age_days": 43}, "https://github.com/picturesonpictures/comfy_PoP": {"stars": 21, "last_update": "2025-06-05 03:53:18", "author_account_age_days": 955}, "https://github.com/pikenrover/ComfyUI_PRNodes": {"stars": 2, "last_update": "2025-04-03 13:31:42", "author_account_age_days": 338}, "https://github.com/pixelworldai/ComfyUI-AlphaFlatten": {"stars": 1, "last_update": "2025-03-13 23:07:04", "author_account_age_days": 339}, "https://github.com/pkpkTech/ComfyUI-SaveAVIF": {"stars": 2, "last_update": "2025-02-01 16:29:22", "author_account_age_days": 1871}, "https://github.com/pkpkTech/ComfyUI-SaveQueues": {"stars": 5, "last_update": "2024-05-22 22:19:54", "author_account_age_days": 1871}, "https://github.com/pkpkTech/ComfyUI-TemporaryLoader": {"stars": 2, "last_update": "2024-05-22 22:19:44", "author_account_age_days": 1871}, "https://github.com/pkpkTech/ComfyUI-ngrok": {"stars": 5, "last_update": "2024-05-22 22:19:32", "author_account_age_days": 1871}, "https://github.com/playbook3d/playbook3d-comfyui-nodes": {"stars": 21, "last_update": "2025-03-25 19:50:08", "author_account_age_days": 1868}, "https://github.com/plugcrypt/CRT-Nodes": {"stars": 5, "last_update": "2025-06-20 04:32:25", "author_account_age_days": 1430}, "https://github.com/pnikolic-amd/ComfyUI_MIGraphX": {"stars": 5, "last_update": "2025-06-24 12:55:12", "author_account_age_days": 168}, "https://github.com/pollockjj/ComfyUI-MultiGPU": {"stars": 324, "last_update": "2025-04-17 23:43:02", "author_account_age_days": 3874}, "https://github.com/portu-sim/comfyui_bmab": {"stars": 107, "last_update": "2025-02-23 12:32:27", "author_account_age_days": 690}, "https://github.com/prodogape/ComfyUI-EasyOCR": {"stars": 36, "last_update": "2024-08-05 07:03:20", "author_account_age_days": 1398}, "https://github.com/prodogape/ComfyUI-Minio": {"stars": 6, "last_update": "2024-05-23 00:13:38", "author_account_age_days": 1398}, "https://github.com/prodogape/ComfyUI-OmDet": {"stars": 3, "last_update": "2024-06-14 13:01:34", "author_account_age_days": 1398}, "https://github.com/prodogape/Comfyui-Yolov8-JSON": {"stars": 24, "last_update": "2024-08-28 02:10:39", "author_account_age_days": 1398}, "https://github.com/prozacgod/comfyui-pzc-multiworkspace": {"stars": 7, "last_update": "2024-05-22 23:11:46", "author_account_age_days": 5936}, "https://github.com/pschroedl/ComfyUI-SAM2-Realtime": {"stars": 14, "last_update": "2025-01-21 05:29:03", "author_account_age_days": 4356}, "https://github.com/ptmaster/comfyui-audio-speed": {"stars": 6, "last_update": "2025-06-24 15:23:03", "author_account_age_days": 4248}, "https://github.com/puke3615/ComfyUI-OneAPI": {"stars": 1, "last_update": "2025-06-26 11:14:43", "author_account_age_days": 3882}, "https://github.com/pupba/Comfy_ForEach": {"stars": 1, "last_update": "2025-05-12 07:08:54", "author_account_age_days": 2168}, "https://github.com/purewater2011/comfyui_color_detection": {"stars": 1, "last_update": "2025-05-19 09:59:44", "author_account_age_days": 4196}, "https://github.com/purpen/ComfyUI-AIRedoon": {"stars": 2, "last_update": "2024-12-11 09:38:42", "author_account_age_days": 5306}, "https://github.com/purpen/ComfyUI-ImageTagger": {"stars": 2, "last_update": "2024-11-27 17:20:49", "author_account_age_days": 5306}, "https://github.com/pxl-pshr/GlitchNodes": {"stars": 49, "last_update": "2025-06-19 22:39:20", "author_account_age_days": 253}, "https://github.com/pydn/ComfyUI-to-Python-Extension": {"stars": 1859, "last_update": "2025-01-14 17:03:18", "author_account_age_days": 3065}, "https://github.com/pythongosssss/ComfyUI-Custom-Scripts": {"stars": 2512, "last_update": "2025-04-30 12:00:10", "author_account_age_days": 866}, "https://github.com/pythongosssss/ComfyUI-WD14-Tagger": {"stars": 889, "last_update": "2025-05-04 08:39:13", "author_account_age_days": 866}, "https://github.com/pzc163/Comfyui-CatVTON": {"stars": 164, "last_update": "2024-10-03 12:50:42", "author_account_age_days": 1145}, "https://github.com/pzc163/Comfyui_MiniCPMv2_6-prompt-generator": {"stars": 79, "last_update": "2024-08-30 08:37:48", "author_account_age_days": 1145}, "https://github.com/quank123wip/ComfyUI-Step1X-Edit": {"stars": 75, "last_update": "2025-04-30 11:03:51", "author_account_age_days": 2871}, "https://github.com/quasiblob/ComfyUI-EsesCompositionGuides": {"stars": 5, "last_update": "2025-06-22 13:43:41", "author_account_age_days": 3655}, "https://github.com/quasiblob/ComfyUI-EsesImageAdjustments": {"stars": 26, "last_update": "2025-06-26 22:46:03", "author_account_age_days": 3655}, "https://github.com/quasiblob/ComfyUI-EsesImageOffset": {"stars": 1, "last_update": "2025-06-26 12:56:51", "author_account_age_days": 3655}, "https://github.com/qwixiwp/queuetools": {"stars": 0, "last_update": "2024-06-14 10:27:57", "author_account_age_days": 979}, "https://github.com/r-vage/ComfyUI-RvTools_v2": {"stars": 4, "last_update": "2025-06-10 06:43:50", "author_account_age_days": 40}, "https://github.com/r3dial/redial-discomphy": {"stars": 1, "last_update": "2025-01-09 19:59:31", "author_account_age_days": 799}, "https://github.com/r3dsd/comfyui-template-loader": {"stars": 0, "last_update": "2025-01-12 08:55:49", "author_account_age_days": 508}, "https://github.com/raindrop313/ComfyUI-WanVideoStartEndFrames": {"stars": 353, "last_update": "2025-03-22 09:59:11", "author_account_age_days": 1433}, "https://github.com/raindrop313/ComfyUI_SD3_Flowedit": {"stars": 6, "last_update": "2025-02-06 19:02:52", "author_account_age_days": 1433}, "https://github.com/rainlizard/ComfyUI-Raffle": {"stars": 4, "last_update": "2025-06-14 23:56:29", "author_account_age_days": 3530}, "https://github.com/rakki194/ComfyUI-ImageCompare": {"stars": 0, "last_update": "2025-05-05 21:00:58", "author_account_age_days": 145}, "https://github.com/ramesh-x90/ComfyUI_pyannote": {"stars": 3, "last_update": "2024-11-23 09:42:16", "author_account_age_days": 1689}, "https://github.com/ramyma/A8R8_ComfyUI_nodes": {"stars": 62, "last_update": "2024-12-09 16:06:25", "author_account_age_days": 3585}, "https://github.com/randjtw/advance-aesthetic-score": {"stars": 0, "last_update": "2024-05-23 01:14:47", "author_account_age_days": 1129}, "https://github.com/randomnoner11/ComfyUI-MistralAI-API": {"stars": 1, "last_update": "2025-04-07 17:34:06", "author_account_age_days": 187}, "https://github.com/ratulrafsan/Comfyui-SAL-VTON": {"stars": 86, "last_update": "2024-08-26 09:52:06", "author_account_age_days": 4862}, "https://github.com/raykindle/ComfyUI_Step1X-Edit": {"stars": 47, "last_update": "2025-05-06 02:01:37", "author_account_age_days": 2290}, "https://github.com/raysers/Mflux-ComfyUI": {"stars": 95, "last_update": "2025-03-09 21:14:27", "author_account_age_days": 2373}, "https://github.com/rcfcu2000/zhihuige-nodes-comfyui": {"stars": 1, "last_update": "2024-05-22 22:13:55", "author_account_age_days": 3793}, "https://github.com/rcsaquino/comfyui-custom-nodes": {"stars": 1, "last_update": "2024-08-26 10:08:29", "author_account_age_days": 1879}, "https://github.com/rdancer/ComfyUI_Florence2SAM2": {"stars": 37, "last_update": "2025-03-14 10:49:55", "author_account_age_days": 5989}, "https://github.com/receyuki/comfyui-prompt-reader-node": {"stars": 366, "last_update": "2025-02-01 15:56:44", "author_account_age_days": 2961}, "https://github.com/recraft-ai/ComfyUI-RecraftAI": {"stars": 61, "last_update": "2025-06-04 11:33:13", "author_account_age_days": 1091}, "https://github.com/redhottensors/ComfyUI-Prediction": {"stars": 14, "last_update": "2024-07-14 21:19:01", "author_account_age_days": 507}, "https://github.com/regiellis/ComfyUI-EasyColorCorrector": {"stars": 51, "last_update": "2025-06-26 15:37:56", "author_account_age_days": 4994}, "https://github.com/regiellis/ComfyUI-EasyNoobai": {"stars": 28, "last_update": "2025-05-12 14:17:10", "author_account_age_days": 4994}, "https://github.com/regiellis/ComfyUI-EasyPony": {"stars": 9, "last_update": "2025-04-05 15:15:29", "author_account_age_days": 4994}, "https://github.com/replicate/comfyui-replicate": {"stars": 185, "last_update": "2024-11-05 15:26:20", "author_account_age_days": 1977}, "https://github.com/revirevy/Comfyui_saveimage_imgbb": {"stars": 1, "last_update": "2025-04-23 10:49:48", "author_account_age_days": 4853}, "https://github.com/rgthree/rgthree-comfy": {"stars": 2004, "last_update": "2025-06-23 04:15:47", "author_account_age_days": 5343}, "https://github.com/rhdunn/comfyui-audio-processing": {"stars": 8, "last_update": "2024-08-22 19:11:01", "author_account_age_days": 6005}, "https://github.com/rhdunn/comfyui-bus-plugin": {"stars": 2, "last_update": "2024-08-22 19:00:56", "author_account_age_days": 6005}, "https://github.com/rhplus0831/ComfyMepi": {"stars": 0, "last_update": "2025-04-12 22:59:21", "author_account_age_days": 520}, "https://github.com/richinsley/Comfy-LFO": {"stars": 5, "last_update": "2024-05-22 20:46:30", "author_account_age_days": 3049}, "https://github.com/ricklove/comfyui-ricklove": {"stars": 1, "last_update": "2024-10-05 03:12:28", "author_account_age_days": 5201}, "https://github.com/rickyars/comfyui-llm-tile": {"stars": 1, "last_update": "2025-06-25 11:56:15", "author_account_age_days": 4571}, "https://github.com/risunobushi/ComfyUI-Similarity-Score": {"stars": 4, "last_update": "2025-01-03 15:27:06", "author_account_age_days": 1015}, "https://github.com/risunobushi/ComfyUI_DisplacementMapTools": {"stars": 3, "last_update": "2025-01-29 18:06:41", "author_account_age_days": 1015}, "https://github.com/risunobushi/comfyUI_FrequencySeparation_RGB-HSV": {"stars": 36, "last_update": "2024-06-14 10:28:04", "author_account_age_days": 1015}, "https://github.com/rkfg/ComfyUI-Dia_tts": {"stars": 0, "last_update": "2025-04-27 15:58:21", "author_account_age_days": 5639}, "https://github.com/rnbwdsh/ComfyUI-LatentWalk": {"stars": 13, "last_update": "2024-08-20 22:39:19", "author_account_age_days": 3915}, "https://github.com/robertvoy/ComfyUI-Flux-Continuum": {"stars": 197, "last_update": "2025-06-27 22:47:53", "author_account_age_days": 4473}, "https://github.com/robin-collins/ComfyUI-TechsToolz": {"stars": 0, "last_update": "2025-06-20 00:25:39", "author_account_age_days": 1492}, "https://github.com/robtl2/ComfyUI-ComfyBridge": {"stars": 0, "last_update": "2024-11-18 23:28:13", "author_account_age_days": 820}, "https://github.com/rohitsainier/ComfyUI-InstagramDownloader": {"stars": 18, "last_update": "2025-01-02 08:47:22", "author_account_age_days": 3521}, "https://github.com/romeobuilderotti/ComfyUI-PNG-Metadata": {"stars": 7, "last_update": "2024-05-22 21:29:25", "author_account_age_days": 659}, "https://github.com/ronaldzgithub/ComfyUI_Appstore": {"stars": 5, "last_update": "2024-12-04 15:02:42", "author_account_age_days": 2678}, "https://github.com/ronniebasak/ComfyUI-Tara-LLM-Integration": {"stars": 107, "last_update": "2024-11-18 05:08:11", "author_account_age_days": 4523}, "https://github.com/ronsantash/Comfyui-flexi-lora-loader": {"stars": 8, "last_update": "2025-01-12 11:57:27", "author_account_age_days": 1385}, "https://github.com/rookiepsi/comfypsi_blur_mask": {"stars": 0, "last_update": "2025-06-25 15:13:33", "author_account_age_days": 125}, "https://github.com/rookiepsi/comfyui-extended": {"stars": 2, "last_update": "2025-06-22 13:42:26", "author_account_age_days": 124}, "https://github.com/roundyyy/ComfyUI-mesh-simplifier": {"stars": 6, "last_update": "2025-03-09 23:39:24", "author_account_age_days": 1295}, "https://github.com/royceschultz/ComfyUI-Notifications": {"stars": 15, "last_update": "2025-04-23 01:40:31", "author_account_age_days": 2898}, "https://github.com/royceschultz/ComfyUI-TranscriptionTools": {"stars": 22, "last_update": "2025-04-23 00:52:31", "author_account_age_days": 2898}, "https://github.com/rubi-du/ComfyUI-BiRefNet-Super": {"stars": 10, "last_update": "2025-05-21 02:21:09", "author_account_age_days": 550}, "https://github.com/rubi-du/ComfyUI-Flux-Inpainting": {"stars": 36, "last_update": "2025-05-14 06:09:10", "author_account_age_days": 550}, "https://github.com/rubi-du/ComfyUI-ICC-nodes": {"stars": 2, "last_update": "2025-05-14 06:10:11", "author_account_age_days": 550}, "https://github.com/rubi-du/ComfyUI-MaskEditor-Extension": {"stars": 9, "last_update": "2025-05-14 06:09:43", "author_account_age_days": 550}, "https://github.com/rui40000/RUI-Nodes": {"stars": 16, "last_update": "2024-05-22 22:12:26", "author_account_age_days": 842}, "https://github.com/ruiqutech/ComfyUI-RuiquNodes": {"stars": 0, "last_update": "2024-05-23 01:21:50", "author_account_age_days": 438}, "https://github.com/runtime44/comfyui_r44_nodes": {"stars": 41, "last_update": "2024-07-01 08:02:04", "author_account_age_days": 536}, "https://github.com/ruucm/ruucm-comfy": {"stars": 2, "last_update": "2025-04-21 15:20:57", "author_account_age_days": 2789}, "https://github.com/ryanontheinside/ComfyUI-DeepLiveCam": {"stars": 7, "last_update": "2025-05-26 14:26:57", "author_account_age_days": 4058}, "https://github.com/ryanontheinside/ComfyUI_ControlFreak": {"stars": 14, "last_update": "2025-04-13 23:18:36", "author_account_age_days": 4058}, "https://github.com/ryanontheinside/ComfyUI_Doom": {"stars": 5, "last_update": "2024-11-08 17:58:21", "author_account_age_days": 4058}, "https://github.com/ryanontheinside/ComfyUI_EfficientTAM": {"stars": 3, "last_update": "2024-12-21 20:25:05", "author_account_age_days": 4058}, "https://github.com/ryanontheinside/ComfyUI_ProfilerX": {"stars": 60, "last_update": "2025-05-27 22:10:23", "author_account_age_days": 4058}, "https://github.com/ryanontheinside/ComfyUI_RealtimeNodes": {"stars": 58, "last_update": "2025-06-19 14:20:29", "author_account_age_days": 4058}, "https://github.com/ryanontheinside/ComfyUI_RyanOnTheInside": {"stars": 515, "last_update": "2025-06-01 22:34:26", "author_account_age_days": 4058}, "https://github.com/ryanontheinside/ComfyUI_SuperResolution": {"stars": 8, "last_update": "2025-04-07 17:53:16", "author_account_age_days": 4058}, "https://github.com/s9roll7/comfyui_cotracker_node": {"stars": 13, "last_update": "2025-06-24 11:30:06", "author_account_age_days": 953}, "https://github.com/saftle/uber_comfy_nodes": {"stars": 1, "last_update": "2024-08-24 02:42:40", "author_account_age_days": 5126}, "https://github.com/sakura1bgx/ComfyUI_FlipStreamViewer": {"stars": 4, "last_update": "2025-05-31 01:17:30", "author_account_age_days": 307}, "https://github.com/sanbuphy/ComfyUI-AudioLDM": {"stars": 0, "last_update": "2025-01-02 02:01:12", "author_account_age_days": 1289}, "https://github.com/santiagosamuel3455/ComfyUI-GeminiImageToPrompt": {"stars": 1, "last_update": "2025-05-04 04:58:56", "author_account_age_days": 318}, "https://github.com/scraed/LanPaint": {"stars": 373, "last_update": "2025-06-21 06:19:09", "author_account_age_days": 3830}, "https://github.com/sdfxai/SDFXBridgeForComfyUI": {"stars": 11, "last_update": "2024-06-14 10:26:56", "author_account_age_days": 603}, "https://github.com/seanlynch/comfyui-optical-flow": {"stars": 32, "last_update": "2024-05-22 20:52:17", "author_account_age_days": 5673}, "https://github.com/seanlynch/srl-nodes": {"stars": 8, "last_update": "2024-06-30 13:47:38", "author_account_age_days": 5673}, "https://github.com/sebord/ComfyUI-LMCQ": {"stars": 72, "last_update": "2025-06-24 15:43:00", "author_account_age_days": 1155}, "https://github.com/sergekatzmann/ComfyUI_Nimbus-Pack": {"stars": 4, "last_update": "2024-05-22 21:34:15", "author_account_age_days": 3701}, "https://github.com/set-soft/ComfyUI-AudioBatch": {"stars": 0, "last_update": "2025-06-04 12:59:15", "author_account_age_days": 3178}, "https://github.com/sh570655308/ComfyUI-GigapixelAI": {"stars": 150, "last_update": "2025-01-15 05:16:31", "author_account_age_days": 2869}, "https://github.com/sh570655308/ComfyUI-TopazVideoAI": {"stars": 216, "last_update": "2025-04-23 08:54:20", "author_account_age_days": 2869}, "https://github.com/shabri-arrahim/ComfyUI-Safety-Checker": {"stars": 1, "last_update": "2025-01-23 05:46:33", "author_account_age_days": 2137}, "https://github.com/shadowcz007/comfyui-Image-reward": {"stars": 31, "last_update": "2024-06-14 10:24:49", "author_account_age_days": 3683}, "https://github.com/shadowcz007/comfyui-consistency-decoder": {"stars": 2, "last_update": "2024-06-14 10:23:35", "author_account_age_days": 3683}, "https://github.com/shadowcz007/comfyui-edit-mask": {"stars": 6, "last_update": "2024-06-20 01:42:48", "author_account_age_days": 3683}, "https://github.com/shadowcz007/comfyui-liveportrait": {"stars": 458, "last_update": "2024-09-01 10:34:41", "author_account_age_days": 2411}, "https://github.com/shadowcz007/comfyui-mixlab-nodes": {"stars": 1643, "last_update": "2025-02-05 10:24:45", "author_account_age_days": 2411}, "https://github.com/shadowcz007/comfyui-sound-lab": {"stars": 120, "last_update": "2024-07-04 12:53:38", "author_account_age_days": 2411}, "https://github.com/shadowcz007/comfyui-try-on": {"stars": 13, "last_update": "2024-08-15 10:50:22", "author_account_age_days": 2411}, "https://github.com/shadowcz007/comfyui-ultralytics-yolo": {"stars": 33, "last_update": "2024-06-22 09:06:04", "author_account_age_days": 3683}, "https://github.com/shahkoorosh/ComfyUI-KGnodes": {"stars": 3, "last_update": "2025-05-23 17:41:55", "author_account_age_days": 560}, "https://github.com/shahkoorosh/ComfyUI-PersianText": {"stars": 19, "last_update": "2025-05-23 17:43:33", "author_account_age_days": 560}, "https://github.com/shenduldh/ComfyUI-Lightning": {"stars": 206, "last_update": "2025-03-13 05:58:04", "author_account_age_days": 2486}, "https://github.com/shi3z/ComfyUI_Memeplex_DALLE": {"stars": 2, "last_update": "2024-05-23 00:14:25", "author_account_age_days": 5461}, "https://github.com/shiertier/ComfyUI-TeaCache-lumina2": {"stars": 1, "last_update": "2025-06-03 10:09:06", "author_account_age_days": 1388}, "https://github.com/shiimizu/ComfyUI-PhotoMaker-Plus": {"stars": 284, "last_update": "2024-12-01 18:40:16", "author_account_age_days": 2133}, "https://github.com/shiimizu/ComfyUI-TiledDiffusion": {"stars": 447, "last_update": "2025-03-18 19:50:35", "author_account_age_days": 2133}, "https://github.com/shiimizu/ComfyUI-semantic-aware-guidance": {"stars": 12, "last_update": "2024-08-08 19:59:57", "author_account_age_days": 2133}, "https://github.com/shiimizu/ComfyUI_smZNodes": {"stars": 278, "last_update": "2025-06-04 15:26:05", "author_account_age_days": 2133}, "https://github.com/shingo1228/ComfyUI-SDXL-EmptyLatentImage": {"stars": 36, "last_update": "2024-05-22 20:41:29", "author_account_age_days": 2591}, "https://github.com/shingo1228/ComfyUI-send-eagle-slim": {"stars": 35, "last_update": "2024-07-30 22:28:41", "author_account_age_days": 2591}, "https://github.com/shinich39/comfyui-break-workflow": {"stars": 0, "last_update": "2025-05-25 10:20:20", "author_account_age_days": 681}, "https://github.com/shinich39/comfyui-civitai-workflow": {"stars": 0, "last_update": "2025-06-11 16:47:58", "author_account_age_days": 681}, "https://github.com/shinich39/comfyui-dynamic-routes": {"stars": 5, "last_update": "2025-05-25 10:17:05", "author_account_age_days": 681}, "https://github.com/shinich39/comfyui-get-meta": {"stars": 7, "last_update": "2025-05-25 10:17:48", "author_account_age_days": 681}, "https://github.com/shinich39/comfyui-innnnnpaint": {"stars": 0, "last_update": "2025-05-25 10:18:06", "author_account_age_days": 681}, "https://github.com/shinich39/comfyui-no-one-above-me": {"stars": 0, "last_update": "2025-05-25 10:19:33", "author_account_age_days": 681}, "https://github.com/shinich39/comfyui-prevent-sleep": {"stars": 0, "last_update": "2025-05-25 10:18:45", "author_account_age_days": 681}, "https://github.com/shobhitic/ComfyUI-PlusMinusTextClip": {"stars": 3, "last_update": "2024-06-20 13:57:29", "author_account_age_days": 4676}, "https://github.com/shockz0rz/comfy-easy-grids": {"stars": 24, "last_update": "2024-05-22 18:14:05", "author_account_age_days": 1994}, "https://github.com/siliconflow/BizyAir": {"stars": 718, "last_update": "2025-06-24 02:35:18", "author_account_age_days": 674}, "https://github.com/siliconflow/onediff_comfy_nodes": {"stars": 23, "last_update": "2024-06-24 10:08:11", "author_account_age_days": 674}, "https://github.com/silveroxides/ComfyUI-ModelUtils": {"stars": 1, "last_update": "2025-05-20 15:08:21", "author_account_age_days": 1869}, "https://github.com/silveroxides/ComfyUI-RR-JointTagger": {"stars": 2, "last_update": "2025-05-09 18:58:06", "author_account_age_days": 1869}, "https://github.com/silveroxides/ComfyUI_EmbeddingToolkit": {"stars": 6, "last_update": "2025-06-16 14:18:31", "author_account_age_days": 1869}, "https://github.com/silveroxides/ComfyUI_SigmoidOffsetScheduler": {"stars": 6, "last_update": "2025-05-11 19:44:35", "author_account_age_days": 1869}, "https://github.com/silveroxides/ComfyUI_bnb_nf4_fp4_Loaders": {"stars": 36, "last_update": "2025-04-28 01:08:43", "author_account_age_days": 1869}, "https://github.com/sipherxyz/comfyui-art-venture": {"stars": 273, "last_update": "2025-06-04 14:13:31", "author_account_age_days": 1475}, "https://github.com/sipie800/ComfyUI-PuLID-Flux-Enhanced": {"stars": 211, "last_update": "2025-02-07 15:04:47", "author_account_age_days": 2495}, "https://github.com/sittere/ComfyUI-YK_Line-loading": {"stars": 2, "last_update": "2025-03-02 09:10:54", "author_account_age_days": 1245}, "https://github.com/sjh00/ComfyUI-LoadImageWithInfo": {"stars": 2, "last_update": "2025-06-05 15:46:52", "author_account_age_days": 4131}, "https://github.com/skfoo/ComfyUI-Coziness": {"stars": 31, "last_update": "2024-08-16 03:10:43", "author_account_age_days": 2450}, "https://github.com/skycoder182/comfyui-filename-tools": {"stars": 0, "last_update": "2025-05-20 18:06:04", "author_account_age_days": 38}, "https://github.com/skycoder182/comfyui-skycoder-tools": {"stars": 1, "last_update": "2025-06-08 12:26:41", "author_account_age_days": 38}, "https://github.com/slvslvslv/ComfyUI-SmartHelperNodes": {"stars": 2, "last_update": "2025-05-06 15:48:22", "author_account_age_days": 345}, "https://github.com/slvslvslv/ComfyUI-SmartImageTools": {"stars": 0, "last_update": "2025-05-03 12:46:43", "author_account_age_days": 345}, "https://github.com/slyt/comfyui-ollama-nodes": {"stars": 0, "last_update": "2024-07-31 13:52:27", "author_account_age_days": 4308}, "https://github.com/sm079/ComfyUI-Face-Detection": {"stars": 0, "last_update": "2025-06-03 14:37:55", "author_account_age_days": 2053}, "https://github.com/smagnetize/kb-comfyui-nodes": {"stars": 0, "last_update": "2024-06-14 12:00:45", "author_account_age_days": 3083}, "https://github.com/smlbiobot/ComfyUI-Flux-Replicate-API": {"stars": 23, "last_update": "2024-12-26 16:21:00", "author_account_age_days": 3090}, "https://github.com/smlbiobot/sml-comfyui-prompt-expansion": {"stars": 13, "last_update": "2025-01-27 13:33:49", "author_account_age_days": 3090}, "https://github.com/smthemex/ComfyUI_AnyDoor": {"stars": 63, "last_update": "2025-02-05 04:01:50", "author_account_age_days": 722}, "https://github.com/smthemex/ComfyUI_CSD_MT": {"stars": 19, "last_update": "2025-02-06 04:30:50", "author_account_age_days": 722}, "https://github.com/smthemex/ComfyUI_CSGO_Wrapper": {"stars": 16, "last_update": "2024-09-07 06:13:48", "author_account_age_days": 722}, "https://github.com/smthemex/ComfyUI_ChatGLM_API": {"stars": 23, "last_update": "2024-07-31 13:53:41", "author_account_age_days": 722}, "https://github.com/smthemex/ComfyUI_CustomNet": {"stars": 10, "last_update": "2024-08-11 08:58:37", "author_account_age_days": 722}, "https://github.com/smthemex/ComfyUI_DICE_Talk": {"stars": 25, "last_update": "2025-05-07 07:47:06", "author_account_age_days": 722}, "https://github.com/smthemex/ComfyUI_DeepFakeDefenders": {"stars": 41, "last_update": "2024-09-14 00:17:59", "author_account_age_days": 722}, "https://github.com/smthemex/ComfyUI_Demucs": {"stars": 8, "last_update": "2025-03-12 05:22:24", "author_account_age_days": 722}, "https://github.com/smthemex/ComfyUI_Diffree": {"stars": 31, "last_update": "2025-03-09 01:16:33", "author_account_age_days": 722}, "https://github.com/smthemex/ComfyUI_DiffuEraser": {"stars": 162, "last_update": "2025-02-14 12:09:00", "author_account_age_days": 722}, "https://github.com/smthemex/ComfyUI_EchoMimic": {"stars": 634, "last_update": "2025-04-05 12:23:33", "author_account_age_days": 722}, "https://github.com/smthemex/ComfyUI_Face_Anon_Simple": {"stars": 16, "last_update": "2025-03-12 05:22:03", "author_account_age_days": 722}, "https://github.com/smthemex/ComfyUI_FoleyCrafter": {"stars": 61, "last_update": "2025-05-29 11:42:48", "author_account_age_days": 722}, "https://github.com/smthemex/ComfyUI_FollowYourEmoji": {"stars": 16, "last_update": "2025-04-11 13:45:15", "author_account_age_days": 722}, "https://github.com/smthemex/ComfyUI_Hallo2": {"stars": 74, "last_update": "2025-03-12 05:22:46", "author_account_age_days": 722}, "https://github.com/smthemex/ComfyUI_HiDiffusion_Pro": {"stars": 52, "last_update": "2025-01-13 03:29:50", "author_account_age_days": 722}, "https://github.com/smthemex/ComfyUI_HunyuanAvatar_Sm": {"stars": 71, "last_update": "2025-06-24 13:06:34", "author_account_age_days": 722}, "https://github.com/smthemex/ComfyUI_ID_Animator": {"stars": 24, "last_update": "2024-07-31 13:53:27", "author_account_age_days": 722}, "https://github.com/smthemex/ComfyUI_InstantIR_Wrapper": {"stars": 237, "last_update": "2025-03-12 05:22:14", "author_account_age_days": 722}, "https://github.com/smthemex/ComfyUI_KV_Edit": {"stars": 58, "last_update": "2025-05-24 00:35:59", "author_account_age_days": 722}, "https://github.com/smthemex/ComfyUI_Light_A_Video": {"stars": 81, "last_update": "2025-04-10 01:05:56", "author_account_age_days": 722}, "https://github.com/smthemex/ComfyUI_Llama3_8B": {"stars": 26, "last_update": "2024-06-25 00:49:01", "author_account_age_days": 722}, "https://github.com/smthemex/ComfyUI_MS_Diffusion": {"stars": 59, "last_update": "2024-09-10 09:50:19", "author_account_age_days": 722}, "https://github.com/smthemex/ComfyUI_MangaNinjia": {"stars": 53, "last_update": "2025-04-09 14:21:57", "author_account_age_days": 722}, "https://github.com/smthemex/ComfyUI_MooER": {"stars": 5, "last_update": "2025-03-09 01:15:38", "author_account_age_days": 722}, "https://github.com/smthemex/ComfyUI_OmniParser": {"stars": 39, "last_update": "2025-03-12 05:22:34", "author_account_age_days": 722}, "https://github.com/smthemex/ComfyUI_PBR_Maker": {"stars": 13, "last_update": "2025-03-12 05:21:53", "author_account_age_days": 722}, "https://github.com/smthemex/ComfyUI_ParlerTTS": {"stars": 44, "last_update": "2024-12-25 06:26:03", "author_account_age_days": 722}, "https://github.com/smthemex/ComfyUI_PartPacker": {"stars": 16, "last_update": "2025-06-25 00:44:53", "author_account_age_days": 722}, "https://github.com/smthemex/ComfyUI_Personalize_Anything": {"stars": 43, "last_update": "2025-03-26 00:38:13", "author_account_age_days": 722}, "https://github.com/smthemex/ComfyUI_PhotoDoodle": {"stars": 97, "last_update": "2025-03-20 08:19:21", "author_account_age_days": 722}, "https://github.com/smthemex/ComfyUI_Pic2Story": {"stars": 9, "last_update": "2024-12-06 12:12:19", "author_account_age_days": 722}, "https://github.com/smthemex/ComfyUI_Pipeline_Tool": {"stars": 10, "last_update": "2024-08-05 06:14:57", "author_account_age_days": 722}, "https://github.com/smthemex/ComfyUI_Pops": {"stars": 21, "last_update": "2024-08-12 09:11:49", "author_account_age_days": 722}, "https://github.com/smthemex/ComfyUI_SVFR": {"stars": 91, "last_update": "2025-03-12 05:21:23", "author_account_age_days": 722}, "https://github.com/smthemex/ComfyUI_Sapiens": {"stars": 176, "last_update": "2025-03-12 05:22:59", "author_account_age_days": 722}, "https://github.com/smthemex/ComfyUI_SongGeneration": {"stars": 34, "last_update": "2025-06-23 13:49:47", "author_account_age_days": 722}, "https://github.com/smthemex/ComfyUI_Sonic": {"stars": 1035, "last_update": "2025-05-22 00:46:49", "author_account_age_days": 722}, "https://github.com/smthemex/ComfyUI_StableAudio_Open": {"stars": 27, "last_update": "2024-08-10 03:45:47", "author_account_age_days": 722}, "https://github.com/smthemex/ComfyUI_Stable_Makeup": {"stars": 97, "last_update": "2025-06-25 00:38:14", "author_account_age_days": 722}, "https://github.com/smthemex/ComfyUI_StoryDiffusion": {"stars": 449, "last_update": "2025-06-25 06:23:47", "author_account_age_days": 722}, "https://github.com/smthemex/ComfyUI_Streamv2v_Plus": {"stars": 10, "last_update": "2024-09-06 08:20:59", "author_account_age_days": 722}, "https://github.com/smthemex/ComfyUI_TRELLIS": {"stars": 167, "last_update": "2025-04-22 00:28:27", "author_account_age_days": 722}, "https://github.com/smthemex/ComfyUI_VisualCloze": {"stars": 11, "last_update": "2025-05-21 08:56:45", "author_account_age_days": 722}, "https://github.com/smthemex/ComfyUI_YuE": {"stars": 142, "last_update": "2025-02-24 12:02:41", "author_account_age_days": 722}, "https://github.com/sn0w12/ComfyUI-Sn0w-Scripts": {"stars": 11, "last_update": "2025-05-04 15:47:59", "author_account_age_days": 1142}, "https://github.com/sn0w12/ComfyUI-Syntax-Highlighting": {"stars": 0, "last_update": "2025-04-07 10:24:12", "author_account_age_days": 1142}, "https://github.com/sneccc/comfyui-snek-nodes": {"stars": 1, "last_update": "2025-06-25 14:25:41", "author_account_age_days": 1936}, "https://github.com/somesomebody/lorainfo-sidebar": {"stars": 2, "last_update": "2025-05-31 07:23:03", "author_account_age_days": 59}, "https://github.com/souki202/ComfyUI-LoadImage-Advanced": {"stars": 1, "last_update": "2025-03-03 03:53:26", "author_account_age_days": 3628}, "https://github.com/sourceful-official/LoadLoraModelOnlyWithUrl": {"stars": 1, "last_update": "2024-12-04 12:14:51", "author_account_age_days": 1850}, "https://github.com/sousakujikken/ComfyUI-PixydustQuantizer": {"stars": 29, "last_update": "2025-03-30 15:07:02", "author_account_age_days": 776}, "https://github.com/space-nuko/ComfyUI-Disco-Diffusion": {"stars": 54, "last_update": "2024-08-07 11:51:17", "author_account_age_days": 3093}, "https://github.com/space-nuko/ComfyUI-OpenPose-Editor": {"stars": 213, "last_update": "2024-05-22 18:10:49", "author_account_age_days": 3093}, "https://github.com/space-nuko/nui-suite": {"stars": 11, "last_update": "2024-05-22 18:11:04", "author_account_age_days": 3093}, "https://github.com/spacepxl/ComfyUI-Depth-Pro": {"stars": 187, "last_update": "2024-10-23 20:05:56", "author_account_age_days": 663}, "https://github.com/spacepxl/ComfyUI-Florence-2": {"stars": 82, "last_update": "2024-07-20 19:44:33", "author_account_age_days": 663}, "https://github.com/spacepxl/ComfyUI-HQ-Image-Save": {"stars": 58, "last_update": "2025-01-30 00:12:58", "author_account_age_days": 663}, "https://github.com/spacepxl/ComfyUI-Image-Filters": {"stars": 229, "last_update": "2025-05-21 21:27:56", "author_account_age_days": 663}, "https://github.com/spacepxl/ComfyUI-LossTesting": {"stars": 2, "last_update": "2025-01-26 05:09:57", "author_account_age_days": 663}, "https://github.com/spacepxl/ComfyUI-RAVE": {"stars": 92, "last_update": "2024-05-22 20:56:19", "author_account_age_days": 663}, "https://github.com/spacepxl/ComfyUI-StyleGan": {"stars": 19, "last_update": "2024-06-10 20:16:34", "author_account_age_days": 663}, "https://github.com/spawner1145/CUI-Lumina2-TeaCache": {"stars": 8, "last_update": "2025-06-08 09:51:09", "author_account_age_days": 305}, "https://github.com/spawner1145/comfyui-aichat": {"stars": 2, "last_update": "2025-06-09 11:06:53", "author_account_age_days": 305}, "https://github.com/spinagon/ComfyUI-seam-carving": {"stars": 22, "last_update": "2025-03-14 08:47:57", "author_account_age_days": 5109}, "https://github.com/spinagon/ComfyUI-seamless-tiling": {"stars": 207, "last_update": "2025-03-14 08:48:11", "author_account_age_days": 5109}, "https://github.com/spro/comfyui-mirror": {"stars": 6, "last_update": "2024-05-22 20:50:25", "author_account_age_days": 5600}, "https://github.com/ssitu/ComfyUI_UltimateSDUpscale": {"stars": 1219, "last_update": "2025-06-05 01:53:42", "author_account_age_days": 2057}, "https://github.com/ssitu/ComfyUI_fabric": {"stars": 93, "last_update": "2024-05-22 18:10:19", "author_account_age_days": 2057}, "https://github.com/ssitu/ComfyUI_restart_sampling": {"stars": 89, "last_update": "2024-05-22 18:09:49", "author_account_age_days": 2057}, "https://github.com/ssitu/ComfyUI_roop": {"stars": 77, "last_update": "2024-05-22 18:10:03", "author_account_age_days": 2057}, "https://github.com/stavsap/comfyui-downloader": {"stars": 0, "last_update": "2025-06-22 20:44:33", "author_account_age_days": 4452}, "https://github.com/stavsap/comfyui-kokoro": {"stars": 52, "last_update": "2025-05-17 13:23:49", "author_account_age_days": 4452}, "https://github.com/stavsap/comfyui-ollama": {"stars": 569, "last_update": "2025-05-28 07:22:35", "author_account_age_days": 4452}, "https://github.com/stepfun-ai/ComfyUI-StepVideo": {"stars": 39, "last_update": "2025-03-27 07:52:26", "author_account_age_days": 320}, "https://github.com/stevenwg/ComfyUI-VideoGrid": {"stars": 0, "last_update": "2025-05-26 06:51:21", "author_account_age_days": 3666}, "https://github.com/stormcenter/ComfyUI-AutoSplitGridImage": {"stars": 32, "last_update": "2025-01-06 12:02:58", "author_account_age_days": 4496}, "https://github.com/stormcenter/ComfyUI-LivePhotoCreator": {"stars": 24, "last_update": "2025-01-06 12:03:42", "author_account_age_days": 4496}, "https://github.com/stormcenter/ComfyUI-SVGFullfill": {"stars": 10, "last_update": "2025-01-06 12:04:18", "author_account_age_days": 4496}, "https://github.com/storyicon/comfyui_musev_evolved": {"stars": 26, "last_update": "2024-06-14 11:02:40", "author_account_age_days": 2920}, "https://github.com/storyicon/comfyui_segment_anything": {"stars": 971, "last_update": "2024-07-12 10:17:33", "author_account_age_days": 2920}, "https://github.com/strand1/ComfyUI-Autogen": {"stars": 3, "last_update": "2025-01-21 05:10:43", "author_account_age_days": 4839}, "https://github.com/strimmlarn/ComfyUI-Strimmlarns-Aesthetic-Score": {"stars": 34, "last_update": "2024-06-17 10:01:44", "author_account_age_days": 2995}, "https://github.com/styler00dollar/ComfyUI-deepcache": {"stars": 11, "last_update": "2024-05-22 22:18:18", "author_account_age_days": 2214}, "https://github.com/styler00dollar/ComfyUI-sudo-latent-upscale": {"stars": 39, "last_update": "2024-05-22 22:18:07", "author_account_age_days": 2214}, "https://github.com/subtleGradient/TinkerBot-tech-for-ComfyUI-Touchpad": {"stars": 38, "last_update": "2024-08-16 01:18:03", "author_account_age_days": 6299}, "https://github.com/sugarkwork/ComfyUI_AspectRatioToSize": {"stars": 2, "last_update": "2025-06-04 00:48:13", "author_account_age_days": 1244}, "https://github.com/sugarkwork/comfyui-trtupscaler": {"stars": 1, "last_update": "2025-06-11 07:43:10", "author_account_age_days": 1244}, "https://github.com/sugarkwork/comfyui_cohere": {"stars": 1, "last_update": "2025-06-11 04:29:08", "author_account_age_days": 1244}, "https://github.com/sugarkwork/comfyui_tag_fillter": {"stars": 52, "last_update": "2025-04-16 07:37:35", "author_account_age_days": 1244}, "https://github.com/superyoman/comfyui_lumaAPI": {"stars": 21, "last_update": "2024-06-17 21:00:05", "author_account_age_days": 817}, "https://github.com/surinder83singh/ComfyUI-compare-videos": {"stars": 1, "last_update": "2025-05-06 01:30:48", "author_account_age_days": 4884}, "https://github.com/svetozarov/AS_LLM_nodes": {"stars": 2, "last_update": "2025-03-23 12:05:43", "author_account_age_days": 853}, "https://github.com/sweetndata/ComfyUI-Image-Harmonizer": {"stars": 2, "last_update": "2024-11-20 06:10:34", "author_account_age_days": 1100}, "https://github.com/sweetndata/ComfyUI-googletrans": {"stars": 3, "last_update": "2024-11-20 04:53:19", "author_account_age_days": 1100}, "https://github.com/sweetndata/ComfyUI_Sticker_Compositer": {"stars": 1, "last_update": "2025-01-02 06:54:51", "author_account_age_days": 1100}, "https://github.com/swhsiang/comfyui-3d-gs-renderer": {"stars": 1, "last_update": "2025-06-09 03:05:11", "author_account_age_days": 3302}, "https://github.com/syllebra/bilbox-comfyui": {"stars": 132, "last_update": "2024-12-06 23:51:55", "author_account_age_days": 3504}, "https://github.com/sylym/comfy_vid2vid": {"stars": 71, "last_update": "2024-05-22 17:53:40", "author_account_age_days": 2266}, "https://github.com/synthetai/ComfyUI-JM-KLing-API": {"stars": 1, "last_update": "2025-05-14 10:42:13", "author_account_age_days": 319}, "https://github.com/synthetai/ComfyUI-JM-MiniMax-API": {"stars": 1, "last_update": "2025-06-24 02:00:05", "author_account_age_days": 319}, "https://github.com/synthetai/ComfyUI-JM-Volcengine-API": {"stars": 0, "last_update": "2025-06-24 08:11:39", "author_account_age_days": 319}, "https://github.com/synthetai/ComfyUI-ToolBox": {"stars": 0, "last_update": "2025-06-25 07:35:50", "author_account_age_days": 319}, "https://github.com/synthetai/ComfyUI_FaceEnhancer": {"stars": 2, "last_update": "2025-04-17 00:34:39", "author_account_age_days": 319}, "https://github.com/synthetai/ComfyUI_PromptBatcher": {"stars": 5, "last_update": "2025-04-14 04:42:03", "author_account_age_days": 319}, "https://github.com/szhublox/ambw_comfyui": {"stars": 15, "last_update": "2024-05-22 18:04:57", "author_account_age_days": 1375}, "https://github.com/taabata/ComfyCanvas": {"stars": 88, "last_update": "2024-12-15 00:59:25", "author_account_age_days": 2051}, "https://github.com/taabata/LCM_Inpaint_Outpaint_Comfy": {"stars": 260, "last_update": "2024-11-18 00:45:28", "author_account_age_days": 2051}, "https://github.com/taabata/SANA_LOWVRAM": {"stars": 5, "last_update": "2024-12-28 01:16:29", "author_account_age_days": 2051}, "https://github.com/taches-ai/comfyui-scene-composer": {"stars": 55, "last_update": "2025-05-28 07:30:03", "author_account_age_days": 275}, "https://github.com/tachyon-beep/comfyui-simplefeed": {"stars": 10, "last_update": "2024-10-16 09:19:29", "author_account_age_days": 5289}, "https://github.com/takemetosiberia/ComfyUI-SAMURAI--SAM2-": {"stars": 36, "last_update": "2024-12-01 13:06:02", "author_account_age_days": 752}, "https://github.com/talesofai/comfyui-browser": {"stars": 590, "last_update": "2024-11-11 01:42:30", "author_account_age_days": 924}, "https://github.com/tanglaoya321/ComfyUI-StoryMaker": {"stars": 17, "last_update": "2024-10-01 01:20:00", "author_account_age_days": 4340}, "https://github.com/tatookan/comfyui_ssl_gemini_EXP": {"stars": 86, "last_update": "2025-03-19 15:54:44", "author_account_age_days": 2098}, "https://github.com/tavyra/ComfyUI_Curves": {"stars": 2, "last_update": "2025-05-08 01:48:55", "author_account_age_days": 2467}, "https://github.com/tetsuoo-online/comfyui-too-xmp-metadata": {"stars": 4, "last_update": "2025-06-07 15:59:26", "author_account_age_days": 2388}, "https://github.com/teward/Comfy-Sentry": {"stars": 1, "last_update": "2024-07-31 21:37:42", "author_account_age_days": 5466}, "https://github.com/teward/ComfyUI-Helper-Nodes": {"stars": 6, "last_update": "2024-05-23 01:22:01", "author_account_age_days": 5466}, "https://github.com/thalismind/ComfyUI-Blend-Nodes": {"stars": 0, "last_update": "2025-06-02 02:22:19", "author_account_age_days": 167}, "https://github.com/theAdamColton/ComfyUI-texflow-extension": {"stars": 1, "last_update": "2025-01-16 19:58:24", "author_account_age_days": 1724}, "https://github.com/theUpsider/ComfyUI-Styles_CSV_Loader": {"stars": 58, "last_update": "2025-05-16 11:01:23", "author_account_age_days": 3091}, "https://github.com/thecooltechguy/ComfyUI-ComfyWorkflows": {"stars": 70, "last_update": "2024-05-22 21:33:47", "author_account_age_days": 2791}, "https://github.com/thecooltechguy/ComfyUI-MagicAnimate": {"stars": 223, "last_update": "2024-05-22 21:33:35", "author_account_age_days": 2791}, "https://github.com/thecooltechguy/ComfyUI-Stable-Video-Diffusion": {"stars": 361, "last_update": "2024-05-24 22:14:42", "author_account_age_days": 2791}, "https://github.com/thedivergentai/divergent_nodes": {"stars": 0, "last_update": "2025-06-23 16:16:30", "author_account_age_days": 827}, "https://github.com/theshubzworld/ComfyUI-FaceCalloutNode": {"stars": 0, "last_update": "2025-05-09 14:38:15", "author_account_age_days": 339}, "https://github.com/theshubzworld/ComfyUI-SD3.5-Latent-Size-Picker": {"stars": 0, "last_update": "2024-12-25 14:09:38", "author_account_age_days": 339}, "https://github.com/theshubzworld/ComfyUI-TogetherVision": {"stars": 3, "last_update": "2025-06-26 18:48:25", "author_account_age_days": 339}, "https://github.com/theshubzworld/ComfyUI-ollama_killer": {"stars": 3, "last_update": "2025-06-09 09:14:55", "author_account_age_days": 339}, "https://github.com/thezveroboy/ComfyUI-CSM-Nodes": {"stars": 35, "last_update": "2025-03-17 10:08:12", "author_account_age_days": 3581}, "https://github.com/thezveroboy/ComfyUI-WAN-ClipSkip": {"stars": 1, "last_update": "2025-03-16 21:12:54", "author_account_age_days": 3581}, "https://github.com/thezveroboy/ComfyUI-lut": {"stars": 2, "last_update": "2025-05-24 21:37:06", "author_account_age_days": 3581}, "https://github.com/thezveroboy/ComfyUI_ACE-Step-zveroboy": {"stars": 1, "last_update": "2025-05-12 11:01:16", "author_account_age_days": 3581}, "https://github.com/thezveroboy/comfyui-random-image-loader": {"stars": 1, "last_update": "2025-05-11 18:04:32", "author_account_age_days": 3581}, "https://github.com/thoddnn/ComfyUI-MLX": {"stars": 141, "last_update": "2024-10-22 06:41:22", "author_account_age_days": 632}, "https://github.com/tianguanggliu/Utools": {"stars": 0, "last_update": "2024-08-29 09:45:03", "author_account_age_days": 2708}, "https://github.com/tiankuan93/ComfyUI-V-Express": {"stars": 112, "last_update": "2024-06-26 02:41:00", "author_account_age_days": 3318}, "https://github.com/tianlang0704/ComfyUI-StableProjectorzBridge": {"stars": 37, "last_update": "2024-12-01 11:46:58", "author_account_age_days": 3693}, "https://github.com/tianyuw/ComfyUI-LLM-API": {"stars": 6, "last_update": "2025-01-25 19:31:47", "author_account_age_days": 3560}, "https://github.com/tigeryy2/comfyui-structured-outputs": {"stars": 1, "last_update": "2025-06-19 04:48:05", "author_account_age_days": 2339}, "https://github.com/tighug/comfyui-eagle-feeder": {"stars": 0, "last_update": "2025-05-09 14:26:15", "author_account_age_days": 2478}, "https://github.com/tighug/comfyui-rating-checker": {"stars": 1, "last_update": "2025-05-09 14:22:51", "author_account_age_days": 2478}, "https://github.com/tkreuziger/comfyui-claude": {"stars": 4, "last_update": "2025-04-10 18:23:35", "author_account_age_days": 904}, "https://github.com/tmagara/ComfyUI-Prediction-Boost": {"stars": 1, "last_update": "2024-07-31 13:51:19", "author_account_age_days": 4743}, "https://github.com/tocubed/ComfyUI-AudioReactor": {"stars": 8, "last_update": "2024-05-22 22:21:57", "author_account_age_days": 4228}, "https://github.com/tocubed/ComfyUI-EvTexture": {"stars": 15, "last_update": "2025-01-05 23:21:23", "author_account_age_days": 4228}, "https://github.com/tomudo/ComfyUI-ascii-art": {"stars": 3, "last_update": "2024-11-21 05:24:12", "author_account_age_days": 3271}, "https://github.com/tooldigital/ComfyUI-Yolo-Cropper": {"stars": 9, "last_update": "2024-06-14 13:59:48", "author_account_age_days": 4686}, "https://github.com/toxicwind/ComfyUI-TTools": {"stars": 1, "last_update": "2024-07-04 20:07:35", "author_account_age_days": 4738}, "https://github.com/toyxyz/ComfyUI_rgbx_Wrapper": {"stars": 88, "last_update": "2025-04-03 08:17:10", "author_account_age_days": 4018}, "https://github.com/toyxyz/ComfyUI_toyxyz_test_nodes": {"stars": 582, "last_update": "2025-06-10 14:20:31", "author_account_age_days": 4018}, "https://github.com/traugdor/ComfyUI-Riffusion": {"stars": 3, "last_update": "2025-05-30 20:15:05", "author_account_age_days": 4188}, "https://github.com/traugdor/ComfyUI-UltimateSDUpscale-GGUF": {"stars": 13, "last_update": "2025-06-21 15:15:07", "author_account_age_days": 4188}, "https://github.com/traugdor/ComfyUI-quadMoons-nodes": {"stars": 14, "last_update": "2025-06-23 15:18:42", "author_account_age_days": 4188}, "https://github.com/tritant/ComfyUI_CreaPrompt": {"stars": 59, "last_update": "2025-06-17 03:16:11", "author_account_age_days": 3509}, "https://github.com/tritant/ComfyUI_Flux_Block_Lora_Merger": {"stars": 1, "last_update": "2025-06-10 01:27:00", "author_account_age_days": 3509}, "https://github.com/tritant/ComfyUI_Flux_Lora_Merger": {"stars": 2, "last_update": "2025-05-09 04:39:16", "author_account_age_days": 3509}, "https://github.com/trojblue/trNodes": {"stars": 8, "last_update": "2024-05-22 18:04:36", "author_account_age_days": 2626}, "https://github.com/troyxmccall/ComfyUI-ScaleToTargetMegapixels": {"stars": 1, "last_update": "2024-11-11 00:07:25", "author_account_age_days": 5757}, "https://github.com/trumanwong/ComfyUI-NSFW-Detection": {"stars": 35, "last_update": "2025-04-21 05:38:12", "author_account_age_days": 3328}, "https://github.com/tsogzark/ComfyUI-load-image-from-url": {"stars": 20, "last_update": "2024-06-14 13:59:05", "author_account_age_days": 1897}, "https://github.com/ttulttul/ComfyUI-Iterative-Mixer": {"stars": 117, "last_update": "2025-03-10 03:33:02", "author_account_age_days": 5125}, "https://github.com/ttulttul/ComfyUI-Tensor-Operations": {"stars": 6, "last_update": "2025-02-03 16:57:00", "author_account_age_days": 5125}, "https://github.com/tungdop2/Comfyui_face_restorer": {"stars": 2, "last_update": "2024-11-21 15:53:59", "author_account_age_days": 1792}, "https://github.com/tungdop2/Comfyui_joy-caption-alpha-two": {"stars": 6, "last_update": "2025-04-19 06:00:23", "author_account_age_days": 1792}, "https://github.com/turkyden/ComfyUI-SmartCrop": {"stars": 3, "last_update": "2024-10-08 09:36:34", "author_account_age_days": 3117}, "https://github.com/tusharbhutt/Endless-Nodes": {"stars": 42, "last_update": "2025-06-25 04:14:23", "author_account_age_days": 3033}, "https://github.com/twri/sdxl_prompt_styler": {"stars": 858, "last_update": "2024-05-22 18:16:58", "author_account_age_days": 4436}, "https://github.com/txt2any/ComfyUI-PromptOrganizer": {"stars": 0, "last_update": "2024-05-23 01:10:33", "author_account_age_days": 453}, "https://github.com/ty0x2333/ComfyUI-Dev-Utils": {"stars": 134, "last_update": "2024-10-03 23:26:45", "author_account_age_days": 4071}, "https://github.com/tzwm/comfyui-profiler": {"stars": 157, "last_update": "2024-08-28 14:27:12", "author_account_age_days": 5137}, "https://github.com/uarefans/ComfyUI-Fans": {"stars": 17, "last_update": "2024-07-14 15:00:38", "author_account_age_days": 1640}, "https://github.com/uetuluk/comfyui-webcam-node": {"stars": 4, "last_update": "2024-06-14 08:25:13", "author_account_age_days": 2682}, "https://github.com/uihp/ComfyUI-String-Chain": {"stars": 0, "last_update": "2025-04-12 12:22:14", "author_account_age_days": 1405}, "https://github.com/uinodes/ComfyUI-uinodesDOC": {"stars": 31, "last_update": "2025-06-26 04:07:59", "author_account_age_days": 2}, "https://github.com/umiyuki/comfyui-pad-to-eight": {"stars": 0, "last_update": "2025-01-07 09:58:36", "author_account_age_days": 4121}, "https://github.com/un-seen/comfyui-tensorops": {"stars": 28, "last_update": "2024-10-26 00:04:07", "author_account_age_days": 1684}, "https://github.com/un-seen/comfyui_segment_anything_plus": {"stars": 8, "last_update": "2024-07-29 06:21:54", "author_account_age_days": 1684}, "https://github.com/unicough/comfy_openai_image_api": {"stars": 0, "last_update": "2025-05-02 04:24:34", "author_account_age_days": 4078}, "https://github.com/unwdef/unwdef-nodes-comfyui": {"stars": 5, "last_update": "2025-03-27 10:42:15", "author_account_age_days": 442}, "https://github.com/usrname0/comfyui-holdup": {"stars": 0, "last_update": "2025-06-12 07:26:10", "author_account_age_days": 2781}, "https://github.com/vadimcro/VKRiez-Edge": {"stars": 7, "last_update": "2025-03-18 11:18:27", "author_account_age_days": 3003}, "https://github.com/vahidzxc/va-nodes": {"stars": 2, "last_update": "2025-03-22 01:50:08", "author_account_age_days": 359}, "https://github.com/vahlok-alunmid/ComfyUI-ExtendIPAdapterClipVision": {"stars": 12, "last_update": "2025-02-09 04:06:34", "author_account_age_days": 2754}, "https://github.com/valofey/Openrouter-Node": {"stars": 5, "last_update": "2025-02-13 21:26:22", "author_account_age_days": 1752}, "https://github.com/vanche1212/ComfyUI-ZMG-Nodes": {"stars": 3, "last_update": "2024-06-25 04:48:19", "author_account_age_days": 3326}, "https://github.com/vanillacode314/SimpleWildcardsComfyUI": {"stars": 5, "last_update": "2025-04-02 04:56:25", "author_account_age_days": 1225}, "https://github.com/var1ableX/ComfyUI_Accessories": {"stars": 1, "last_update": "2025-02-09 14:31:19", "author_account_age_days": 5132}, "https://github.com/vault-developer/comfyui-image-blender": {"stars": 20, "last_update": "2025-04-02 19:37:15", "author_account_age_days": 2982}, "https://github.com/veighnsche/comfyui_gr85": {"stars": 1, "last_update": "2024-11-26 17:26:48", "author_account_age_days": 3468}, "https://github.com/vekitan55/SimpleFlux1Merger": {"stars": 0, "last_update": "2025-04-23 12:09:47", "author_account_age_days": 698}, "https://github.com/victorchall/comfyui_webcamcapture": {"stars": 14, "last_update": "2025-04-16 20:39:32", "author_account_age_days": 3514}, "https://github.com/vienteck/ComfyUI-Chat-GPT-Integration": {"stars": 31, "last_update": "2024-05-22 22:11:14", "author_account_age_days": 3795}, "https://github.com/vincentfs/ComfyUI-ArchiGraph": {"stars": 2, "last_update": "2025-01-23 17:29:09", "author_account_age_days": 4034}, "https://github.com/violet-chen/comfyui-psd2png": {"stars": 20, "last_update": "2025-06-04 11:41:34", "author_account_age_days": 1766}, "https://github.com/violet0927/ComfyUI-HuggingFaceLoraUploader": {"stars": 0, "last_update": "2025-06-03 05:46:11", "author_account_age_days": 148}, "https://github.com/viperyl/ComfyUI-RGT": {"stars": 8, "last_update": "2024-06-20 15:33:50", "author_account_age_days": 2401}, "https://github.com/vivax3794/ComfyUI-Sub-Nodes": {"stars": 163, "last_update": "2025-02-21 07:03:30", "author_account_age_days": 2207}, "https://github.com/vivax3794/ComfyUI-Vivax-Nodes": {"stars": 3, "last_update": "2024-09-07 18:42:27", "author_account_age_days": 2207}, "https://github.com/vivi-gomez/ComfyUI-fixnodetranslate": {"stars": 0, "last_update": "2025-06-01 08:42:50", "author_account_age_days": 4716}, "https://github.com/vkff5833/ComfyUI-MobileClient": {"stars": 4, "last_update": "2025-02-11 00:34:36", "author_account_age_days": 662}, "https://github.com/vkff5833/ComfyUI-PromptConverter": {"stars": 2, "last_update": "2025-01-27 18:35:41", "author_account_age_days": 663}, "https://github.com/vladpro3/ComfyUI_BishaNodes": {"stars": 1, "last_update": "2025-06-08 19:23:23", "author_account_age_days": 2694}, "https://github.com/vsevolod-oparin/comfyui-kandinsky22": {"stars": 10, "last_update": "2025-04-02 03:48:05", "author_account_age_days": 5352}, "https://github.com/vuongminh1907/ComfyUI_ZenID": {"stars": 179, "last_update": "2025-03-27 00:11:23", "author_account_age_days": 940}, "https://github.com/wTechArtist/ComfyUI-CustomNodes": {"stars": 2, "last_update": "2024-08-21 03:03:16", "author_account_age_days": 1729}, "https://github.com/wTechArtist/ComfyUI-StableDelight-weiweiliang": {"stars": 2, "last_update": "2025-03-23 07:52:36", "author_account_age_days": 1729}, "https://github.com/wTechArtist/ComfyUI_VVL_VideoCamera_Advanced": {"stars": 1, "last_update": "2025-06-23 10:08:04", "author_account_age_days": 1729}, "https://github.com/wakattac/ComfyUI-AbstractImaGen": {"stars": 1, "last_update": "2025-05-09 22:37:03", "author_account_age_days": 52}, "https://github.com/wallish77/wlsh_nodes": {"stars": 125, "last_update": "2024-06-19 12:01:29", "author_account_age_days": 2588}, "https://github.com/wandbrandon/comfyui-pixel": {"stars": 4, "last_update": "2024-06-14 07:07:09", "author_account_age_days": 3752}, "https://github.com/wasilone11/comfyui-sync-lipsync-node": {"stars": 0, "last_update": "2025-06-25 21:57:56", "author_account_age_days": 2574}, "https://github.com/waterminer/ComfyUI-tagcomplete": {"stars": 11, "last_update": "2025-01-06 00:13:57", "author_account_age_days": 2480}, "https://github.com/web3nomad/ComfyUI_Invisible_Watermark": {"stars": 1, "last_update": "2024-05-23 01:16:54", "author_account_age_days": 1336}, "https://github.com/webfiltered/DebugNode-ComfyUI": {"stars": 8, "last_update": "2025-05-06 16:15:33", "author_account_age_days": 342}, "https://github.com/wei30172/comfygen": {"stars": 7, "last_update": "2024-11-07 22:10:50", "author_account_age_days": 1981}, "https://github.com/weilin9999/WeiLin-Comfyui-Tools": {"stars": 166, "last_update": "2025-05-08 04:25:11", "author_account_age_days": 2275}, "https://github.com/welltop-cn/ComfyUI-TeaCache": {"stars": 833, "last_update": "2025-06-22 07:43:14", "author_account_age_days": 1939}, "https://github.com/wentao-uw/ComfyUI-template-matching": {"stars": 1, "last_update": "2024-11-06 06:52:30", "author_account_age_days": 2148}, "https://github.com/westNeighbor/ComfyUI-ultimate-openpose-editor": {"stars": 59, "last_update": "2025-06-16 17:57:31", "author_account_age_days": 662}, "https://github.com/westNeighbor/ComfyUI-ultimate-openpose-estimator": {"stars": 12, "last_update": "2025-06-03 21:06:33", "author_account_age_days": 662}, "https://github.com/westNeighbor/ComfyUI-ultimate-openpose-render": {"stars": 8, "last_update": "2025-01-25 05:54:27", "author_account_age_days": 662}, "https://github.com/whatbirdisthat/cyberdolphin": {"stars": 14, "last_update": "2024-07-31 13:40:12", "author_account_age_days": 5855}, "https://github.com/whmc76/ComfyUI-Openpose-Editor-Plus": {"stars": 38, "last_update": "2024-06-20 13:52:34", "author_account_age_days": 820}, "https://github.com/whmc76/ComfyUI-RemoveBackgroundSuite": {"stars": 3, "last_update": "2025-06-11 10:11:09", "author_account_age_days": 820}, "https://github.com/whmc76/ComfyUI-UniversalToolkit": {"stars": 1, "last_update": "2025-06-24 11:07:09", "author_account_age_days": 820}, "https://github.com/wildminder/000_ComfyUI-Optim": {"stars": 4, "last_update": "2025-06-02 21:30:04", "author_account_age_days": 4600}, "https://github.com/wildminder/ComfyUI-Chatterbox": {"stars": 28, "last_update": "2025-05-30 06:27:05", "author_account_age_days": 4600}, "https://github.com/wildminder/ComfyUI-KEEP": {"stars": 33, "last_update": "2025-05-30 21:03:54", "author_account_age_days": 4600}, "https://github.com/willchil/ComfyUI-Environment-Visualizer": {"stars": 12, "last_update": "2025-03-29 23:09:07", "author_account_age_days": 3006}, "https://github.com/willmiao/ComfyUI-Lora-Manager": {"stars": 321, "last_update": "2025-06-27 02:12:25", "author_account_age_days": 3725}, "https://github.com/windfancy/zsq_prompt": {"stars": 0, "last_update": "2024-12-15 14:58:52", "author_account_age_days": 1904}, "https://github.com/wings6407/ComfyUI_HBH-image_overlay": {"stars": 1, "last_update": "2025-05-12 02:52:38", "author_account_age_days": 456}, "https://github.com/wirytiox/ComfyUI-SelectStringFromListWithIndex": {"stars": 1, "last_update": "2025-02-16 09:09:34", "author_account_age_days": 1591}, "https://github.com/withmpx/mpx-comfyui-nodes": {"stars": 2, "last_update": "2025-04-16 22:08:20", "author_account_age_days": 97}, "https://github.com/without-ordinary/openoutpaint_comfyui_interface": {"stars": 0, "last_update": "2025-06-13 10:37:31", "author_account_age_days": 3291}, "https://github.com/wjl0313/ComfyUI_KimNodes": {"stars": 33, "last_update": "2025-05-12 03:25:33", "author_account_age_days": 2239}, "https://github.com/wmatson/easy-comfy-nodes": {"stars": 18, "last_update": "2025-04-17 16:26:02", "author_account_age_days": 4486}, "https://github.com/wmpmiles/comfyui-some-image-processing-stuff": {"stars": 4, "last_update": "2025-05-10 05:51:42", "author_account_age_days": 3398}, "https://github.com/wolfden/ComfyUi_PromptStylers": {"stars": 95, "last_update": "2025-02-15 18:38:12", "author_account_age_days": 6089}, "https://github.com/wolfden/ComfyUi_String_Function_Tree": {"stars": 10, "last_update": "2024-05-22 18:29:16", "author_account_age_days": 6089}, "https://github.com/wootwootwootwoot/ComfyUI-RK-Sampler": {"stars": 58, "last_update": "2024-08-17 21:12:43", "author_account_age_days": 1936}, "https://github.com/wqjuser/ComfyUI-Chat-Image": {"stars": 0, "last_update": "2024-12-26 07:00:30", "author_account_age_days": 3301}, "https://github.com/wu12023/ComfyUI-Image-Evaluation": {"stars": 9, "last_update": "2024-12-06 06:51:15", "author_account_age_days": 684}, "https://github.com/wujm424606/ComfyUi-Ollama-YN": {"stars": 82, "last_update": "2024-09-17 13:20:02", "author_account_age_days": 2631}, "https://github.com/wutipong/ComfyUI-TextUtils": {"stars": 1, "last_update": "2024-06-14 09:34:31", "author_account_age_days": 4553}, "https://github.com/wwwins/ComfyUI-Simple-Aspect-Ratio": {"stars": 1, "last_update": "2024-05-22 22:22:25", "author_account_age_days": 5418}, "https://github.com/wywywywy/ComfyUI-pause": {"stars": 16, "last_update": "2025-05-05 21:37:34", "author_account_age_days": 3296}, "https://github.com/xLegende/ComfyUI-Prompt-Formatter": {"stars": 2, "last_update": "2025-06-10 19:29:54", "author_account_age_days": 1800}, "https://github.com/xXAdonesXx/NodeGPT": {"stars": 349, "last_update": "2024-06-20 11:41:30", "author_account_age_days": 1847}, "https://github.com/xfgexo/EXO-Custom-ComfyUI-Nodes": {"stars": 2, "last_update": "2024-12-24 14:07:18", "author_account_age_days": 796}, "https://github.com/xhiroga/ComfyUI-FramePackWrapper_PlusOne": {"stars": 20, "last_update": "2025-06-26 01:39:27", "author_account_age_days": 3632}, "https://github.com/xiaogui8dangjia/Comfyui-imagetoSTL": {"stars": 2, "last_update": "2025-06-06 04:08:30", "author_account_age_days": 2035}, "https://github.com/xiaowc-lib/comfyui-dynamic-params": {"stars": 0, "last_update": "2025-06-09 08:56:11", "author_account_age_days": 3247}, "https://github.com/xiaoxiaodesha/hd_node": {"stars": 15, "last_update": "2024-06-11 02:36:48", "author_account_age_days": 3243}, "https://github.com/xingBaGan/ComfyUI-connect-ui": {"stars": 1, "last_update": "2025-04-07 09:54:46", "author_account_age_days": 2162}, "https://github.com/xlinx/ComfyUI-decadetw-auto-messaging-realtime": {"stars": 8, "last_update": "2024-08-30 17:38:52", "author_account_age_days": 4867}, "https://github.com/xlinx/ComfyUI-decadetw-auto-prompt-llm": {"stars": 24, "last_update": "2025-02-01 18:36:52", "author_account_age_days": 4867}, "https://github.com/xlinx/ComfyUI-decadetw-spout-syphon-im-vj": {"stars": 12, "last_update": "2024-09-03 08:55:08", "author_account_age_days": 4867}, "https://github.com/xliry/ComfyUI_SendDiscord": {"stars": 0, "last_update": "2024-05-23 02:21:38", "author_account_age_days": 1642}, "https://github.com/xmarre/TorchCompileModel_LoRASafe": {"stars": 3, "last_update": "2025-06-06 18:40:09", "author_account_age_days": 2123}, "https://github.com/xobiomesh/ComfyUI_xObiomesh": {"stars": 2, "last_update": "2024-11-08 17:10:40", "author_account_age_days": 2058}, "https://github.com/xs315431/Comfyui_Get_promptId": {"stars": 1, "last_update": "2024-12-02 09:30:53", "author_account_age_days": 1642}, "https://github.com/xuhongming251/ComfyUI-GPEN": {"stars": 4, "last_update": "2025-04-16 21:37:02", "author_account_age_days": 4474}, "https://github.com/xuhongming251/ComfyUI-Jimeng": {"stars": 2, "last_update": "2025-06-11 09:39:59", "author_account_age_days": 4474}, "https://github.com/xuhongming251/ComfyUI-MuseTalkUtils": {"stars": 21, "last_update": "2025-04-16 21:36:46", "author_account_age_days": 4474}, "https://github.com/xuhongming251/ComfyUI_Camera": {"stars": 3, "last_update": "2025-05-05 18:30:40", "author_account_age_days": 4474}, "https://github.com/yanhuifair/comfyui-janus": {"stars": 4, "last_update": "2025-04-08 09:13:57", "author_account_age_days": 3929}, "https://github.com/yanlang0123/ComfyUI_Lam": {"stars": 44, "last_update": "2025-05-24 12:20:05", "author_account_age_days": 3176}, "https://github.com/yasser-baalla/comfyUI-SemanticImageFetch": {"stars": 0, "last_update": "2025-03-22 11:04:33", "author_account_age_days": 1764}, "https://github.com/ycchanau/ComfyUI_Preview_Magnifier": {"stars": 2, "last_update": "2024-07-31 13:59:12", "author_account_age_days": 2485}, "https://github.com/ycyy/ComfyUI-YCYY-LoraInfo": {"stars": 5, "last_update": "2024-09-30 02:33:25", "author_account_age_days": 3793}, "https://github.com/yffyhk/comfyui_auto_danbooru": {"stars": 1, "last_update": "2024-05-22 23:23:03", "author_account_age_days": 4090}, "https://github.com/yhayano-ponotech/ComfyUI-Fal-API-Flux": {"stars": 56, "last_update": "2025-01-16 08:47:22", "author_account_age_days": 939}, "https://github.com/yhayano-ponotech/comfyui-save-image-local": {"stars": 4, "last_update": "2025-01-15 12:30:50", "author_account_age_days": 939}, "https://github.com/yhayano-ponotech/comfyui-stability-ai-api": {"stars": 0, "last_update": "2025-02-19 00:38:33", "author_account_age_days": 938}, "https://github.com/yichengup/ComfyUI-LinearTransition": {"stars": 1, "last_update": "2025-05-10 14:50:25", "author_account_age_days": 493}, "https://github.com/yichengup/ComfyUI-YCNodes": {"stars": 23, "last_update": "2025-06-08 17:30:52", "author_account_age_days": 493}, "https://github.com/yichengup/ComfyUI_Yc_JanusPro": {"stars": 7, "last_update": "2025-01-29 22:26:38", "author_account_age_days": 493}, "https://github.com/yichengup/Comfyui-Deepseek": {"stars": 32, "last_update": "2025-02-23 19:36:53", "author_account_age_days": 493}, "https://github.com/yichengup/Comfyui-Ycanvas": {"stars": 72, "last_update": "2024-12-22 01:26:50", "author_account_age_days": 493}, "https://github.com/yichengup/Comfyui_Flux_Style_Adjust": {"stars": 295, "last_update": "2025-02-19 05:08:27", "author_account_age_days": 493}, "https://github.com/yichengup/Comfyui_Redux_Advanced": {"stars": 99, "last_update": "2025-04-10 18:36:47", "author_account_age_days": 493}, "https://github.com/yichengup/comfyui-face-liquify": {"stars": 1, "last_update": "2025-05-08 17:59:05", "author_account_age_days": 493}, "https://github.com/yiwangsimple/ComfyUI_DW_Chat": {"stars": 88, "last_update": "2025-02-06 03:34:59", "author_account_age_days": 920}, "https://github.com/yiwangsimple/florence_dw": {"stars": 47, "last_update": "2025-02-13 01:52:15", "author_account_age_days": 920}, "https://github.com/yogurt7771/ComfyUI-YogurtNodes": {"stars": 0, "last_update": "2025-06-25 03:25:33", "author_account_age_days": 3197}, "https://github.com/yolain/ComfyUI-Easy-Use": {"stars": 1701, "last_update": "2025-06-26 08:45:49", "author_account_age_days": 1709}, "https://github.com/yolanother/ComfyUI-Save16bitPng": {"stars": 3, "last_update": "2024-12-23 01:50:04", "author_account_age_days": 5232}, "https://github.com/yolanother/DTAIComfyImageSubmit": {"stars": 1, "last_update": "2024-09-25 04:40:23", "author_account_age_days": 5232}, "https://github.com/yolanother/DTAIComfyLoaders": {"stars": 1, "last_update": "2024-11-18 09:35:46", "author_account_age_days": 5232}, "https://github.com/yolanother/DTAIComfyPromptAgent": {"stars": 5, "last_update": "2024-05-22 18:14:18", "author_account_age_days": 5232}, "https://github.com/yolanother/DTAIComfyQRCodes": {"stars": 4, "last_update": "2024-05-22 18:15:09", "author_account_age_days": 5232}, "https://github.com/yolanother/DTAIComfyVariables": {"stars": 12, "last_update": "2024-05-22 18:15:21", "author_account_age_days": 5232}, "https://github.com/yolanother/DTAIImageToTextNode": {"stars": 19, "last_update": "2024-05-22 18:14:31", "author_account_age_days": 5232}, "https://github.com/yondonfu/ComfyUI-Background-Edit": {"stars": 22, "last_update": "2024-12-31 23:15:33", "author_account_age_days": 4244}, "https://github.com/yondonfu/ComfyUI-Torch-Compile": {"stars": 4, "last_update": "2025-04-30 18:46:47", "author_account_age_days": 4244}, "https://github.com/yorkane/ComfyUI-KYNode": {"stars": 7, "last_update": "2025-06-12 16:16:02", "author_account_age_days": 3758}, "https://github.com/younyokel/comfyui_prompt_formatter": {"stars": 3, "last_update": "2025-05-16 16:33:11", "author_account_age_days": 2162}, "https://github.com/youyegit/tdxh_node_comfyui": {"stars": 2, "last_update": "2025-03-17 08:22:16", "author_account_age_days": 797}, "https://github.com/yuan199696/add_text_2_img": {"stars": 7, "last_update": "2025-03-27 14:40:27", "author_account_age_days": 2811}, "https://github.com/yuan199696/chinese_clip_encode": {"stars": 9, "last_update": "2025-03-27 14:39:40", "author_account_age_days": 2811}, "https://github.com/yushan777/ComfyUI-Y7-SBS-2Dto3D": {"stars": 4, "last_update": "2025-06-13 18:44:06", "author_account_age_days": 885}, "https://github.com/yushan777/ComfyUI-Y7Nodes": {"stars": 3, "last_update": "2025-06-14 19:55:01", "author_account_age_days": 885}, "https://github.com/yuvraj108c/ComfyUI-Depth-Anything-Tensorrt": {"stars": 105, "last_update": "2025-05-20 08:34:27", "author_account_age_days": 2521}, "https://github.com/yuvraj108c/ComfyUI-Dwpose-Tensorrt": {"stars": 31, "last_update": "2025-05-03 19:32:24", "author_account_age_days": 2521}, "https://github.com/yuvraj108c/ComfyUI-FLOAT": {"stars": 191, "last_update": "2025-05-28 07:27:16", "author_account_age_days": 2521}, "https://github.com/yuvraj108c/ComfyUI-Facerestore-Tensorrt": {"stars": 21, "last_update": "2024-09-22 13:07:19", "author_account_age_days": 2521}, "https://github.com/yuvraj108c/ComfyUI-PiperTTS": {"stars": 28, "last_update": "2024-05-22 23:17:27", "author_account_age_days": 2521}, "https://github.com/yuvraj108c/ComfyUI-Pronodes": {"stars": 3, "last_update": "2025-01-05 10:06:31", "author_account_age_days": 2521}, "https://github.com/yuvraj108c/ComfyUI-Rife-Tensorrt": {"stars": 18, "last_update": "2024-10-04 10:23:26", "author_account_age_days": 2521}, "https://github.com/yuvraj108c/ComfyUI-Thera": {"stars": 35, "last_update": "2025-05-01 07:52:54", "author_account_age_days": 2521}, "https://github.com/yuvraj108c/ComfyUI-Upscaler-Tensorrt": {"stars": 136, "last_update": "2025-05-03 17:15:09", "author_account_age_days": 2521}, "https://github.com/yuvraj108c/ComfyUI-Video-Depth-Anything": {"stars": 28, "last_update": "2025-05-01 09:04:25", "author_account_age_days": 2521}, "https://github.com/yuvraj108c/ComfyUI-Vsgan": {"stars": 3, "last_update": "2024-05-22 23:17:02", "author_account_age_days": 2521}, "https://github.com/yuvraj108c/ComfyUI-Whisper": {"stars": 115, "last_update": "2025-05-02 07:59:15", "author_account_age_days": 2521}, "https://github.com/yuvraj108c/ComfyUI-YoloNasPose-Tensorrt": {"stars": 13, "last_update": "2024-06-28 15:59:14", "author_account_age_days": 2521}, "https://github.com/yuvraj108c/ComfyUI_InvSR": {"stars": 213, "last_update": "2025-04-30 08:19:02", "author_account_age_days": 2521}, "https://github.com/yvann-ba/ComfyUI_Yvann-Nodes": {"stars": 442, "last_update": "2025-06-02 12:11:14", "author_account_age_days": 1268}, "https://github.com/za-wa-n-go/ComfyUI_Zwng_Nodes": {"stars": 7, "last_update": "2025-03-27 23:13:16", "author_account_age_days": 956}, "https://github.com/zaheenrahman/ComfyUI-ColorCorrection": {"stars": 1, "last_update": "2025-03-21 09:52:29", "author_account_age_days": 2728}, "https://github.com/zakantonio/AvatarGen-experience": {"stars": 0, "last_update": "2025-03-26 20:58:18", "author_account_age_days": 4144}, "https://github.com/zccrs/comfyui-dci": {"stars": 1, "last_update": "2025-06-13 07:35:50", "author_account_age_days": 3628}, "https://github.com/zcfrank1st/Comfyui-Toolbox": {"stars": 6, "last_update": "2024-05-22 22:08:07", "author_account_age_days": 4783}, "https://github.com/zcfrank1st/Comfyui-Yolov8": {"stars": 25, "last_update": "2024-06-14 07:08:40", "author_account_age_days": 4783}, "https://github.com/zcfrank1st/comfyui_visual_anagrams": {"stars": 8, "last_update": "2024-06-14 07:07:27", "author_account_age_days": 4783}, "https://github.com/zentrocdot/ComfyUI-RealESRGAN_Upscaler": {"stars": 7, "last_update": "2025-02-09 18:27:16", "author_account_age_days": 578}, "https://github.com/zentrocdot/ComfyUI-Simple_Image_To_Prompt": {"stars": 1, "last_update": "2025-02-20 06:30:19", "author_account_age_days": 578}, "https://github.com/zentrocdot/ComfyUI_Circle_Detection": {"stars": 1, "last_update": "2025-02-07 17:32:46", "author_account_age_days": 578}, "https://github.com/zer0TF/cute-comfy": {"stars": 35, "last_update": "2024-05-22 21:18:53", "author_account_age_days": 3038}, "https://github.com/zer0thgear/zer0-comfy-utils": {"stars": 0, "last_update": "2025-01-26 19:33:59", "author_account_age_days": 485}, "https://github.com/zeroxoxo/ComfyUI-Fast-Style-Transfer": {"stars": 71, "last_update": "2025-04-07 05:52:19", "author_account_age_days": 2811}, "https://github.com/zfkun/ComfyUI_zfkun": {"stars": 21, "last_update": "2025-06-03 23:57:53", "author_account_age_days": 5231}, "https://github.com/zhangp365/ComfyUI-utils-nodes": {"stars": 79, "last_update": "2025-06-27 07:38:36", "author_account_age_days": 659}, "https://github.com/zhangp365/ComfyUI_photomakerV2_native": {"stars": 10, "last_update": "2025-04-07 10:58:52", "author_account_age_days": 659}, "https://github.com/zhilemann/ComfyUI-moondream2": {"stars": 1, "last_update": "2024-12-29 13:17:31", "author_account_age_days": 661}, "https://github.com/zhiselfly/ComfyUI-Alimama-ControlNet-compatible": {"stars": 18, "last_update": "2024-09-14 13:46:05", "author_account_age_days": 3718}, "https://github.com/zhongpei/ComfyUI-InstructIR": {"stars": 71, "last_update": "2024-05-22 23:19:43", "author_account_age_days": 3827}, "https://github.com/zhuanqianfish/ComfyUI-EasyNode": {"stars": 67, "last_update": "2024-06-14 07:10:18", "author_account_age_days": 4603}, "https://github.com/zhulu111/ComfyUI_Bxb": {"stars": 1407, "last_update": "2025-02-05 10:33:45", "author_account_age_days": 407}, "https://github.com/zichongc/ComfyUI-Attention-Distillation": {"stars": 110, "last_update": "2025-03-18 02:48:42", "author_account_age_days": 864}, "https://github.com/ziwang-com/comfyui-deepseek-r1": {"stars": 60, "last_update": "2025-02-02 14:24:35", "author_account_age_days": 3743}, "https://github.com/zmwv823/ComfyUI_Anytext": {"stars": 82, "last_update": "2025-05-28 01:02:37", "author_account_age_days": 3636}, "https://github.com/zohac/ComfyUI_ZC_DrawShape": {"stars": 3, "last_update": "2024-06-25 15:05:28", "author_account_age_days": 3030}, "https://github.com/zombieyang/sd-ppp": {"stars": 1527, "last_update": "2025-06-19 14:40:11", "author_account_age_days": 4286}, "https://github.com/zubenelakrab/ComfyUI-ASV-Nodes": {"stars": 1, "last_update": "2024-11-04 00:51:29", "author_account_age_days": 5330}, "https://github.com/zygion/comfyui-zygion-util-nodes": {"stars": 0, "last_update": "2025-04-26 05:11:35", "author_account_age_days": 173}, "https://github.com/zzubnik/TT_TextTools": {"stars": 0, "last_update": "2025-04-02 23:40:24", "author_account_age_days": 3093}, "https://github.com/zzw5516/ComfyUI-zw-tools": {"stars": 1, "last_update": "2025-04-16 08:24:48", "author_account_age_days": 4510}}