"""Module contains base class for prompts.

BaseSimplePrompt ← <PERSON><PERSON><PERSON><PERSON><PERSON> ← SecretPrompt ...
        ↑
BaseComplexPrompt
        ↑
BaseListPrompt ← FuzzyPrompt
        ↑
ListPrompt ← ExpandPrompt ...
"""

from .complex import BaseComplexPrompt, FakeDocument
from .control import Choice, InquirerPyUIListControl
from .list import BaseListPrompt
from .simple import BaseSimplePrompt
