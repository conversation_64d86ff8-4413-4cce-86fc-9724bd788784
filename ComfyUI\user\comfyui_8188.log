## ComfyUI-Manager: installing dependencies done.
[2025-06-28 19:17:15.770] ** ComfyUI startup time: 2025-06-28 19:17:15.770
[2025-06-28 19:17:15.770] ** Platform: Windows
[2025-06-28 19:17:15.770] ** Python version: 3.13.5 (tags/v3.13.5:6cb20a2, Jun 11 2025, 16:15:46) [MSC v.1943 64 bit (AMD64)]
[2025-06-28 19:17:15.771] ** Python executable: C:\Users\<USER>\ai\ComfiGit\comfyui_env\Scripts\python.exe
[2025-06-28 19:17:15.771] ** ComfyUI Path: C:\Users\<USER>\ai\ComfiGit\ComfyUI
[2025-06-28 19:17:15.771] ** ComfyUI Base Folder Path: C:\Users\<USER>\ai\ComfiGit\ComfyUI
[2025-06-28 19:17:15.771] ** User directory: C:\Users\<USER>\ai\ComfiGit\ComfyUI\user
[2025-06-28 19:17:15.771] ** ComfyUI-Manager config path: C:\Users\<USER>\ai\ComfiGit\ComfyUI\user\default\ComfyUI-Manager\config.ini
[2025-06-28 19:17:15.771] ** Log path: C:\Users\<USER>\ai\ComfiGit\ComfyUI\user\comfyui.log
[ComfyUI-Manager] In Python 3.13 and above, PIP Fixer does not downgrade `numpy` below version 2.0. If you need to force a downgrade of `numpy`, please use `pip_auto_fix.list`.
[2025-06-28 19:17:16.591] 
Prestartup times for custom nodes:
[2025-06-28 19:17:16.592]    2.1 seconds: C:\Users\<USER>\ai\ComfiGit\ComfyUI\custom_nodes\ComfyUI-Manager
[2025-06-28 19:17:16.592] 
[2025-06-28 19:17:17.935] Checkpoint files will always be loaded safely.
[2025-06-28 19:17:18.050] Total VRAM 24463 MB, total RAM 64957 MB
[2025-06-28 19:17:18.050] pytorch version: 2.7.1+cu128
[2025-06-28 19:17:18.051] Set vram state to: NORMAL_VRAM
[2025-06-28 19:17:18.051] Device: cuda:0 NVIDIA GeForce RTX 5090 Laptop GPU : cudaMallocAsync
[2025-06-28 19:17:19.054] Using pytorch attention
[2025-06-28 19:17:20.524] Python version: 3.13.5 (tags/v3.13.5:6cb20a2, Jun 11 2025, 16:15:46) [MSC v.1943 64 bit (AMD64)]
[2025-06-28 19:17:20.524] ComfyUI version: 0.3.43
[2025-06-28 19:17:20.580] ComfyUI frontend version: 1.23.4
[2025-06-28 19:17:20.581] [Prompt Server] web root: C:\Users\<USER>\ai\ComfiGit\comfyui_env\Lib\site-packages\comfyui_frontend_package\static
[2025-06-28 19:17:21.256] ### Loading: ComfyUI-Impact-Pack (V8.17.1)
[2025-06-28 19:17:21.336] [Impact Pack] Wildcards loading done.
[2025-06-28 19:17:21.352] ### Loading: ComfyUI-Manager (V3.33.3)
[2025-06-28 19:17:21.353] [ComfyUI-Manager] network_mode: public
[2025-06-28 19:17:21.623] ### ComfyUI Version: v0.3.43 | Released on '2025-06-27'
[2025-06-28 19:17:22.138] [ComfyUI-Manager] default cache updated: https://raw.githubusercontent.com/ltdrdata/ComfyUI-Manager/main/alter-list.json
[2025-06-28 19:17:22.271] [ComfyUI-Manager] default cache updated: https://raw.githubusercontent.com/ltdrdata/ComfyUI-Manager/main/model-list.json
[2025-06-28 19:17:22.409] [ComfyUI-Manager] default cache updated: https://raw.githubusercontent.com/ltdrdata/ComfyUI-Manager/main/extension-node-map.json
[2025-06-28 19:17:22.504] [ComfyUI-Manager] default cache updated: https://raw.githubusercontent.com/ltdrdata/ComfyUI-Manager/main/github-stats.json
[2025-06-28 19:17:22.850] [ComfyUI-Manager] default cache updated: https://raw.githubusercontent.com/ltdrdata/ComfyUI-Manager/main/custom-node-list.json
[2025-06-28 19:17:23.926] Warning: Could not load sageattention: No module named 'sageattention'
[2025-06-28 19:17:23.926] sageattention package is not installed
[2025-06-28 19:17:24.131] 
Import times for custom nodes:
[2025-06-28 19:17:24.131]    0.0 seconds: C:\Users\<USER>\ai\ComfiGit\ComfyUI\custom_nodes\websocket_image_save.py
[2025-06-28 19:17:24.132]    0.0 seconds: C:\Users\<USER>\ai\ComfiGit\ComfyUI\custom_nodes\ComfyUI-WanVideoKsampler
[2025-06-28 19:17:24.132]    0.0 seconds: C:\Users\<USER>\ai\ComfiGit\ComfyUI\custom_nodes\ComfyUI-Custom-Scripts
[2025-06-28 19:17:24.132]    0.0 seconds: C:\Users\<USER>\ai\ComfiGit\ComfyUI\custom_nodes\efficiency-nodes-comfyui
[2025-06-28 19:17:24.133]    0.0 seconds: C:\Users\<USER>\ai\ComfiGit\ComfyUI\custom_nodes\ComfyUI-Advanced-ControlNet
[2025-06-28 19:17:24.133]    0.0 seconds: C:\Users\<USER>\ai\ComfiGit\ComfyUI\custom_nodes\ComfyUI-AnimateDiff-Evolved
[2025-06-28 19:17:24.133]    0.1 seconds: C:\Users\<USER>\ai\ComfiGit\ComfyUI\custom_nodes\ComfyUI-Impact-Pack
[2025-06-28 19:17:24.133]    0.2 seconds: C:\Users\<USER>\ai\ComfiGit\ComfyUI\custom_nodes\ComfyUI-VideoHelperSuite
[2025-06-28 19:17:24.133]    0.7 seconds: C:\Users\<USER>\ai\ComfiGit\ComfyUI\custom_nodes\ComfyUI-Manager
[2025-06-28 19:17:24.133]    1.9 seconds: C:\Users\<USER>\ai\ComfiGit\ComfyUI\custom_nodes\ComfyUI-WanVideoWrapper
[2025-06-28 19:17:24.134] 
[2025-06-28 19:17:24.645] Context impl SQLiteImpl.
[2025-06-28 19:17:24.646] Will assume non-transactional DDL.
[2025-06-28 19:17:24.647] No target revision found.
[2025-06-28 19:17:24.667] Starting server

[2025-06-28 19:17:24.668] To see the GUI go to: http://127.0.0.1:8188
[2025-06-28 19:17:27.094] FETCH ComfyRegistry Data: 5/90
[2025-06-28 19:17:31.824] FETCH ComfyRegistry Data: 10/90
[2025-06-28 19:17:35.795] FETCH ComfyRegistry Data: 15/90
[2025-06-28 19:17:39.946] FETCH ComfyRegistry Data: 20/90
[2025-06-28 19:17:45.069] FETCH ComfyRegistry Data: 25/90
[2025-06-28 19:17:48.856] FETCH ComfyRegistry Data: 30/90
[2025-06-28 19:17:52.631] FETCH ComfyRegistry Data: 35/90
[2025-06-28 19:17:56.426] FETCH ComfyRegistry Data: 40/90
[2025-06-28 19:18:01.601] FETCH ComfyRegistry Data: 45/90
[2025-06-28 19:18:05.964] FETCH ComfyRegistry Data: 50/90
[2025-06-28 19:18:10.699] FETCH ComfyRegistry Data: 55/90
[2025-06-28 19:18:15.351] FETCH ComfyRegistry Data: 60/90
[2025-06-28 19:18:20.354] FETCH ComfyRegistry Data: 65/90
[2025-06-28 19:18:24.491] FETCH ComfyRegistry Data: 70/90
[2025-06-28 19:18:28.406] FETCH ComfyRegistry Data: 75/90
[2025-06-28 19:18:32.381] FETCH ComfyRegistry Data: 80/90
[2025-06-28 19:18:36.289] FETCH ComfyRegistry Data: 85/90
[2025-06-28 19:18:40.154] FETCH ComfyRegistry Data: 90/90
[2025-06-28 19:18:40.655] FETCH ComfyRegistry Data [DONE]
[2025-06-28 19:18:40.731] [ComfyUI-Manager] default cache updated: https://api.comfy.org/nodes
[2025-06-28 19:18:40.742] FETCH DATA from: https://raw.githubusercontent.com/ltdrdata/ComfyUI-Manager/main/custom-node-list.json [DONE]
[2025-06-28 19:18:41.268] [ComfyUI-Manager] All startup tasks have been completed.
[2025-06-28 19:25:39.746] 
Stopped server
