## ComfyUI-Manager: installing dependencies done.
[2025-06-28 19:17:15.770] ** ComfyUI startup time: 2025-06-28 19:17:15.770
[2025-06-28 19:17:15.770] ** Platform: Windows
[2025-06-28 19:17:15.770] ** Python version: 3.13.5 (tags/v3.13.5:6cb20a2, Jun 11 2025, 16:15:46) [MSC v.1943 64 bit (AMD64)]
[2025-06-28 19:17:15.771] ** Python executable: C:\Users\<USER>\ai\ComfiGit\comfyui_env\Scripts\python.exe
[2025-06-28 19:17:15.771] ** ComfyUI Path: C:\Users\<USER>\ai\ComfiGit\ComfyUI
[2025-06-28 19:17:15.771] ** ComfyUI Base Folder Path: C:\Users\<USER>\ai\ComfiGit\ComfyUI
[2025-06-28 19:17:15.771] ** User directory: C:\Users\<USER>\ai\ComfiGit\ComfyUI\user
[2025-06-28 19:17:15.771] ** ComfyUI-Manager config path: C:\Users\<USER>\ai\ComfiGit\ComfyUI\user\default\ComfyUI-Manager\config.ini
[2025-06-28 19:17:15.771] ** Log path: C:\Users\<USER>\ai\ComfiGit\ComfyUI\user\comfyui.log
[ComfyUI-Manager] In Python 3.13 and above, PIP Fixer does not downgrade `numpy` below version 2.0. If you need to force a downgrade of `numpy`, please use `pip_auto_fix.list`.
[2025-06-28 19:17:16.591] 
Prestartup times for custom nodes:
[2025-06-28 19:17:16.592]    2.1 seconds: C:\Users\<USER>\ai\ComfiGit\ComfyUI\custom_nodes\ComfyUI-Manager
[2025-06-28 19:17:16.592] 
[2025-06-28 19:17:17.935] Checkpoint files will always be loaded safely.
[2025-06-28 19:17:18.050] Total VRAM 24463 MB, total RAM 64957 MB
[2025-06-28 19:17:18.050] pytorch version: 2.7.1+cu128
[2025-06-28 19:17:18.051] Set vram state to: NORMAL_VRAM
[2025-06-28 19:17:18.051] Device: cuda:0 NVIDIA GeForce RTX 5090 Laptop GPU : cudaMallocAsync
[2025-06-28 19:17:19.054] Using pytorch attention
[2025-06-28 19:17:20.524] Python version: 3.13.5 (tags/v3.13.5:6cb20a2, Jun 11 2025, 16:15:46) [MSC v.1943 64 bit (AMD64)]
[2025-06-28 19:17:20.524] ComfyUI version: 0.3.43
[2025-06-28 19:17:20.580] ComfyUI frontend version: 1.23.4
[2025-06-28 19:17:20.581] [Prompt Server] web root: C:\Users\<USER>\ai\ComfiGit\comfyui_env\Lib\site-packages\comfyui_frontend_package\static
