## ComfyUI-Manager: installing dependencies done.
[2025-06-28 19:06:15.384] ** ComfyUI startup time: 2025-06-28 19:06:15.384
[2025-06-28 19:06:15.385] ** Platform: Windows
[2025-06-28 19:06:15.385] ** Python version: 3.13.5 (tags/v3.13.5:6cb20a2, Jun 11 2025, 16:15:46) [MSC v.1943 64 bit (AMD64)]
[2025-06-28 19:06:15.385] ** Python executable: C:\Users\<USER>\ai\ComfiGit\comfyui_env\Scripts\python.exe
[2025-06-28 19:06:15.385] ** ComfyUI Path: C:\Users\<USER>\ai\ComfiGit\ComfyUI
[2025-06-28 19:06:15.385] ** ComfyUI Base Folder Path: C:\Users\<USER>\ai\ComfiGit\ComfyUI
[2025-06-28 19:06:15.385] ** User directory: C:\Users\<USER>\ai\ComfiGit\ComfyUI\user
[2025-06-28 19:06:15.386] ** ComfyUI-Manager config path: C:\Users\<USER>\ai\ComfiGit\ComfyUI\user\default\ComfyUI-Manager\config.ini
[2025-06-28 19:06:15.386] ** Log path: C:\Users\<USER>\ai\ComfiGit\ComfyUI\user\comfyui.log
