## ComfyUI-Manager: installing dependencies done.
[2025-06-28 20:08:55.954] ** ComfyUI startup time: 2025-06-28 20:08:55.954
[2025-06-28 20:08:55.954] ** Platform: Windows
[2025-06-28 20:08:55.954] ** Python version: 3.12.10 (tags/v3.12.10:0cc8128, Apr  8 2025, 12:21:36) [MSC v.1943 64 bit (AMD64)]
[2025-06-28 20:08:55.954] ** Python executable: C:\Users\<USER>\.pyenv\pyenv-win\versions\3.12.10\python.exe
[2025-06-28 20:08:55.954] ** ComfyUI Path: C:\Users\<USER>\ai\ComfiGit\ComfyUI
[2025-06-28 20:08:55.955] ** ComfyUI Base Folder Path: C:\Users\<USER>\ai\ComfiGit\ComfyUI
[2025-06-28 20:08:55.955] ** User directory: C:\Users\<USER>\ai\ComfiGit\ComfyUI\user
[2025-06-28 20:08:55.955] ** ComfyUI-Manager config path: C:\Users\<USER>\ai\ComfiGit\ComfyUI\user\default\ComfyUI-Manager\config.ini
[2025-06-28 20:08:55.955] ** Log path: C:\Users\<USER>\ai\ComfiGit\ComfyUI\user\comfyui.log

Prestartup times for custom nodes:
[2025-06-28 20:08:56.543]    1.6 seconds: C:\Users\<USER>\ai\ComfiGit\ComfyUI\custom_nodes\ComfyUI-Manager
[2025-06-28 20:08:56.543] 
