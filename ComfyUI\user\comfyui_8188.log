## ComfyUI-Manager: installing dependencies done.
[2025-06-28 17:37:24.515] ** ComfyUI startup time: 2025-06-28 17:37:24.515
[2025-06-28 17:37:24.516] ** Platform: Windows
[2025-06-28 17:37:24.516] ** Python version: 3.13.5 (tags/v3.13.5:6cb20a2, Jun 11 2025, 16:15:46) [MSC v.1943 64 bit (AMD64)]
[2025-06-28 17:37:24.516] ** Python executable: C:\Users\<USER>\ai\ComfiGit\comfyui_env\Scripts\python.exe
[2025-06-28 17:37:24.516] ** ComfyUI Path: C:\Users\<USER>\ai\ComfiGit\ComfyUI
[2025-06-28 17:37:24.516] ** ComfyUI Base Folder Path: C:\Users\<USER>\ai\ComfiGit\ComfyUI
[2025-06-28 17:37:24.516] ** User directory: C:\Users\<USER>\ai\ComfiGit\ComfyUI\user
[2025-06-28 17:37:24.517] ** ComfyUI-Manager config path: C:\Users\<USER>\ai\ComfiGit\ComfyUI\user\default\ComfyUI-Manager\config.ini
[2025-06-28 17:37:24.517] ** Log path: C:\Users\<USER>\ai\ComfiGit\ComfyUI\user\comfyui.log
[ComfyUI-Manager] In Python 3.13 and above, PIP Fixer does not downgrade `numpy` below version 2.0. If you need to force a downgrade of `numpy`, please use `pip_auto_fix.list`.
[2025-06-28 17:37:25.216] 
Prestartup times for custom nodes:
[2025-06-28 17:37:25.216]    1.9 seconds: C:\Users\<USER>\ai\ComfiGit\ComfyUI\custom_nodes\ComfyUI-Manager
[2025-06-28 17:37:25.216] 
[2025-06-28 17:37:26.493] Checkpoint files will always be loaded safely.
[2025-06-28 17:37:27.434] Total VRAM 24463 MB, total RAM 64957 MB
[2025-06-28 17:37:27.435] pytorch version: 2.7.1+cu128
[2025-06-28 17:37:27.435] Set vram state to: NORMAL_VRAM
[2025-06-28 17:37:27.436] Device: cuda:0 NVIDIA GeForce RTX 5090 Laptop GPU : cudaMallocAsync
[2025-06-28 17:37:28.406] Using pytorch attention
[2025-06-28 17:37:29.951] Python version: 3.13.5 (tags/v3.13.5:6cb20a2, Jun 11 2025, 16:15:46) [MSC v.1943 64 bit (AMD64)]
[2025-06-28 17:37:29.951] ComfyUI version: 0.3.43
[2025-06-28 17:37:29.994] ComfyUI frontend version: 1.23.4
[2025-06-28 17:37:29.995] [Prompt Server] web root: C:\Users\<USER>\ai\ComfiGit\comfyui_env\Lib\site-packages\comfyui_frontend_package\static
[2025-06-28 17:37:30.688] ### Loading: ComfyUI-Impact-Pack (V8.17.1)
[2025-06-28 17:37:30.748] [Impact Pack] Wildcards loading done.
[2025-06-28 17:37:30.753] ### Loading: ComfyUI-Manager (V3.33.3)
[2025-06-28 17:37:30.754] [ComfyUI-Manager] network_mode: public
[2025-06-28 17:37:30.963] ### ComfyUI Version: v0.3.43 | Released on '2025-06-27'
[2025-06-28 17:37:31.323] [ComfyUI-Manager] default cache updated: https://raw.githubusercontent.com/ltdrdata/ComfyUI-Manager/main/alter-list.json
[2025-06-28 17:37:31.334] [ComfyUI-Manager] default cache updated: https://raw.githubusercontent.com/ltdrdata/ComfyUI-Manager/main/model-list.json
[2025-06-28 17:37:31.346] [ComfyUI-Manager] default cache updated: https://raw.githubusercontent.com/ltdrdata/ComfyUI-Manager/main/github-stats.json
[2025-06-28 17:37:31.377] [ComfyUI-Manager] default cache updated: https://raw.githubusercontent.com/ltdrdata/ComfyUI-Manager/main/extension-node-map.json
[2025-06-28 17:37:31.404] [ComfyUI-Manager] default cache updated: https://raw.githubusercontent.com/ltdrdata/ComfyUI-Manager/main/custom-node-list.json
[2025-06-28 17:37:31.477] 
Import times for custom nodes:
[2025-06-28 17:37:31.478]    0.0 seconds: C:\Users\<USER>\ai\ComfiGit\ComfyUI\custom_nodes\websocket_image_save.py
[2025-06-28 17:37:31.478]    0.0 seconds: C:\Users\<USER>\ai\ComfiGit\ComfyUI\custom_nodes\ComfyUI-Custom-Scripts
[2025-06-28 17:37:31.478]    0.0 seconds: C:\Users\<USER>\ai\ComfiGit\ComfyUI\custom_nodes\efficiency-nodes-comfyui
[2025-06-28 17:37:31.478]    0.0 seconds: C:\Users\<USER>\ai\ComfiGit\ComfyUI\custom_nodes\ComfyUI-Advanced-ControlNet
[2025-06-28 17:37:31.478]    0.1 seconds: C:\Users\<USER>\ai\ComfiGit\ComfyUI\custom_nodes\ComfyUI-Impact-Pack
[2025-06-28 17:37:31.478]    0.2 seconds: C:\Users\<USER>\ai\ComfiGit\ComfyUI\custom_nodes\ComfyUI-VideoHelperSuite
[2025-06-28 17:37:31.478]    0.5 seconds: C:\Users\<USER>\ai\ComfiGit\ComfyUI\custom_nodes\ComfyUI-Manager
[2025-06-28 17:37:31.478] 
[2025-06-28 17:37:31.681] Context impl SQLiteImpl.
[2025-06-28 17:37:31.681] Will assume non-transactional DDL.
[2025-06-28 17:37:31.682] No target revision found.
[2025-06-28 17:37:31.695] Starting server

[2025-06-28 17:37:31.695] To see the GUI go to: http://127.0.0.1:8188
