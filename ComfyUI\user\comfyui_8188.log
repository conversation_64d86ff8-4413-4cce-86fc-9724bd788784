## ComfyUI-Manager: installing dependencies done.
[2025-06-28 20:08:55.954] ** ComfyUI startup time: 2025-06-28 20:08:55.954
[2025-06-28 20:08:55.954] ** Platform: Windows
[2025-06-28 20:08:55.954] ** Python version: 3.12.10 (tags/v3.12.10:0cc8128, Apr  8 2025, 12:21:36) [MSC v.1943 64 bit (AMD64)]
[2025-06-28 20:08:55.954] ** Python executable: C:\Users\<USER>\.pyenv\pyenv-win\versions\3.12.10\python.exe
[2025-06-28 20:08:55.954] ** ComfyUI Path: C:\Users\<USER>\ai\ComfiGit\ComfyUI
[2025-06-28 20:08:55.955] ** ComfyUI Base Folder Path: C:\Users\<USER>\ai\ComfiGit\ComfyUI
[2025-06-28 20:08:55.955] ** User directory: C:\Users\<USER>\ai\ComfiGit\ComfyUI\user
[2025-06-28 20:08:55.955] ** ComfyUI-Manager config path: C:\Users\<USER>\ai\ComfiGit\ComfyUI\user\default\ComfyUI-Manager\config.ini
[2025-06-28 20:08:55.955] ** Log path: C:\Users\<USER>\ai\ComfiGit\ComfyUI\user\comfyui.log

Prestartup times for custom nodes:
[2025-06-28 20:08:56.543]    1.6 seconds: C:\Users\<USER>\ai\ComfiGit\ComfyUI\custom_nodes\ComfyUI-Manager
[2025-06-28 20:08:56.543] 
[2025-06-28 20:09:44.160] Checkpoint files will always be loaded safely.
[2025-06-28 20:09:44.191] C:\Users\<USER>\.pyenv\pyenv-win\versions\3.12.10\Lib\site-packages\torch\cuda\__init__.py:235: UserWarning: 
NVIDIA GeForce RTX 5090 Laptop GPU with CUDA capability sm_120 is not compatible with the current PyTorch installation.
The current PyTorch install supports CUDA capabilities sm_50 sm_60 sm_61 sm_70 sm_75 sm_80 sm_86 sm_90.
If you want to use the NVIDIA GeForce RTX 5090 Laptop GPU GPU with PyTorch, please check the instructions at https://pytorch.org/get-started/locally/

  warnings.warn(
[2025-06-28 20:09:44.290] Total VRAM 24463 MB, total RAM 64957 MB
[2025-06-28 20:09:44.291] pytorch version: 2.5.1+cu121
[2025-06-28 20:09:44.291] Set vram state to: NORMAL_VRAM
[2025-06-28 20:09:44.291] Device: cuda:0 NVIDIA GeForce RTX 5090 Laptop GPU : cudaMallocAsync
[2025-06-28 20:10:01.030] Using pytorch attention
[2025-06-28 20:10:56.424] Python version: 3.12.10 (tags/v3.12.10:0cc8128, Apr  8 2025, 12:21:36) [MSC v.1943 64 bit (AMD64)]
[2025-06-28 20:10:56.424] ComfyUI version: 0.3.43
[2025-06-28 20:10:56.449] ComfyUI frontend version: 1.23.4
[2025-06-28 20:10:56.450] [Prompt Server] web root: C:\Users\<USER>\.pyenv\pyenv-win\versions\3.12.10\Lib\site-packages\comfyui_frontend_package\static
[2025-06-28 20:10:58.139] ### Loading: ComfyUI-Impact-Pack (V8.17.1)
[2025-06-28 20:10:59.468] [Impact Pack] Wildcards loading done.
[2025-06-28 20:10:59.507] ### Loading: ComfyUI-Manager (V3.33.3)
[2025-06-28 20:10:59.507] [ComfyUI-Manager] network_mode: public
[2025-06-28 20:10:59.697] ### ComfyUI Version: v0.3.43 | Released on '2025-06-27'
[2025-06-28 20:11:00.017] [ComfyUI-Manager] default cache updated: https://raw.githubusercontent.com/ltdrdata/ComfyUI-Manager/main/alter-list.json
[2025-06-28 20:11:00.033] [ComfyUI-Manager] default cache updated: https://raw.githubusercontent.com/ltdrdata/ComfyUI-Manager/main/model-list.json
[2025-06-28 20:11:00.105] [ComfyUI-Manager] default cache updated: https://raw.githubusercontent.com/ltdrdata/ComfyUI-Manager/main/github-stats.json
[2025-06-28 20:11:00.184] [ComfyUI-Manager] default cache updated: https://raw.githubusercontent.com/ltdrdata/ComfyUI-Manager/main/custom-node-list.json
[2025-06-28 20:11:00.204] [ComfyUI-Manager] default cache updated: https://raw.githubusercontent.com/ltdrdata/ComfyUI-Manager/main/extension-node-map.json
[2025-06-28 20:11:01.828] Warning: Could not load sageattention: No module named 'sageattention'
[2025-06-28 20:11:01.829] sageattention package is not installed
[2025-06-28 20:11:02.031] 
Import times for custom nodes:
[2025-06-28 20:11:02.032]    0.0 seconds: C:\Users\<USER>\ai\ComfiGit\ComfyUI\custom_nodes\websocket_image_save.py
[2025-06-28 20:11:02.032]    0.0 seconds: C:\Users\<USER>\ai\ComfiGit\ComfyUI\custom_nodes\ComfyUI-WanVideoKsampler
[2025-06-28 20:11:02.032]    0.0 seconds: C:\Users\<USER>\ai\ComfiGit\ComfyUI\custom_nodes\ComfyUI-Custom-Scripts
[2025-06-28 20:11:02.032]    0.1 seconds: C:\Users\<USER>\ai\ComfiGit\ComfyUI\custom_nodes\ComfyUI-Advanced-ControlNet
[2025-06-28 20:11:02.032]    0.1 seconds: C:\Users\<USER>\ai\ComfiGit\ComfyUI\custom_nodes\efficiency-nodes-comfyui
[2025-06-28 20:11:02.032]    0.1 seconds: C:\Users\<USER>\ai\ComfiGit\ComfyUI\custom_nodes\ComfyUI-AnimateDiff-Evolved
[2025-06-28 20:11:02.032]    0.5 seconds: C:\Users\<USER>\ai\ComfiGit\ComfyUI\custom_nodes\ComfyUI-Manager
[2025-06-28 20:11:02.032]    0.8 seconds: C:\Users\<USER>\ai\ComfiGit\ComfyUI\custom_nodes\ComfyUI-WanVideoWrapper
[2025-06-28 20:11:02.032]    1.2 seconds: C:\Users\<USER>\ai\ComfiGit\ComfyUI\custom_nodes\ComfyUI-VideoHelperSuite
[2025-06-28 20:11:02.032]    1.3 seconds: C:\Users\<USER>\ai\ComfiGit\ComfyUI\custom_nodes\ComfyUI-Impact-Pack
[2025-06-28 20:11:02.032] 
[2025-06-28 20:11:02.619] Context impl SQLiteImpl.
[2025-06-28 20:11:02.619] Will assume non-transactional DDL.
[2025-06-28 20:11:02.620] No target revision found.
[2025-06-28 20:11:02.631] Starting server

[2025-06-28 20:11:02.631] To see the GUI go to: http://127.0.0.1:8188
[2025-06-28 20:11:04.214] FETCH ComfyRegistry Data: 5/90
[2025-06-28 20:11:07.821] FETCH ComfyRegistry Data: 10/90
[2025-06-28 20:11:12.358] FETCH ComfyRegistry Data: 15/90
[2025-06-28 20:11:15.951] FETCH ComfyRegistry Data: 20/90
[2025-06-28 20:11:19.715] FETCH ComfyRegistry Data: 25/90
[2025-06-28 20:11:23.599] FETCH ComfyRegistry Data: 30/90
[2025-06-28 20:11:27.514] FETCH ComfyRegistry Data: 35/90
[2025-06-28 20:11:31.251] FETCH ComfyRegistry Data: 40/90
[2025-06-28 20:11:34.839] FETCH ComfyRegistry Data: 45/90
[2025-06-28 20:11:38.640] FETCH ComfyRegistry Data: 50/90
[2025-06-28 20:11:42.197] FETCH ComfyRegistry Data: 55/90
[2025-06-28 20:11:45.903] FETCH ComfyRegistry Data: 60/90
[2025-06-28 20:11:49.797] FETCH ComfyRegistry Data: 65/90
[2025-06-28 20:11:53.938] FETCH ComfyRegistry Data: 70/90
[2025-06-28 20:11:57.775] FETCH ComfyRegistry Data: 75/90
[2025-06-28 20:12:02.127] FETCH ComfyRegistry Data: 80/90
[2025-06-28 20:12:06.135] FETCH ComfyRegistry Data: 85/90
[2025-06-28 20:12:10.008] FETCH ComfyRegistry Data: 90/90
[2025-06-28 20:12:10.509] FETCH ComfyRegistry Data [DONE]
[2025-06-28 20:12:10.583] [ComfyUI-Manager] default cache updated: https://api.comfy.org/nodes
[2025-06-28 20:12:10.597] FETCH DATA from: https://raw.githubusercontent.com/ltdrdata/ComfyUI-Manager/main/custom-node-list.json [DONE]
[2025-06-28 20:12:10.739] [ComfyUI-Manager] All startup tasks have been completed.
[2025-06-28 20:14:22.144] got prompt
[2025-06-28 20:14:22.144] invalid prompt: {'type': 'invalid_prompt', 'message': 'Cannot execute because node WanVideoT2V does not exist.', 'details': "Node ID '#1'", 'extra_info': {}}
[2025-06-28 20:15:06.253] got prompt
[2025-06-28 20:15:06.253] Failed to validate prompt for output 7:
[2025-06-28 20:15:06.253] * (prompt):
[2025-06-28 20:15:06.253]   - Required input is missing: video
[2025-06-28 20:15:06.253]   - Value not in list: format: 'video/h264-mp4' not in ['auto', 'mp4']
[2025-06-28 20:15:06.254]   - Required input is missing: codec
[2025-06-28 20:15:06.254] * SaveVideo 7:
[2025-06-28 20:15:06.254]   - Required input is missing: video
[2025-06-28 20:15:06.254]   - Value not in list: format: 'video/h264-mp4' not in ['auto', 'mp4']
[2025-06-28 20:15:06.254]   - Required input is missing: codec
[2025-06-28 20:15:06.254] Output will be ignored
[2025-06-28 20:15:06.254] invalid prompt: {'type': 'prompt_outputs_failed_validation', 'message': 'Prompt outputs failed validation', 'details': "Required input is missing: video\nValue not in list: format: 'video/h264-mp4' not in ['auto', 'mp4']\nRequired input is missing: codec", 'extra_info': {}}
[2025-06-28 20:15:38.193] got prompt
[2025-06-28 20:15:38.194] Failed to validate prompt for output 7:
[2025-06-28 20:15:38.194] * (prompt):
[2025-06-28 20:15:38.194]   - Return type mismatch between linked nodes: video, received_type(IMAGE) mismatch input_type(VIDEO)
[2025-06-28 20:15:38.194] * SaveVideo 7:
[2025-06-28 20:15:38.194]   - Return type mismatch between linked nodes: video, received_type(IMAGE) mismatch input_type(VIDEO)
[2025-06-28 20:15:38.194] Output will be ignored
[2025-06-28 20:15:38.194] invalid prompt: {'type': 'prompt_outputs_failed_validation', 'message': 'Prompt outputs failed validation', 'details': 'Return type mismatch between linked nodes: video, received_type(IMAGE) mismatch input_type(VIDEO)', 'extra_info': {}}
[2025-06-28 20:15:58.161] got prompt
[2025-06-28 20:15:58.162] Failed to validate prompt for output 7:
[2025-06-28 20:15:58.162] * (prompt):
[2025-06-28 20:15:58.163]   - Required input is missing: save_output
[2025-06-28 20:15:58.163]   - Required input is missing: pingpong
[2025-06-28 20:15:58.163] * WanVideoModelLoader 1:
[2025-06-28 20:15:58.163]   - Required input is missing: base_precision
[2025-06-28 20:15:58.163]   - Required input is missing: load_device
[2025-06-28 20:15:58.163]   - Required input is missing: model
[2025-06-28 20:15:58.163]   - Required input is missing: quantization
[2025-06-28 20:15:58.164] * WanVideoSampler 5:
[2025-06-28 20:15:58.164]   - Required input is missing: shift
[2025-06-28 20:15:58.164]   - Required input is missing: image_embeds
[2025-06-28 20:15:58.164]   - Required input is missing: force_offload
[2025-06-28 20:15:58.164]   - Required input is missing: scheduler
[2025-06-28 20:15:58.164]   - Required input is missing: riflex_freq_index
[2025-06-28 20:15:58.164] * WanVideoVAELoader 3:
[2025-06-28 20:15:58.165]   - Required input is missing: model_name
[2025-06-28 20:15:58.165] * WanVideoDecode 6:
[2025-06-28 20:15:58.165]   - Exception when validating inner node: WanVideoDecode.VALIDATE_INPUTS() missing 4 required positional arguments: 'tile_x', 'tile_y', 'tile_stride_x', and 'tile_stride_y'
[2025-06-28 20:15:58.165] * VHS_VideoCombine 7:
[2025-06-28 20:15:58.165]   - Required input is missing: save_output
[2025-06-28 20:15:58.165]   - Required input is missing: pingpong
[2025-06-28 20:15:58.165] Output will be ignored
[2025-06-28 20:15:58.166] invalid prompt: {'type': 'prompt_outputs_failed_validation', 'message': 'Prompt outputs failed validation', 'details': 'Required input is missing: save_output\nRequired input is missing: pingpong', 'extra_info': {}}
[2025-06-28 20:16:46.730] got prompt
[2025-06-28 20:16:46.732] Failed to validate prompt for output 7:
[2025-06-28 20:16:46.732] * WanVideoSampler 5:
[2025-06-28 20:16:46.732]   - Value not in list: scheduler: 'linear' not in ['unipc', 'unipc/beta', 'dpm++', 'dpm++/beta', 'dpm++_sde', 'dpm++_sde/beta', 'euler', 'euler/beta', 'euler/accvideo', 'deis', 'lcm', 'lcm/beta', 'flowmatch_causvid', 'flowmatch_distill']
[2025-06-28 20:16:46.732] * WanVideoDecode 6:
[2025-06-28 20:16:46.732]   - Required input is missing: enable_vae_tiling
[2025-06-28 20:16:46.732]   - Custom validation failed for node: tile_x - Tile width must be larger than the tile stride width.
[2025-06-28 20:16:46.733]   - Custom validation failed for node: tile_y - Tile width must be larger than the tile stride width.
[2025-06-28 20:16:46.733]   - Custom validation failed for node: tile_stride_x - Tile width must be larger than the tile stride width.
[2025-06-28 20:16:46.733]   - Custom validation failed for node: tile_stride_y - Tile width must be larger than the tile stride width.
[2025-06-28 20:16:46.733] Output will be ignored
[2025-06-28 20:16:46.733] invalid prompt: {'type': 'prompt_outputs_failed_validation', 'message': 'Prompt outputs failed validation', 'details': '', 'extra_info': {}}
[2025-06-28 20:17:13.887] got prompt
[2025-06-28 20:17:13.890] !!! Exception during processing !!! WanVideoVAELoader.loadmodel() missing 1 required positional argument: 'precision'
[2025-06-28 20:17:13.891] Traceback (most recent call last):
  File "C:\Users\<USER>\ai\ComfiGit\ComfyUI\execution.py", line 361, in execute
    output_data, output_ui, has_subgraph = get_output_data(obj, input_data_all, execution_block_cb=execution_block_cb, pre_execute_cb=pre_execute_cb)
                                           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\ai\ComfiGit\ComfyUI\execution.py", line 236, in get_output_data
    return_values = _map_node_over_list(obj, input_data_all, obj.FUNCTION, allow_interrupt=True, execution_block_cb=execution_block_cb, pre_execute_cb=pre_execute_cb)
                    ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\ai\ComfiGit\ComfyUI\execution.py", line 208, in _map_node_over_list
    process_inputs(input_dict, i)
  File "C:\Users\<USER>\ai\ComfiGit\ComfyUI\execution.py", line 197, in process_inputs
    results.append(getattr(obj, func)(**inputs))
                   ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
TypeError: WanVideoVAELoader.loadmodel() missing 1 required positional argument: 'precision'

[2025-06-28 20:17:13.892] Prompt executed in 0.00 seconds
[2025-06-28 20:17:33.486] got prompt
[2025-06-28 20:17:33.501] !!! Exception during processing !!! Error while deserializing header: HeaderTooLarge

File path: C:\Users\<USER>\ai\ComfiGit\ComfyUI\models\vae\wan_2.1_vae.safetensors

The safetensors file is corrupt or invalid. Make sure this is actually a safetensors file and not a ckpt or pt or other filetype.
[2025-06-28 20:17:33.502] Traceback (most recent call last):
  File "C:\Users\<USER>\ai\ComfiGit\ComfyUI\comfy\utils.py", line 58, in load_torch_file
    with safetensors.safe_open(ckpt, framework="pt", device=device.type) as f:
         ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
safetensors_rust.SafetensorError: Error while deserializing header: HeaderTooLarge

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "C:\Users\<USER>\ai\ComfiGit\ComfyUI\execution.py", line 361, in execute
    output_data, output_ui, has_subgraph = get_output_data(obj, input_data_all, execution_block_cb=execution_block_cb, pre_execute_cb=pre_execute_cb)
                                           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\ai\ComfiGit\ComfyUI\execution.py", line 236, in get_output_data
    return_values = _map_node_over_list(obj, input_data_all, obj.FUNCTION, allow_interrupt=True, execution_block_cb=execution_block_cb, pre_execute_cb=pre_execute_cb)
                    ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\ai\ComfiGit\ComfyUI\execution.py", line 208, in _map_node_over_list
    process_inputs(input_dict, i)
  File "C:\Users\<USER>\ai\ComfiGit\ComfyUI\execution.py", line 197, in process_inputs
    results.append(getattr(obj, func)(**inputs))
                   ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\ai\ComfiGit\ComfyUI\custom_nodes\ComfyUI-WanVideoWrapper\nodes.py", line 1063, in loadmodel
    vae_sd = load_torch_file(model_path, safe_load=True)
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\ai\ComfiGit\ComfyUI\comfy\utils.py", line 68, in load_torch_file
    raise ValueError("{}\n\nFile path: {}\n\nThe safetensors file is corrupt or invalid. Make sure this is actually a safetensors file and not a ckpt or pt or other filetype.".format(message, ckpt))
ValueError: Error while deserializing header: HeaderTooLarge

File path: C:\Users\<USER>\ai\ComfiGit\ComfyUI\models\vae\wan_2.1_vae.safetensors

The safetensors file is corrupt or invalid. Make sure this is actually a safetensors file and not a ckpt or pt or other filetype.

[2025-06-28 20:17:33.503] Prompt executed in 0.01 seconds
[2025-06-28 20:18:10.237] got prompt
[2025-06-28 20:18:10.795] !!! Exception during processing !!! LoadWanVideoT5TextEncoder.loadmodel() missing 2 required positional arguments: 'model_name' and 'precision'
[2025-06-28 20:18:10.795] Traceback (most recent call last):
  File "C:\Users\<USER>\ai\ComfiGit\ComfyUI\execution.py", line 361, in execute
    output_data, output_ui, has_subgraph = get_output_data(obj, input_data_all, execution_block_cb=execution_block_cb, pre_execute_cb=pre_execute_cb)
                                           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\ai\ComfiGit\ComfyUI\execution.py", line 236, in get_output_data
    return_values = _map_node_over_list(obj, input_data_all, obj.FUNCTION, allow_interrupt=True, execution_block_cb=execution_block_cb, pre_execute_cb=pre_execute_cb)
                    ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\ai\ComfiGit\ComfyUI\execution.py", line 204, in _map_node_over_list
    process_inputs({})
  File "C:\Users\<USER>\ai\ComfiGit\ComfyUI\execution.py", line 197, in process_inputs
    results.append(getattr(obj, func)(**inputs))
                   ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
TypeError: LoadWanVideoT5TextEncoder.loadmodel() missing 2 required positional arguments: 'model_name' and 'precision'

[2025-06-28 20:18:10.796] Prompt executed in 0.56 seconds
[2025-06-28 20:19:11.068] got prompt
[2025-06-28 20:19:17.699] !!! Exception during processing !!! WanVideoTextEncode.process() missing 2 required positional arguments: 'positive_prompt' and 'negative_prompt'
[2025-06-28 20:19:17.699] Traceback (most recent call last):
  File "C:\Users\<USER>\ai\ComfiGit\ComfyUI\execution.py", line 361, in execute
    output_data, output_ui, has_subgraph = get_output_data(obj, input_data_all, execution_block_cb=execution_block_cb, pre_execute_cb=pre_execute_cb)
                                           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\ai\ComfiGit\ComfyUI\execution.py", line 236, in get_output_data
    return_values = _map_node_over_list(obj, input_data_all, obj.FUNCTION, allow_interrupt=True, execution_block_cb=execution_block_cb, pre_execute_cb=pre_execute_cb)
                    ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\ai\ComfiGit\ComfyUI\execution.py", line 208, in _map_node_over_list
    process_inputs(input_dict, i)
  File "C:\Users\<USER>\ai\ComfiGit\ComfyUI\execution.py", line 197, in process_inputs
    results.append(getattr(obj, func)(**inputs))
                   ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
TypeError: WanVideoTextEncode.process() missing 2 required positional arguments: 'positive_prompt' and 'negative_prompt'

[2025-06-28 20:19:17.700] Prompt executed in 6.63 seconds
[2025-06-28 20:19:39.153] got prompt
[2025-06-28 20:19:42.658] !!! Exception during processing !!! CUDA error: no kernel image is available for execution on the device
CUDA kernel errors might be asynchronously reported at some other API call, so the stacktrace below might be incorrect.
For debugging consider passing CUDA_LAUNCH_BLOCKING=1
Compile with `TORCH_USE_CUDA_DSA` to enable device-side assertions.

[2025-06-28 20:19:42.659] Traceback (most recent call last):
  File "C:\Users\<USER>\ai\ComfiGit\ComfyUI\execution.py", line 361, in execute
    output_data, output_ui, has_subgraph = get_output_data(obj, input_data_all, execution_block_cb=execution_block_cb, pre_execute_cb=pre_execute_cb)
                                           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\ai\ComfiGit\ComfyUI\execution.py", line 236, in get_output_data
    return_values = _map_node_over_list(obj, input_data_all, obj.FUNCTION, allow_interrupt=True, execution_block_cb=execution_block_cb, pre_execute_cb=pre_execute_cb)
                    ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\ai\ComfiGit\ComfyUI\execution.py", line 208, in _map_node_over_list
    process_inputs(input_dict, i)
  File "C:\Users\<USER>\ai\ComfiGit\ComfyUI\execution.py", line 197, in process_inputs
    results.append(getattr(obj, func)(**inputs))
                   ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\ai\ComfiGit\ComfyUI\custom_nodes\ComfyUI-WanVideoWrapper\nodes.py", line 1352, in process
    context = encoder(positive_prompts, device)
              ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\ai\ComfiGit\ComfyUI\custom_nodes\ComfyUI-WanVideoWrapper\wanvideo\modules\t5.py", line 517, in __call__
    seq_lens = mask.gt(0).sum(dim=1).long()
               ^^^^^^^^^^
RuntimeError: CUDA error: no kernel image is available for execution on the device
CUDA kernel errors might be asynchronously reported at some other API call, so the stacktrace below might be incorrect.
For debugging consider passing CUDA_LAUNCH_BLOCKING=1
Compile with `TORCH_USE_CUDA_DSA` to enable device-side assertions.


[2025-06-28 20:19:42.660] Prompt executed in 3.51 seconds
[2025-06-28 20:20:12.550] got prompt
[2025-06-28 20:20:12.559] !!! Exception during processing !!! CUDA error: no kernel image is available for execution on the device
CUDA kernel errors might be asynchronously reported at some other API call, so the stacktrace below might be incorrect.
For debugging consider passing CUDA_LAUNCH_BLOCKING=1
Compile with `TORCH_USE_CUDA_DSA` to enable device-side assertions.

[2025-06-28 20:20:12.560] Traceback (most recent call last):
  File "C:\Users\<USER>\ai\ComfiGit\ComfyUI\execution.py", line 361, in execute
    output_data, output_ui, has_subgraph = get_output_data(obj, input_data_all, execution_block_cb=execution_block_cb, pre_execute_cb=pre_execute_cb)
                                           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\ai\ComfiGit\ComfyUI\execution.py", line 236, in get_output_data
    return_values = _map_node_over_list(obj, input_data_all, obj.FUNCTION, allow_interrupt=True, execution_block_cb=execution_block_cb, pre_execute_cb=pre_execute_cb)
                    ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\ai\ComfiGit\ComfyUI\execution.py", line 208, in _map_node_over_list
    process_inputs(input_dict, i)
  File "C:\Users\<USER>\ai\ComfiGit\ComfyUI\execution.py", line 197, in process_inputs
    results.append(getattr(obj, func)(**inputs))
                   ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\ai\ComfiGit\ComfyUI\custom_nodes\ComfyUI-WanVideoWrapper\nodes.py", line 1352, in process
    context = encoder(positive_prompts, device)
              ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\ai\ComfiGit\ComfyUI\custom_nodes\ComfyUI-WanVideoWrapper\wanvideo\modules\t5.py", line 517, in __call__
    seq_lens = mask.gt(0).sum(dim=1).long()
               ^^^^^^^^^^
RuntimeError: CUDA error: no kernel image is available for execution on the device
CUDA kernel errors might be asynchronously reported at some other API call, so the stacktrace below might be incorrect.
For debugging consider passing CUDA_LAUNCH_BLOCKING=1
Compile with `TORCH_USE_CUDA_DSA` to enable device-side assertions.


[2025-06-28 20:20:12.561] Prompt executed in 0.01 seconds
[2025-06-28 20:21:03.455] got prompt
[2025-06-28 20:21:03.457] Failed to validate prompt for output 9:
[2025-06-28 20:21:03.457] * KSampler 7:
[2025-06-28 20:21:03.457]   - Return type mismatch between linked nodes: model, received_type(M_MODELS) mismatch input_type(MODEL)
[2025-06-28 20:21:03.457] Output will be ignored
[2025-06-28 20:21:03.457] invalid prompt: {'type': 'prompt_outputs_failed_validation', 'message': 'Prompt outputs failed validation', 'details': '', 'extra_info': {}}
[2025-06-28 20:21:24.186] got prompt
[2025-06-28 20:21:24.186] invalid prompt: {'type': 'invalid_prompt', 'message': 'Cannot execute because node ADE_AnimateDiffSampler does not exist.', 'details': "Node ID '#7'", 'extra_info': {}}
[2025-06-28 20:21:56.687] got prompt
[2025-06-28 20:21:56.688] Failed to validate prompt for output 9:
[2025-06-28 20:21:56.689] * KSampler 7:
[2025-06-28 20:21:56.689]   - Return type mismatch between linked nodes: model, received_type(M_MODELS) mismatch input_type(MODEL)
[2025-06-28 20:21:56.689] Output will be ignored
[2025-06-28 20:21:56.689] invalid prompt: {'type': 'prompt_outputs_failed_validation', 'message': 'Prompt outputs failed validation', 'details': '', 'extra_info': {}}
[2025-06-28 20:22:19.612] got prompt
[2025-06-28 20:22:19.613] Failed to validate prompt for output 9:
[2025-06-28 20:22:19.614] * VAEDecode 8:
[2025-06-28 20:22:19.614]   - Return type mismatch between linked nodes: samples, received_type(MODEL) mismatch input_type(LATENT)
[2025-06-28 20:22:19.614] Output will be ignored
[2025-06-28 20:22:19.614] invalid prompt: {'type': 'prompt_outputs_failed_validation', 'message': 'Prompt outputs failed validation', 'details': '', 'extra_info': {}}
[2025-06-28 20:22:49.942] got prompt
[2025-06-28 20:22:49.943] Failed to validate prompt for output 9:
[2025-06-28 20:22:49.945] * VAEDecode 8:
[2025-06-28 20:22:49.945]   - Exception when validating inner node: tuple index out of range
[2025-06-28 20:22:49.945] Output will be ignored
[2025-06-28 20:22:49.945] invalid prompt: {'type': 'prompt_outputs_failed_validation', 'message': 'Prompt outputs failed validation', 'details': '', 'extra_info': {}}
