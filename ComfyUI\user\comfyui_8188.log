## ComfyUI-Manager: installing dependencies done.
[2025-06-28 19:57:11.335] ** ComfyUI startup time: 2025-06-28 19:57:11.335
[2025-06-28 19:57:11.336] ** Platform: Windows
[2025-06-28 19:57:11.336] ** Python version: 3.10.11 (tags/v3.10.11:7d4cc5a, Apr  5 2023, 00:38:17) [MSC v.1929 64 bit (AMD64)]
[2025-06-28 19:57:11.336] ** Python executable: C:\Users\<USER>\.pyenv\pyenv-win\versions\3.10.11\python.exe
[2025-06-28 19:57:11.336] ** ComfyUI Path: C:\Users\<USER>\ai\ComfiGit\ComfyUI
[2025-06-28 19:57:11.337] ** ComfyUI Base Folder Path: C:\Users\<USER>\ai\ComfiGit\ComfyUI
[2025-06-28 19:57:11.337] ** User directory: C:\Users\<USER>\ai\ComfiGit\ComfyUI\user
[2025-06-28 19:57:11.337] ** ComfyUI-Manager config path: C:\Users\<USER>\ai\ComfiGit\ComfyUI\user\default\ComfyUI-Manager\config.ini
[2025-06-28 19:57:11.337] ** Log path: C:\Users\<USER>\ai\ComfiGit\ComfyUI\user\comfyui.log

Prestartup times for custom nodes:
[2025-06-28 19:57:12.258]    2.0 seconds: C:\Users\<USER>\ai\ComfiGit\ComfyUI\custom_nodes\ComfyUI-Manager
[2025-06-28 19:57:12.259] 
