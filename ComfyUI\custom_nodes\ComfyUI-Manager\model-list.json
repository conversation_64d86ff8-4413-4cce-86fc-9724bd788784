{"models": [{"name": "TAEF1 Decoder", "type": "TAESD", "base": "FLUX.1", "save_path": "vae_approx", "description": "(FLUX.1 Verison) To view the preview in high quality while running samples in ComfyUI, you will need this model.", "reference": "https://github.com/madebyollin/taesd", "filename": "taef1_decoder.pth", "url": "https://github.com/madebyollin/taesd/raw/main/taef1_decoder.pth", "size": "4.71MB"}, {"name": "TAEF1 Encoder", "type": "TAESD", "base": "FLUX.1", "save_path": "vae_approx", "description": "(FLUX.1 Verison) To view the preview in high quality while running samples in ComfyUI, you will need this model.", "reference": "https://github.com/madebyollin/taesd", "filename": "taef1_encoder.pth", "url": "https://github.com/madebyollin/taesd/raw/main/taef1_encoder.pth", "size": "4.71MB"}, {"name": "TAESD3 Decoder", "type": "TAESD", "base": "SD3", "save_path": "vae_approx", "description": "(SD3 Verison) To view the preview in high quality while running samples in ComfyUI, you will need this model.", "reference": "https://github.com/madebyollin/taesd", "filename": "taesd3_decoder.pth", "url": "https://github.com/madebyollin/taesd/raw/main/taesd3_decoder.pth", "size": "4.94MB"}, {"name": "TAESD3 Encoder", "type": "TAESD", "base": "SD3", "save_path": "vae_approx", "description": "(SD3 Verison) To view the preview in high quality while running samples in ComfyUI, you will need this model.", "reference": "https://github.com/madebyollin/taesd", "filename": "taesd3_encoder.pth", "url": "https://github.com/madebyollin/taesd/raw/main/taesd3_encoder.pth", "size": "4.94MB"}, {"name": "TAESDXL Decoder", "type": "TAESD", "base": "SDXL", "save_path": "vae_approx", "description": "(SDXL Verison) To view the preview in high quality while running samples in ComfyUI, you will need this model.", "reference": "https://github.com/madebyollin/taesd", "filename": "taesdxl_decoder.pth", "url": "https://github.com/madebyollin/taesd/raw/main/taesdxl_decoder.pth", "size": "4.91MB"}, {"name": "TAESDXL Encoder", "type": "TAESD", "base": "SDXL", "save_path": "vae_approx", "description": "(SDXL Verison) To view the preview in high quality while running samples in ComfyUI, you will need this model.", "reference": "https://github.com/madebyollin/taesd", "filename": "taesdxl_encoder.pth", "url": "https://github.com/madebyollin/taesd/raw/main/taesdxl_encoder.pth", "size": "4.91MB"}, {"name": "TAESD Decoder", "type": "TAESD", "base": "SD1.x", "save_path": "vae_approx", "description": "To view the preview in high quality while running samples in ComfyUI, you will need this model.", "reference": "https://github.com/madebyollin/taesd", "filename": "taesd_decoder.pth", "url": "https://github.com/madebyollin/taesd/raw/main/taesd_decoder.pth", "size": "4.91MB"}, {"name": "TAESD Encoder", "type": "TAESD", "base": "SD1.x", "save_path": "vae_approx", "description": "To view the preview in high quality while running samples in ComfyUI, you will need this model.", "reference": "https://github.com/madebyollin/taesd", "filename": "taesd_encoder.pth", "url": "https://github.com/madebyollin/taesd/raw/main/taesd_encoder.pth", "size": "4.91MB"}, {"name": "RealESRGAN x2", "type": "upscale", "base": "upscale", "save_path": "default", "description": "RealESRGAN x2 upscaler model", "reference": "https://huggingface.co/ai-forever/Real-ESRGAN", "filename": "RealESRGAN_x2.pth", "url": "https://huggingface.co/ai-forever/Real-ESRGAN/resolve/main/RealESRGAN_x2.pth", "size": "67.1MB"}, {"name": "RealESRGAN x4", "type": "upscale", "base": "upscale", "save_path": "default", "description": "RealESRGAN x4 upscaler model", "reference": "https://huggingface.co/ai-forever/Real-ESRGAN", "filename": "RealESRGAN_x4.pth", "url": "https://huggingface.co/ai-forever/Real-ESRGAN/resolve/main/RealESRGAN_x4.pth", "size": "67.0MB"}, {"name": "ESRGAN x4", "type": "upscale", "base": "upscale", "save_path": "default", "description": "ESRGAN x4 upscaler model", "reference": "https://huggingface.co/Afizi/ESRGAN_4x.pth", "filename": "ESRGAN_4x.pth", "url": "https://huggingface.co/Afizi/ESRGAN_4x.pth/resolve/main/ESRGAN_4x.pth", "size": "66.9MB"}, {"name": "4x_foolhardy_<PERSON><PERSON><PERSON><PERSON>", "type": "upscale", "base": "upscale", "save_path": "default", "description": "4x_foolhardy_Remacri upscaler model", "reference": "https://huggingface.co/FacehugmanIII/4x_foolhardy_Re<PERSON><PERSON>ri", "filename": "4x_foolhardy_Remacri.pth", "url": "https://huggingface.co/FacehugmanIII/4x_foolhardy_Remacri/resolve/main/4x_foolhardy_Remacri.pth", "size": "67.0MB"}, {"name": "4x-AnimeSharp", "type": "upscale", "base": "upscale", "save_path": "default", "description": "4x-AnimeSharp upscaler model", "reference": "https://huggingface.co/Kim2091/AnimeSharp/", "filename": "4x-AnimeSharp.pth", "url": "https://huggingface.co/Kim2091/AnimeSharp/resolve/main/4x-AnimeSharp.pth", "size": "67.0MB"}, {"name": "4x-UltraSharp", "type": "upscale", "base": "upscale", "save_path": "default", "description": "4x-UltraSharp upscaler model", "reference": "https://huggingface.co/Kim2091/UltraSharp/", "filename": "4x-UltraSharp.pth", "url": "https://huggingface.co/Kim2091/UltraSharp/resolve/main/4x-UltraSharp.pth", "size": "67.0MB"}, {"name": "4x_NMKD-Siax_200k", "type": "upscale", "base": "upscale", "save_path": "default", "description": "4x_NMKD-Siax_200k upscaler model", "reference": "https://huggingface.co/gemasai/4x_NMKD-Siax_200k", "filename": "4x_NMKD-Siax_200k.pth", "url": "https://huggingface.co/gemasai/4x_NMKD-Siax_200k/resolve/main/4x_NMKD-Siax_200k.pth", "size": "67.0MB"}, {"name": "8x_NMKD-Superscale_150000_G", "type": "upscale", "base": "upscale", "save_path": "default", "description": "8x_NMKD-Superscale_150000_G upscaler model", "reference": "https://huggingface.co/uwg/upscaler", "filename": "8x_NMKD-Superscale_150000_G.pth", "url": "https://huggingface.co/uwg/upscaler/resolve/main/ESRGAN/8x_NMKD-Superscale_150000_G.pth", "size": "67.1MB"}, {"name": "8x_NMKD-Faces_160000_G", "type": "upscale", "base": "upscale", "save_path": "default", "description": "8x_NMKD-Faces_160000_G upscaler model", "reference": "https://huggingface.co/gemasai/8x_NMKD-Faces_160000_G/tree/main", "filename": "8x_NMKD-Faces_160000_G.pth", "url": "https://huggingface.co/gemasai/8x_NMKD-Faces_160000_G/resolve/main/8x_NMKD-Faces_160000_G.pth", "size": "67.2MB"}, {"name": "LDSR(Latent Diffusion Super Resolution)", "type": "upscale", "base": "upscale", "save_path": "upscale_models/ldsr", "description": "LDSR upscale model. Through the [a/ComfyUI-Flowty-LDSR](https://github.com/flowtyone/ComfyUI-Flowty-LDSR) extension, the upscale model can be utilized.", "reference": "https://github.com/CompVis/latent-diffusion", "filename": "last.ckpt", "url": "https://heibox.uni-heidelberg.de/f/578df07c8fc04ffbadf3/?dl=1", "size": "2.04GB"}, {"name": "stabilityai/stable-diffusion-x4-upscaler", "type": "checkpoint", "base": "upscale", "save_path": "checkpoints/upscale", "description": "This upscaling model is a latent text-guided diffusion model and should be used with SD_4XUpscale_Conditioning and KSampler.", "reference": "https://huggingface.co/stabilityai/stable-diffusion-x4-upscaler", "filename": "x4-upscaler-ema.safetensors", "url": "https://huggingface.co/stabilityai/stable-diffusion-x4-upscaler/resolve/main/x4-upscaler-ema.safetensors", "size": "3.53GB"}, {"name": "Deepbump", "type": "deepbump", "base": "deepbump", "save_path": "deepbump", "description": "Checkpoint of the deepbump model to generate height and normal maps textures from an image (requires comfy_mtb)", "reference": "https://github.com/HugoTini/DeepBump", "filename": "deepbump256.onnx", "url": "https://github.com/HugoTini/DeepBump/raw/master/deepbump256.onnx", "size": "26.7MB"}, {"name": "GFPGAN 1.3", "type": "face_restore", "base": "face_restore", "save_path": "face_restore", "description": "Face restoration", "reference": "https://github.com/TencentARC/GFPGAN", "filename": "GFPGANv1.3.pth", "url": "https://github.com/TencentARC/GFPGAN/releases/download/v1.3.0/GFPGANv1.3.pth", "size": "348.6MB"}, {"name": "GFPGAN 1.4", "type": "face_restore", "base": "face_restore", "save_path": "face_restore", "description": "Face restoration", "reference": "https://github.com/TencentARC/GFPGAN", "filename": "GFPGANv1.4.pth", "url": "https://github.com/TencentARC/GFPGAN/releases/download/v1.3.0/GFPGANv1.4.pth", "size": "348.6MB"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "type": "face_restore", "base": "face_restore", "save_path": "face_restore", "description": "Face restoration", "reference": "https://github.com/TencentARC/GFPGAN", "filename": "RestoreFormer.pth", "url": "https://github.com/TencentARC/GFPGAN/releases/download/v1.3.4/RestoreFormer.pth", "size": "290.8MB"}, {"name": "Stable Video Diffusion Image-to-Video", "type": "checkpoint", "base": "SVD", "save_path": "checkpoints/SVD", "description": "Stable Video Diffusion (SVD) Image-to-Video is a diffusion model that takes in a still image as a conditioning frame, and generates a video from it.\nNOTE: 14 frames @ 576x1024", "reference": "https://huggingface.co/stabilityai/stable-video-diffusion-img2vid", "filename": "svd.safetensors", "url": "https://huggingface.co/stabilityai/stable-video-diffusion-img2vid/resolve/main/svd.safetensors", "size": "9.56GB"}, {"name": "stabilityai/Stable Zero123", "type": "zero123", "base": "zero123", "save_path": "checkpoints/zero123", "description": "Stable Zero123 is a model for view-conditioned image generation based on [a/Zero123](https://github.com/cvlab-columbia/zero123).", "reference": "https://huggingface.co/stabilityai/stable-zero123", "filename": "stable_zero123.ckpt", "url": "https://huggingface.co/stabilityai/stable-zero123/resolve/main/stable_zero123.ckpt", "size": "8.58GB"}, {"name": "Stable Video Diffusion Image-to-Video (XT)", "type": "checkpoint", "base": "SVD", "save_path": "checkpoints/SVD", "description": "Stable Video Diffusion (SVD) Image-to-Video is a diffusion model that takes in a still image as a conditioning frame, and generates a video from it.\nNOTE: 25 frames @ 576x1024 ", "reference": "https://huggingface.co/stabilityai/stable-video-diffusion-img2vid-xt", "filename": "svd_xt.safetensors", "url": "https://huggingface.co/stabilityai/stable-video-diffusion-img2vid-xt/resolve/main/svd_xt.safetensors", "size": "9.56GB"}, {"name": "negative_hand Negative Embedding", "type": "embedding", "base": "SD1.5", "save_path": "embeddings/SD1.5", "description": "If you use this embedding with negatives, you can solve the issue of damaging your hands.", "reference": "https://civitai.com/models/56519/negativehand-negative-embedding", "filename": "negative_hand-neg.pt", "url": "https://civitai.com/api/download/models/60938", "size": "25KB"}, {"name": "bad_prompt Negative Embedding", "type": "embedding", "base": "SD1.5", "save_path": "embeddings/SD1.5", "description": "The idea behind this embedding was to somehow train the negative prompt as an embedding, thus unifying the basis of the negative prompt into one word or embedding.", "reference": "https://civitai.com/models/55700/badprompt-negative-embedding", "filename": "bad_prompt_version2-neg.pt", "url": "https://civitai.com/api/download/models/60095", "size": "25KB"}, {"name": "Deep Negative V1.75", "type": "embedding", "base": "SD1.5", "save_path": "embeddings/SD1.5", "description": "These embedding learn what disgusting compositions and color patterns are, including faulty human anatomy, offensive color schemes, upside-down spatial structures, and more. Placing it in the negative can go a long way to avoiding these things.", "reference": "https://civitai.com/models/4629/deep-negative-v1x", "filename": "ng_deepnegative_v1_75t.pt", "url": "https://civitai.com/api/download/models/5637", "size": "226KB"}, {"name": "EasyNegative", "type": "embedding", "base": "SD1.5", "save_path": "embeddings/SD1.5", "description": "This embedding should be used in your NEGATIVE prompt. Adjust the strength as desired (seems to scale well without any distortions), the strength required may vary based on positive and negative prompts.", "reference": "https://civitai.com/models/7808/easynegative", "filename": "easynegative.safetensors", "url": "https://civitai.com/api/download/models/9208", "size": "25KB"}, {"name": "stabilityai/comfyui_checkpoints/stable_cascade_stage_b.safetensors", "type": "checkpoint", "base": "Stable Cascade", "save_path": "checkpoints/Stable-Cascade", "description": "Stable Cascade stage_b checkpoints", "reference": "https://huggingface.co/stabilityai/stable-cascade", "filename": "stable_cascade_stage_b.safetensors", "url": "https://huggingface.co/stabilityai/stable-cascade/resolve/main/comfyui_checkpoints/stable_cascade_stage_b.safetensors", "size": "4.55GB"}, {"name": "stabilityai/comfyui_checkpoints/stable_cascade_stage_c.safetensors", "type": "checkpoint", "base": "Stable Cascade", "save_path": "checkpoints/Stable-Cascade", "description": "Stable Cascade stage_c checkpoints", "reference": "https://huggingface.co/stabilityai/stable-cascade", "filename": "stable_cascade_stage_c.safetensors", "url": "https://huggingface.co/stabilityai/stable-cascade/resolve/main/comfyui_checkpoints/stable_cascade_stage_c.safetensors", "size": "9.22GB"}, {"name": "stabilityai/Stable Cascade: stage_a.safetensors (VAE)", "type": "VAE", "base": "Stable Cascade", "save_path": "vae/Stable-Cascade", "description": "Stable Cascade: stage_a", "reference": "https://huggingface.co/stabilityai/stable-cascade", "filename": "stage_a.safetensors", "url": "https://huggingface.co/stabilityai/stable-cascade/resolve/main/stage_a.safetensors", "size": "73.7MB"}, {"name": "stabilityai/Stable Cascade: effnet_encoder.safetensors (VAE)", "type": "VAE", "base": "Stable Cascade", "save_path": "vae/Stable-Cascade", "description": "Stable Cascade: effnet_encoder.\nVAE encoder for stage_c latent.", "reference": "https://huggingface.co/stabilityai/stable-cascade", "filename": "effnet_encoder.safetensors", "url": "https://huggingface.co/stabilityai/stable-cascade/resolve/main/effnet_encoder.safetensors", "size": "81.5MB"}, {"name": "stabilityai/Stable Cascade: stage_b.safetensors (UNET)", "type": "diffusion_model", "base": "Stable Cascade", "save_path": "diffusion_models/Stable-Cascade", "description": "Stable Cascade: stage_b", "reference": "https://huggingface.co/stabilityai/stable-cascade", "filename": "stage_b.safetensors", "url": "https://huggingface.co/stabilityai/stable-cascade/resolve/main/stage_b.safetensors", "size": "6.25GB"}, {"name": "stabilityai/Stable Cascade: stage_b_bf16.safetensors (UNET)", "type": "diffusion_model", "base": "Stable Cascade", "save_path": "diffusion_models/Stable-Cascade", "description": "Stable Cascade: stage_b/bf16", "reference": "https://huggingface.co/stabilityai/stable-cascade", "filename": "stage_b_bf16.safetensors", "url": "https://huggingface.co/stabilityai/stable-cascade/resolve/main/stage_b_bf16.safetensors", "size": "3.13GB"}, {"name": "stabilityai/Stable Cascade: stage_b_lite.safetensors (UNET)", "type": "diffusion_model", "base": "Stable Cascade", "save_path": "diffusion_models/Stable-Cascade", "description": "Stable Cascade: stage_b/lite", "reference": "https://huggingface.co/stabilityai/stable-cascade", "filename": "stage_b_lite.safetensors", "url": "https://huggingface.co/stabilityai/stable-cascade/resolve/main/stage_b_lite.safetensors", "size": "2.80GB"}, {"name": "stabilityai/Stable Cascade: stage_b_lite.safetensors (UNET)", "type": "diffusion_model", "base": "Stable Cascade", "save_path": "diffusion_models/Stable-Cascade", "description": "Stable Cascade: stage_b/bf16,lite", "reference": "https://huggingface.co/stabilityai/stable-cascade", "filename": "stage_b_lite_bf16.safetensors", "url": "https://huggingface.co/stabilityai/stable-cascade/resolve/main/stage_b_lite_bf16.safetensors", "size": "1.40GB"}, {"name": "stabilityai/Stable Cascade: stage_c.safetensors (UNET)", "type": "diffusion_model", "base": "Stable Cascade", "save_path": "diffusion_models/Stable-Cascade", "description": "Stable Cascade: stage_c", "reference": "https://huggingface.co/stabilityai/stable-cascade", "filename": "stage_c.safetensors", "url": "https://huggingface.co/stabilityai/stable-cascade/resolve/main/stage_c.safetensors", "size": "14.4GB"}, {"name": "stabilityai/Stable Cascade: stage_c_bf16.safetensors (UNET)", "type": "diffusion_model", "base": "Stable Cascade", "save_path": "diffusion_models/Stable-Cascade", "description": "Stable Cascade: stage_c/bf16", "reference": "https://huggingface.co/stabilityai/stable-cascade", "filename": "stage_c_bf16.safetensors", "url": "https://huggingface.co/stabilityai/stable-cascade/resolve/main/stage_c_bf16.safetensors", "size": "7.18GB"}, {"name": "stabilityai/Stable Cascade: stage_c_lite.safetensors (UNET)", "type": "diffusion_model", "base": "Stable Cascade", "save_path": "diffusion_models/Stable-Cascade", "description": "Stable Cascade: stage_c/lite", "reference": "https://huggingface.co/stabilityai/stable-cascade", "filename": "stage_c_lite.safetensors", "url": "https://huggingface.co/stabilityai/stable-cascade/resolve/main/stage_c_lite.safetensors", "size": "4.12GB"}, {"name": "stabilityai/Stable Cascade: stage_c_lite.safetensors (UNET)", "type": "diffusion_model", "base": "Stable Cascade", "save_path": "diffusion_models/Stable-Cascade", "description": "Stable Cascade: stage_c/bf16,lite", "reference": "https://huggingface.co/stabilityai/stable-cascade", "filename": "stage_c_lite_bf16.safetensors", "url": "https://huggingface.co/stabilityai/stable-cascade/resolve/main/stage_c_lite_bf16.safetensors", "size": "2.06GB"}, {"name": "stabilityai/Stable Cascade: text_encoder (CLIP)", "type": "clip", "base": "Stable Cascade", "save_path": "text_encoders/Stable-Cascade", "description": "Stable Cascade: text_encoder", "reference": "https://huggingface.co/stabilityai/stable-cascade", "filename": "model.safetensors", "url": "https://huggingface.co/stabilityai/stable-cascade/resolve/main/text_encoder/model.safetensors", "size": "1.39GB"}, {"name": "SDXL-Turbo 1.0 (fp16)", "type": "checkpoint", "base": "SDXL", "save_path": "checkpoints/SDXL-TURBO", "description": "SDXL-Turbo 1.0 fp16", "reference": "https://huggingface.co/stabilityai/sdxl-turbo", "filename": "sd_xl_turbo_1.0_fp16.safetensors", "url": "https://huggingface.co/stabilityai/sdxl-turbo/resolve/main/sd_xl_turbo_1.0_fp16.safetensors", "size": "6.94GB"}, {"name": "SDXL-Turbo 1.0", "type": "checkpoint", "base": "SDXL", "save_path": "checkpoints/SDXL-TURBO", "description": "SDXL-Turbo 1.0", "reference": "https://huggingface.co/stabilityai/sdxl-turbo", "filename": "sd_xl_turbo_1.0.safetensors", "url": "https://huggingface.co/stabilityai/sdxl-turbo/resolve/main/sd_xl_turbo_1.0.safetensors", "size": "13.9GB"}, {"name": "sd_xl_base_1.0_0.9vae.safetensors", "type": "checkpoint", "base": "SDXL", "save_path": "checkpoints/SDXL", "description": "Stable Diffusion XL base model (VAE 0.9)", "reference": "https://huggingface.co/stabilityai/stable-diffusion-xl-base-1.0", "filename": "sd_xl_base_1.0_0.9vae.safetensors", "url": "https://huggingface.co/stabilityai/stable-diffusion-xl-base-1.0/resolve/main/sd_xl_base_1.0_0.9vae.safetensors", "size": "6.94GB"}, {"name": "sd_xl_base_1.0.safetensors", "type": "checkpoint", "base": "SDXL", "save_path": "checkpoints/SDXL", "description": "Stable Diffusion XL base model", "reference": "https://huggingface.co/stabilityai/stable-diffusion-xl-base-1.0", "filename": "sd_xl_base_1.0.safetensors", "url": "https://huggingface.co/stabilityai/stable-diffusion-xl-base-1.0/resolve/main/sd_xl_base_1.0.safetensors", "size": "6.94GB"}, {"name": "sd_xl_refiner_1.0_0.9vae.safetensors", "type": "checkpoint", "base": "SDXL", "save_path": "checkpoints/SDXL", "description": "Stable Diffusion XL refiner model (VAE 0.9)", "reference": "https://huggingface.co/stabilityai/stable-diffusion-xl-refiner-1.0", "filename": "sd_xl_refiner_1.0_0.9vae.safetensors", "url": "https://huggingface.co/stabilityai/stable-diffusion-xl-refiner-1.0/resolve/main/sd_xl_refiner_1.0_0.9vae.safetensors", "size": "6.08GB"}, {"name": "stable-diffusion-xl-refiner-1.0", "type": "checkpoint", "base": "SDXL", "save_path": "checkpoints/SDXL", "description": "Stable Diffusion XL refiner model", "reference": "https://huggingface.co/stabilityai/stable-diffusion-xl-refiner-1.0", "filename": "sd_xl_refiner_1.0.safetensors", "url": "https://huggingface.co/stabilityai/stable-diffusion-xl-refiner-1.0/resolve/main/sd_xl_refiner_1.0.safetensors", "size": "6.08GB"}, {"name": "diffusers/stable-diffusion-xl-1.0-inpainting-0.1 (diffusion_models/fp16)", "type": "diffusion_model", "base": "SDXL", "save_path": "diffusion_models/xl-inpaint-0.1", "description": "Stable Diffusion XL inpainting model 0.1. You need UNETLoader instead of CheckpointLoader.", "reference": "https://huggingface.co/diffusers/stable-diffusion-xl-1.0-inpainting-0.1", "filename": "diffusion_pytorch_model.fp16.safetensors", "url": "https://huggingface.co/diffusers/stable-diffusion-xl-1.0-inpainting-0.1/resolve/main/unet/diffusion_pytorch_model.fp16.safetensors", "size": "5.14GB"}, {"name": "diffusers/stable-diffusion-xl-1.0-inpainting-0.1 (UNET)", "type": "diffusion_model", "base": "SDXL", "save_path": "diffusion_models/xl-inpaint-0.1", "description": "Stable Diffusion XL inpainting model 0.1. You need UNETLoader instead of CheckpointLoader.", "reference": "https://huggingface.co/diffusers/stable-diffusion-xl-1.0-inpainting-0.1", "filename": "diffusion_pytorch_model.safetensors", "url": "https://huggingface.co/diffusers/stable-diffusion-xl-1.0-inpainting-0.1/resolve/main/unet/diffusion_pytorch_model.safetensors", "size": "10.3GB"}, {"name": "sd_xl_offset_example-lora_1.0.safetensors", "type": "lora", "base": "SDXL", "save_path": "loras/SDXL", "description": "Stable Diffusion XL offset LoRA", "reference": "https://huggingface.co/stabilityai/stable-diffusion-xl-base-1.0", "filename": "sd_xl_offset_example-lora_1.0.safetensors", "url": "https://huggingface.co/stabilityai/stable-diffusion-xl-base-1.0/resolve/main/sd_xl_offset_example-lora_1.0.safetensors", "size": "49.6MB"}, {"name": "SDXL Lightning LoRA (2steps)", "type": "lora", "base": "SDXL", "save_path": "loras/SDXL-Lightning", "description": "SDXL Lightning LoRA (2steps)", "reference": "https://huggingface.co/ByteDance/SDXL-Lightning", "filename": "sdxl_lightning_2step_lora.safetensors", "url": "https://huggingface.co/ByteDance/SDXL-Lightning/resolve/main/sdxl_lightning_2step_lora.safetensors", "size": "393.9MB"}, {"name": "SDXL Lightning LoRA (4steps)", "type": "lora", "base": "SDXL", "save_path": "loras/SDXL-Lightning", "description": "SDXL Lightning LoRA (4steps)", "reference": "https://huggingface.co/ByteDance/SDXL-Lightning", "filename": "sdxl_lightning_4step_lora.safetensors", "url": "https://huggingface.co/ByteDance/SDXL-Lightning/resolve/main/sdxl_lightning_4step_lora.safetensors", "size": "393.9MB"}, {"name": "SDXL Lightning LoRA (8steps)", "type": "lora", "base": "SDXL", "save_path": "loras/SDXL-Lightning", "description": "SDXL Lightning LoRA (8steps)", "reference": "https://huggingface.co/ByteDance/SDXL-Lightning", "filename": "sdxl_lightning_8step_lora.safetensors", "url": "https://huggingface.co/ByteDance/SDXL-Lightning/resolve/main/sdxl_lightning_8step_lora.safetensors", "size": "393.9MB"}, {"name": "DMD2 LoRA (4steps)", "type": "lora", "base": "SDXL", "save_path": "loras/DMD2", "description": "DMD2 LoRA (4steps)", "reference": "https://huggingface.co/tianweiy/DMD2", "filename": "dmd2_sdxl_4step_lora.safetensors", "url": "https://huggingface.co/tianweiy/DMD2/resolve/main/dmd2_sdxl_4step_lora.safetensors", "size": "787MB"}, {"name": "DMD2 LoRA (4steps/fp16)", "type": "lora", "base": "SDXL", "save_path": "loras/DMD2", "description": "DMD2 LoRA (4steps/fp16)", "reference": "https://huggingface.co/tianweiy/DMD2", "filename": "dmd2_sdxl_4step_lora_fp16.safetensors", "url": "https://huggingface.co/tianweiy/DMD2/resolve/main/dmd2_sdxl_4step_lora_fp16.safetensors", "size": "394MB"}, {"name": "Hyper-SD LoRA (8steps) - FLUX.1 [Dev]", "type": "lora", "base": "FLUX.1", "save_path": "loras/HyperSD/FLUX.1", "description": "Hyper-SD LoRA (8steps) - FLUX.1 [Dev]", "reference": "https://huggingface.co/ByteDance/Hyper-SD", "filename": "Hyper-FLUX.1-dev-8steps-lora.safetensors", "url": "https://huggingface.co/ByteDance/Hyper-SD/resolve/main/Hyper-FLUX.1-dev-8steps-lora.safetensors", "size": "1.39GB"}, {"name": "Hyper-SD LoRA (16steps) - FLUX.1 [Dev]", "type": "lora", "base": "FLUX.1", "save_path": "loras/HyperSD/FLUX.1", "description": "Hyper-SD LoRA (16steps) - FLUX.1 [Dev]", "reference": "https://huggingface.co/ByteDance/Hyper-SD", "filename": "Hyper-FLUX.1-dev-16steps-lora.safetensors", "url": "https://huggingface.co/ByteDance/Hyper-SD/resolve/main/Hyper-FLUX.1-dev-16steps-lora.safetensors", "size": "1.39GB"}, {"name": "Hyper-SD LoRA (1step) - SD1.5", "type": "lora", "base": "SD1.5", "save_path": "loras/HyperSD/SD15", "description": "Hyper-SD LoRA (1step) - SD1.5", "reference": "https://huggingface.co/ByteDance/Hyper-SD", "filename": "Hyper-SD15-1step-lora.safetensors", "url": "https://huggingface.co/ByteDance/Hyper-SD/resolve/main/Hyper-SD15-1step-lora.safetensors", "size": "269MB"}, {"name": "Hyper-SD LoRA (2steps) - SD1.5", "type": "lora", "base": "SD1.5", "save_path": "loras/HyperSD/SD15", "description": "Hyper-SD LoRA (2steps) - SD1.5", "reference": "https://huggingface.co/ByteDance/Hyper-SD", "filename": "Hyper-SD15-2steps-lora.safetensors", "url": "https://huggingface.co/ByteDance/Hyper-SD/resolve/main/Hyper-SD15-2steps-lora.safetensors", "size": "269MB"}, {"name": "Hyper-SD LoRA (4steps) - SD1.5", "type": "lora", "base": "SD1.5", "save_path": "loras/HyperSD/SD15", "description": "Hyper-SD LoRA (4steps)", "reference": "https://huggingface.co/ByteDance/Hyper-SD", "filename": "Hyper-SD15-4steps-lora.safetensors", "url": "https://huggingface.co/ByteDance/Hyper-SD/resolve/main/Hyper-SD15-4steps-lora.safetensors", "size": "269MB"}, {"name": "Hyper-SD LoRA (8steps) - SD1.5", "type": "lora", "base": "SD1.5", "save_path": "loras/HyperSD/SD15", "description": "Hyper-SD LoRA (8steps)", "reference": "https://huggingface.co/ByteDance/Hyper-SD", "filename": "Hyper-SD15-8steps-lora.safetensors", "url": "https://huggingface.co/ByteDance/Hyper-SD/resolve/main/Hyper-SD15-8steps-lora.safetensors", "size": "269MB"}, {"name": "Hyper-SD CFG LoRA (8steps) - SD1.5", "type": "lora", "base": "SD1.5", "save_path": "loras/HyperSD/SD15", "description": "Hyper-SD CFG LoRA (8steps)", "reference": "https://huggingface.co/ByteDance/Hyper-SD", "filename": "Hyper-SD15-8steps-CFG-lora.safetensors", "url": "https://huggingface.co/ByteDance/Hyper-SD/resolve/main/Hyper-SD15-8steps-CFG-lora.safetensors", "size": "269MB"}, {"name": "Hyper-SD CFG LoRA (12steps) - SD1.5", "type": "lora", "base": "SD1.5", "save_path": "loras/HyperSD/SD15", "description": "Hyper-SD CFG LoRA (12steps)", "reference": "https://huggingface.co/ByteDance/Hyper-SD", "filename": "Hyper-SD15-12steps-CFG-lora.safetensors", "url": "https://huggingface.co/ByteDance/Hyper-SD/resolve/main/Hyper-SD15-12steps-CFG-lora.safetensors", "size": "269MB"}, {"name": "Hyper-SD LoRA (1step) - SDXL", "type": "lora", "base": "SDXL", "save_path": "loras/HyperSD/SDXL", "description": "Hyper-SD LoRA (1step) - SDXL", "reference": "https://huggingface.co/ByteDance/Hyper-SD", "filename": "Hyper-SDXL-1step-lora.safetensors", "url": "https://huggingface.co/ByteDance/Hyper-SD/resolve/main/Hyper-SDXL-1step-lora.safetensors", "size": "787MB"}, {"name": "Hyper-SD LoRA (2steps) - SDXL", "type": "lora", "base": "SDXL", "save_path": "loras/HyperSD/SDXL", "description": "Hyper-SD LoRA (2steps) - SDXL", "reference": "https://huggingface.co/ByteDance/Hyper-SD", "filename": "Hyper-SDXL-2steps-lora.safetensors", "url": "https://huggingface.co/ByteDance/Hyper-SD/resolve/main/Hyper-SDXL-2steps-lora.safetensors", "size": "787MB"}, {"name": "Hyper-SD LoRA (4steps) - SDXL", "type": "lora", "base": "SDXL", "save_path": "loras/HyperSD/SDXL", "description": "Hyper-SD LoRA (4steps) - SDXL", "reference": "https://huggingface.co/ByteDance/Hyper-SD", "filename": "Hyper-SDXL-4steps-lora.safetensors", "url": "https://huggingface.co/ByteDance/Hyper-SD/resolve/main/Hyper-SDXL-4steps-lora.safetensors", "size": "787MB"}, {"name": "Hyper-SD LoRA (8steps) - SDXL", "type": "lora", "base": "SDXL", "save_path": "loras/HyperSD/SDXL", "description": "Hyper-SD LoRA (8steps) - SDXL", "reference": "https://huggingface.co/ByteDance/Hyper-SD", "filename": "Hyper-SDXL-8steps-lora.safetensors", "url": "https://huggingface.co/ByteDance/Hyper-SD/resolve/main/Hyper-SDXL-8steps-lora.safetensors", "size": "787MB"}, {"name": "Hyper-SD CFG LoRA (8steps) - SDXL", "type": "lora", "base": "SDXL", "save_path": "loras/HyperSD/SDXL", "description": "Hyper-SD CFG LoRA (8steps) - SDXL", "reference": "https://huggingface.co/ByteDance/Hyper-SD", "filename": "Hyper-SDXL-8steps-CFG-lora.safetensors", "url": "https://huggingface.co/ByteDance/Hyper-SD/resolve/main/Hyper-SDXL-8steps-CFG-lora.safetensors", "size": "787MB"}, {"name": "Hyper-SD CFG LoRA (12steps) - SDXL", "type": "lora", "base": "SDXL", "save_path": "loras/HyperSD/SDXL", "description": "Hyper-SD CFG LoRA (12steps) - SDXL", "reference": "https://huggingface.co/ByteDance/Hyper-SD", "filename": "Hyper-SDXL-12steps-CFG-lora.safetensors", "url": "https://huggingface.co/ByteDance/Hyper-SD/resolve/main/Hyper-SDXL-12steps-CFG-lora.safetensors", "size": "787MB"}, {"name": "Hyper-SD CFG LoRA (4steps) - SD3", "type": "lora", "base": "SD3", "save_path": "loras/HyperSD/SD3", "description": "Hyper-SD CFG LoRA (4steps) - SD3", "reference": "https://huggingface.co/ByteDance/Hyper-SD", "filename": "Hyper-SD3-4steps-CFG-lora.safetensors", "url": "https://huggingface.co/ByteDance/Hyper-SD/resolve/main/Hyper-SD3-4steps-CFG-lora.safetensors", "size": "472MB"}, {"name": "Hyper-SD CFG LoRA (8steps) - SD3", "type": "lora", "base": "SD3", "save_path": "loras/HyperSD/SD3", "description": "Hyper-SD CFG LoRA (8steps) - SD3", "reference": "https://huggingface.co/ByteDance/Hyper-SD", "filename": "Hyper-SD3-8steps-CFG-lora.safetensors", "url": "https://huggingface.co/ByteDance/Hyper-SD/resolve/main/Hyper-SD3-8steps-CFG-lora.safetensors", "size": "472MB"}, {"name": "Hyper-SD CFG LoRA (16steps) - SD3", "type": "lora", "base": "SD3", "save_path": "loras/HyperSD/SD3", "description": "Hyper-SD CFG LoRA (16steps) - SD3", "reference": "https://huggingface.co/ByteDance/Hyper-SD", "filename": "Hyper-SD3-16steps-CFG-lora.safetensors", "url": "https://huggingface.co/ByteDance/Hyper-SD/resolve/main/Hyper-SD3-16steps-CFG-lora.safetensors", "size": "472MB"}, {"name": "comfyanonymous/flux_text_encoders - t5xxl (fp16)", "type": "clip", "base": "t5", "save_path": "text_encoders/t5", "description": "Text Encoders for FLUX (fp16)", "reference": "https://huggingface.co/comfyanonymous/flux_text_encoders", "filename": "t5xxl_fp16.safetensors", "url": "https://huggingface.co/comfyanonymous/flux_text_encoders/resolve/main/t5xxl_fp16.safetensors", "size": "9.79GB"}, {"name": "comfyanonymous/flux_text_encoders - t5xxl (fp8_e4m3fn)", "type": "clip", "base": "t5", "save_path": "text_encoders/t5", "description": "Text Encoders for FLUX (fp8_e4m3fn)", "reference": "https://huggingface.co/comfyanonymous/flux_text_encoders", "filename": "t5xxl_fp8_e4m3fn.safetensors", "url": "https://huggingface.co/comfyanonymous/flux_text_encoders/resolve/main/t5xxl_fp8_e4m3fn.safetensors", "size": "4.89GB"}, {"name": "comfyanonymous/flux_text_encoders - t5xxl (fp8_e4m3fn_scaled)", "type": "clip", "base": "t5", "save_path": "text_encoders/t5", "description": "Text Encoders for FLUX (fp16)", "reference": "https://huggingface.co/comfyanonymous/flux_text_encoders", "filename": "t5xxl_fp8_e4m3fn_scaled.safetensors", "url": "https://huggingface.co/comfyanonymous/flux_text_encoders/resolve/main/t5xxl_fp8_e4m3fn_scaled.safetensors", "size": "5.16GB"}, {"name": "comfyanonymous/cosmos_cv8x8x8_1.0.safetensors", "type": "VAE", "base": "Cosmos-1.0", "save_path": "default", "description": "VAE model for Cosmos 1.0", "reference": "https://huggingface.co/comfyanonymous/cosmos_1.0_text_encoder_and_VAE_ComfyUI/tree/main", "filename": "cosmos_cv8x8x8_1.0.safetensors", "url": "https://huggingface.co/comfyanonymous/cosmos_1.0_text_encoder_and_VAE_ComfyUI/resolve/main/vae/cosmos_cv8x8x8_1.0.safetensors", "size": "211MB"}, {"name": "mcmonkey/Cosmos-1_0-Diffusion-7B-Text2World.safetensors", "type": "diffusion_model", "base": "Cosmos-1.0", "save_path": "diffusion_models/cosmos-1.0", "description": "Cosmos 1.0 Text2World Diffusion Model (7B)", "reference": "https://huggingface.co/mcmonkey/cosmos-1.0", "filename": "Cosmos-1_0-Diffusion-7B-Text2World.safetensors", "url": "https://huggingface.co/mcmonkey/cosmos-1.0/resolve/main/Cosmos-1_0-Diffusion-7B-Text2World.safetensors", "size": "14.5GB"}, {"name": "mcmonkey/Cosmos-1_0-Diffusion-7B-Video2World.safetensors", "type": "diffusion_model", "base": "Cosmos-1.0", "save_path": "diffusion_models/cosmos-1.0", "description": "Cosmos 1.0 Video2World Diffusion Model (7B)", "reference": "https://huggingface.co/mcmonkey/cosmos-1.0", "filename": "Cosmos-1_0-Diffusion-7B-Video2World.safetensors", "url": "https://huggingface.co/mcmonkey/cosmos-1.0/resolve/main/Cosmos-1_0-Diffusion-7B-Video2World.safetensors", "size": "14.5GB"}, {"name": "mcmonkey/Cosmos-1_0-Diffusion-14B-Text2World.safetensors", "type": "diffusion_model", "base": "Cosmos-1.0", "save_path": "diffusion_models/cosmos-1.0", "description": "Cosmos 1.0 Text2World Diffusion Model (14B)", "reference": "https://huggingface.co/mcmonkey/cosmos-1.0", "filename": "Cosmos-1_0-Diffusion-14B-Text2World.safetensors", "url": "https://huggingface.co/mcmonkey/cosmos-1.0/resolve/main/Cosmos-1_0-Diffusion-14B-Text2World.safetensors", "size": "28.5GB"}, {"name": "mcmonkey/Cosmos-1_0-Diffusion-14B-Video2World.safetensors", "type": "diffusion_model", "base": "Cosmos-1.0", "save_path": "diffusion_models/cosmos-1.0", "description": "Cosmos 1.0 Video2World Diffusion Model (14B)", "reference": "https://huggingface.co/mcmonkey/cosmos-1.0", "filename": "Cosmos-1_0-Diffusion-14B-Video2World.safetensors", "url": "https://huggingface.co/mcmonkey/cosmos-1.0/resolve/main/Cosmos-1_0-Diffusion-14B-Video2World.safetensors", "size": "28.5GB"}, {"name": "google-t5/t5-base", "type": "clip", "base": "t5-base", "save_path": "text_encoders/t5-base", "description": "T5 Base: Text-To-Text Transfer Transformer. This model can be loaded via CLIPLoader for Stable Audio workflow.", "reference": "https://huggingface.co/google-t5/t5-base", "filename": "model.safetensors", "url": "https://huggingface.co/google-t5/t5-base/resolve/main/model.safetensors", "size": "892MB"}, {"name": "google-t5/t5-v1_1-xxl_encoderonly-fp16", "type": "clip", "base": "t5", "save_path": "text_encoders/t5", "description": "The encoder part of https://huggingface.co/google/t5-v1_1-xxl, used with SD3 and Flux1", "reference": "https://huggingface.co/mcmonkey/google_t5-v1_1-xxl_encoderonly", "filename": "google_t5-v1_1-xxl_encoderonly-fp16.safetensors", "url": "https://huggingface.co/mcmonkey/google_t5-v1_1-xxl_encoderonly/resolve/main/model.safetensors", "size": "10.1GB"}, {"name": "google-t5/t5-v1_1-xxl_encoderonly-fp8_e4m3fn", "type": "clip", "base": "t5", "save_path": "text_encoders/t5", "description": "The encoder part of https://huggingface.co/google/t5-v1_1-xxl, used with SD3 and Flux1", "reference": "https://huggingface.co/mcmonkey/google_t5-v1_1-xxl_encoderonly", "filename": "google_t5-v1_1-xxl_encoderonly-fp8_e4m3fn.safetensors", "url": "https://huggingface.co/mcmonkey/google_t5-v1_1-xxl_encoderonly/resolve/main/t5xxl_fp8_e4m3fn.safetensors", "size": "4.89GB"}, {"name": "city96/t5-v1_1-xxl-encoder-Q3_K_L.gguf", "type": "clip", "base": "t5", "save_path": "text_encoders/t5", "description": "t5xxl Text Encoder GGUF model. (Q3_K_L quantized)", "reference": "https://huggingface.co/city96/t5-v1_1-xxl-encoder-gguf", "filename": "t5-v1_1-xxl-encoder-Q3_K_L.gguf", "url": "https://huggingface.co/city96/t5-v1_1-xxl-encoder-gguf/resolve/main/t5-v1_1-xxl-encoder-Q3_K_L.gguf", "size": "2.46GB"}, {"name": "city96/t5-v1_1-xxl-encoder-Q3_K_M.gguf", "type": "clip", "base": "t5", "save_path": "text_encoders/t5", "description": "t5xxl Text Encoder GGUF model. (Q3_K_M quantized)", "reference": "https://huggingface.co/city96/t5-v1_1-xxl-encoder-gguf", "filename": "t5-v1_1-xxl-encoder-Q3_K_M.gguf", "url": "https://huggingface.co/city96/t5-v1_1-xxl-encoder-gguf/resolve/main/t5-v1_1-xxl-encoder-Q3_K_M.gguf", "size": "2.3GB"}, {"name": "city96/t5-v1_1-xxl-encoder-Q3_K_S.gguf", "type": "clip", "base": "t5", "save_path": "text_encoders/t5", "description": "t5xxl Text Encoder GGUF model. (Q3_K_S quantized)", "reference": "https://huggingface.co/city96/t5-v1_1-xxl-encoder-gguf", "filename": "t5-v1_1-xxl-encoder-Q3_K_S.gguf", "url": "https://huggingface.co/city96/t5-v1_1-xxl-encoder-gguf/resolve/main/t5-v1_1-xxl-encoder-Q3_K_S.gguf", "size": "2.1GB"}, {"name": "city96/t5-v1_1-xxl-encoder-Q4_K_M.gguf", "type": "clip", "base": "t5", "save_path": "text_encoders/t5", "description": "t5xxl Text Encoder GGUF model. (Q4_K_M quantized)", "reference": "https://huggingface.co/city96/t5-v1_1-xxl-encoder-gguf", "filename": "t5-v1_1-xxl-encoder-Q4_K_M.gguf", "url": "https://huggingface.co/city96/t5-v1_1-xxl-encoder-gguf/resolve/main/t5-v1_1-xxl-encoder-Q4_K_M.gguf", "size": "2.9GB"}, {"name": "city96/t5-v1_1-xxl-encoder-Q4_K_S.gguf", "type": "clip", "base": "t5", "save_path": "text_encoders/t5", "description": "t5xxl Text Encoder GGUF model. (Q4_K_S quantized)", "reference": "https://huggingface.co/city96/t5-v1_1-xxl-encoder-gguf", "filename": "t5-v1_1-xxl-encoder-Q4_K_S.gguf", "url": "https://huggingface.co/city96/t5-v1_1-xxl-encoder-gguf/resolve/main/t5-v1_1-xxl-encoder-Q4_K_S.gguf", "size": "2.74GB"}, {"name": "city96/t5-v1_1-xxl-encoder-Q5_K_M.gguf", "type": "clip", "base": "t5", "save_path": "text_encoders/t5", "description": "t5xxl Text Encoder GGUF model. (Q5_K_M quantized)", "reference": "https://huggingface.co/city96/t5-v1_1-xxl-encoder-gguf", "filename": "t5-v1_1-xxl-encoder-Q5_K_M.gguf", "url": "https://huggingface.co/city96/t5-v1_1-xxl-encoder-gguf/resolve/main/t5-v1_1-xxl-encoder-Q5_K_M.gguf", "size": "3.39GB"}, {"name": "city96/t5-v1_1-xxl-encoder-Q5_K_S.gguf", "type": "clip", "base": "t5", "save_path": "text_encoders/t5", "description": "t5xxl Text Encoder GGUF model. (Q5_K_S quantized)", "reference": "https://huggingface.co/city96/t5-v1_1-xxl-encoder-gguf", "filename": "t5-v1_1-xxl-encoder-Q5_K_S.gguf", "url": "https://huggingface.co/city96/t5-v1_1-xxl-encoder-gguf/resolve/main/t5-v1_1-xxl-encoder-Q5_K_S.gguf", "size": "3.29GB"}, {"name": "city96/t5-v1_1-xxl-encoder-Q6_K.gguf", "type": "clip", "base": "t5", "save_path": "text_encoders/t5", "description": "t5xxl Text Encoder GGUF model. (Q6_K quantized)", "reference": "https://huggingface.co/city96/t5-v1_1-xxl-encoder-gguf", "filename": "t5-v1_1-xxl-encoder-Q6_K.gguf", "url": "https://huggingface.co/city96/t5-v1_1-xxl-encoder-gguf/resolve/main/t5-v1_1-xxl-encoder-Q6_K.gguf", "size": "3.91GB"}, {"name": "city96/t5-v1_1-xxl-encoder-Q8_0.gguf", "type": "clip", "base": "t5", "save_path": "text_encoders/t5", "description": "t5xxl Text Encoder GGUF model. (Q8_0 quantized)", "reference": "https://huggingface.co/city96/t5-v1_1-xxl-encoder-gguf", "filename": "t5-v1_1-xxl-encoder-Q8_0.gguf", "url": "https://huggingface.co/city96/t5-v1_1-xxl-encoder-gguf/resolve/main/t5-v1_1-xxl-encoder-Q8_0.gguf", "size": "5.06GB"}, {"name": "city96/t5-v1_1-xxl-encoder-f16.gguf", "type": "clip", "base": "t5", "save_path": "text_encoders/t5", "description": "t5xxl Text Encoder GGUF model. (float 16)", "reference": "https://huggingface.co/city96/t5-v1_1-xxl-encoder-gguf", "filename": "t5-v1_1-xxl-encoder-f16.gguf", "url": "https://huggingface.co/city96/t5-v1_1-xxl-encoder-gguf/resolve/main/t5-v1_1-xxl-encoder-f16.gguf", "size": "9.53GB"}, {"name": "city96/t5-v1_1-xxl-encoder-f32.gguf", "type": "clip", "base": "t5", "save_path": "text_encoders/t5", "description": "t5xxl Text Encoder GGUF model. (float 32)", "reference": "https://huggingface.co/city96/t5-v1_1-xxl-encoder-gguf", "filename": "t5-v1_1-xxl-encoder-f32.gguf", "url": "https://huggingface.co/city96/t5-v1_1-xxl-encoder-gguf/resolve/main/t5-v1_1-xxl-encoder-f32.gguf", "size": "19.1GB"}, {"name": "Comfy-Org/clip_l", "type": "clip", "base": "clip", "save_path": "default", "description": "clip_l model (for SD1.x, SD2.x, SDXL, SD3.5, FLUX.1, Hu<PERSON>uanVideo, ...) ", "reference": "https://huggingface.co/Comfy-Org/stable-diffusion-3.5-fp8", "filename": "clip_l.safetensors", "url": "https://huggingface.co/Comfy-Org/stable-diffusion-3.5-fp8/resolve/main/text_encoders/clip_l.safetensors", "size": "246MB"}, {"name": "Comfy-Org/clip_g", "type": "clip", "base": "clip", "save_path": "default", "description": "clip_g model (for SDXL, SD3.5)", "reference": "https://huggingface.co/Comfy-Org/stable-diffusion-3.5-fp8", "filename": "clip_g.safetensors", "url": "https://huggingface.co/Comfy-Org/stable-diffusion-3.5-fp8/resolve/main/text_encoders/clip_g.safetensors", "size": "1.39GB"}, {"name": "v1-5-pruned-emaonly.ckpt", "type": "checkpoint", "base": "SD1.5", "save_path": "checkpoints/SD1.5", "description": "Stable Diffusion 1.5 base model", "reference": "https://huggingface.co/runwayml/stable-diffusion-v1-5", "filename": "v1-5-pruned-emaonly.ckpt", "url": "https://huggingface.co/runwayml/stable-diffusion-v1-5/resolve/main/v1-5-pruned-emaonly.ckpt", "size": "4.27GB"}, {"name": "v2-1_512-ema-pruned.safetensors", "type": "checkpoint", "base": "SD2", "save_path": "checkpoints/SD2.1", "description": "Stable Diffusion 2 base model (512)", "reference": "https://huggingface.co/stabilityai/stable-diffusion-2-1-base", "filename": "v2-1_512-ema-pruned.safetensors", "url": "https://huggingface.co/stabilityai/stable-diffusion-2-1-base/resolve/main/v2-1_512-ema-pruned.safetensors", "size": "5.21GB"}, {"name": "v2-1_768-ema-pruned.safetensors", "type": "checkpoint", "base": "SD2", "save_path": "checkpoints/SD2.1", "description": "Stable Diffusion 2 base model (768)", "reference": "https://huggingface.co/stabilityai/stable-diffusion-2-1", "filename": "v2-1_768-ema-pruned.safetensors", "url": "https://huggingface.co/stabilityai/stable-diffusion-2-1/resolve/main/v2-1_768-ema-pruned.safetensors", "size": "5.21GB"}, {"name": "AbyssOrangeMix2 (hard)", "type": "checkpoint", "base": "SD1.5", "save_path": "checkpoints/SD1.5", "description": "AbyssOrangeMix2 - hard version (anime style)", "reference": "https://huggingface.co/WarriorMama777/OrangeMixs", "filename": "AbyssOrangeMix2_hard.safetensors", "url": "https://huggingface.co/WarriorMama777/OrangeMixs/resolve/main/Models/AbyssOrangeMix2/AbyssOrangeMix2_hard.safetensors", "size": "5.57GB"}, {"name": "AbyssOrangeMix3 A1", "type": "checkpoint", "base": "SD1.5", "save_path": "checkpoints/SD1.5", "description": "AbyssOrangeMix3 - A1 (anime style)", "reference": "https://huggingface.co/WarriorMama777/OrangeMixs", "filename": "AOM3A1_orangemixs.safetensors", "url": "https://huggingface.co/WarriorMama777/OrangeMixs/resolve/main/Models/AbyssOrangeMix3/AOM3A1_orangemixs.safetensors", "size": "2.13GB"}, {"name": "AbyssOrangeMix3 A3", "type": "checkpoint", "base": "SD1.5", "save_path": "checkpoints/SD1.5", "description": "AbyssOrangeMix - A3 (anime style)", "reference": "https://huggingface.co/WarriorMama777/OrangeMixs", "filename": "AOM3A3_orangemixs.safetensors", "url": "https://huggingface.co/WarriorMama777/OrangeMixs/resolve/main/Models/AbyssOrangeMix3/AOM3A3_orangemixs.safetensors", "size": "2.13GB"}, {"name": "Waifu Diffusion 1.5 Beta3 (fp16)", "type": "checkpoint", "base": "SD2.1", "save_path": "checkpoints/SD2.1", "description": "Waifu Diffusion 1.5 Beta3", "reference": "https://huggingface.co/waifu-diffusion/wd-1-5-beta3", "filename": "wd-illusion-fp16.safetensors", "url": "https://huggingface.co/waifu-diffusion/wd-1-5-beta3/resolve/main/wd-illusion-fp16.safetensors", "size": "2.58GB"}, {"name": "illuminatiDiffusionV1_v11 unCLIP model", "type": "unclip", "base": "SD2.1", "save_path": "checkpoints/SD2.1", "description": "Mix model (SD2.1 unCLIP + illuminatiDiffusionV1_v11)", "reference": "https://huggingface.co/comfyanonymous/illuminatiDiffusionV1_v11_unCLIP", "filename": "illuminatiDiffusionV1_v11-unclip-h-fp16.safetensors", "url": "https://huggingface.co/comfyanonymous/illuminatiDiffusionV1_v11_unCLIP/resolve/main/illuminatiDiffusionV1_v11-unclip-h-fp16.safetensors", "size": "3.98GB"}, {"name": "Waifu Diffusion 1.5 unCLIP model", "type": "unclip", "base": "SD2.1", "save_path": "checkpoints/SD2.1", "description": "Mix model (SD2.1 unCLIP + Waifu Diffusion 1.5)", "reference": "https://huggingface.co/comfyanonymous/wd-1.5-beta2_unCLIP", "filename": "wd-1-5-beta2-aesthetic-unclip-h-fp16.safetensors", "url": "https://huggingface.co/comfyanonymous/wd-1.5-beta2_unCLIP/resolve/main/wd-1-5-beta2-aesthetic-unclip-h-fp16.safetensors", "size": "3.98GB"}, {"name": "sdxl_vae.safetensors", "type": "VAE", "base": "SDXL", "save_path": "vae/SDXL", "description": "SDXL-VAE", "reference": "https://huggingface.co/stabilityai/sdxl-vae", "filename": "sdxl_vae.safetensors", "url": "https://huggingface.co/stabilityai/sdxl-vae/resolve/main/sdxl_vae.safetensors", "size": "334.6MB"}, {"name": "vae-ft-mse-840000-ema-pruned", "type": "VAE", "base": "SD1.5", "save_path": "vae/SD1.5", "description": "vae-ft-mse-840000-ema-pruned", "reference": "https://huggingface.co/stabilityai/sd-vae-ft-mse-original", "filename": "vae-ft-mse-840000-ema-pruned.safetensors", "url": "https://huggingface.co/stabilityai/sd-vae-ft-mse-original/resolve/main/vae-ft-mse-840000-ema-pruned.safetensors", "size": "334.6MB"}, {"name": "orangemix.vae", "type": "VAE", "base": "SD1.5", "save_path": "vae/SD1.5", "description": "orangemix vae model", "reference": "https://huggingface.co/WarriorMama777/OrangeMixs", "filename": "orangemix.vae.pt", "url": "https://huggingface.co/WarriorMama777/OrangeMixs/resolve/main/VAEs/orangemix.vae.pt", "size": "822.8MB"}, {"name": "kl-f8-anime2", "type": "VAE", "base": "SD2.1", "save_path": "vae/SD2.1", "description": "kl-f8-anime2 vae model", "reference": "https://huggingface.co/hakurei/waifu-diffusion-v1-4", "filename": "kl-f8-anime2.ckpt", "url": "https://huggingface.co/hakurei/waifu-diffusion-v1-4/resolve/main/vae/kl-f8-anime2.ckpt", "size": "404.7MB"}, {"name": "OpenAI Consistency Decoder", "type": "VAE", "base": "SD1.5", "save_path": "vae/SD1.5/openai_consistency_decoder", "description": "OpenAI Consistency Decoder. Improved decoding for stable diffusion vaes.", "reference": "https://github.com/openai/consistencydecoder", "filename": "decoder.pt", "url": "https://openaipublic.azureedge.net/diff-vae/c9cebd3132dd9c42936d803e33424145a748843c8f716c0814838bdc8a2fe7cb/decoder.pt", "size": "2.49GB"}, {"name": "LCM LoRA SD1.5", "type": "lora", "base": "SD1.5", "save_path": "loras/SD1.5/lcm", "description": "Latent Consistency LoRA for SD1.5", "reference": "https://huggingface.co/latent-consistency/lcm-lora-sdv1-5", "filename": "pytorch_lora_weights.safetensors", "url": "https://huggingface.co/latent-consistency/lcm-lora-sdv1-5/resolve/main/pytorch_lora_weights.safetensors", "size": "134.6MB"}, {"name": "LCM LoRA SSD-1B", "type": "lora", "base": "SSD-1B", "save_path": "loras/SSD-1B/lcm", "description": "Latent Consistency LoRA for SSD-1B", "reference": "https://huggingface.co/latent-consistency/lcm-lora-ssd-1b", "filename": "pytorch_lora_weights.safetensors", "url": "https://huggingface.co/latent-consistency/lcm-lora-ssd-1b/resolve/main/pytorch_lora_weights.safetensors", "size": "210.0MB"}, {"name": "LCM LoRA SDXL", "type": "lora", "base": "SDXL", "save_path": "loras/SDXL/lcm", "description": "Latent Consistency LoRA for SDXL", "reference": "https://huggingface.co/latent-consistency/lcm-lora-sdxl", "filename": "pytorch_lora_weights.safetensors", "url": "https://huggingface.co/latent-consistency/lcm-lora-sdxl/resolve/main/pytorch_lora_weights.safetensors", "size": "393.9MB"}, {"name": "Segmind<PERSON>Vega", "type": "checkpoint", "base": "segmind-vega", "save_path": "checkpoints/segmind-vega", "description": "The Segmind-Vega Model is a distilled version of the Stable Diffusion XL (SDXL), offering a remarkable 70% reduction in size and an impressive 100% speedup while retaining high-quality text-to-image generation capabilities.", "reference": "https://huggingface.co/segmind/Segmind-<PERSON>", "filename": "segmind-vega.safetensors", "url": "https://huggingface.co/segmind/Segmind-Vega/resolve/main/segmind-vega.safetensors", "size": "3.29GB"}, {"name": "Segmind-VegaRT - Latent Consistency Model (LCM) LoRA of Segmind-Vega", "type": "lora", "base": "segmind-vega", "save_path": "loras/segmind-vega", "description": "Segmind-VegaRT a distilled consistency adapter for Segmind-Vega that allows to reduce the number of inference steps to only between 2 - 8 steps.", "reference": "https://huggingface.co/segmind/Segmind-VegaRT", "filename": "pytorch_lora_weights.safetensors", "url": "https://huggingface.co/segmind/Segmind-VegaRT/resolve/main/pytorch_lora_weights.safetensors", "size": "239.2MB"}, {"name": "Theovercomer8's Contrast Fix (SD2.1)", "type": "lora", "base": "SD2.1", "save_path": "loras/SD2.1", "description": "LORA: Theovercomer8's Contrast Fix (SD2.1)", "reference": "https://civitai.com/models/8765/theovercomer8s-contrast-fix-sd15sd21-768", "filename": "theovercomer8sContrastFix_sd21768.safetensors", "url": "https://civitai.com/api/download/models/10350", "size": "163MB"}, {"name": "Theovercomer8's Contrast Fix (SD1.5)", "type": "lora", "base": "SD1.5", "save_path": "loras/SD1.5", "description": "LORA: Theovercomer8's Contrast Fix (SD1.5)", "reference": "https://civitai.com/models/8765/theovercomer8s-contrast-fix-sd15sd21-768", "filename": "theovercomer8sContrastFix_sd15.safetensors", "url": "https://civitai.com/api/download/models/10638", "size": "113MB"}, {"name": "T2I-Adapter (depth)", "type": "T2I-Adapter", "base": "SD1.5", "save_path": "controlnet/SD1.5", "description": "ControlNet T2I-Adapter for depth", "reference": "https://huggingface.co/TencentARC/T2I-Adapter", "filename": "t2iadapter_depth_sd14v1.pth", "url": "https://huggingface.co/TencentARC/T2I-Adapter/resolve/main/models/t2iadapter_depth_sd14v1.pth", "size": "309.5MB"}, {"name": "T2I-Adapter (seg)", "type": "T2I-Adapter", "base": "SD1.5", "save_path": "controlnet/SD1.5", "description": "ControlNet T2I-Adapter for seg", "reference": "https://huggingface.co/TencentARC/T2I-Adapter", "filename": "t2iadapter_seg_sd14v1.pth", "url": "https://huggingface.co/TencentARC/T2I-Adapter/resolve/main/models/t2iadapter_seg_sd14v1.pth", "size": "309.5MB"}, {"name": "T2I-Adapter (sketch)", "type": "T2I-Adapter", "base": "SD1.5", "save_path": "controlnet/SD1.5", "description": "ControlNet T2I-Adapter for sketch", "reference": "https://huggingface.co/TencentARC/T2I-Adapter", "filename": "t2iadapter_sketch_sd14v1.pth", "url": "https://huggingface.co/TencentARC/T2I-Adapter/resolve/main/models/t2iadapter_sketch_sd14v1.pth", "size": "308.0MB"}, {"name": "T2I-Adapter (keypose)", "type": "T2I-Adapter", "base": "SD1.5", "save_path": "controlnet/SD1.5", "description": "ControlNet T2I-Adapter for keypose", "reference": "https://huggingface.co/TencentARC/T2I-Adapter", "filename": "t2iadapter_keypose_sd14v1.pth", "url": "https://huggingface.co/TencentARC/T2I-Adapter/resolve/main/models/t2iadapter_keypose_sd14v1.pth", "size": "309.5MB"}, {"name": "T2I-Adapter (openpose)", "type": "T2I-Adapter", "base": "SD1.5", "save_path": "controlnet/SD1.5", "description": "ControlNet T2I-Adapter for openpose", "reference": "https://huggingface.co/TencentARC/T2I-Adapter", "filename": "t2iadapter_openpose_sd14v1.pth", "url": "https://huggingface.co/TencentARC/T2I-Adapter/resolve/main/models/t2iadapter_openpose_sd14v1.pth", "size": "309.5MB"}, {"name": "T2I-Adapter (color)", "type": "T2I-Adapter", "base": "SD1.5", "save_path": "controlnet/SD1.5", "description": "ControlNet T2I-Adapter for color", "reference": "https://huggingface.co/TencentARC/T2I-Adapter", "filename": "t2iadapter_color_sd14v1.pth", "url": "https://huggingface.co/TencentARC/T2I-Adapter/resolve/main/models/t2iadapter_color_sd14v1.pth", "size": "74.8MB"}, {"name": "T2I-Adapter (canny)", "type": "T2I-Adapter", "base": "SD1.5", "save_path": "controlnet/SD1.5", "description": "ControlNet T2I-Adapter for canny", "reference": "https://huggingface.co/TencentARC/T2I-Adapter", "filename": "t2iadapter_canny_sd14v1.pth", "url": "https://huggingface.co/TencentARC/T2I-Adapter/resolve/main/models/t2iadapter_canny_sd14v1.pth", "size": "308.0MB"}, {"name": "T2I-Style model", "type": "T2I-Style", "base": "SD1.5", "save_path": "controlnet/SD1.5", "description": "ControlNet T2I-Adapter style model. Need to download CLIPVision model.", "reference": "https://huggingface.co/TencentARC/T2I-Adapter", "filename": "t2iadapter_style_sd14v1.pth", "url": "https://huggingface.co/TencentARC/T2I-Adapter/resolve/main/models/t2iadapter_style_sd14v1.pth", "size": "154.4MB"}, {"name": "T2I-Adapter XL (lineart) FP16", "type": "T2I-Adapter", "base": "SDXL", "save_path": "controlnet/SDXL", "description": "ControlNet T2I-Adapter XL for lineart", "reference": "https://huggingface.co/TencentARC/t2i-adapter-lineart-sdxl-1.0", "filename": "t2i-adapter-lineart-sdxl-1.0.fp16.safetensors", "url": "https://huggingface.co/TencentARC/t2i-adapter-lineart-sdxl-1.0/resolve/main/diffusion_pytorch_model.fp16.safetensors", "size": "158.1MB"}, {"name": "T2I-Adapter XL (canny) FP16", "type": "T2I-Adapter", "base": "SDXL", "save_path": "controlnet/SDXL", "description": "ControlNet T2I-Adapter XL for canny", "reference": "https://huggingface.co/TencentARC/t2i-adapter-canny-sdxl-1.0", "filename": "t2i-adapter-canny-sdxl-1.0.fp16.safetensors", "url": "https://huggingface.co/TencentARC/t2i-adapter-canny-sdxl-1.0/resolve/main/diffusion_pytorch_model.fp16.safetensors", "size": "158.1MB"}, {"name": "T2I-Adapter XL (depth-zoe) FP16", "type": "T2I-Adapter", "base": "SDXL", "save_path": "controlnet/SDXL", "description": "ControlNet T2I-Adapter XL for depth-zoe", "reference": "https://huggingface.co/TencentARC/t2i-adapter-depth-zoe-sdxl-1.0", "filename": "t2i-adapter-depth-zoe-sdxl-1.0.fp16.safetensors", "url": "https://huggingface.co/TencentARC/t2i-adapter-depth-zoe-sdxl-1.0/resolve/main/diffusion_pytorch_model.fp16.safetensors", "size": "158.1MB"}, {"name": "T2I-Adapter XL (depth-midas) FP16", "type": "T2I-Adapter", "base": "SDXL", "save_path": "controlnet/SDXL", "description": "ControlNet T2I-Adapter XL for depth-midas", "reference": "https://huggingface.co/TencentARC/t2i-adapter-depth-midas-sdxl-1.0", "filename": "t2i-adapter-depth-midas-sdxl-1.0.fp16.safetensors", "url": "https://huggingface.co/TencentARC/t2i-adapter-depth-midas-sdxl-1.0/resolve/main/diffusion_pytorch_model.fp16.safetensors", "size": "158.1MB"}, {"name": "T2I-Adapter XL (sketch) FP16", "type": "T2I-Adapter", "base": "SDXL", "save_path": "controlnet/SDXL", "description": "ControlNet T2I-Adapter XL for sketch", "reference": "https://huggingface.co/TencentARC/t2i-adapter-sketch-sdxl-1.0", "filename": "t2i-adapter-sketch-sdxl-1.0.fp16.safetensors", "url": "https://huggingface.co/TencentARC/t2i-adapter-sketch-sdxl-1.0/resolve/main/diffusion_pytorch_model.fp16.safetensors", "size": "158.1MB"}, {"name": "T2I-Adapter XL (lineart)", "type": "T2I-Adapter", "base": "SDXL", "save_path": "controlnet/SDXL", "description": "ControlNet T2I-Adapter XL for lineart", "reference": "https://huggingface.co/TencentARC/t2i-adapter-lineart-sdxl-1.0", "filename": "t2i-adapter-lineart-sdxl-1.0.safetensors", "url": "https://huggingface.co/TencentARC/t2i-adapter-lineart-sdxl-1.0/resolve/main/diffusion_pytorch_model.safetensors", "size": "316.1MB"}, {"name": "T2I-Adapter XL (canny)", "type": "T2I-Adapter", "base": "SDXL", "save_path": "controlnet/SDXL", "description": "ControlNet T2I-Adapter XL for canny", "reference": "https://huggingface.co/TencentARC/t2i-adapter-canny-sdxl-1.0", "filename": "t2i-adapter-canny-sdxl-1.0.safetensors", "url": "https://huggingface.co/TencentARC/t2i-adapter-canny-sdxl-1.0/resolve/main/diffusion_pytorch_model.safetensors", "size": "316.1MB"}, {"name": "T2I-Adapter XL (depth-zoe)", "type": "T2I-Adapter", "base": "SDXL", "save_path": "controlnet/SDXL", "description": "ControlNet T2I-Adapter XL for depth-zoe", "reference": "https://huggingface.co/TencentARC/t2i-adapter-depth-zoe-sdxl-1.0", "filename": "t2i-adapter-depth-zoe-sdxl-1.0.safetensors", "url": "https://huggingface.co/TencentARC/t2i-adapter-depth-zoe-sdxl-1.0/resolve/main/diffusion_pytorch_model.safetensors", "size": "316.1MB"}, {"name": "T2I-Adapter XL (depth-midas)", "type": "T2I-Adapter", "base": "SDXL", "save_path": "controlnet/SDXL", "description": "ControlNet T2I-Adapter XL for depth-midas", "reference": "https://huggingface.co/TencentARC/t2i-adapter-depth-midas-sdxl-1.0", "filename": "t2i-adapter-depth-midas-sdxl-1.0.safetensors", "url": "https://huggingface.co/TencentARC/t2i-adapter-depth-midas-sdxl-1.0/resolve/main/diffusion_pytorch_model.safetensors", "size": "316.1MB"}, {"name": "T2I-Adapter XL (sketch)", "type": "T2I-Adapter", "base": "SDXL", "save_path": "controlnet/SDXL", "description": "ControlNet T2I-Adapter XL for sketch", "reference": "https://huggingface.co/TencentARC/t2i-adapter-sketch-sdxl-1.0", "filename": "t2i-adapter-sketch-sdxl-1.0.safetensors", "url": "https://huggingface.co/TencentARC/t2i-adapter-sketch-sdxl-1.0/resolve/main/diffusion_pytorch_model.safetensors", "size": "316.1MB"}, {"name": "T2I-Adapter XL (openpose)", "type": "T2I-Adapter", "base": "SDXL", "save_path": "controlnet/SDXL", "description": "ControlNet T2I-Adapter XL for openpose", "reference": "https://huggingface.co/TencentARC/t2i-adapter-openpose-sdxl-1.0", "filename": "t2i-adapter-openpose-sdxl-1.0.safetensors", "url": "https://huggingface.co/TencentARC/t2i-adapter-openpose-sdxl-1.0/resolve/main/diffusion_pytorch_model.safetensors", "size": "316.1MB"}, {"name": "CiaraRowles/TemporalNet2", "type": "controlnet", "base": "SD1.5", "save_path": "controlnet/SD1.5", "description": "TemporalNet was a ControlNet model designed to enhance the temporal consistency of generated outputs", "reference": "https://huggingface.co/CiaraRowles/TemporalNet2", "filename": "temporalnetversion2.safetensors", "url": "https://huggingface.co/CiaraRowles/TemporalNet2/resolve/main/temporalnetversion2.safetensors", "size": "5.71GB"}, {"name": "CiaraRowles/TemporalNet1XL (1.0)", "type": "controlnet", "base": "SDXL", "save_path": "controlnet/SDXL/TemporalNet1XL", "description": "This is TemporalNet1XL, it is a re-train of the controlnet TemporalNet1 with Stable Diffusion XL.", "reference": "https://huggingface.co/CiaraRowles/controlnet-temporalnet-sdxl-1.0", "filename": "diffusion_pytorch_model.safetensors", "url": "https://huggingface.co/CiaraRowles/controlnet-temporalnet-sdxl-1.0/resolve/main/diffusion_pytorch_model.safetensors", "size": "5.00GB"}, {"name": "Comfy-Org/sigclip_vision_384 (patch14_384)", "type": "clip_vision", "base": "sigclip", "save_path": "clip_vision", "description": "This clip vision model is required for FLUX.1 Redux.", "reference": "https://huggingface.co/Comfy-Org/sigclip_vision_384/tree/main", "filename": "sigclip_vision_patch14_384.safetensors", "url": "https://huggingface.co/Comfy-Org/sigclip_vision_384/resolve/main/sigclip_vision_patch14_384.safetensors", "size": "857MB"}, {"name": "CLIPVision model (stabilityai/clip_vision_g)", "type": "clip_vision", "base": "ViT-G", "save_path": "clip_vision", "description": "clip_g vision model", "reference": "https://huggingface.co/stabilityai/control-lora", "filename": "clip_vision_g.safetensors", "url": "https://huggingface.co/stabilityai/control-lora/resolve/main/revision/clip_vision_g.safetensors", "size": "3.69GB"}, {"name": "CLIPVision model (openai/clip-vit-large)", "type": "clip_vision", "base": "ViT-L", "save_path": "clip_vision", "description": "CLIPVision model (needed for styles model)", "reference": "https://huggingface.co/openai/clip-vit-large-patch14", "filename": "clip-vit-large-patch14.safetensors", "url": "https://huggingface.co/openai/clip-vit-large-patch14/resolve/main/model.safetensors", "size": "1.71GB"}, {"name": "CLIPVision model (Kwai-Kolors/Kolors-IP-Adapter-Plus/clip-vit-large)", "type": "clip_vision", "base": "ViT-L", "save_path": "clip_vision", "description": "CLIPVision model (needed for IP-Adapter)", "reference": "https://huggingface.co/K<PERSON>-Kolors/Kolors-IP-Adapter-Plus", "filename": "clip-vit-large-patch14-336.bin", "url": "https://huggingface.co/<PERSON><PERSON>-Kolors/Kolors-IP-Adapter-Plus/resolve/main/image_encoder/pytorch_model.bin", "size": "1.71GB"}, {"name": "CLIPVision model (IP-Adapter) CLIP-ViT-H-14-laion2B-s32B-b79K", "type": "clip_vision", "base": "ViT-H", "save_path": "clip_vision", "description": "CLIPVision model (needed for IP-Adapter)", "reference": "https://huggingface.co/h94/IP-Adapter", "filename": "CLIP-ViT-H-14-laion2B-s32B-b79K.safetensors", "url": "https://huggingface.co/h94/IP-Adapter/resolve/main/models/image_encoder/model.safetensors", "size": "2.53GB"}, {"name": "CLIPVision model (IP-Adapter) CLIP-ViT-bigG-14-laion2B-39B-b160k", "type": "clip_vision", "base": "ViT-G", "save_path": "clip_vision", "description": "CLIPVision model (needed for IP-Adapter)", "reference": "https://huggingface.co/h94/IP-Adapter", "filename": "CLIP-ViT-bigG-14-laion2B-39B-b160k.safetensors", "url": "https://huggingface.co/h94/IP-Adapter/resolve/main/sdxl_models/image_encoder/model.safetensors", "size": "3.69GB"}, {"name": "stabilityai/control-lora-canny-rank128.safetensors", "type": "controlnet", "base": "SDXL", "save_path": "controlnet/SDXL", "description": "Control-LoRA: canny rank128", "reference": "https://huggingface.co/stabilityai/control-lora", "filename": "control-lora-canny-rank128.safetensors", "url": "https://huggingface.co/stabilityai/control-lora/resolve/main/control-LoRAs-rank128/control-lora-canny-rank128.safetensors", "size": "395.7MB"}, {"name": "stabilityai/control-lora-depth-rank128.safetensors", "type": "controlnet", "base": "SDXL", "save_path": "controlnet/SDXL", "description": "Control-LoRA: depth rank128", "reference": "https://huggingface.co/stabilityai/control-lora", "filename": "control-lora-depth-rank128.safetensors", "url": "https://huggingface.co/stabilityai/control-lora/resolve/main/control-LoRAs-rank128/control-lora-depth-rank128.safetensors", "size": "395.7MB"}, {"name": "stabilityai/control-lora-recolor-rank128.safetensors", "type": "controlnet", "base": "SDXL", "save_path": "controlnet/SDXL", "description": "Control-LoRA: recolor rank128", "reference": "https://huggingface.co/stabilityai/control-lora", "filename": "control-lora-recolor-rank128.safetensors", "url": "https://huggingface.co/stabilityai/control-lora/resolve/main/control-LoRAs-rank128/control-lora-recolor-rank128.safetensors", "size": "395.7MB"}, {"name": "stabilityai/control-lora-sketch-rank128-metadata.safetensors", "type": "controlnet", "base": "SDXL", "save_path": "controlnet/SDXL", "description": "Control-LoRA: sketch rank128 metadata", "reference": "https://huggingface.co/stabilityai/control-lora", "filename": "control-lora-sketch-rank128-metadata.safetensors", "url": "https://huggingface.co/stabilityai/control-lora/resolve/main/control-LoRAs-rank128/control-lora-sketch-rank128-metadata.safetensors", "size": "395.7MB"}, {"name": "stabilityai/control-lora-canny-rank256.safetensors", "type": "controlnet", "base": "SDXL", "save_path": "controlnet/SDXL", "description": "Control-LoRA: canny rank256", "reference": "https://huggingface.co/stabilityai/control-lora", "filename": "control-lora-canny-rank256.safetensors", "url": "https://huggingface.co/stabilityai/control-lora/resolve/main/control-LoRAs-rank256/control-lora-canny-rank256.safetensors", "size": "774.5MB"}, {"name": "stabilityai/control-lora-depth-rank256.safetensors", "type": "controlnet", "base": "SDXL", "save_path": "controlnet/SDXL", "description": "Control-LoRA: depth rank256", "reference": "https://huggingface.co/stabilityai/control-lora", "filename": "control-lora-depth-rank256.safetensors", "url": "https://huggingface.co/stabilityai/control-lora/resolve/main/control-LoRAs-rank256/control-lora-depth-rank256.safetensors", "size": "774.4MB"}, {"name": "stabilityai/control-lora-recolor-rank256.safetensors", "type": "controlnet", "base": "SDXL", "save_path": "controlnet/SDXL", "description": "Control-LoRA: recolor rank256", "reference": "https://huggingface.co/stabilityai/control-lora", "filename": "control-lora-recolor-rank256.safetensors", "url": "https://huggingface.co/stabilityai/control-lora/resolve/main/control-LoRAs-rank256/control-lora-recolor-rank256.safetensors", "size": "774.4MB"}, {"name": "stabilityai/control-lora-sketch-rank256.safetensors", "type": "controlnet", "base": "SDXL", "save_path": "controlnet/SDXL", "description": "Control-LoRA: sketch rank256", "reference": "https://huggingface.co/stabilityai/control-lora", "filename": "control-lora-sketch-rank256.safetensors", "url": "https://huggingface.co/stabilityai/control-lora/resolve/main/control-LoRAs-rank256/control-lora-sketch-rank256.safetensors", "size": "774.5MB"}, {"name": "SDXL-controlnet: OpenPose (v2)", "type": "controlnet", "base": "SDXL", "save_path": "controlnet/SDXL", "description": "ControlNet openpose model for SDXL", "reference": "https://huggingface.co/thibaud/controlnet-openpose-sdxl-1.0", "filename": "OpenPoseXL2.safetensors", "url": "https://huggingface.co/thibaud/controlnet-openpose-sdxl-1.0/resolve/main/OpenPoseXL2.safetensors", "size": "5.00GB"}, {"name": "controlnet-SargeZT/controlnet-sd-xl-1.0-softedge-dexined", "type": "controlnet", "base": "SDXL", "save_path": "controlnet/SDXL", "description": "ControlNet softedge model for SDXL", "reference": "https://huggingface.co/SargeZT/controlnet-sd-xl-1.0-softedge-dexined", "filename": "controlnet-sd-xl-1.0-softedge-dexined.safetensors", "url": "https://huggingface.co/SargeZT/controlnet-sd-xl-1.0-softedge-dexined/resolve/main/controlnet-sd-xl-1.0-softedge-dexined.safetensors", "size": "5.00GB"}, {"name": "controlnet-SargeZT/controlnet-sd-xl-1.0-depth-16bit-zoe", "type": "controlnet", "base": "SDXL", "save_path": "controlnet/SDXL", "description": "ControlNet depth-zoe model for SDXL", "reference": "https://huggingface.co/SargeZT/controlnet-sd-xl-1.0-depth-16bit-zoe", "filename": "depth-zoe-xl-v1.0-controlnet.safetensors", "url": "https://huggingface.co/SargeZT/controlnet-sd-xl-1.0-depth-16bit-zoe/resolve/main/depth-zoe-xl-v1.0-controlnet.safetensors", "size": "5.00GB"}, {"name": "ControlNet-v1-1 (ip2p; fp16)", "type": "controlnet", "base": "SD1.5", "save_path": "controlnet/1.5", "description": "Safetensors/FP16 versions of the new ControlNet-v1-1 checkpoints (ip2p)", "reference": "https://huggingface.co/comfyanonymous/ControlNet-v1-1_fp16_safetensors", "filename": "control_v11e_sd15_ip2p_fp16.safetensors", "url": "https://huggingface.co/comfyanonymous/ControlNet-v1-1_fp16_safetensors/resolve/main/control_v11e_sd15_ip2p_fp16.safetensors", "size": "722.6MB"}, {"name": "ControlNet-v1-1 (shuffle; fp16)", "type": "controlnet", "base": "SD1.5", "save_path": "controlnet/1.5", "description": "Safetensors/FP16 versions of the new ControlNet-v1-1 checkpoints (shuffle)", "reference": "https://huggingface.co/comfyanonymous/ControlNet-v1-1_fp16_safetensors", "filename": "control_v11e_sd15_shuffle_fp16.safetensors", "url": "https://huggingface.co/comfyanonymous/ControlNet-v1-1_fp16_safetensors/resolve/main/control_v11e_sd15_shuffle_fp16.safetensors", "size": "722.6MB"}, {"name": "ControlNet-v1-1 (canny; fp16)", "type": "controlnet", "base": "SD1.5", "save_path": "controlnet/1.5", "description": "Safetensors/FP16 versions of the new ControlNet-v1-1 checkpoints (canny)", "reference": "https://huggingface.co/comfyanonymous/ControlNet-v1-1_fp16_safetensors", "filename": "control_v11p_sd15_canny_fp16.safetensors", "url": "https://huggingface.co/comfyanonymous/ControlNet-v1-1_fp16_safetensors/resolve/main/control_v11p_sd15_canny_fp16.safetensors", "size": "722.6MB"}, {"name": "ControlNet-v1-1 (depth; fp16)", "type": "controlnet", "base": "SD1.5", "save_path": "controlnet/1.5", "description": "Safetensors/FP16 versions of the new ControlNet-v1-1 checkpoints (depth)", "reference": "https://huggingface.co/comfyanonymous/ControlNet-v1-1_fp16_safetensors", "filename": "control_v11f1p_sd15_depth_fp16.safetensors", "url": "https://huggingface.co/comfyanonymous/ControlNet-v1-1_fp16_safetensors/resolve/main/control_v11f1p_sd15_depth_fp16.safetensors", "size": "722.6MB"}, {"name": "ControlNet-v1-1 (inpaint; fp16)", "type": "controlnet", "base": "SD1.5", "save_path": "controlnet/1.5", "description": "Safetensors/FP16 versions of the new ControlNet-v1-1 checkpoints (inpaint)", "reference": "https://huggingface.co/comfyanonymous/ControlNet-v1-1_fp16_safetensors", "filename": "control_v11p_sd15_inpaint_fp16.safetensors", "url": "https://huggingface.co/comfyanonymous/ControlNet-v1-1_fp16_safetensors/resolve/main/control_v11p_sd15_inpaint_fp16.safetensors", "size": "722.6MB"}, {"name": "ControlNet-v1-1 (lineart; fp16)", "type": "controlnet", "base": "SD1.5", "save_path": "controlnet/1.5", "description": "Safetensors/FP16 versions of the new ControlNet-v1-1 checkpoints (lineart)", "reference": "https://huggingface.co/comfyanonymous/ControlNet-v1-1_fp16_safetensors", "filename": "control_v11p_sd15_lineart_fp16.safetensors", "url": "https://huggingface.co/comfyanonymous/ControlNet-v1-1_fp16_safetensors/resolve/main/control_v11p_sd15_lineart_fp16.safetensors", "size": "722.6MB"}, {"name": "ControlNet-v1-1 (mlsd; fp16)", "type": "controlnet", "base": "SD1.5", "save_path": "controlnet/1.5", "description": "Safetensors/FP16 versions of the new ControlNet-v1-1 checkpoints (mlsd)", "reference": "https://huggingface.co/comfyanonymous/ControlNet-v1-1_fp16_safetensors", "filename": "control_v11p_sd15_mlsd_fp16.safetensors", "url": "https://huggingface.co/comfyanonymous/ControlNet-v1-1_fp16_safetensors/resolve/main/control_v11p_sd15_mlsd_fp16.safetensors", "size": "722.6MB"}, {"name": "ControlNet-v1-1 (normalbae; fp16)", "type": "controlnet", "base": "SD1.5", "save_path": "controlnet/1.5", "description": "Safetensors/FP16 versions of the new ControlNet-v1-1 checkpoints (normalbae)", "reference": "https://huggingface.co/comfyanonymous/ControlNet-v1-1_fp16_safetensors", "filename": "control_v11p_sd15_normalbae_fp16.safetensors", "url": "https://huggingface.co/comfyanonymous/ControlNet-v1-1_fp16_safetensors/resolve/main/control_v11p_sd15_normalbae_fp16.safetensors", "size": "722.6MB"}, {"name": "ControlNet-v1-1 (openpose; fp16)", "type": "controlnet", "base": "SD1.5", "save_path": "controlnet/1.5", "description": "Safetensors/FP16 versions of the new ControlNet-v1-1 checkpoints (openpose)", "reference": "https://huggingface.co/comfyanonymous/ControlNet-v1-1_fp16_safetensors", "filename": "control_v11p_sd15_openpose_fp16.safetensors", "url": "https://huggingface.co/comfyanonymous/ControlNet-v1-1_fp16_safetensors/resolve/main/control_v11p_sd15_openpose_fp16.safetensors", "size": "722.6MB"}, {"name": "ControlNet-v1-1 (scribble; fp16)", "type": "controlnet", "base": "SD1.5", "save_path": "controlnet/1.5", "description": "Safetensors/FP16 versions of the new ControlNet-v1-1 checkpoints (scribble)", "reference": "https://huggingface.co/comfyanonymous/ControlNet-v1-1_fp16_safetensors", "filename": "control_v11p_sd15_scribble_fp16.safetensors", "url": "https://huggingface.co/comfyanonymous/ControlNet-v1-1_fp16_safetensors/resolve/main/control_v11p_sd15_scribble_fp16.safetensors", "size": "722.6MB"}, {"name": "ControlNet-v1-1 (seg; fp16)", "type": "controlnet", "base": "SD1.5", "save_path": "controlnet/1.5", "description": "Safetensors/FP16 versions of the new ControlNet-v1-1 checkpoints (seg)", "reference": "https://huggingface.co/comfyanonymous/ControlNet-v1-1_fp16_safetensors", "filename": "control_v11p_sd15_seg_fp16.safetensors", "url": "https://huggingface.co/comfyanonymous/ControlNet-v1-1_fp16_safetensors/resolve/main/control_v11p_sd15_seg_fp16.safetensors", "size": "722.6MB"}, {"name": "ControlNet-v1-1 (softedge; fp16)", "type": "controlnet", "base": "SD1.5", "save_path": "controlnet/1.5", "description": "Safetensors/FP16 versions of the new ControlNet-v1-1 checkpoints (softedge)", "reference": "https://huggingface.co/comfyanonymous/ControlNet-v1-1_fp16_safetensors", "filename": "control_v11p_sd15_softedge_fp16.safetensors", "url": "https://huggingface.co/comfyanonymous/ControlNet-v1-1_fp16_safetensors/resolve/main/control_v11p_sd15_softedge_fp16.safetensors", "size": "722.6MB"}, {"name": "ControlNet-v1-1 (anime; fp16)", "type": "controlnet", "base": "SD1.5", "save_path": "controlnet/1.5", "description": "Safetensors/FP16 versions of the new ControlNet-v1-1 checkpoints (anime)", "reference": "https://huggingface.co/comfyanonymous/ControlNet-v1-1_fp16_safetensors", "filename": "control_v11p_sd15s2_lineart_anime_fp16.safetensors", "url": "https://huggingface.co/comfyanonymous/ControlNet-v1-1_fp16_safetensors/resolve/main/control_v11p_sd15s2_lineart_anime_fp16.safetensors", "size": "722.6MB"}, {"name": "ControlNet-v1-1 (tile; fp16; v11u)", "type": "controlnet", "base": "SD1.5", "save_path": "controlnet/1.5", "description": "Safetensors/FP16 versions of the new ControlNet-v1-1 checkpoints (tile) / v11u", "reference": "https://huggingface.co/comfyanonymous/ControlNet-v1-1_fp16_safetensors", "filename": "control_v11u_sd15_tile_fp16.safetensors", "url": "https://huggingface.co/comfyanonymous/ControlNet-v1-1_fp16_safetensors/resolve/main/control_v11u_sd15_tile_fp16.safetensors", "size": "722.6MB"}, {"name": "ControlNet-v1-1 (tile; fp16; v11f1e)", "type": "controlnet", "base": "SD1.5", "save_path": "controlnet/1.5", "description": "Safetensors/FP16 versions of the new ControlNet-v1-1 checkpoints (tile) / v11f1e\nYou need to this model for <B>Tiled Resample</B>", "reference": "https://huggingface.co/comfyanonymous/ControlNet-v1-1_fp16_safetensors", "filename": "control_v11f1e_sd15_tile_fp16.safetensors", "url": "https://huggingface.co/comfyanonymous/ControlNet-v1-1_fp16_safetensors/resolve/main/control_v11f1e_sd15_tile_fp16.safetensors", "size": "722.6MB"}, {"name": "ControlNet-HandRefiner-pruned (inpaint-depth-hand; fp16)", "type": "controlnet", "base": "SD1.5", "save_path": "controlnet/1.5", "description": "This inpaint-depth controlnet model is specialized for the hand refiner.", "reference": "https://huggingface.co/hr16/ControlNet-HandRefiner-pruned", "filename": "control_sd15_inpaint_depth_hand_fp16.safetensors", "url": "https://huggingface.co/hr16/ControlNet-HandRefiner-pruned/resolve/main/control_sd15_inpaint_depth_hand_fp16.safetensors", "size": "722.6MB"}, {"name": "control_boxdepth_LooseControlfp16 (fp16)", "type": "controlnet", "base": "SD1.5", "save_path": "controlnet/1.5", "description": "Loose ControlNet model", "reference": "https://huggingface.co/ioclab/LooseControl_WebUICombine", "filename": "control_boxdepth_LooseControlfp16.safetensors", "url": "https://huggingface.co/ioclab/LooseControl_WebUICombine/resolve/main/control_boxdepth_LooseControlfp16.safetensors", "size": "722.6MB"}, {"name": "GLIGEN textbox (fp16; pruned)", "type": "gligen", "base": "SD1.5", "save_path": "gligen/SD1.5", "description": "GLIGEN textbox model", "reference": "https://huggingface.co/comfyanonymous/GL<PERSON><PERSON>_pruned_safetensors", "filename": "gligen_sd14_textbox_pruned_fp16.safetensors", "url": "https://huggingface.co/comfyanonymous/G<PERSON><PERSON><PERSON>_pruned_safetensors/resolve/main/gligen_sd14_textbox_pruned_fp16.safetensors", "size": "418.2MB"}, {"name": "ViT-H SAM model", "type": "sam", "base": "SAM", "save_path": "sams", "description": "Segmenty Anything SAM model (ViT-H)", "reference": "https://github.com/facebookresearch/segment-anything#model-checkpoints", "filename": "sam_vit_h_4b8939.pth", "url": "https://dl.fbaipublicfiles.com/segment_anything/sam_vit_h_4b8939.pth", "size": "2.56GB"}, {"name": "ViT-L SAM model", "type": "sam", "base": "SAM", "save_path": "sams", "description": "Segmenty Anything SAM model (ViT-L)", "reference": "https://github.com/facebookresearch/segment-anything#model-checkpoints", "filename": "sam_vit_l_0b3195.pth", "url": "https://dl.fbaipublicfiles.com/segment_anything/sam_vit_l_0b3195.pth", "size": "1.25GB"}, {"name": "ViT-B SAM model", "type": "sam", "base": "SAM", "save_path": "sams", "description": "Segmenty Anything SAM model (ViT-B)", "reference": "https://github.com/facebookresearch/segment-anything#model-checkpoints", "filename": "sam_vit_b_01ec64.pth", "url": "https://dl.fbaipublicfiles.com/segment_anything/sam_vit_b_01ec64.pth", "size": "375.0MB"}, {"name": "seecoder v1.0", "type": "seecoder", "base": "SEECODER", "save_path": "seecoders", "description": "SeeCoder model", "reference": "https://huggingface.co/shi-labs/prompt-free-diffusion/tree/main/pretrained/pfd/seecoder", "filename": "seecoder-v1-0.safetensors", "url": "https://huggingface.co/shi-labs/prompt-free-diffusion/resolve/main/pretrained/pfd/seecoder/seecoder-v1-0.safetensors", "size": "1.18GB"}, {"name": "seecoder pa v1.0", "type": "seecoder", "base": "SEECODER", "save_path": "seecoders", "description": "SeeCoder model", "reference": "https://huggingface.co/shi-labs/prompt-free-diffusion/tree/main/pretrained/pfd/seecoder", "filename": "seecoder-pa-v1-0.safetensors", "url": "https://huggingface.co/shi-labs/prompt-free-diffusion/resolve/main/pretrained/pfd/seecoder/seecoder-pa-v1-0.safetensors", "size": "1.19GB"}, {"name": "seecoder anime v1.0", "type": "seecoder", "base": "SEECODER", "save_path": "seecoders", "description": "SeeCoder model", "reference": "https://huggingface.co/shi-labs/prompt-free-diffusion/tree/main/pretrained/pfd/seecoder", "filename": "seecoder-anime-v1-0.safetensors", "url": "https://huggingface.co/shi-labs/prompt-free-diffusion/resolve/main/pretrained/pfd/seecoder/seecoder-anime-v1-0.safetensors", "size": "1.18GB"}, {"name": "face_yolov8m (bbox)", "type": "Ultralytics", "base": "Ultralytics", "save_path": "ultralytics/bbox", "description": "These are the available models in the UltralyticsDetectorProvider of Impact Pack.", "reference": "https://huggingface.co/Bingsu/adetailer/tree/main", "filename": "face_yolov8m.pt", "url": "https://huggingface.co/Bingsu/adetailer/resolve/main/face_yolov8m.pt", "size": "52.0MB"}, {"name": "face_yolov8n (bbox)", "type": "Ultralytics", "base": "Ultralytics", "save_path": "ultralytics/bbox", "description": "These are the available models in the UltralyticsDetectorProvider of Impact Pack.", "reference": "https://huggingface.co/Bingsu/adetailer/tree/main", "filename": "face_yolov8n.pt", "url": "https://huggingface.co/Bingsu/adetailer/resolve/main/face_yolov8n.pt", "size": "6.23MB"}, {"name": "face_yolov8n_v2 (bbox)", "type": "Ultralytics", "base": "Ultralytics", "save_path": "ultralytics/bbox", "description": "These are the available models in the UltralyticsDetectorProvider of Impact Pack.", "reference": "https://huggingface.co/Bingsu/adetailer/tree/main", "filename": "face_yolov8n_v2.pt", "url": "https://huggingface.co/Bingsu/adetailer/resolve/main/face_yolov8n_v2.pt", "size": "6.24MB"}, {"name": "face_yolov8s (bbox)", "type": "Ultralytics", "base": "Ultralytics", "save_path": "ultralytics/bbox", "description": "These are the available models in the UltralyticsDetectorProvider of Impact Pack.", "reference": "https://huggingface.co/Bingsu/adetailer/tree/main", "filename": "face_yolov8s.pt", "url": "https://huggingface.co/Bingsu/adetailer/resolve/main/face_yolov8s.pt", "size": "22.5MB"}, {"name": "hand_yolov8n (bbox)", "type": "Ultralytics", "base": "Ultralytics", "save_path": "ultralytics/bbox", "description": "These are the available models in the UltralyticsDetectorProvider of Impact Pack.", "reference": "https://huggingface.co/Bingsu/adetailer/tree/main", "filename": "hand_yolov8n.pt", "url": "https://huggingface.co/Bingsu/adetailer/resolve/main/hand_yolov8n.pt", "size": "6.24MB"}, {"name": "hand_yolov8s (bbox)", "type": "Ultralytics", "base": "Ultralytics", "save_path": "ultralytics/bbox", "description": "These are the available models in the UltralyticsDetectorProvider of Impact Pack.", "reference": "https://huggingface.co/Bingsu/adetailer/tree/main", "filename": "hand_yolov8s.pt", "url": "https://huggingface.co/Bingsu/adetailer/resolve/main/hand_yolov8s.pt", "size": "22.5MB"}, {"name": "person_yolov8m (segm)", "type": "Ultralytics", "base": "Ultralytics", "save_path": "ultralytics/segm", "description": "These are the available models in the UltralyticsDetectorProvider of Impact Pack.", "reference": "https://huggingface.co/Bingsu/adetailer/tree/main", "filename": "person_yolov8m-seg.pt", "url": "https://huggingface.co/Bingsu/adetailer/resolve/main/person_yolov8m-seg.pt", "size": "54.8MB"}, {"name": "person_yolov8n (segm)", "type": "Ultralytics", "base": "Ultralytics", "save_path": "ultralytics/segm", "description": "These are the available models in the UltralyticsDetectorProvider of Impact Pack.", "reference": "https://huggingface.co/Bingsu/adetailer/tree/main", "filename": "person_yolov8n-seg.pt", "url": "https://huggingface.co/Bingsu/adetailer/resolve/main/person_yolov8n-seg.pt", "size": "6.78MB"}, {"name": "person_yolov8s (segm)", "type": "Ultralytics", "base": "Ultralytics", "save_path": "ultralytics/segm", "description": "These are the available models in the UltralyticsDetectorProvider of Impact Pack.", "reference": "https://huggingface.co/Bingsu/adetailer/tree/main", "filename": "person_yolov8s-seg.pt", "url": "https://huggingface.co/Bingsu/adetailer/resolve/main/person_yolov8s-seg.pt", "size": "23.9MB"}, {"name": "deepfashion2_yolov8s (segm)", "type": "Ultralytics", "base": "Ultralytics", "save_path": "ultralytics/segm", "description": "These are the available models in the UltralyticsDetectorProvider of Impact Pack.", "reference": "https://huggingface.co/Bingsu/adetailer/tree/main", "filename": "deepfashion2_yolov8s-seg.pt", "url": "https://huggingface.co/Bingsu/adetailer/resolve/main/deepfashion2_yolov8s-seg.pt", "size": "23.9MB"}, {"name": "face_yolov8m-seg_60.pt (segm)", "type": "Ultralytics", "base": "Ultralytics", "save_path": "ultralytics/segm", "description": "These are the available models in the UltralyticsDetectorProvider of Impact Pack.", "reference": "https://github.com/hben35096/assets/releases/tag/yolo8", "filename": "face_yolov8m-seg_60.pt", "url": "https://github.com/hben35096/assets/releases/download/yolo8/face_yolov8m-seg_60.pt", "size": "54.8MB"}, {"name": "face_yolov8n-seg2_60.pt (segm)", "type": "Ultralytics", "base": "Ultralytics", "save_path": "ultralytics/segm", "description": "These are the available models in the UltralyticsDetectorProvider of Impact Pack.", "reference": "https://github.com/hben35096/assets/releases/tag/yolo8", "filename": "face_yolov8n-seg2_60.pt", "url": "https://github.com/hben35096/assets/releases/download/yolo8/face_yolov8n-seg2_60.pt", "size": "6.77MB"}, {"name": "hair_yolov8n-seg_60.pt (segm)", "type": "Ultralytics", "base": "Ultralytics", "save_path": "ultralytics/segm", "description": "These are the available models in the UltralyticsDetectorProvider of Impact Pack.", "reference": "https://github.com/hben35096/assets/releases/tag/yolo8", "filename": "hair_yolov8n-seg_60.pt", "url": "https://github.com/hben35096/assets/releases/download/yolo8/hair_yolov8n-seg_60.pt", "size": "6.77MB"}, {"name": "skin_yolov8m-seg_400.pt (segm)", "type": "Ultralytics", "base": "Ultralytics", "save_path": "ultralytics/segm", "description": "These are the available models in the UltralyticsDetectorProvider of Impact Pack.", "reference": "https://github.com/hben35096/assets/releases/tag/yolo8", "filename": "skin_yolov8m-seg_400.pt", "url": "https://github.com/hben35096/assets/releases/download/yolo8/skin_yolov8m-seg_400.pt", "size": "54.9MB"}, {"name": "skin_yolov8n-seg_400.pt (segm)", "type": "Ultralytics", "base": "Ultralytics", "save_path": "ultralytics/segm", "description": "These are the available models in the UltralyticsDetectorProvider of Impact Pack.", "reference": "https://github.com/hben35096/assets/releases/tag/yolo8", "filename": "skin_yolov8n-seg_400.pt", "url": "https://github.com/hben35096/assets/releases/download/yolo8/skin_yolov8n-seg_400.pt", "size": "6.83MB"}, {"name": "skin_yolov8n-seg_800.pt (segm)", "type": "Ultralytics", "base": "Ultralytics", "save_path": "ultralytics/segm", "description": "These are the available models in the UltralyticsDetectorProvider of Impact Pack.", "reference": "https://github.com/hben35096/assets/releases/tag/yolo8", "filename": "skin_yolov8n-seg_800.pt", "url": "https://github.com/hben35096/assets/releases/download/yolo8/skin_yolov8n-seg_800.pt", "size": "6.84MB"}, {"name": "animatediff/mmd_sd_v14.ckpt (comfyui-animatediff) (Updated path)", "type": "animatediff", "base": "SD1.x", "save_path": "AnimateDiff", "description": "Pressing 'install' directly downloads the model from the ArtVentureX/AnimateDiff extension node.", "reference": "https://huggingface.co/guoyww/animatediff", "filename": "mm_sd_v14.ckpt", "url": "https://huggingface.co/guoyww/animatediff/resolve/main/mm_sd_v14.ckpt", "size": "1.67GB"}, {"name": "animatediff/mm_sd_v15.ckpt (comfyui-animatediff) (Updated path)", "type": "animatediff", "base": "SD1.x", "save_path": "AnimateDiff", "description": "Pressing 'install' directly downloads the model from the ArtVentureX/AnimateDiff extension node.", "reference": "https://huggingface.co/guoyww/animatediff", "filename": "mm_sd_v15.ckpt", "url": "https://huggingface.co/guoyww/animatediff/resolve/main/mm_sd_v15.ckpt", "size": "1.67GB"}, {"name": "animatediff/mmd_sd_v14.ckpt (ComfyUI-AnimateDiff-Evolved) (Updated path)", "type": "animatediff", "base": "SD1.x", "save_path": "animatediff_models", "description": "Pressing 'install' directly downloads the model from the Kosinkadink/ComfyUI-AnimateDiff-Evolved extension node.", "reference": "https://huggingface.co/guoyww/animatediff", "filename": "mm_sd_v14.ckpt", "url": "https://huggingface.co/guoyww/animatediff/resolve/main/mm_sd_v14.ckpt", "size": "1.67GB"}, {"name": "animatediff/mm_sd_v15.ckpt (ComfyUI-AnimateDiff-Evolved) (Updated path)", "type": "animatediff", "base": "SD1.x", "save_path": "animatediff_models", "description": "Pressing 'install' directly downloads the model from the Kosinkadink/ComfyUI-AnimateDiff-Evolved extension node.", "reference": "https://huggingface.co/guoyww/animatediff", "filename": "mm_sd_v15.ckpt", "url": "https://huggingface.co/guoyww/animatediff/resolve/main/mm_sd_v15.ckpt", "size": "1.67GB"}, {"name": "animatediff/mm_sd_v15_v2.ckpt (ComfyUI-AnimateDiff-Evolved) (Updated path)", "type": "animatediff", "base": "SD1.x", "save_path": "animatediff_models", "description": "Pressing 'install' directly downloads the model from the Kosinkadink/ComfyUI-AnimateDiff-Evolved extension node.", "reference": "https://huggingface.co/guoyww/animatediff", "filename": "mm_sd_v15_v2.ckpt", "url": "https://huggingface.co/guoyww/animatediff/resolve/main/mm_sd_v15_v2.ckpt", "size": "1.82GB"}, {"name": "animatediff/v3_sd15_mm.ckpt (ComfyUI-AnimateDiff-Evolved) (Updated path)", "type": "animatediff", "base": "SD1.x", "save_path": "animatediff_models", "description": "Pressing 'install' directly downloads the model from the Kosinkadink/ComfyUI-AnimateDiff-Evolved extension node.", "reference": "https://huggingface.co/guoyww/animatediff", "filename": "v3_sd15_mm.ckpt", "url": "https://huggingface.co/guoyww/animatediff/resolve/main/v3_sd15_mm.ckpt", "size": "1.67GB"}, {"name": "animatediff/mm_sdxl_v10_beta.ckpt (ComfyUI-AnimateDiff-Evolved) (Updated path)", "type": "animatediff", "base": "SDXL", "save_path": "animatediff_models", "description": "Pressing 'install' directly downloads the model from the Kosinkadink/ComfyUI-AnimateDiff-Evolved extension node.", "reference": "https://huggingface.co/guoyww/animatediff", "filename": "mm_sdxl_v10_beta.ckpt", "url": "https://huggingface.co/guoyww/animatediff/resolve/main/mm_sdxl_v10_beta.ckpt", "size": "950.1MB"}, {"name": "AD_Stabilized_Motion/mm-Stabilized_high.pth (ComfyUI-AnimateDiff-Evolved) (Updated path)", "type": "animatediff", "base": "SD1.x", "save_path": "animatediff_models", "description": "Pressing 'install' directly downloads the model from the Kosinkadink/ComfyUI-AnimateDiff-Evolved extension node.", "reference": "https://huggingface.co/manshoety/AD_Stabilized_Motion", "filename": "mm-Stabilized_high.pth", "url": "https://huggingface.co/manshoety/AD_Stabilized_Motion/resolve/main/mm-Stabilized_high.pth", "size": "1.67GB"}, {"name": "AD_Stabilized_Motion/mm-Stabilized_mid.pth (ComfyUI-AnimateDiff-Evolved) (Updated path)", "type": "animatediff", "base": "SD1.x", "save_path": "animatediff_models", "description": "Pressing 'install' directly downloads the model from the Kosinkadink/ComfyUI-AnimateDiff-Evolved extension node.", "reference": "https://huggingface.co/manshoety/AD_Stabilized_Motion", "filename": "mm-Stabilized_mid.pth", "url": "https://huggingface.co/manshoety/AD_Stabilized_Motion/resolve/main/mm-Stabilized_mid.pth", "size": "1.67GB"}, {"name": "CiaraRowles/temporaldiff-v1-animatediff.ckpt (ComfyUI-AnimateDiff-Evolved) (Updated path)", "type": "animatediff", "base": "SD1.x", "save_path": "animatediff_models", "description": "Pressing 'install' directly downloads the model from the Kosinkadink/ComfyUI-AnimateDiff-Evolved extension node.", "reference": "https://huggingface.co/CiaraRowles/TemporalDiff", "filename": "temporaldiff-v1-animatediff.ckpt", "url": "https://huggingface.co/CiaraRowles/TemporalDiff/resolve/main/temporaldiff-v1-animatediff.ckpt", "size": "1.67GB"}, {"name": "Leoxing/pia.ckpt", "type": "<PERSON>iff-pia", "base": "SD1.x", "save_path": "animatediff_models", "description": "AnimateDiff-PIA Model", "reference": "https://huggingface.co/Leoxing/PIA/tree/main", "filename": "pia.ckpt", "url": "https://huggingface.co/Leoxing/PIA/resolve/main/pia.ckpt", "size": "1.67GB"}, {"name": "animatediff/v2_lora_PanLeft.ckpt (ComfyUI-AnimateDiff-Evolved) (Updated path)", "type": "motion lora", "base": "SD1.x", "save_path": "animatediff_motion_lora", "description": "Pressing 'install' directly downloads the model from the Kosinkadink/ComfyUI-AnimateDiff-Evolved extension node.", "reference": "https://huggingface.co/guoyww/animatediff", "filename": "v2_lora_PanLeft.ckpt", "url": "https://huggingface.co/guoyww/animatediff/resolve/main/v2_lora_PanLeft.ckpt", "size": "77.5MB"}, {"name": "animatediff/v2_lora_PanRight.ckpt (ComfyUI-AnimateDiff-Evolved) (Updated path)", "type": "motion lora", "base": "SD1.x", "save_path": "animatediff_motion_lora", "description": "Pressing 'install' directly downloads the model from the Kosinkadink/ComfyUI-AnimateDiff-Evolved extension node.", "reference": "https://huggingface.co/guoyww/animatediff", "filename": "v2_lora_PanRight.ckpt", "url": "https://huggingface.co/guoyww/animatediff/resolve/main/v2_lora_PanRight.ckpt", "size": "77.5MB"}, {"name": "animatediff/v2_lora_RollingAnticlockwise.ckpt (ComfyUI-AnimateDiff-Evolved) (Updated path)", "type": "motion lora", "base": "SD1.x", "save_path": "animatediff_motion_lora", "description": "Pressing 'install' directly downloads the model from the Kosinkadink/ComfyUI-AnimateDiff-Evolved extension node.", "reference": "https://huggingface.co/guoyww/animatediff", "filename": "v2_lora_RollingAnticlockwise.ckpt", "url": "https://huggingface.co/guoyww/animatediff/resolve/main/v2_lora_RollingAnticlockwise.ckpt", "size": "77.5MB"}, {"name": "animatediff/v2_lora_RollingClockwise.ckpt (ComfyUI-AnimateDiff-Evolved) (Updated path)", "type": "motion lora", "base": "SD1.x", "save_path": "animatediff_motion_lora", "description": "Pressing 'install' directly downloads the model from the Kosinkadink/ComfyUI-AnimateDiff-Evolved extension node.", "reference": "https://huggingface.co/guoyww/animatediff", "filename": "v2_lora_RollingClockwise.ckpt", "url": "https://huggingface.co/guoyww/animatediff/resolve/main/v2_lora_RollingClockwise.ckpt", "size": "77.5MB"}, {"name": "animatediff/v2_lora_TiltDown.ckpt (ComfyUI-AnimateDiff-Evolved) (Updated path)", "type": "motion lora", "base": "SD1.x", "save_path": "animatediff_motion_lora", "description": "Pressing 'install' directly downloads the model from the Kosinkadink/ComfyUI-AnimateDiff-Evolved extension node.", "reference": "https://huggingface.co/guoyww/animatediff", "filename": "v2_lora_TiltDown.ckpt", "url": "https://huggingface.co/guoyww/animatediff/resolve/main/v2_lora_TiltDown.ckpt", "size": "77.5MB"}, {"name": "animatediff/v2_lora_TiltUp.ckpt (ComfyUI-AnimateDiff-Evolved) (Updated path)", "type": "motion lora", "base": "SD1.x", "save_path": "animatediff_motion_lora", "description": "Pressing 'install' directly downloads the model from the Kosinkadink/ComfyUI-AnimateDiff-Evolved extension node.", "reference": "https://huggingface.co/guoyww/animatediff", "filename": "v2_lora_TiltUp.ckpt", "url": "https://huggingface.co/guoyww/animatediff/resolve/main/v2_lora_TiltUp.ckpt", "size": "77.5MB"}, {"name": "animatediff/v2_lora_ZoomIn.ckpt (ComfyUI-AnimateDiff-Evolved) (Updated path)", "type": "motion lora", "base": "SD1.x", "save_path": "animatediff_motion_lora", "description": "Pressing 'install' directly downloads the model from the Kosinkadink/ComfyUI-AnimateDiff-Evolved extension node.", "reference": "https://huggingface.co/guoyww/animatediff", "filename": "v2_lora_ZoomIn.ckpt", "url": "https://huggingface.co/guoyww/animatediff/resolve/main/v2_lora_ZoomIn.ckpt", "size": "77.5MB"}, {"name": "animatediff/v2_lora_ZoomOut.ckpt (ComfyUI-AnimateDiff-Evolved) (Updated path)", "type": "motion lora", "base": "SD1.x", "save_path": "animatediff_motion_lora", "description": "Pressing 'install' directly downloads the model from the Kosinkadink/ComfyUI-AnimateDiff-Evolved extension node.", "reference": "https://huggingface.co/guoyww/animatediff", "filename": "v2_lora_ZoomOut.ckpt", "url": "https://huggingface.co/guoyww/animatediff/resolve/main/v2_lora_ZoomOut.ckpt", "size": "77.5MB"}, {"name": "LongAnimatediff/lt_long_mm_32_frames.ckpt (ComfyUI-AnimateDiff-Evolved) (Updated path)", "type": "animatediff", "base": "SD1.x", "save_path": "animatediff_models", "description": "Pressing 'install' directly downloads the model from the Kosinkadink/ComfyUI-AnimateDiff-Evolved extension node.", "reference": "https://huggingface.co/Lightricks/LongAnimateDiff", "filename": "lt_long_mm_32_frames.ckpt", "url": "https://huggingface.co/Lightricks/LongAnimateDiff/resolve/main/lt_long_mm_32_frames.ckpt", "size": "1.82GB"}, {"name": "LongAnimatediff/lt_long_mm_16_64_frames.ckpt (ComfyUI-AnimateDiff-Evolved) (Updated path)", "type": "animatediff", "base": "SD1.x", "save_path": "animatediff_models", "description": "Pressing 'install' directly downloads the model from the Kosinkadink/ComfyUI-AnimateDiff-Evolved extension node.", "reference": "https://huggingface.co/Lightricks/LongAnimateDiff", "filename": "lt_long_mm_16_64_frames.ckpt", "url": "https://huggingface.co/Lightricks/LongAnimateDiff/resolve/main/lt_long_mm_16_64_frames.ckpt", "size": "1.83GB"}, {"name": "LongAnimatediff/lt_long_mm_16_64_frames_v1.1.ckpt (ComfyUI-AnimateDiff-Evolved) (Updated path)", "type": "animatediff", "base": "SD1.x", "save_path": "animatediff_models", "description": "Pressing 'install' directly downloads the model from the Kosinkadink/ComfyUI-AnimateDiff-Evolved extension node.", "reference": "https://huggingface.co/Lightricks/LongAnimateDiff", "filename": "lt_long_mm_16_64_frames_v1.1.ckpt", "url": "https://huggingface.co/Lightricks/LongAnimateDiff/resolve/main/lt_long_mm_16_64_frames_v1.1.ckpt", "size": "1.83GB"}, {"name": "animatediff/v3_sd15_sparsectrl_rgb.ckpt (ComfyUI-AnimateDiff-Evolved)", "type": "controlnet", "base": "SD1.x", "save_path": "controlnet/SD1.5/animatediff", "description": "AnimateDiff SparseCtrl RGB ControlNet model", "reference": "https://huggingface.co/guoyww/animatediff", "filename": "v3_sd15_sparsectrl_rgb.ckpt", "url": "https://huggingface.co/guoyww/animatediff/resolve/main/v3_sd15_sparsectrl_rgb.ckpt", "size": "1.99GB"}, {"name": "animatediff/v3_sd15_sparsectrl_scribble.ckpt", "type": "controlnet", "base": "SD1.x", "save_path": "controlnet/SD1.5/animatediff", "description": "AnimateDiff SparseCtrl Scribble ControlNet model", "reference": "https://huggingface.co/guoyww/animatediff", "filename": "v3_sd15_sparsectrl_scribble.ckpt", "url": "https://huggingface.co/guoyww/animatediff/resolve/main/v3_sd15_sparsectrl_scribble.ckpt", "size": "1.99GB"}, {"name": "animatediff/v3_sd15_adapter.ckpt", "type": "lora", "base": "SD1.x", "save_path": "loras/SD1.5/animatediff", "description": "AnimateDiff Adapter LoRA (SD1.5)", "reference": "https://huggingface.co/guoyww/animatediff", "filename": "v3_sd15_adapter.ckpt", "url": "https://huggingface.co/guoyww/animatediff/resolve/main/v3_sd15_adapter.ckpt", "size": "102.1MB"}, {"name": "TencentARC/motionctrl.pth", "type": "checkpoint", "base": "MotionCtrl", "save_path": "checkpoints/motionctrl", "description": "To use the ComfyUI-MotionCtrl extension, downloading this model is required.", "reference": "https://huggingface.co/TencentARC/MotionCtrl", "filename": "motionctrl.pth", "url": "https://huggingface.co/TencentARC/MotionCtrl/resolve/main/motionctrl.pth", "size": "4.02GB"}, {"name": "ip-adapter_sd15.safetensors", "type": "IP-Adapter", "base": "SD1.5", "save_path": "ipadapter", "description": "You can use this model in the [a/ComfyUI IPAdapter plus](https://github.com/cubiq/ComfyUI_IPAdapter_plus) extension.", "reference": "https://huggingface.co/h94/IP-Adapter", "filename": "ip-adapter_sd15.safetensors", "url": "https://huggingface.co/h94/IP-Adapter/resolve/main/models/ip-adapter_sd15.safetensors", "size": "44.6MB"}, {"name": "ip-adapter_sd15_light_v11.bin", "type": "IP-Adapter", "base": "SD1.5", "save_path": "ipadapter", "description": "You can use this model in the [a/ComfyUI IPAdapter plus](https://github.com/cubiq/ComfyUI_IPAdapter_plus) extension.", "reference": "https://huggingface.co/h94/IP-Adapter", "filename": "ip-adapter_sd15_light_v11.bin", "url": "https://huggingface.co/h94/IP-Adapter/resolve/main/models/ip-adapter_sd15_light_v11.bin", "size": "44.6MB"}, {"name": "ip-adapter_sd15_light.safetensors [DEPRECATED]", "type": "IP-Adapter", "base": "SD1.5", "save_path": "ipadapter", "description": "You can use this model in the [a/ComfyUI IPAdapter plus](https://github.com/cubiq/ComfyUI_IPAdapter_plus) extension.", "reference": "https://huggingface.co/h94/IP-Adapter", "filename": "ip-adapter_sd15_light.safetensors", "url": "https://huggingface.co/h94/IP-Adapter/resolve/main/models/ip-adapter_sd15_light.safetensors", "size": "44.6MB"}, {"name": "ip-adapter-plus_sd15.safetensors", "type": "IP-Adapter", "base": "SD1.5", "save_path": "ipadapter", "description": "You can use this model in the [a/ComfyUI IPAdapter plus](https://github.com/cubiq/ComfyUI_IPAdapter_plus) extension.", "reference": "https://huggingface.co/h94/IP-Adapter", "filename": "ip-adapter-plus_sd15.safetensors", "url": "https://huggingface.co/h94/IP-Adapter/resolve/main/models/ip-adapter-plus_sd15.safetensors", "size": "98.2MB"}, {"name": "ip-adapter-plus-face_sd15.safetensors", "type": "IP-Adapter", "base": "SD1.5", "save_path": "ipadapter", "description": "You can use this model in the [a/ComfyUI IPAdapter plus](https://github.com/cubiq/ComfyUI_IPAdapter_plus) extension.", "reference": "https://huggingface.co/h94/IP-Adapter", "filename": "ip-adapter-plus-face_sd15.safetensors", "url": "https://huggingface.co/h94/IP-Adapter/resolve/main/models/ip-adapter-plus-face_sd15.safetensors", "size": "98.2MB"}, {"name": "ip-adapter-full-face_sd15.safetensors", "type": "IP-Adapter", "base": "SD1.5", "save_path": "ipadapter", "description": "You can use this model in the [a/ComfyUI IPAdapter plus](https://github.com/cubiq/ComfyUI_IPAdapter_plus) extension.", "reference": "https://huggingface.co/h94/IP-Adapter", "filename": "ip-adapter-full-face_sd15.safetensors", "url": "https://huggingface.co/h94/IP-Adapter/resolve/main/models/ip-adapter-full-face_sd15.safetensors", "size": "43.6MB"}, {"name": "ip-adapter_sd15_vit-G.safetensors", "type": "IP-Adapter", "base": "SD1.5", "save_path": "ipadapter", "description": "You can use this model in the [a/ComfyUI IPAdapter plus](https://github.com/cubiq/ComfyUI_IPAdapter_plus) extension.", "reference": "https://huggingface.co/h94/IP-Adapter", "filename": "ip-adapter_sd15_vit-G.safetensors", "url": "https://huggingface.co/h94/IP-Adapter/resolve/main/models/ip-adapter_sd15_vit-G.safetensors", "size": "46.2MB"}, {"name": "ip-adapter-faceid_sd15.bin", "type": "IP-Adapter", "base": "SD1.5", "save_path": "ipadapter", "description": "IP-Adapter-FaceID Model (SD1.5) [ipadapter]", "reference": "https://huggingface.co/h94/IP-Adapter-FaceID", "filename": "ip-adapter-faceid_sd15.bin", "url": "https://huggingface.co/h94/IP-Adapter-FaceID/resolve/main/ip-adapter-faceid_sd15.bin", "size": "96.7MB"}, {"name": "ip-adapter-faceid-plusv2_sd15.bin", "type": "IP-Adapter", "base": "SD1.5", "save_path": "ipadapter", "description": "IP-Adapter-FaceID Plus V2 Model (SD1.5) [ipadapter]", "reference": "https://huggingface.co/h94/IP-Adapter-FaceID", "filename": "ip-adapter-faceid-plusv2_sd15.bin", "url": "https://huggingface.co/h94/IP-Adapter-FaceID/resolve/main/ip-adapter-faceid-plusv2_sd15.bin", "size": "156.6MB"}, {"name": "ip-adapter-faceid-plus_sd15.bin [DEPRECATED]", "type": "IP-Adapter", "base": "SD1.5", "save_path": "ipadapter", "description": "IP-Adapter-FaceID Plus Model (SD1.5) [ipadapter]", "reference": "https://huggingface.co/h94/IP-Adapter-FaceID", "filename": "ip-adapter-faceid-plus_sd15.bin", "url": "https://huggingface.co/h94/IP-Adapter-FaceID/resolve/main/ip-adapter-faceid-plus_sd15.bin", "size": "156.6MB"}, {"name": "ip-adapter-faceid-portrait-v11_sd15.bin", "type": "IP-Adapter", "base": "SD1.5", "save_path": "ipadapter", "description": "IP-Adapter-FaceID Portrait V11 Model (SD1.5) [ipadapter]", "reference": "https://huggingface.co/h94/IP-Adapter-FaceID", "filename": "ip-adapter-faceid-portrait-v11_sd15.bin", "url": "https://huggingface.co/h94/IP-Adapter-FaceID/resolve/main/ip-adapter-faceid-portrait-v11_sd15.bin", "size": "64.6MB"}, {"name": "ip-adapter-faceid-portrait_sd15.bin [DEPRECATED]", "type": "IP-Adapter", "base": "SD1.5", "save_path": "ipadapter", "description": "IP-Adapter-FaceID Portrait Model (SD1.5) [ipadapter]", "reference": "https://huggingface.co/h94/IP-Adapter-FaceID", "filename": "ip-adapter-faceid-portrait_sd15.bin", "url": "https://huggingface.co/h94/IP-Adapter-FaceID/resolve/main/ip-adapter-faceid-portrait_sd15.bin", "size": "64.6MB"}, {"name": "ip-adapter-faceid_sdxl.bin", "type": "IP-Adapter", "base": "SDXL", "save_path": "ipadapter", "description": "IP-Adapter-FaceID Model (SDXL) [ipadapter]", "reference": "https://huggingface.co/h94/IP-Adapter-FaceID", "filename": "ip-adapter-faceid_sdxl.bin", "url": "https://huggingface.co/h94/IP-Adapter-FaceID/resolve/main/ip-adapter-faceid_sdxl.bin", "size": "1.07GB"}, {"name": "ip-adapter-faceid-plusv2_sdxl.bin", "type": "IP-Adapter", "base": "SDXL", "save_path": "ipadapter", "description": "IP-Adapter-FaceID Plus Model (SDXL) [ipadapter]", "reference": "https://huggingface.co/h94/IP-Adapter-FaceID", "filename": "ip-adapter-faceid-plusv2_sdxl.bin", "url": "https://huggingface.co/h94/IP-Adapter-FaceID/resolve/main/ip-adapter-faceid-plusv2_sdxl.bin", "size": "1.49GB"}, {"name": "ip-adapter-faceid-portrait_sdxl.bin", "type": "IP-Adapter", "base": "SDXL", "save_path": "ipadapter", "description": "IP-Adapter-FaceID Portrait Model (SDXL) [ipadapter]", "reference": "https://huggingface.co/h94/IP-Adapter-FaceID", "filename": "ip-adapter-faceid-portrait_sdxl.bin", "url": "https://huggingface.co/h94/IP-Adapter-FaceID/resolve/main/ip-adapter-faceid-portrait_sdxl.bin", "size": "749.8MB"}, {"name": "ip-adapter-faceid-portrait_sdxl_unnorm.bin", "type": "IP-Adapter", "base": "SDXL", "save_path": "ipadapter", "description": "IP-Adapter-FaceID Portrait Model (SDXL/unnorm) [ipadapter]", "reference": "https://huggingface.co/h94/IP-Adapter-FaceID", "filename": "ip-adapter-faceid-portrait_sdxl_unnorm.bin", "url": "https://huggingface.co/h94/IP-Adapter-FaceID/resolve/main/ip-adapter-faceid-portrait_sdxl_unnorm.bin", "size": "1.01GB"}, {"name": "ip-adapter-faceid_sd15_lora.safetensors", "type": "lora", "base": "SD1.5", "save_path": "loras/ipadapter", "description": "IP-Adapter-FaceID LoRA Model (SD1.5) [ipadapter]", "reference": "https://huggingface.co/h94/IP-Adapter-FaceID", "filename": "ip-adapter-faceid_sd15_lora.safetensors", "url": "https://huggingface.co/h94/IP-Adapter-FaceID/resolve/main/ip-adapter-faceid_sd15_lora.safetensors", "size": "51.1MB"}, {"name": "ip-adapter-faceid-plus_sd15_lora.safetensors [DEPRECATED]", "type": "lora", "base": "SD1.5", "save_path": "loras/ipadapter", "description": "IP-Adapter-FaceID Plus LoRA Model (SD1.5) [ipadapter]", "reference": "https://huggingface.co/h94/IP-Adapter-FaceID", "filename": "ip-adapter-faceid-plus_sd15_lora.safetensors", "url": "https://huggingface.co/h94/IP-Adapter-FaceID/resolve/main/ip-adapter-faceid-plus_sd15_lora.safetensors", "size": "51.1MB"}, {"name": "ip-adapter-faceid-plusv2_sd15_lora.safetensors", "type": "lora", "base": "SD1.5", "save_path": "loras/ipadapter", "description": "IP-Adapter-FaceID-Plus V2 LoRA Model (SD1.5) [ipadapter]", "reference": "https://huggingface.co/h94/IP-Adapter-FaceID", "filename": "ip-adapter-faceid-plusv2_sd15_lora.safetensors", "url": "https://huggingface.co/h94/IP-Adapter-FaceID/resolve/main/ip-adapter-faceid-plusv2_sd15_lora.safetensors", "size": "51.1MB"}, {"name": "ip-adapter-faceid_sdxl_lora.safetensors", "type": "lora", "base": "SDXL", "save_path": "loras/ipadapter", "description": "IP-Adapter-FaceID LoRA Model (SDXL) [ipadapter]", "reference": "https://huggingface.co/h94/IP-Adapter-FaceID", "filename": "ip-adapter-faceid_sdxl_lora.safetensors", "url": "https://huggingface.co/h94/IP-Adapter-FaceID/resolve/main/ip-adapter-faceid_sdxl_lora.safetensors", "size": "371.8MB"}, {"name": "ip-adapter-faceid-plusv2_sdxl_lora.safetensors", "type": "lora", "base": "SDXL", "save_path": "loras/ipadapter", "description": "IP-Adapter-FaceID-Plus V2 LoRA Model (SDXL) [ipadapter]", "reference": "https://huggingface.co/h94/IP-Adapter-FaceID", "filename": "ip-adapter-faceid-plusv2_sdxl_lora.safetensors", "url": "https://huggingface.co/h94/IP-Adapter-FaceID/resolve/main/ip-adapter-faceid-plusv2_sdxl_lora.safetensors", "size": "371.8MB"}, {"name": "ip-adapter_sdxl.safetensors", "type": "IP-Adapter", "base": "SDXL", "save_path": "ipadapter", "description": "You can use this model in the [a/ComfyUI IPAdapter plus](https://github.com/cubiq/ComfyUI_IPAdapter_plus) extension.", "reference": "https://huggingface.co/h94/IP-Adapter", "filename": "ip-adapter_sdxl.safetensors", "url": "https://huggingface.co/h94/IP-Adapter/resolve/main/sdxl_models/ip-adapter_sdxl.safetensors", "size": "702.6MB"}, {"name": "ip-adapter_sdxl_vit-h.safetensors", "type": "IP-Adapter", "base": "SDXL", "save_path": "ipadapter", "description": "This model requires the use of the SD1.5 encoder despite being for SDXL checkpoints [ipadapter]", "reference": "https://huggingface.co/h94/IP-Adapter", "filename": "ip-adapter_sdxl_vit-h.safetensors", "url": "https://huggingface.co/h94/IP-Adapter/resolve/main/sdxl_models/ip-adapter_sdxl_vit-h.safetensors", "size": "698.4MB"}, {"name": "ip-adapter-plus_sdxl_vit-h.safetensors", "type": "IP-Adapter", "base": "SDXL", "save_path": "ipadapter", "description": "This model requires the use of the SD1.5 encoder despite being for SDXL checkpoints [ipadapter]", "reference": "https://huggingface.co/h94/IP-Adapter", "filename": "ip-adapter-plus_sdxl_vit-h.safetensors", "url": "https://huggingface.co/h94/IP-Adapter/resolve/main/sdxl_models/ip-adapter-plus_sdxl_vit-h.safetensors", "size": "847.5MB"}, {"name": "ip-adapter-plus-face_sdxl_vit-h.safetensors", "type": "IP-Adapter", "base": "SDXL", "save_path": "ipadapter", "description": "This model requires the use of the SD1.5 encoder despite being for SDXL checkpoints [ipadapter]", "reference": "https://huggingface.co/h94/IP-Adapter", "filename": "ip-adapter-plus-face_sdxl_vit-h.safetensors", "url": "https://huggingface.co/h94/IP-Adapter/resolve/main/sdxl_models/ip-adapter-plus-face_sdxl_vit-h.safetensors", "size": "847.5MB"}, {"name": "ip_plus_composition_sd15.safetensors", "type": "IP-Adapter", "base": "SD1.5", "save_path": "ipadapter", "description": "You can use this model in the [a/ComfyUI IPAdapter plus](https://github.com/cubiq/ComfyUI_IPAdapter_plus) extension.", "reference": "https://huggingface.co/ostris/ip-composition-adapter", "filename": "ip_plus_composition_sd15.safetensors", "url": "https://huggingface.co/ostris/ip-composition-adapter/resolve/main/ip_plus_composition_sd15.safetensors", "size": "98.2MB"}, {"name": "ip_plus_composition_sdxl.safetensors", "type": "IP-Adapter", "base": "SDXL", "save_path": "ipadapter", "description": "You can use this model in the [a/ComfyUI IPAdapter plus](https://github.com/cubiq/ComfyUI_IPAdapter_plus) extension.", "reference": "https://huggingface.co/ostris/ip-composition-adapter", "filename": "ip_plus_composition_sdxl.safetensors", "url": "https://huggingface.co/ostris/ip-composition-adapter/resolve/main/ip_plus_composition_sdxl.safetensors", "size": "847.5MB"}, {"name": "Kolors-IP-Adapter-Plus.bin (Kwai-Kolors/Kolors-IP-Adapter-Plus)", "type": "IP-Adapter", "base": "<PERSON><PERSON><PERSON>", "save_path": "ipadapter", "description": "You can use this model in the [a/ComfyUI IPAdapter plus](https://github.com/cubiq/ComfyUI_IPAdapter_plus) extension.", "reference": "https://huggingface.co/K<PERSON>-Kolors/Kolors-IP-Adapter-Plus", "filename": "Kolors-IP-Adapter-Plus.bin", "url": "https://huggingface.co/<PERSON><PERSON>-Kolors/Kolors-IP-Adapter-Plus/resolve/main/ip_adapter_plus_general.bin", "size": "1.01GB"}, {"name": "Kolors-IP-Adapter-FaceID-Plus.bin (Kwai-Kolors/Kolors-IP-Adapter-Plus)", "type": "IP-Adapter", "base": "<PERSON><PERSON><PERSON>", "save_path": "ipadapter", "description": "You can use this model in the [a/ComfyUI IPAdapter plus](https://github.com/cubiq/ComfyUI_IPAdapter_plus) extension.", "reference": "https://huggingface.co/K<PERSON>-Kolors/Kolors-IP-Adapter-FaceID-Plus", "filename": "Kolors-IP-Adapter-FaceID-Plus.bin", "url": "https://huggingface.co/<PERSON><PERSON>-Kolors/Kolors-IP-Adapter-FaceID-Plus/resolve/main/ipa-faceid-plus.bin", "size": "2.39GB"}, {"name": "GFPGANv1.4.pth", "type": "GFPGAN", "base": "GFPGAN", "save_path": "facerestore_models", "description": "Face Restoration Models. Download the model required for using the 'Facerestore CF (Code Former)' custom node.", "reference": "https://github.com/TencentARC/GFPGAN/releases", "filename": "GFPGANv1.4.pth", "url": "https://github.com/TencentARC/GFPGAN/releases/download/v1.3.4/GFPGANv1.4.pth", "size": "348.6MB"}, {"name": "codeformer.pth", "type": "CodeFormer", "base": "CodeFormer", "save_path": "facerestore_models", "description": "Face Restoration Models. Download the model required for using the 'Facerestore CF (Code Former)' custom node.", "reference": "https://github.com/sczhou/CodeFormer/releases", "filename": "codeformer.pth", "url": "https://github.com/sczhou/CodeFormer/releases/download/v0.1.0/codeformer.pth", "size": "376.6MB"}, {"name": "detection_Resnet50_Final.pth", "type": "facexlib", "base": "facexlib", "save_path": "facerestore_models", "description": "Face Detection Models. Download the model required for using the 'Facerestore CF (Code Former)' custom node.", "reference": "https://github.com/xinntao/facexlib", "filename": "detection_Resnet50_Final.pth", "url": "https://github.com/xinntao/facexlib/releases/download/v0.1.0/detection_Resnet50_Final.pth", "size": "109.5MB"}, {"name": "detection_mobilenet0.25_Final.pth", "type": "facexlib", "base": "facexlib", "save_path": "facerestore_models", "description": "Face Detection Models. Download the model required for using the 'Facerestore CF (Code Former)' custom node.", "reference": "https://github.com/xinntao/facexlib", "filename": "detection_mobilenet0.25_Final.pth", "url": "https://github.com/xinntao/facexlib/releases/download/v0.1.0/detection_mobilenet0.25_Final.pth", "size": "1.79MB"}, {"name": "yolov5l-face.pth", "type": "facexlib", "base": "facexlib", "save_path": "facedetection", "description": "Face Detection Models. Download the model required for using the 'Facerestore CF (Code Former)' custom node.", "reference": "https://github.com/xinntao/facexlib", "filename": "yolov5l-face.pth", "url": "https://github.com/sczhou/CodeFormer/releases/download/v0.1.0/yolov5l-face.pth", "size": "187.0MB"}, {"name": "yolov5n-face.pth", "type": "facexlib", "base": "facexlib", "save_path": "facedetection", "description": "Face Detection Models. Download the model required for using the 'Facerestore CF (Code Former)' custom node.", "reference": "https://github.com/xinntao/facexlib", "filename": "yolov5n-face.pth", "url": "https://github.com/sczhou/CodeFormer/releases/download/v0.1.0/yolov5n-face.pth", "size": "7.15MB"}, {"name": "photomaker-v1.bin", "type": "photomaker", "base": "SDXL", "save_path": "photomaker", "description": "PhotoMaker model. This model is compatible with SDXL.", "reference": "https://huggingface.co/TencentARC/PhotoMaker", "filename": "photomaker-v1.bin", "url": "https://huggingface.co/TencentARC/PhotoMaker/resolve/main/photomaker-v1.bin", "size": "934.1MB"}, {"name": "photomaker-v2.bin", "type": "photomaker", "base": "SDXL", "save_path": "photomaker", "description": "PhotoMaker model. This model is compatible with SDXL.", "reference": "https://huggingface.co/TencentARC/PhotoMaker-V2", "filename": "photomaker-v2.bin", "url": "https://huggingface.co/TencentARC/PhotoMaker-V2/resolve/main/photomaker-v2.bin", "size": "1.8GB"}, {"name": "1k3d68.onnx", "type": "insightface", "base": "inswapper", "save_path": "insightface/models/antelopev2", "description": "Antelopev2 1k3d68.onnx model for InstantId. (InstantId needs all Antelopev2 models)", "reference": "https://github.com/cubiq/ComfyUI_InstantID#installation", "filename": "1k3d68.onnx", "url": "https://huggingface.co/MonsterMMORPG/tools/resolve/main/1k3d68.onnx", "size": "143.6MB"}, {"name": "2d106det.onnx", "type": "insightface", "base": "inswapper", "save_path": "insightface/models/antelopev2", "description": "Antelopev2 2d106det.onnx model for InstantId. (InstantId needs all Antelopev2 models)", "reference": "https://github.com/cubiq/ComfyUI_InstantID#installation", "filename": "2d106det.onnx", "url": "https://huggingface.co/MonsterMMORPG/tools/resolve/main/2d106det.onnx", "size": "5.03MB"}, {"name": "genderage.onnx", "type": "insightface", "base": "inswapper", "save_path": "insightface/models/antelopev2", "description": "Antelopev2 genderage.onnx model for InstantId. (InstantId needs all Antelopev2 models)", "reference": "https://github.com/cubiq/ComfyUI_InstantID#installation", "filename": "genderage.onnx", "url": "https://huggingface.co/MonsterMMORPG/tools/resolve/main/genderage.onnx", "size": "1.32MB"}, {"name": "glintr100.onnx", "type": "insightface", "base": "inswapper", "save_path": "insightface/models/antelopev2", "description": "Antelopev2 glintr100.onnx model for InstantId. (InstantId needs all Antelopev2 models)", "reference": "https://github.com/cubiq/ComfyUI_InstantID#installation", "filename": "glintr100.onnx", "url": "https://huggingface.co/MonsterMMORPG/tools/resolve/main/glintr100.onnx", "size": "260.7MB"}, {"name": "scrfd_10g_bnkps.onnx", "type": "insightface", "base": "inswapper", "save_path": "insightface/models/antelopev2", "description": "Antelopev2 scrfd_10g_bnkps.onnx model for InstantId. (InstantId needs all Antelopev2 models)", "reference": "https://github.com/cubiq/ComfyUI_InstantID#installation", "filename": "scrfd_10g_bnkps.onnx", "url": "https://huggingface.co/MonsterMMORPG/tools/resolve/main/scrfd_10g_bnkps.onnx", "size": "16.9MB"}, {"name": "ip-adapter.bin", "type": "instantid", "base": "SDXL", "save_path": "instantid", "description": "InstantId main model based on IpAdapter", "reference": "https://huggingface.co/InstantX/InstantID", "filename": "ip-adapter.bin", "url": "https://huggingface.co/InstantX/InstantID/resolve/main/ip-adapter.bin", "size": "1.69GB"}, {"name": "diffusion_pytorch_model.safetensors", "type": "controlnet", "base": "SDXL", "save_path": "controlnet/instantid", "description": "InstantId controlnet model", "reference": "https://huggingface.co/InstantX/InstantID", "filename": "diffusion_pytorch_model.safetensors", "url": "https://huggingface.co/InstantX/InstantID/resolve/main/ControlNetModel/diffusion_pytorch_model.safetensors", "size": "2.50GB"}, {"name": "InstanceDiffusion/fusers", "type": "InstanceDiffusion", "base": "SD1.5", "save_path": "instance_models/fuser_models", "description": "Fusers checkpoints for multi-object prompting with InstanceDiffusion.", "reference": "https://huggingface.co/logtd/instance_diffusion", "filename": "fusers.ckpt", "url": "https://huggingface.co/logtd/instance_diffusion/resolve/main/fusers.ckpt", "size": "832.1MB"}, {"name": "InstanceDiffusion/position_net", "type": "InstanceDiffusion", "base": "SD1.5", "save_path": "instance_models/positionnet_models", "description": "PositionNet checkpoints for multi-object prompting with InstanceDiffusion.", "reference": "https://huggingface.co/logtd/instance_diffusion", "filename": "position_net.ckpt", "url": "https://huggingface.co/logtd/instance_diffusion/resolve/main/position_net.ckpt", "size": "643.2MB"}, {"name": "InstanceDiffusion/scaleu", "type": "InstanceDiffusion", "base": "SD1.5", "save_path": "instance_models/scaleu_models", "description": "ScaleU checkpoints for multi-object prompting with InstanceDiffusion.", "reference": "https://huggingface.co/logtd/instance_diffusion", "filename": "scaleu.ckpt", "url": "https://huggingface.co/logtd/instance_diffusion/resolve/main/scaleu.ckpt", "size": "53.1KB"}, {"name": "1k3d68.onnx", "type": "insightface", "base": "inswapper", "save_path": "insightface/models/buffalo_l", "description": "Buffalo_l 1k3d68.onnx model for IpAdapterPlus", "reference": "https://github.com/cubiq/ComfyUI_IPAdapter_plus?tab=readme-ov-file#faceid", "filename": "1k3d68.onnx", "url": "https://huggingface.co/public-data/insightface/resolve/main/models/buffalo_l/1k3d68.onnx", "size": "143.6MB"}, {"name": "2d106det.onnx", "type": "insightface", "base": "inswapper", "save_path": "insightface/models/buffalo_l", "description": "Buffalo_l 2d106det.onnx model for IpAdapterPlus", "reference": "https://github.com/cubiq/ComfyUI_IPAdapter_plus?tab=readme-ov-file#faceid", "filename": "2d106det.onnx", "url": "https://huggingface.co/public-data/insightface/resolve/main/models/buffalo_l/2d106det.onnx", "size": "5.03MB"}, {"name": "det_10g.onnx", "type": "insightface", "base": "inswapper", "save_path": "insightface/models/buffalo_l", "description": "Buffalo_l det_10g.onnx model for IpAdapterPlus", "reference": "https://github.com/cubiq/ComfyUI_IPAdapter_plus?tab=readme-ov-file#faceid", "filename": "det_10g.onnx", "url": "https://huggingface.co/public-data/insightface/resolve/main/models/buffalo_l/det_10g.onnx", "size": "16.9MB"}, {"name": "genderage.onnx", "type": "insightface", "base": "inswapper", "save_path": "insightface/models/buffalo_l", "description": "Buffalo_l genderage.onnx model for IpAdapterPlus", "reference": "https://github.com/cubiq/ComfyUI_IPAdapter_plus?tab=readme-ov-file#faceid", "filename": "genderage.onnx", "url": "https://huggingface.co/public-data/insightface/resolve/main/models/buffalo_l/genderage.onnx", "size": "1.32MB"}, {"name": "w600k_r50.onnx", "type": "insightface", "base": "inswapper", "save_path": "insightface/models/buffalo_l", "description": "Buffalo_l w600k_r50.onnx model for IpAdapterPlus", "reference": "https://github.com/cubiq/ComfyUI_IPAdapter_plus?tab=readme-ov-file#faceid", "filename": "w600k_r50.onnx", "url": "https://huggingface.co/public-data/insightface/resolve/main/models/buffalo_l/w600k_r50.onnx", "size": "174.4MB"}, {"name": "BLIP ImageCaption (COCO) w/ ViT-B and CapFilt-L", "type": "BLIP_MODEL", "base": "blip_model", "save_path": "blip", "description": "BLIP ImageCaption (COCO) w/ ViT-B and CapFilt-L", "reference": "https://github.com/salesforce/BLIP", "filename": "model_base_capfilt_large.pth", "url": "https://storage.googleapis.com/sfr-vision-language-research/BLIP/models/model_base_capfilt_large.pth", "size": "2.12GB"}, {"name": "GroundingDINO SwinT OGC - Model", "type": "GroundingDINO", "base": "DINO", "save_path": "grounding<PERSON><PERSON>", "description": "GroundingDINO SwinT OGC Model", "reference": "https://huggingface.co/ShilongLiu/GroundingDINO", "filename": "groundingdino_swint_ogc.pth", "url": "https://huggingface.co/ShilongLiu/GroundingDINO/resolve/main/groundingdino_swint_ogc.pth", "size": "694.0MB"}, {"name": "GroundingDINO SwinT OGC - CFG File", "type": "GroundingDINO", "base": "DINO", "save_path": "grounding<PERSON><PERSON>", "description": "GroundingDINO SwinT OGC CFG File", "reference": "https://huggingface.co/ShilongLiu/GroundingDINO/resolve/main/GroundingDINO_SwinT_OGC.cfg.py", "filename": "GroundingDINO_SwinT_OGC.cfg.py", "url": "https://huggingface.co/ShilongLiu/GroundingDINO/raw/main/GroundingDINO_SwinT_OGC.cfg.py", "size": "1.01KB"}, {"name": "MobileSAM", "type": "sam", "base": "SAM", "save_path": "sams", "description": "MobileSAM", "reference": "https://github.com/ChaoningZhang/MobileSAM/", "filename": "mobile_sam.pt", "url": "https://github.com/ChaoningZhang/MobileSAM/blob/master/weights/mobile_sam.pt", "size": "38.8MB"}, {"name": "DynamiCrafter 1024 bf16 safetensors", "type": "checkpoint", "base": "DynamiCrafter", "save_path": "checkpoints/dynamicrafter", "description": "DynamiCrafter image2video model 1024x575", "reference": "https://huggingface.co/Kijai/DynamiCrafter_pruned/", "filename": "dynamicrafter_1024_v1_bf16.safetensors", "url": "https://huggingface.co/Kijai/DynamiCrafter_pruned/resolve/main/dynamicrafter_1024_v1_bf16.safetensors", "size": "5.22GB"}, {"name": "DynamiCrafter 512 interpolation bf16 safetensors", "type": "checkpoint", "base": "DynamiCrafter", "save_path": "checkpoints/dynamicrafter", "description": "DynamiCrafter image2video interpolation model 512", "reference": "https://huggingface.co/Kijai/DynamiCrafter_pruned/", "filename": "dynamicrafter_512_interp_v1_bf16.safetensors", "url": "https://huggingface.co/Kijai/DynamiCrafter_pruned/resolve/main/dynamicrafter_512_interp_v1_bf16.safetensors", "size": "5.22GB"}, {"name": "monster-labs - Controlnet QR Code Monster v1 For SDXL", "type": "controlnet", "base": "SDXL", "save_path": "controlnet/SDXL", "description": "monster-labs - Controlnet QR Code Monster v1 For SDXL", "reference": "https://huggingface.co/monster-labs/control_v1p_sdxl_qrcode_monster", "filename": "control_v1p_sdxl_qrcode_monster.safetensors", "url": "https://huggingface.co/monster-labs/control_v1p_sdxl_qrcode_monster/resolve/main/diffusion_pytorch_model.safetensors", "size": "5.00GB"}, {"name": "Depth-FM-v1 fp16 safetensors", "type": "checkpoint", "base": "Depth-FM", "save_path": "checkpoints/depthfm", "description": "Depth-FM monocular depth estimation model", "reference": "https://huggingface.co/Kijai/depth-fm-pruned", "filename": "depthfm-v1_fp16.safetensors", "url": "https://huggingface.co/Kijai/depth-fm-pruned/resolve/main/depthfm-v1_fp16.safetensors", "size": "1.73GB"}, {"name": "Depth-FM-v1 fp32 safetensors", "type": "checkpoint", "base": "Depth-FM", "save_path": "checkpoints/depthfm", "description": "Depth-FM monocular depth estimation model", "reference": "https://huggingface.co/Kijai/depth-fm-pruned", "filename": "depthfm-v1_fp32.safetensors", "url": "https://huggingface.co/Kijai/depth-fm-pruned/resolve/main/depthfm-v1_fp32.safetensors", "size": "3.46GB"}, {"name": "SUPIR-v0F.ckpt", "type": "checkpoint", "base": "SUPIR", "save_path": "checkpoints/SUPIR", "description": "SUPIR checkpoint model", "reference": "https://huggingface.co/camenduru/SUPIR/tree/main", "filename": "SUPIR-v0F.ckpt", "url": "https://huggingface.co/camenduru/SUPIR/resolve/main/SUPIR-v0F.ckpt", "size": "5.33GB"}, {"name": "SUPIR-v0Q.ckpt", "type": "checkpoint", "base": "SUPIR", "save_path": "checkpoints/SUPIR", "description": "SUPIR checkpoint model", "reference": "https://huggingface.co/camenduru/SUPIR/tree/main", "filename": "SUPIR-v0Q.ckpt", "url": "https://huggingface.co/camenduru/SUPIR/resolve/main/SUPIR-v0Q.ckpt", "size": "5.33GB"}, {"name": "Kijai/SUPIR-v0F_fp16.safetensors (pruned)", "type": "checkpoint", "base": "SUPIR", "save_path": "checkpoints/SUPIR", "description": "SUPIR checkpoint model", "reference": "https://huggingface.co/Kijai/SUPIR_pruned/tree/main", "filename": "SUPIR-v0F_fp16.safetensors", "url": "https://huggingface.co/Kijai/SUPIR_pruned/resolve/main/SUPIR-v0F_fp16.safetensors", "size": "2.66GB"}, {"name": "Kijai/SUPIR-v0Q_fp16.safetensors (pruned)", "type": "checkpoint", "base": "SUPIR", "save_path": "checkpoints/SUPIR", "description": "SUPIR checkpoint model", "reference": "https://huggingface.co/Kijai/SUPIR_pruned/tree/main", "filename": "SUPIR-v0Q_fp16.safetensors", "url": "https://huggingface.co/Kijai/SUPIR_pruned/resolve/main/SUPIR-v0Q_fp16.safetensors", "size": "2.66GB"}, {"name": "RAM", "type": "RAM", "base": "RAM", "save_path": "rams", "description": "RAM Recognize Anything Model", "reference": "https://huggingface.co/xinyu1205/recognize_anything_model", "filename": "ram_swin_large_14m.pth", "url": "https://huggingface.co/xinyu1205/recognize_anything_model/resolve/main/ram_swin_large_14m.pth", "size": "5.63GB"}, {"name": "RAM++", "type": "RAM", "base": "RAM", "save_path": "rams", "description": "RAM++ Recognize Anything Model", "reference": "https://huggingface.co/xinyu1205/recognize-anything-plus-model", "filename": "ram_plus_swin_large_14m.pth", "url": "https://huggingface.co/xinyu1205/recognize-anything-plus-model/resolve/main/ram_plus_swin_large_14m.pth", "size": "3.01GB"}, {"name": "tag2text", "type": "RAM", "base": "RAM", "save_path": "rams", "description": "tag2text Recognize Anything Model", "reference": "https://huggingface.co/xinyu1205/recognize_anything_model", "filename": "tag2text_swin_14m.pth", "url": "https://huggingface.co/xinyu1205/recognize_anything_model/resolve/main/tag2text_swin_14m.pth", "size": "4.48GB"}, {"name": "Zero123 3D object Model", "type": "zero123", "base": "zero123", "save_path": "checkpoints/zero123", "description": "model that been trained on 10M+ 3D objects from Objaverse-XL, used for generated rotated CamView", "reference": "https://objaverse.allenai.org/docs/zero123-xl/", "filename": "zero123-xl.ckpt", "url": "https://huggingface.co/kealiu/zero123-xl/resolve/main/zero123-xl.ckpt", "size": "15.5GB"}, {"name": "Zero123 3D object Model", "type": "zero123", "base": "zero123", "save_path": "checkpoints/zero123", "description": "Stable Zero123 is a model for view-conditioned image generation based on [a/Zero123](https://github.com/cvlab-columbia/zero123).", "reference": "https://huggingface.co/stabilityai/stable-zero123", "filename": "stable_zero123.ckpt", "url": "https://huggingface.co/stabilityai/stable-zero123/resolve/main/stable_zero123.ckpt", "size": "8.58GB"}, {"name": "Zero123 3D object Model", "type": "zero123", "base": "zero123", "save_path": "checkpoints/zero123", "description": "Zero123 original checkpoints in 105000 steps.", "reference": "https://huggingface.co/cvlab/zero123-weights", "filename": "zero123-105000.ckpt", "url": "https://huggingface.co/cvlab/zero123-weights/resolve/main/105000.ckpt", "size": "15.5GB"}, {"name": "Zero123 3D object Model", "type": "zero123", "base": "zero123", "save_path": "checkpoints/zero123", "description": "Zero123 original checkpoints in 165000 steps.", "reference": "https://huggingface.co/cvlab/zero123-weights", "filename": "zero123-165000.ckpt", "url": "https://huggingface.co/cvlab/zero123-weights/resolve/main/165000.ckpt", "size": "15.5GB"}, {"name": "InstantID/ip-adapter", "type": "instantid", "base": "SDXL", "save_path": "instantid/SDXL", "description": "ip-adapter model for cubiq/InstantID", "reference": "https://huggingface.co/InstantX/InstantID", "filename": "ip-adapter.bin", "url": "https://huggingface.co/InstantX/InstantID/resolve/main/ip-adapter.bin", "size": "1.69GB"}, {"name": "InstantID/ControlNet", "type": "controlnet", "base": "SDXL", "save_path": "controlnet/SDXL/instantid", "description": "instantid controlnet model for cubiq/InstantID", "reference": "https://huggingface.co/InstantX/InstantID", "filename": "diffusion_pytorch_model.safetensors", "url": "https://huggingface.co/InstantX/InstantID/resolve/main/ControlNetModel/diffusion_pytorch_model.safetensors", "size": "2.50GB"}, {"name": "MonsterMMORPG/insightface (for InstantID)", "type": "insightface", "base": "SDXL", "save_path": "insightface/models", "description": "MonsterMMORPG insightface model for cubiq/InstantID", "reference": "https://huggingface.co/MonsterMMORPG/tools/tree/main", "filename": "antelopev2.zip", "url": "https://huggingface.co/MonsterMMORPG/tools/resolve/main/antelopev2.zip", "size": "360.7MB"}, {"name": "IC-Light/fc", "type": "IC-Light", "base": "SD1.5", "save_path": "diffusion_models/IC-Light", "description": "The default relighting model, conditioned on text and foreground", "reference": "https://huggingface.co/lllyasviel/ic-light", "filename": "iclight_sd15_fc.safetensors", "url": "https://huggingface.co/lllyasviel/ic-light/resolve/main/iclight_sd15_fc.safetensors", "size": "1.72GB"}, {"name": "IC-Light/fbc", "type": "IC-Light", "base": "SD1.5", "save_path": "diffusion_models/IC-Light", "description": "Relighting model conditioned with text, foreground, and background", "reference": "https://huggingface.co/lllyasviel/ic-light", "filename": "iclight_sd15_fbc.safetensors", "url": "https://huggingface.co/lllyasviel/ic-light/resolve/main/iclight_sd15_fbc.safetensors", "size": "1.72GB"}, {"name": "IC-Light/fcon", "type": "IC-Light", "base": "SD1.5", "save_path": "diffusion_models/IC-Light", "description": "Same as iclight_sd15_fc.safetensors, but trained with offset noise", "reference": "https://huggingface.co/lllyasviel/ic-light", "filename": "iclight_sd15_fcon.safetensors", "url": "https://huggingface.co/lllyasviel/ic-light/resolve/main/iclight_sd15_fcon.safetensors", "size": "1.72GB"}, {"name": "TTPlanet/TTPLanet_SDXL_Controlnet_Tile_Realistic v2 (fp16)", "type": "controlnet", "base": "SDXL", "save_path": "controlnet/SDXL", "description": "Controlnet SDXL Tile model realistic version.", "reference": "https://huggingface.co/TTPlanet/TTPLanet_SDXL_Controlnet_Tile_Realistic", "filename": "TTPLANET_Controlnet_Tile_realistic_v2_fp16.safetensors", "url": "https://huggingface.co/TTPlanet/TTPLanet_SDXL_Controlnet_Tile_Realistic/resolve/main/TTPLANET_Controlnet_Tile_realistic_v2_fp16.safetensors", "size": "2.50GB"}, {"name": "TTPlanet/TTPLanet_SDXL_Controlnet_Tile_Realistic v2 (rank256)", "type": "controlnet", "base": "SDXL", "save_path": "controlnet/SDXL", "description": "Controlnet SDXL Tile model realistic version.", "reference": "https://huggingface.co/TTPlanet/TTPLanet_SDXL_Controlnet_Tile_Realistic", "filename": "TTPLANET_Controlnet_Tile_realistic_v2_rank256.safetensors", "url": "https://huggingface.co/TTPlanet/TTPLanet_SDXL_Controlnet_Tile_Realistic/resolve/main/TTPLANET_Controlnet_Tile_realistic_v2_rank256.safetensors", "size": "774.4MB"}, {"name": "ViperYX/RGT_x2.pth", "type": "RGT", "base": "RGT", "save_path": "RGT/RGT", "description": "RGT x2 upscale model for ComfyUI-RGT", "reference": "https://huggingface.co/ViperYX/RGT/tree/main", "filename": "RGT_x2.pth", "url": "https://huggingface.co/ViperYX/RGT/resolve/main/RGT/RGT_x2.pth", "size": "179.8MB"}, {"name": "ViperYX/RGT_x3.pth", "type": "RGT", "base": "RGT", "save_path": "RGT/RGT", "description": "RGT x3 upscale model for ComfyUI-RGT", "reference": "https://huggingface.co/ViperYX/RGT/tree/main", "filename": "RGT_x3.pth", "url": "https://huggingface.co/ViperYX/RGT/resolve/main/RGT/RGT_x3.pth", "size": "180.5MB"}, {"name": "ViperYX/RGT_x4.pth", "type": "RGT", "base": "RGT", "save_path": "RGT/RGT", "description": "RGT_S x4 upscale model for ComfyUI-RGT", "reference": "https://huggingface.co/ViperYX/RGT/tree/main", "filename": "RGT_x4.pth", "url": "https://huggingface.co/ViperYX/RGT/resolve/main/RGT/RGT_x4.pth", "size": "180.4MB"}, {"name": "ViperYX/RGT_S_x2.pth", "type": "RGT", "base": "RGT", "save_path": "RGT/RGT_S", "description": "RGT_S x2 upscale model for ComfyUI-RGT", "reference": "https://huggingface.co/ViperYX/RGT/tree/main", "filename": "RGT_S_x2.pth", "url": "https://huggingface.co/ViperYX/RGT/resolve/main/RGT_S/RGT_S_x2.pth", "size": "135.4MB"}, {"name": "ViperYX/RGT_S_x3.pth", "type": "RGT", "base": "RGT", "save_path": "RGT/RGT_S", "description": "RGT_S x3 upscale model for ComfyUI-RGT", "reference": "https://huggingface.co/ViperYX/RGT/tree/main", "filename": "RGT_S_x3.pth", "url": "https://huggingface.co/ViperYX/RGT/resolve/main/RGT_S/RGT_S_x3.pth", "size": "136.1MB"}, {"name": "ViperYX/RGT_S_x4.pth", "type": "RGT", "base": "RGT", "save_path": "RGT/RGT_S", "description": "RGT_S x4 upscale model for ComfyUI-RGT", "reference": "https://huggingface.co/ViperYX/RGT/tree/main", "filename": "RGT_S_x4.pth", "url": "https://huggingface.co/ViperYX/RGT/resolve/main/RGT_S/RGT_S_x4.pth", "size": "136.0MB"}, {"name": "InstantX/FLUX.1-dev Controlnet (Union)", "type": "controlnet", "base": "FLUX.1", "save_path": "controlnet/FLUX.1/InstantX-FLUX1-Dev-Union", "description": "FLUX.1 [Dev] Union Controlnet. Supports Canny, <PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, Gray Low Quality.", "reference": "https://huggingface.co/InstantX/FLUX.1-dev-Controlnet-Union", "filename": "diffusion_pytorch_model.safetensors", "url": "https://huggingface.co/InstantX/FLUX.1-dev-Controlnet-Union/resolve/main/diffusion_pytorch_model.safetensors", "size": "6.6GB"}, {"name": "InstantX/FLUX.1-dev-IP-Adapter", "type": "IP-Adapter", "base": "FLUX.1", "save_path": "ipadapter-flux", "description": "FLUX.1-dev-IP-Adapter", "reference": "https://huggingface.co/InstantX/FLUX.1-dev-IP-Adapter", "filename": "ip-adapter.bin", "url": "https://huggingface.co/InstantX/FLUX.1-dev-IP-Adapter/resolve/main/ip-adapter.bin", "size": "5.29GB"}, {"name": "Shakker-Labs/FLUX.1-dev-ControlNet-Union-Pro", "type": "controlnet", "base": "FLUX.1", "save_path": "controlnet/FLUX.1/Shakker-Labs-ControlNet-Union-Pro", "description": "FLUX.1 [Dev] Union Controlnet. Supports <PERSON>ny, <PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, <PERSON>, Low Quality", "reference": "https://huggingface.co/Shakker-Labs/FLUX.1-dev-ControlNet-Union-Pro", "filename": "diffusion_pytorch_model.safetensors", "url": "https://huggingface.co/Shakker-Labs/FLUX.1-dev-ControlNet-Union-Pro/resolve/main/diffusion_pytorch_model.safetensors", "size": "6.6GB"}, {"name": "Shakker-Labs/FLUX.1-dev-ControlNet-Union-Pro (fp8_e4m3fn) by <PERSON><PERSON><PERSON>", "type": "controlnet", "base": "FLUX.1", "save_path": "controlnet/FLUX.1", "description": "FLUX.1 [Dev] Union Controlnet. Supports <PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, <PERSON>, Low Quality\nVersion quantized to fp8_e4m3fn by <PERSON><PERSON><PERSON>", "reference": "https://huggingface.co/Kijai/flux-fp8", "filename": "flux_shakker_labs_union_pro-fp8_e4m3fn.safetensors", "url": "https://huggingface.co/Kijai/flux-fp8/resolve/main/flux_shakker_labs_union_pro-fp8_e4m3fn.safetensors", "size": "3.3GB"}, {"name": "jasperai/FLUX.1-dev-Controlnet-Upscaler", "type": "controlnet", "base": "FLUX.1", "save_path": "controlnet/FLUX.1/jasperai-dev-Upscaler", "description": "This is Flux.1-dev ControlNet for low resolution images developed by Jasper research team.", "reference": "https://huggingface.co/jasperai/Flux.1-dev-Controlnet-Upscaler", "filename": "diffusion_pytorch_model.safetensors", "url": "https://huggingface.co/jasperai/Flux.1-dev-Controlnet-Upscaler/resolve/main/diffusion_pytorch_model.safetensors", "size": "3.58GB"}, {"name": "jasperai/FLUX.1-dev-Controlnet-Depth", "type": "controlnet", "base": "FLUX.1", "save_path": "controlnet/FLUX.1/jasperai-dev-Depth", "description": "This is Flux.1-dev ControlNet for Depth map developed by Jasper research team.", "reference": "https://huggingface.co/jasperai/Flux.1-dev-Controlnet-Depth", "filename": "diffusion_pytorch_model.safetensors", "url": "https://huggingface.co/jasperai/Flux.1-dev-Controlnet-Depth/resolve/main/diffusion_pytorch_model.safetensors", "size": "3.58GB"}, {"name": "jasperai/Flux.1-dev-Controlnet-Surface-Normals", "type": "controlnet", "base": "FLUX.1", "save_path": "controlnet/FLUX.1/jasperai-dev-Surface-Normals", "description": "This is Flux.1-dev ControlNet for Surface Normals map developed by Jasper research team.", "reference": "https://huggingface.co/jasperai/Flux.1-dev-Controlnet-Surface-Normals", "filename": "diffusion_pytorch_model.safetensors", "url": "https://huggingface.co/jasperai/Flux.1-dev-Controlnet-Surface-Normals/resolve/main/diffusion_pytorch_model.safetensors", "size": "3.58GB"}, {"name": "xinsir/ControlNet++: All-in-one ControlNet", "type": "controlnet", "base": "SDXL", "save_path": "controlnet/SDXL/controlnet-union-sdxl-1.0", "description": "All-in-one ControlNet for image generations and editing!", "reference": "https://huggingface.co/xinsir/controlnet-union-sdxl-1.0", "filename": "diffusion_pytorch_model.safetensors", "url": "https://huggingface.co/xinsir/controlnet-union-sdxl-1.0/resolve/main/diffusion_pytorch_model.safetensors", "size": "2.50GB"}, {"name": "xinsir/ControlNet++: All-in-one ControlNet (ProMax model)", "type": "controlnet", "base": "SDXL", "save_path": "controlnet/SDXL/controlnet-union-sdxl-1.0", "description": "All-in-one ControlNet for image generations and editing! (ProMax model)", "reference": "https://huggingface.co/xinsir/controlnet-union-sdxl-1.0", "filename": "diffusion_pytorch_model_promax.safetensors", "url": "https://huggingface.co/xinsir/controlnet-union-sdxl-1.0/resolve/main/diffusion_pytorch_model_promax.safetensors", "size": "2.50GB"}, {"name": "xinsir/Controlnet-Scribble-Sdxl-1.0", "type": "controlnet", "base": "SDXL", "save_path": "controlnet/SDXL/controlnet-scribble-sdxl-1.0", "description": "Controlnet SDXL Scribble model.", "reference": "https://huggingface.co/xinsir/controlnet-scribble-sdxl-1.0", "filename": "diffusion_pytorch_model.safetensors", "url": "https://huggingface.co/xinsir/controlnet-scribble-sdxl-1.0/resolve/main/diffusion_pytorch_model.safetensors", "size": "2.50GB"}, {"name": "xinsir/Controlnet-Canny-Sdxl-1.0 (V2)", "type": "controlnet", "base": "SDXL", "save_path": "controlnet/SDXL/controlnet-canny-sdxl-1.0", "description": "Controlnet SDXL Canny model.", "reference": "https://huggingface.co/xinsir/controlnet-canny-sdxl-1.0", "filename": "diffusion_pytorch_model_V2.safetensors", "url": "https://huggingface.co/xinsir/controlnet-canny-sdxl-1.0/resolve/main/diffusion_pytorch_model_V2.safetensors", "size": "2.50GB"}, {"name": "xinsir/Controlnet-Openpose-Sdxl-1.0", "type": "controlnet", "base": "SDXL", "save_path": "controlnet/SDXL/controlnet-openpose-sdxl-1.0", "description": "Controlnet SDXL Openpose model.", "reference": "https://huggingface.co/xinsir/controlnet-openpose-sdxl-1.0", "filename": "diffusion_pytorch_model.safetensors", "url": "https://huggingface.co/xinsir/controlnet-openpose-sdxl-1.0/resolve/main/diffusion_pytorch_model.safetensors", "size": "2.50GB"}, {"name": "xinsir/Controlnet-Openpose-Sdxl-1.0 (Ver. twins)", "type": "controlnet", "base": "SDXL", "save_path": "controlnet/SDXL/controlnet-openpose-sdxl-1.0", "description": "Controlnet SDXL Openpose model. (Ver. twins)", "reference": "https://huggingface.co/xinsir/controlnet-openpose-sdxl-1.0", "filename": "diffusion_pytorch_model_twins.safetensors", "url": "https://huggingface.co/xinsir/controlnet-openpose-sdxl-1.0/resolve/main/diffusion_pytorch_model_twins.safetensors", "size": "2.50GB"}, {"name": "xinsir/Controlnet-Scribble-Sdxl-1.0-Anime", "type": "controlnet", "base": "SDXL", "save_path": "controlnet/SDXL/controlnet-scribble-sdxl-1.0-anime", "description": "Controlnet SDXL Scribble model. (Ver. anime)", "reference": "https://huggingface.co/xinsir/anime-painter", "filename": "diffusion_pytorch_model.safetensors", "url": "https://huggingface.co/xinsir/anime-painter/resolve/main/diffusion_pytorch_model.safetensors", "size": "2.50GB"}, {"name": "xinsir/ControlNet Depth SDXL, support zoe, midias", "type": "controlnet", "base": "SDXL", "save_path": "controlnet/SDXL/controlnet-depth-sdxl-1.0", "description": "Controlnet SDXL Depth model.", "reference": "https://huggingface.co/xinsir/controlnet-depth-sdxl-1.0", "filename": "diffusion_pytorch_model.safetensors", "url": "https://huggingface.co/xinsir/controlnet-depth-sdxl-1.0/resolve/main/diffusion_pytorch_model.safetensors", "size": "2.50GB"}, {"name": "xinsir/ControlNet Tile SDXL", "type": "controlnet", "base": "SDXL", "save_path": "controlnet/SDXL/controlnet-tile-sdxl-1.0", "description": "Controlnet SDXL Tile model.", "reference": "https://huggingface.co/xinsir/controlnet-tile-sdxl-1.0", "filename": "diffusion_pytorch_model.safetensors", "url": "https://huggingface.co/xinsir/controlnet-tile-sdxl-1.0/resolve/main/diffusion_pytorch_model.safetensors", "size": "2.50GB"}, {"name": "InstantX/SD3-Controlnet-Canny", "type": "controlnet", "base": "SD3", "save_path": "controlnet/SD3/InstantX-Controlnet-Canny", "description": "Controlnet SD3 Canny model.", "reference": "https://huggingface.co/InstantX/SD3-Controlnet-Canny", "filename": "diffusion_pytorch_model.safetensors", "url": "https://huggingface.co/InstantX/SD3-Controlnet-Canny/resolve/main/diffusion_pytorch_model.safetensors", "size": "1.19GB"}, {"name": "InstantX/SD3-Controlnet-Pose", "type": "controlnet", "base": "SD3", "save_path": "controlnet/SD3/InstantX-Controlnet-Pose", "description": "Controlnet SD3 Pose model.", "reference": "https://huggingface.co/InstantX/SD3-Controlnet-Pose", "filename": "diffusion_pytorch_model.safetensors", "url": "https://huggingface.co/InstantX/SD3-Controlnet-Pose/resolve/main/diffusion_pytorch_model.safetensors", "size": "1.19GB"}, {"name": "InstantX/SD3-Controlnet-Tile", "type": "controlnet", "base": "SD3", "save_path": "controlnet/SD3/InstantX-Controlnet-Tile", "description": "Controlnet SD3 Tile model.", "reference": "https://huggingface.co/InstantX/SD3-Controlnet-Tile", "filename": "diffusion_pytorch_model.safetensors", "url": "https://huggingface.co/InstantX/SD3-Controlnet-Tile/resolve/main/diffusion_pytorch_model.safetensors", "size": "1.19GB"}, {"name": "stabilityai/SD3.5-Large-Controlnet-Blur", "type": "controlnet", "base": "SD3.5", "save_path": "controlnet/SD3.5", "description": "Blur Controlnet model for SD3.5 Large", "reference": "https://huggingface.co/stabilityai/stable-diffusion-3.5-controlnets", "filename": "sd3.5_large_controlnet_blur.safetensors", "url": "https://huggingface.co/stabilityai/stable-diffusion-3.5-controlnets/resolve/main/sd3.5_large_controlnet_blur.safetensors", "size": "8.65GB"}, {"name": "stabilityai/SD3.5-Large-Controlnet-Canny", "type": "controlnet", "base": "SD3.5", "save_path": "controlnet/SD3.5", "description": "Canny Controlnet model for SD3.5 Large", "reference": "https://huggingface.co/stabilityai/stable-diffusion-3.5-controlnets", "filename": "sd3.5_large_controlnet_canny.safetensors", "url": "https://huggingface.co/stabilityai/stable-diffusion-3.5-controlnets/resolve/main/sd3.5_large_controlnet_canny.safetensors", "size": "8.65GB"}, {"name": "stabilityai/SD3.5-Large-Controlnet-Depth", "type": "controlnet", "base": "SD3.5", "save_path": "controlnet/SD3.5", "description": "Depth Controlnet model for SD3.5 Large", "reference": "https://huggingface.co/stabilityai/stable-diffusion-3.5-controlnets", "filename": "sd3.5_large_controlnet_depth.safetensors", "url": "https://huggingface.co/stabilityai/stable-diffusion-3.5-controlnets/resolve/main/sd3.5_large_controlnet_depth.safetensors", "size": "8.65GB"}, {"name": "Kijai/ToonCrafter model checkpoint (interpolation fp16)", "type": "checkpoint", "base": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "save_path": "checkpoints/ToonCrafter", "description": "ToonCrafter checkpoint model for ComfyUI-DynamiCrafterWrapper", "reference": "https://huggingface.co/Kijai/DynamiCrafter_pruned", "filename": "tooncrafter_512_interp-fp16.safetensors", "url": "https://huggingface.co/Kijai/DynamiCrafter_pruned/resolve/main/tooncrafter_512_interp-fp16.safetensors", "size": "5.25GB"}, {"name": "CN-anytest_v4-marged.safetensors", "type": "controlnet", "base": "SDXL", "save_path": "controlnet/SDXL", "description": "AnyTest Controlnet. A model for style transfer.", "reference": "https://huggingface.co/2vXpSwA7/iroiro-lora/tree/main", "filename": "CN-anytest_v4-marged.safetensors", "url": "https://huggingface.co/2vXpSwA7/iroiro-lora/resolve/main/test_controlnet2/CN-anytest_v4-marged.safetensors", "size": "2.50GB"}, {"name": "CN-anytest_v4-marged_am_dim256.safetensors (dim256/Animagine)", "type": "controlnet", "base": "SDXL", "save_path": "controlnet/SDXL", "description": "AnyTest Controlnet Lora (dim256) for Animagine. A model for style transfer.", "reference": "https://huggingface.co/2vXpSwA7/iroiro-lora/tree/main", "filename": "CN-anytest_v4-marged_am_dim256.safetensors", "url": "https://huggingface.co/2vXpSwA7/iroiro-lora/resolve/main/test_controlnet2/CN-anytest_v4-marged_am_dim256.safetensors", "size": "774.4MB"}, {"name": "CN-anytest_v4-marged_am_dim128.safetensors (dim128/Animagine)", "type": "controlnet", "base": "SDXL", "save_path": "controlnet/SDXL", "description": "AnyTest Controlnet Lora (dim128) for Animagine. A model for style transfer.", "reference": "https://huggingface.co/2vXpSwA7/iroiro-lora/tree/main", "filename": "CN-anytest_v4-marged_am_dim128.safetensors", "url": "https://huggingface.co/2vXpSwA7/iroiro-lora/resolve/main/test_controlnet2/CN-anytest_v4-marged_am_dim128.safetensors", "size": "395.7MB"}, {"name": "CN-anytest_v4-marged_pn_dim256.safetensors (dim256/Pony)", "type": "controlnet", "base": "SDXL", "save_path": "controlnet/SDXL", "description": "AnyTest Controlnet Lora (dim256) for Pony. A model for style transfer.", "reference": "https://huggingface.co/2vXpSwA7/iroiro-lora/tree/main", "filename": "CN-anytest_v4-marged_pn_dim256.safetensors", "url": "https://huggingface.co/2vXpSwA7/iroiro-lora/resolve/main/test_controlnet2/CN-anytest_v4-marged_pn_dim256.safetensors", "size": "774.4MB"}, {"name": "CN-anytest_v4-marged_pn_dim128.safetensors (dim128/Pony)", "type": "controlnet", "base": "SDXL", "save_path": "controlnet/SDXL", "description": "AnyTest Controlnet Lora (dim128) for Pony. A model for style transfer.", "reference": "https://huggingface.co/2vXpSwA7/iroiro-lora/tree/main", "filename": "CN-anytest_v4-marged_pn_dim128.safetensors", "url": "https://huggingface.co/2vXpSwA7/iroiro-lora/resolve/main/test_controlnet2/CN-anytest_v4-marged_pn_dim128.safetensors", "size": "395.7MB"}, {"name": "CN-anytest_v3-50000_fp16.safetensors (fp16)", "type": "controlnet", "base": "SDXL", "save_path": "controlnet/SDXL", "description": "AnyTest Controlnet. A strict control model.", "reference": "https://huggingface.co/2vXpSwA7/iroiro-lora/tree/main", "filename": "CN-anytest_v3-50000_fp16.safetensors", "url": "https://huggingface.co/2vXpSwA7/iroiro-lora/resolve/main/test_controlnet2/CN-anytest_v3-50000_fp16.safetensors", "size": "2.50GB"}, {"name": "CN-anytest_v3-50000_am_dim256.safetensors (dim256/Animagine)", "type": "controlnet", "base": "SDXL", "save_path": "controlnet/SDXL", "description": "AnyTest Controlnet Lora (dim256) for Animagine. A strict control model.", "reference": "https://huggingface.co/2vXpSwA7/iroiro-lora/tree/main", "filename": "CN-anytest_v3-50000_am_dim256.safetensors", "url": "https://huggingface.co/2vXpSwA7/iroiro-lora/resolve/main/test_controlnet2/CN-anytest_v3-50000_am_dim256.safetensors", "size": "774.4MB"}, {"name": "CN-anytest_v3-50000_am_dim128.safetensors (dim128/Animagine)", "type": "controlnet", "base": "SDXL", "save_path": "controlnet/SDXL", "description": "AnyTest Controlnet Lora (dim128) for Animagine. A strict control model.", "reference": "https://huggingface.co/2vXpSwA7/iroiro-lora/tree/main", "filename": "CN-anytest_v3-50000_am_dim128.safetensors", "url": "https://huggingface.co/2vXpSwA7/iroiro-lora/resolve/main/test_controlnet2/CN-anytest_v3-50000_am_dim128.safetensors", "size": "395.7MB"}, {"name": "CN-anytest_v3-50000_pn_dim256.safetensors (dim256/Pony)", "type": "controlnet", "base": "SDXL", "save_path": "controlnet/SDXL", "description": "AnyTest Controlnet Lora (dim256) for Pony. A strict control model.", "reference": "https://huggingface.co/2vXpSwA7/iroiro-lora/tree/main", "filename": "CN-anytest_v3-50000_pn_dim256.safetensors", "url": "https://huggingface.co/2vXpSwA7/iroiro-lora/resolve/main/test_controlnet2/CN-anytest_v3-50000_pn_dim256.safetensors", "size": "774.4MB"}, {"name": "CN-anytest_v3-50000_pn_dim128.safetensors (dim128/Pony)", "type": "controlnet", "base": "SDXL", "save_path": "controlnet/SDXL", "description": "AnyTest Controlnet Lora (dim128) for Pony. A strict control model.", "reference": "https://huggingface.co/2vXpSwA7/iroiro-lora/tree/main", "filename": "CN-anytest_v3-50000_pn_dim128.safetensors", "url": "https://huggingface.co/2vXpSwA7/iroiro-lora/resolve/main/test_controlnet2/CN-anytest_v3-50000_pn_dim128.safetensors", "size": "395.7MB"}, {"name": "kijai/DepthAnythingV2 (vitb/fp16)", "type": "depthanything", "base": "depthanything", "save_path": "depthanything", "description": "DepthAnythingV2 model", "reference": "https://huggingface.co/Kijai/DepthAnythingV2-safetensors/tree/main", "filename": "depth_anything_v2_vitb_fp16.safetensors", "url": "https://huggingface.co/Kijai/DepthAnythingV2-safetensors/resolve/main/depth_anything_v2_vitb_fp16.safetensors", "size": "195.0MB"}, {"name": "kijai/DepthAnythingV2 (vitb/fp32)", "type": "depthanything", "base": "depthanything", "save_path": "depthanything", "description": "DepthAnythingV2 model", "reference": "https://huggingface.co/Kijai/DepthAnythingV2-safetensors/tree/main", "filename": "depth_anything_v2_vitb_fp32.safetensors", "url": "https://huggingface.co/Kijai/DepthAnythingV2-safetensors/resolve/main/depth_anything_v2_vitb_fp32.safetensors", "size": "389.9MB"}, {"name": "kijai/DepthAnythingV2 (vitl/fp16)", "type": "depthanything", "base": "depthanything", "save_path": "depthanything", "description": "DepthAnythingV2 model", "reference": "https://huggingface.co/Kijai/DepthAnythingV2-safetensors/tree/main", "filename": "depth_anything_v2_vitl_fp16.safetensors", "url": "https://huggingface.co/Kijai/DepthAnythingV2-safetensors/resolve/main/depth_anything_v2_vitl_fp16.safetensors", "size": "670.7MB"}, {"name": "kijai/DepthAnythingV2 (vitl/fp32)", "type": "depthanything", "base": "depthanything", "save_path": "depthanything", "description": "DepthAnythingV2 model", "reference": "https://huggingface.co/Kijai/DepthAnythingV2-safetensors/tree/main", "filename": "depth_anything_v2_vitl_fp32.safetensors", "url": "https://huggingface.co/Kijai/DepthAnythingV2-safetensors/resolve/main/depth_anything_v2_vitl_fp32.safetensors", "size": "1.34GB"}, {"name": "kijai/DepthAnythingV2 (vits/fp16)", "type": "depthanything", "base": "depthanything", "save_path": "depthanything", "description": "DepthAnythingV2 model", "reference": "https://huggingface.co/Kijai/DepthAnythingV2-safetensors/tree/main", "filename": "depth_anything_v2_vits_fp16.safetensors", "url": "https://huggingface.co/Kijai/DepthAnythingV2-safetensors/resolve/main/depth_anything_v2_vits_fp16.safetensors", "size": "49.6MB"}, {"name": "kijai/DepthAnythingV2 (vitb/fp32)", "type": "depthanything", "base": "depthanything", "save_path": "depthanything", "description": "DepthAnythingV2 model", "reference": "https://huggingface.co/Kijai/DepthAnythingV2-safetensors/tree/main", "filename": "depth_anything_v2_vits_fp32.safetensors", "url": "https://huggingface.co/Kijai/DepthAnythingV2-safetensors/resolve/main/depth_anything_v2_vits_fp32.safetensors", "size": "99.2MB"}, {"name": "PixArt-Sigma-XL-2-1024-MS.pth (checkpoint)", "type": "checkpoint", "base": "pixart-sigma", "save_path": "checkpoints/PixArt-Sigma", "description": "PixArt-Sigma Checkpoint model", "reference": "https://huggingface.co/PixArt-alpha/PixArt-Sigma/tree/main", "filename": "PixArt-Sigma-XL-2-1024-MS.pth", "url": "https://huggingface.co/PixArt-alpha/PixArt-Sigma/resolve/main/PixArt-Sigma-XL-2-1024-MS.pth", "size": "2.47GB"}, {"name": "PixArt-Sigma-XL-2-512-MS.safetensors (diffusion)", "type": "diffusion_model", "base": "pixart-sigma", "save_path": "diffusion_models/PixArt-Sigma", "description": "PixArt-Sigma Diffusion model", "reference": "https://huggingface.co/PixArt-alpha/PixArt-Sigma-XL-2-512-MS", "filename": "PixArt-Sigma-XL-2-512-MS.safetensors", "url": "https://huggingface.co/PixArt-alpha/PixArt-Sigma-XL-2-512-MS/resolve/main/transformer/diffusion_pytorch_model.safetensors", "size": "2.44GB"}, {"name": "PixArt-Sigma-XL-2-1024-MS.safetensors (diffusion)", "type": "diffusion_model", "base": "pixart-sigma", "save_path": "diffusion_models/PixArt-Sigma", "description": "PixArt-Sigma Diffusion model", "reference": "https://huggingface.co/PixArt-alpha/PixArt-Sigma-XL-2-1024-MS", "filename": "PixArt-Sigma-XL-2-1024-MS.safetensors", "url": "https://huggingface.co/PixArt-alpha/PixArt-Sigma-XL-2-1024-MS/resolve/main/transformer/diffusion_pytorch_model.safetensors", "size": "2.44GB"}, {"name": "PixArt-XL-2-1024-MS.safetensors (diffusion)", "type": "diffusion_model", "base": "pixart-alpha", "save_path": "diffusion_models/PixArt-Alpha", "description": "PixArt-Alpha Diffusion model", "reference": "https://huggingface.co/PixArt-alpha/PixArt-XL-2-1024-MS", "filename": "PixArt-XL-2-1024-MS.safetensors", "url": "https://huggingface.co/PixArt-alpha/PixArt-XL-2-1024-MS/resolve/main/transformer/diffusion_pytorch_model.safetensors", "size": "2.45GB"}, {"name": "hunyuan_dit_1.2.safetensors", "type": "checkpoint", "base": "Hunyuan-DiT", "save_path": "checkpoints/hunyuan_dit_comfyui", "description": "Different versions of HunyuanDIT packaged for ComfyUI use.", "reference": "https://huggingface.co/comfyanonymous/hunyuan_dit_comfyui", "filename": "hunyuan_dit_1.2.safetensors", "url": "https://huggingface.co/comfyanonymous/hunyuan_dit_comfyui/resolve/main/hunyuan_dit_1.2.safetensors", "size": "8.24GB"}, {"name": "hunyuan_dit_1.1.safetensors", "type": "checkpoint", "base": "Hunyuan-DiT", "save_path": "checkpoints/hunyuan_dit_comfyui", "description": "Different versions of HunyuanDIT packaged for ComfyUI use.", "reference": "https://huggingface.co/comfyanonymous/hunyuan_dit_comfyui", "filename": "hunyuan_dit_1.1.safetensors", "url": "https://huggingface.co/comfyanonymous/hunyuan_dit_comfyui/resolve/main/hunyuan_dit_1.1.safetensors", "size": "8.24GB"}, {"name": "hunyuan_dit_1.0.safetensors", "type": "checkpoint", "base": "Hunyuan-DiT", "save_path": "checkpoints/hunyuan_dit_comfyui", "description": "Different versions of HunyuanDIT packaged for ComfyUI use.", "reference": "https://huggingface.co/comfyanonymous/hunyuan_dit_comfyui", "filename": "hunyuan_dit_1.0.safetensors", "url": "https://huggingface.co/comfyanonymous/hunyuan_dit_comfyui/resolve/main/hunyuan_dit_1.0.safetensors", "size": "8.24GB"}, {"name": "Comfy-Org/hunyuan_video_t2v_720p_bf16.safetensors", "type": "diffusion_model", "base": "Hunyuan Video", "save_path": "diffusion_models/hunyuan_video", "description": "Huyuan Video diffusion model. repackaged version.", "reference": "https://huggingface.co/Comfy-Org/HunyuanVideo_repackaged", "filename": "hun<PERSON>_video_t2v_720p_bf16.safetensors", "url": "https://huggingface.co/Comfy-Org/HunyuanVideo_repackaged/resolve/main/split_files/diffusion_models/hunyuan_video_t2v_720p_bf16.safetensors", "size": "25.6GB"}, {"name": "Comfy-Org/hunyuan_video_vae_bf16.safetensors", "type": "VAE", "base": "Hunyuan Video", "save_path": "default", "description": "Huyuan Video VAE model. repackaged version.", "reference": "https://huggingface.co/Comfy-Org/HunyuanVideo_repackaged", "filename": "hunyuan_video_vae_bf16.safetensors", "url": "https://huggingface.co/Comfy-Org/HunyuanVideo_repackaged/resolve/main/split_files/vae/hunyuan_video_vae_bf16.safetensors", "size": "493MB"}, {"name": "Comfy-Org/hunyuan_video_image_to_video_720p_bf16.safetensors", "type": "diffusion_model", "base": "Hunyuan Video", "save_path": "diffusion_models/hunyuan_video", "description": "Huyuan Video Image2Video diffusion model. repackaged version.", "reference": "https://huggingface.co/Comfy-Org/HunyuanVideo_repackaged", "filename": "hunyuan_video_image_to_video_720p_bf16.safetensors", "url": "https://huggingface.co/Comfy-Org/HunyuanVideo_repackaged/resolve/main/split_files/diffusion_models/hunyuan_video_image_to_video_720p_bf16.safetensors", "size": "25.6GB"}, {"name": "Comfy-Org/llava_llama3_fp8_scaled.safetensors", "type": "clip", "base": "LLaVA-Llama-3", "save_path": "text_encoders", "description": "llava_llama3_fp8_scaled text encoder model. This is required for using Hunyuan Video.", "reference": "https://huggingface.co/Comfy-Org/HunyuanVideo_repackaged", "filename": "llava_llama3_fp8_scaled.safetensors", "url": "https://huggingface.co/Comfy-Org/HunyuanVideo_repackaged/resolve/main/split_files/text_encoders/llava_llama3_fp8_scaled.safetensors", "size": "9.09GB"}, {"name": "Comfy-Org/llava_llama3_fp16.safetensors", "type": "clip", "base": "LLaVA-Llama-3", "save_path": "text_encoders", "description": "llava_llama3_fp16 text encoder model. This is required for using Hunyuan Video.", "reference": "https://huggingface.co/Comfy-Org/HunyuanVideo_repackaged", "filename": "llava_llama3_fp16.safetensors", "url": "https://huggingface.co/Comfy-Org/HunyuanVideo_repackaged/resolve/main/split_files/text_encoders/llava_llama3_fp16.safetensors", "size": "16.1GB"}, {"name": "Comfy-Org/llava_llama3_vision.safetensors", "type": "clip_vision", "base": "LLaVA-Llama-3", "save_path": "text_encoders", "description": "llava_llama3_vision clip vison model. This is required for using Hunyuan Video Image2Video.", "reference": "https://huggingface.co/Comfy-Org/HunyuanVideo_repackaged", "filename": "llava_llama3_vision.safetensors", "url": "https://huggingface.co/Comfy-Org/HunyuanVideo_repackaged/resolve/main/split_files/clip_vision/llava_llama3_vision.safetensors", "size": "649MB"}, {"name": "Comfy-Org/omnigen2_fp16.safetensors", "type": "diffusion_model", "base": "OmniGen2", "save_path": "default", "description": "OmniGen2 diffusion model. This is required for using OmniGen2.", "reference": "https://huggingface.co/Comfy-Org/Omnigen2_ComfyUI_repackaged", "filename": "omnigen2_fp16.safetensors", "url": "https://huggingface.co/Comfy-Org/Omnigen2_ComfyUI_repackaged/resolve/main/split_files/diffusion_models/omnigen2_fp16.safetensors", "size": "7.93GB"}, {"name": "Comfy-Org/qwen_2.5_vl_fp16.safetensors", "type": "clip", "base": "qwen-2.5", "save_path": "default", "description": "text encoder for OmniGen2", "reference": "https://huggingface.co/Comfy-Org/Omnigen2_ComfyUI_repackaged", "filename": "qwen_2.5_vl_fp16.safetensors", "url": "https://huggingface.co/Comfy-Org/Omnigen2_ComfyUI_repackaged/resolve/main/split_files/text_encoders/qwen_2.5_vl_fp16.safetensors", "size": "7.51GB"}, {"name": "FLUX.1 [<PERSON><PERSON><PERSON>] Diffusion model", "type": "diffusion_model", "base": "FLUX.1", "save_path": "diffusion_models/FLUX1", "description": "FLUX.1 [<PERSON><PERSON><PERSON>] Diffusion model (a.k.a. FLUX.1 turbo model)[w/Due to the large size of the model, it is recommended to download it through a browser if possible.]", "reference": "https://huggingface.co/black-forest-labs/FLUX.1-schnell", "filename": "flux1-schnell.safetensors", "url": "https://huggingface.co/black-forest-labs/FLUX.1-schnell/resolve/main/flux1-schnell.safetensors", "size": "23.8GB"}, {"name": "FLUX.1 VAE model", "type": "VAE", "base": "FLUX.1", "save_path": "vae/FLUX1", "description": "FLUX.1 VAE model\nNOTE: This VAE model can also be used for image generation with OmniGen2.", "reference": "https://huggingface.co/black-forest-labs/FLUX.1-schnell", "filename": "ae.safetensors", "url": "https://huggingface.co/black-forest-labs/FLUX.1-schnell/resolve/main/ae.safetensors", "size": "335MB"}, {"name": "kijai/FLUX.1 [schnell] Diffusion model (float8_e4m3fn)", "type": "diffusion_model", "base": "FLUX.1", "save_path": "diffusion_models/FLUX1", "description": "FLUX.1 [<PERSON><PERSON><PERSON>] Diffusion model (float8_e4m3fn)", "reference": "https://huggingface.co/Kijai/flux-fp8", "filename": "flux1-schnell-fp8.safetensors", "url": "https://huggingface.co/Kijai/flux-fp8/resolve/main/flux1-schnell-fp8.safetensors", "size": "11.9GB"}, {"name": "FLUX.1 [Dev] Diffusion model (scaled fp8)", "type": "diffusion_model", "base": "FLUX.1", "save_path": "diffusion_models/FLUX1", "description": "FLUX.1 [Dev] Diffusion model (scaled fp8)[w/Due to the large size of the model, it is recommended to download it through a browser if possible.]", "reference": "https://huggingface.co/comfyanonymous/flux_dev_scaled_fp8_test", "filename": "flux_dev_fp8_scaled_diffusion_model.safetensors", "url": "https://huggingface.co/comfyanonymous/flux_dev_scaled_fp8_test/resolve/main/flux_dev_fp8_scaled_diffusion_model.safetensors", "size": "11.9GB"}, {"name": "kijai/FLUX.1 [dev] Diffusion model (float8_e4m3fn)", "type": "diffusion_model", "base": "FLUX.1", "save_path": "diffusion_models/FLUX1", "description": "FLUX.1 [dev] Diffusion model (float8_e4m3fn)", "reference": "https://huggingface.co/Kijai/flux-fp8", "filename": "flux1-dev-fp8.safetensors", "url": "https://huggingface.co/Kijai/flux-fp8/resolve/main/flux1-dev-fp8.safetensors", "size": "11.9GB"}, {"name": "Comfy Org/FLUX.1 [dev] Checkpoint model (fp8)", "type": "checkpoint", "base": "FLUX.1", "save_path": "checkpoints/FLUX1", "description": "FLUX.1 [dev] Checkpoint model (fp8)", "reference": "https://huggingface.co/Comfy-Org/flux1-dev/tree/main", "filename": "flux1-dev-fp8.safetensors", "url": "https://huggingface.co/Comfy-Org/flux1-dev/resolve/main/flux1-dev-fp8.safetensors", "size": "17.2GB"}, {"name": "Comfy Org/FLUX.1 [schnell] Checkpoint model (fp8)", "type": "checkpoint", "base": "FLUX.1", "save_path": "checkpoints/FLUX1", "description": "FLUX.1 [schnell] Checkpoint model (fp8)", "reference": "https://huggingface.co/Comfy-Org/flux1-dev/tree/main", "filename": "flux1-schnell-fp8.safetensors", "url": "https://huggingface.co/Comfy-Org/flux1-schnell/resolve/main/flux1-schnell-fp8.safetensors", "size": "17.2GB"}, {"name": "city96/flux1-dev-F16.gguf", "type": "diffusion_model", "base": "FLUX.1", "save_path": "diffusion_models/FLUX1", "description": "FLUX.1 [Dev] Diffusion model (f16/.gguf)", "reference": "https://huggingface.co/city96/FLUX.1-dev-gguf", "filename": "flux1-dev-F16.gguf", "url": "https://huggingface.co/city96/FLUX.1-dev-gguf/resolve/main/flux1-dev-F16.gguf", "size": "23.8GB"}, {"name": "city96/flux1-dev-Q2_K.gguf", "type": "diffusion_model", "base": "FLUX.1", "save_path": "diffusion_models/FLUX1", "description": "FLUX.1 [Dev] Diffusion model (Q2_K/.gguf)", "reference": "https://huggingface.co/city96/FLUX.1-dev-gguf", "filename": "flux1-dev-Q2_K.gguf", "url": "https://huggingface.co/city96/FLUX.1-dev-gguf/resolve/main/flux1-dev-Q2_K.gguf", "size": "4.03GB"}, {"name": "city96/flux1-dev-Q3_K_S.gguf", "type": "diffusion_model", "base": "FLUX.1", "save_path": "diffusion_models/FLUX1", "description": "FLUX.1 [Dev] Diffusion model (Q3_K_S/.gguf)", "reference": "https://huggingface.co/city96/FLUX.1-dev-gguf", "filename": "flux1-dev-Q3_K_S.gguf", "url": "https://huggingface.co/city96/FLUX.1-dev-gguf/resolve/main/flux1-dev-Q3_K_S.gguf", "size": "5.23GB"}, {"name": "city96/flux1-dev-Q4_0.gguf", "type": "diffusion_model", "base": "FLUX.1", "save_path": "diffusion_models/FLUX1", "description": "FLUX.1 [Dev] Diffusion model (Q4_0/.gguf)", "reference": "https://huggingface.co/city96/FLUX.1-dev-gguf", "filename": "flux1-dev-Q4_0.gguf", "url": "https://huggingface.co/city96/FLUX.1-dev-gguf/resolve/main/flux1-dev-Q4_0.gguf", "size": "6.79GB"}, {"name": "city96/flux1-dev-Q4_1.gguf", "type": "diffusion_model", "base": "FLUX.1", "save_path": "diffusion_models/FLUX1", "description": "FLUX.1 [Dev] Diffusion model (Q4_1/.gguf)", "reference": "https://huggingface.co/city96/FLUX.1-dev-gguf", "filename": "flux1-dev-Q4_1.gguf", "url": "https://huggingface.co/city96/FLUX.1-dev-gguf/resolve/main/flux1-dev-Q4_1.gguf", "size": "7.53GB"}, {"name": "city96/flux1-dev-Q4_K_S.gguf", "type": "diffusion_model", "base": "FLUX.1", "save_path": "diffusion_models/FLUX1", "description": "FLUX.1 [Dev] Diffusion model (Q4_K_S/.gguf)", "reference": "https://huggingface.co/city96/FLUX.1-dev-gguf", "filename": "flux1-dev-Q4_K_S.gguf", "url": "https://huggingface.co/city96/FLUX.1-dev-gguf/resolve/main/flux1-dev-Q4_K_S.gguf", "size": "6.81GB"}, {"name": "city96/flux1-dev-Q5_0.gguf", "type": "diffusion_model", "base": "FLUX.1", "save_path": "diffusion_models/FLUX1", "description": "FLUX.1 [Dev] Diffusion model (Q5_0/.gguf)", "reference": "https://huggingface.co/city96/FLUX.1-dev-gguf", "filename": "flux1-dev-Q5_0.gguf", "url": "https://huggingface.co/city96/FLUX.1-dev-gguf/resolve/main/flux1-dev-Q5_0.gguf", "size": "8.27GB"}, {"name": "city96/flux1-dev-Q5_1.gguf", "type": "diffusion_model", "base": "FLUX.1", "save_path": "diffusion_models/FLUX1", "description": "FLUX.1 [Dev] Diffusion model (Q5_1/.gguf)", "reference": "https://huggingface.co/city96/FLUX.1-dev-gguf", "filename": "flux1-dev-Q5_1.gguf", "url": "https://huggingface.co/city96/FLUX.1-dev-gguf/resolve/main/flux1-dev-Q5_1.gguf", "size": "9.01GB"}, {"name": "city96/flux1-dev-Q5_K_S.gguf", "type": "diffusion_model", "base": "FLUX.1", "save_path": "diffusion_models/FLUX1", "description": "FLUX.1 [Dev] Diffusion model (Q5_K_S/.gguf)", "reference": "https://huggingface.co/city96/FLUX.1-dev-gguf", "filename": "flux1-dev-Q5_K_S.gguf", "url": "https://huggingface.co/city96/FLUX.1-dev-gguf/resolve/main/flux1-dev-Q5_K_S.gguf", "size": "8.29GB"}, {"name": "city96/flux1-dev-Q6_K.gguf", "type": "diffusion_model", "base": "FLUX.1", "save_path": "diffusion_models/FLUX1", "description": "FLUX.1 [Dev] Diffusion model (Q6_K/.gguf)", "reference": "https://huggingface.co/city96/FLUX.1-dev-gguf", "filename": "flux1-dev-Q6_K.gguf", "url": "https://huggingface.co/city96/FLUX.1-dev-gguf/resolve/main/flux1-dev-Q6_K.gguf", "size": "9.86GB"}, {"name": "city96/flux1-dev-Q8_0.gguf", "type": "diffusion_model", "base": "FLUX.1", "save_path": "diffusion_models/FLUX1", "description": "FLUX.1 [Dev] Diffusion model (Q8_0/.gguf)", "reference": "https://huggingface.co/city96/FLUX.1-dev-gguf", "filename": "flux1-dev-Q8_0.gguf", "url": "https://huggingface.co/city96/FLUX.1-dev-gguf/resolve/main/flux1-dev-Q8_0.gguf", "size": "12.7GB"}, {"name": "city96/flux1-schnell-F16.gguf", "type": "diffusion_model", "base": "FLUX.1", "save_path": "diffusion_models/FLUX1", "description": "FLUX.1 [Dev] Diffusion model (f16/.gguf)", "reference": "https://huggingface.co/city96/FLUX.1-schnell-gguf", "filename": "flux1-s<PERSON><PERSON>-F16.gguf", "url": "https://huggingface.co/city96/FLUX.1-schnell-gguf/resolve/main/flux1-schnell-F16.gguf", "size": "23.8GB"}, {"name": "city96/flux1-schnell-Q2_K.gguf", "type": "diffusion_model", "base": "FLUX.1", "save_path": "diffusion_models/FLUX1", "description": "FLUX.1 [Dev] Diffusion model (Q2_K/.gguf)", "reference": "https://huggingface.co/city96/FLUX.1-schnell-gguf", "filename": "flux1-schnell-Q2_<PERSON>.gguf", "url": "https://huggingface.co/city96/FLUX.1-schnell-gguf/resolve/main/flux1-schnell-Q2_K.gguf", "size": "4.01GB"}, {"name": "city96/flux1-schnell-Q3_K_S.gguf", "type": "diffusion_model", "base": "FLUX.1", "save_path": "diffusion_models/FLUX1", "description": "FLUX.1 [Dev] Diffusion model (Q3_K_S/.gguf)", "reference": "https://huggingface.co/city96/FLUX.1-schnell-gguf", "filename": "flux1-schnell-Q3_K_S.gguf", "url": "https://huggingface.co/city96/FLUX.1-schnell-gguf/resolve/main/flux1-schnell-Q3_K_S.gguf", "size": "5.21GB"}, {"name": "city96/flux1-schnell-Q4_0.gguf", "type": "diffusion_model", "base": "FLUX.1", "save_path": "diffusion_models/FLUX1", "description": "FLUX.1 [Dev] Diffusion model (Q4_0/.gguf)", "reference": "https://huggingface.co/city96/FLUX.1-schnell-gguf", "filename": "flux1-schnell-Q4_0.gguf", "url": "https://huggingface.co/city96/FLUX.1-schnell-gguf/resolve/main/flux1-schnell-Q4_0.gguf", "size": "6.77GB"}, {"name": "city96/flux1-schnell-Q4_1.gguf", "type": "diffusion_model", "base": "FLUX.1", "save_path": "diffusion_models/FLUX1", "description": "FLUX.1 [Dev] Diffusion model (Q4_1/.gguf)", "reference": "https://huggingface.co/city96/FLUX.1-schnell-gguf", "filename": "flux1-schnell-Q4_1.gguf", "url": "https://huggingface.co/city96/FLUX.1-schnell-gguf/resolve/main/flux1-schnell-Q4_1.gguf", "size": "7.51GB"}, {"name": "city96/flux1-sch<PERSON>-Q4_K_S.gguf", "type": "diffusion_model", "base": "FLUX.1", "save_path": "diffusion_models/FLUX1", "description": "FLUX.1 [Dev] Diffusion model (Q4_K_S/.gguf)", "reference": "https://huggingface.co/city96/FLUX.1-schnell-gguf", "filename": "flux1-schnell-Q4_K_S.gguf", "url": "https://huggingface.co/city96/FLUX.1-schnell-gguf/resolve/main/flux1-schnell-Q4_K_S.gguf", "size": "6.78GB"}, {"name": "city96/flux1-schnell-Q5_0.gguf", "type": "diffusion_model", "base": "FLUX.1", "save_path": "diffusion_models/FLUX1", "description": "FLUX.1 [Dev] Diffusion model (Q5_0/.gguf)", "reference": "https://huggingface.co/city96/FLUX.1-schnell-gguf", "filename": "flux1-schnell-Q5_0.gguf", "url": "https://huggingface.co/city96/FLUX.1-schnell-gguf/resolve/main/flux1-schnell-Q5_0.gguf", "size": "8.25GB"}, {"name": "city96/flux1-schnell-Q5_1.gguf", "type": "diffusion_model", "base": "FLUX.1", "save_path": "diffusion_models/FLUX1", "description": "FLUX.1 [Dev] Diffusion model (Q5_1/.gguf)", "reference": "https://huggingface.co/city96/FLUX.1-schnell-gguf", "filename": "flux1-schnell-Q5_1.gguf", "url": "https://huggingface.co/city96/FLUX.1-schnell-gguf/resolve/main/flux1-schnell-Q5_1.gguf", "size": "8.99GB"}, {"name": "city96/flux1-sch<PERSON>-Q5_K_S.gguf", "type": "diffusion_model", "base": "FLUX.1", "save_path": "diffusion_models/FLUX1", "description": "FLUX.1 [Dev] Diffusion model (Q5_K_S/.gguf)", "reference": "https://huggingface.co/city96/FLUX.1-schnell-gguf", "filename": "flux1-sch<PERSON>-Q5_K_S.gguf", "url": "https://huggingface.co/city96/FLUX.1-schnell-gguf/resolve/main/flux1-schnell-Q5_K_S.gguf", "size": "8.26GB"}, {"name": "city96/flux1-schnell-Q6_K.gguf", "type": "diffusion_model", "base": "FLUX.1", "save_path": "diffusion_models/FLUX1", "description": "FLUX.1 [Dev] Diffusion model (Q6_K/.gguf)", "reference": "https://huggingface.co/city96/FLUX.1-schnell-gguf", "filename": "flux1-sch<PERSON>-Q6_<PERSON>.gguf", "url": "https://huggingface.co/city96/FLUX.1-schnell-gguf/resolve/main/flux1-schnell-Q6_K.gguf", "size": "9.83GB"}, {"name": "city96/flux1-schnell-Q8_0.gguf", "type": "diffusion_model", "base": "FLUX.1", "save_path": "diffusion_models/FLUX1", "description": "FLUX.1 [Dev] Diffusion model (Q8_0/.gguf)", "reference": "https://huggingface.co/city96/FLUX.1-schnell-gguf", "filename": "flux1-schnell-Q8_0.gguf", "url": "https://huggingface.co/city96/FLUX.1-schnell-gguf/resolve/main/flux1-schnell-Q8_0.gguf", "size": "12.7GB"}, {"name": "ViT-L-14-TEXT-detail-improved-hiT-GmP-HF.safetensors [Long CLIP L]", "type": "clip", "base": "clip", "save_path": "text_encoders/long_clip", "description": "Greatly improved TEXT + Detail (as CLIP-L for Flux.1)", "reference": "https://huggingface.co/zer0int", "filename": "ViT-L-14-TEXT-detail-improved-hiT-GmP-HF.safetensors", "url": "https://huggingface.co/zer0int/CLIP-GmP-ViT-L-14/resolve/main/ViT-L-14-TEXT-detail-improved-hiT-GmP-HF.safetensors", "size": "931MB"}, {"name": "ViT-L-14-TEXT-detail-improved-hiT-GmP-HF.safetensors [Long CLIP L]", "type": "clip", "base": "clip", "save_path": "text_encoders/long_clip", "description": "Greatly improved TEXT + Detail (as CLIP-L for Flux.1)", "reference": "https://huggingface.co/zer0int", "filename": "ViT-L-14-TEXT-detail-improved-hiT-GmP-TE-only-HF.safetensors", "url": "https://huggingface.co/zer0int/CLIP-GmP-ViT-L-14/resolve/main/ViT-L-14-TEXT-detail-improved-hiT-GmP-TE-only-HF.safetensors", "size": "323MB"}, {"name": "Depth Pro model", "type": "depth-pro", "base": "depth-pro", "save_path": "depth/ml-depth-pro", "description": "Depth pro model for [a/ComfyUI-Depth-Pro](https://github.com/spacepxl/ComfyUI-Depth-Pro)", "reference": "https://huggingface.co/spacepxl/ml-depth-pro", "filename": "depth_pro.fp16.safetensors", "url": "https://huggingface.co/spacepxl/ml-depth-pro/resolve/main/depth_pro.fp16.safetensors", "size": "1.9GB"}, {"name": "kijai/lotus depth d model v1.1 (fp16)", "type": "diffusion_model", "base": "lotus", "save_path": "diffusion_models", "description": "lotus depth d model v1.1 (fp16). This model can be used in ComfyUI-Lotus custom nodes.", "reference": "https://huggingface.co/Kijai/lotus-comfyui", "filename": "lotus-depth-d-v-1-1-fp16.safetensors", "url": "https://huggingface.co/Kijai/lotus-comfyui/resolve/main/lotus-depth-d-v-1-1-fp16.safetensors", "size": "1.74GB"}, {"name": "kijai/lotus depth g model v1.0 (fp16)", "type": "diffusion_model", "base": "lotus", "save_path": "diffusion_models", "description": "lotus depth g model v1.0 (fp16). This model can be used in ComfyUI-Lotus custom nodes.", "reference": "https://huggingface.co/Kijai/lotus-comfyui", "filename": "lotus-depth-g-v1-0-fp16.safetensors", "url": "https://huggingface.co/Kijai/lotus-comfyui/resolve/main/lotus-depth-g-v1-0-fp16.safetensors", "size": "1.74GB"}, {"name": "kijai/lotus depth g model v1.0", "type": "diffusion_model", "base": "lotus", "save_path": "diffusion_models", "description": "lotus depth g model v1.0. This model can be used in ComfyUI-Lotus custom nodes.", "reference": "https://huggingface.co/Kijai/lotus-comfyui", "filename": "lotus-depth-g-v1-0.safetensors", "url": "https://huggingface.co/Kijai/lotus-comfyui/resolve/main/lotus-depth-g-v1-0.safetensors", "size": "3.47GB"}, {"name": "kijai/lotus normal d model v1.0 (fp16)", "type": "diffusion_model", "base": "lotus", "save_path": "diffusion_models", "description": "lotus normal d model v1.0 (fp16). This model can be used in ComfyUI-Lotus custom nodes.", "reference": "https://huggingface.co/Kijai/lotus-comfyui", "filename": "lotus-normal-d-v1-0-fp16.safetensors", "url": "https://huggingface.co/Kijai/lotus-comfyui/resolve/main/lotus-normal-d-v1-0-fp16.safetensors", "size": "1.74GB"}, {"name": "kijai/lotus normal d model v1.0", "type": "diffusion_model", "base": "lotus", "save_path": "diffusion_models", "description": "lotus normal d model v1.0. This model can be used in ComfyUI-Lotus custom nodes.", "reference": "https://huggingface.co/Kijai/lotus-comfyui", "filename": "lotus-normal-d-v1-0.safetensors", "url": "https://huggingface.co/Kijai/lotus-comfyui/resolve/main/lotus-normal-d-v1-0.safetensors", "size": "3.47GB"}, {"name": "kijai/lotus normal g model v1.0 (fp16)", "type": "diffusion_model", "base": "lotus", "save_path": "diffusion_models", "description": "lotus normal g model v1.0 (fp16). This model can be used in ComfyUI-Lotus custom nodes.", "reference": "https://huggingface.co/Kijai/lotus-comfyui", "filename": "lotus-normal-g-v1-0-fp16.safetensors", "url": "https://huggingface.co/Kijai/lotus-comfyui/resolve/main/lotus-normal-g-v1-0-fp16.safetensors", "size": "1.74GB"}, {"name": "kijai/lotus normal g model v1.0", "type": "diffusion_model", "base": "lotus", "save_path": "diffusion_models", "description": "lotus normal g model v1.0. This model can be used in ComfyUI-Lotus custom nodes.", "reference": "https://huggingface.co/Kijai/lotus-comfyui", "filename": "lotus-normal-g-v1-0.safetensors", "url": "https://huggingface.co/Kijai/lotus-comfyui/resolve/main/lotus-normal-g-v1-0.safetensors", "size": "3.47GB"}, {"name": "Kolors UNet model", "type": "diffusion_model", "base": "<PERSON><PERSON><PERSON>", "save_path": "diffusion_models/kolors", "description": "Kolors UNet model", "reference": "https://huggingface.co/<PERSON><PERSON>-<PERSON>lor<PERSON>/Kolors", "filename": "diffusion_pytorch_model.safetensors", "url": "https://huggingface.co/<PERSON><PERSON>-<PERSON>lor<PERSON>/Kolors/resolve/main/unet/diffusion_pytorch_model.safetensors", "size": "10.3GB"}, {"name": "Kolors UNet model (fp16)", "type": "diffusion_model", "base": "<PERSON><PERSON><PERSON>", "save_path": "diffusion_models/kolors", "description": "Kolors UNet model", "reference": "https://huggingface.co/<PERSON><PERSON>-<PERSON>lor<PERSON>/Kolors", "filename": "diffusion_pytorch_model.fp16.safetensors", "url": "https://huggingface.co/<PERSON><PERSON>-<PERSON>lor<PERSON>/Kolors/resolve/main/unet/diffusion_pytorch_model.fp16.safetensors", "size": "5.16GB"}, {"name": "Kijai/ChatGLM3 (4bit)", "type": "LLM", "base": "ChatGLM3", "save_path": "LLM", "description": "This is required for <PERSON><PERSON><PERSON>", "reference": "https://huggingface.co/Kijai/ChatGLM3-safetensors/tree/main", "filename": "chatglm3-4bit.safetensors", "url": "https://huggingface.co/Kijai/ChatGLM3-safetensors/resolve/main/chatglm3-4bit.safetensors", "size": "3.92GB"}, {"name": "Kijai/ChatGLM3 (8bit)", "type": "LLM", "base": "ChatGLM3", "save_path": "LLM", "description": "This is required for <PERSON><PERSON><PERSON>", "reference": "https://huggingface.co/Kijai/ChatGLM3-safetensors/tree/main", "filename": "chatglm3-8bit.safetensors", "url": "https://huggingface.co/Kijai/ChatGLM3-safetensors/resolve/main/chatglm3-8bit.safetensors", "size": "3.92GB"}, {"name": "Kijai/ChatGLM3 (16bit)", "type": "LLM", "base": "ChatGLM3", "save_path": "LLM", "description": "This is required for <PERSON><PERSON><PERSON>", "reference": "https://huggingface.co/Kijai/ChatGLM3-safetensors/tree/main", "filename": "chatglm3-fp16.safetensors", "url": "https://huggingface.co/Kijai/ChatGLM3-safetensors/resolve/main/chatglm3-fp16.safetensors", "size": "12.52GB"}, {"name": "pulid_flux_v0.9.1.safetensors", "type": "PuLID", "base": "FLUX.1", "save_path": "pu<PERSON>", "description": "This is required for PuLID (FLUX)", "reference": "https://huggingface.co/guozinan/PuLID", "filename": "pulid_flux_v0.9.1.safetensors", "url": "https://huggingface.co/guozinan/PuLID/resolve/main/pulid_flux_v0.9.1.safetensors", "size": "1.14GB"}, {"name": "pulid_v1.1.safetensors", "type": "PuLID", "base": "SDXL", "save_path": "pu<PERSON>", "description": "This is required for PuLID (SDXL)", "reference": "https://huggingface.co/guozinan/PuLID", "filename": "pulid_v1.1.safetensors", "url": "https://huggingface.co/guozinan/PuLID/resolve/main/pulid_v1.1.safetensors", "size": "984MB"}, {"name": "kijai/MoGe_ViT_L_fp16.safetensors", "type": "MoGe", "base": "MoGe", "save_path": "MoGe", "description": "Safetensors versions of [a/https://github.com/microsoft/MoGe](https://github.com/microsoft/MoGe)", "reference": "https://huggingface.co/Kijai/MoGe_safetensors", "filename": "MoGe_ViT_L_fp16.safetensors", "url": "https://huggingface.co/Kijai/MoGe_safetensors/resolve/main/MoGe_ViT_L_fp16.safetensors", "size": "628MB"}, {"name": "kijai/MoGe_ViT_L_fp16.safetensors", "type": "MoGe", "base": "MoGe", "save_path": "MoGe", "description": "Safetensors versions of [a/https://github.com/microsoft/MoGe](https://github.com/microsoft/MoGe)", "reference": "https://huggingface.co/Kijai/MoGe_safetensors", "filename": "MoGe_ViT_L_fp16.safetensors", "url": "https://huggingface.co/Kijai/MoGe_safetensors/resolve/main/MoGe_ViT_L_fp16.safetensors", "size": "1.26GB"}, {"name": "LTX-Video 2B v0.9 Checkpoint", "type": "checkpoint", "base": "LTX-Video", "save_path": "checkpoints/LTXV", "description": "LTX-Video is the first DiT-based video generation model capable of generating high-quality videos in real-time. It produces 24 FPS videos at a 768x512 resolution faster than they can be watched. Trained on a large-scale dataset of diverse videos, the model generates high-resolution videos with realistic and varied content.", "reference": "https://huggingface.co/Lightricks/LTX-Video", "filename": "ltx-video-2b-v0.9.safetensors", "url": "https://huggingface.co/Lightricks/LTX-Video/resolve/main/ltx-video-2b-v0.9.safetensors", "size": "9.37GB"}, {"name": "LTX-Video 2B v0.9.1 Checkpoint", "type": "checkpoint", "base": "LTX-Video", "save_path": "checkpoints/LTXV", "description": "LTX-Video is the first DiT-based video generation model capable of generating high-quality videos in real-time. It produces 24 FPS videos at a 768x512 resolution faster than they can be watched. Trained on a large-scale dataset of diverse videos, the model generates high-resolution videos with realistic and varied content.", "reference": "https://huggingface.co/Lightricks/LTX-Video", "filename": "ltx-video-2b-v0.9.1.safetensors", "url": "https://huggingface.co/Lightricks/LTX-Video/resolve/main/ltx-video-2b-v0.9.1.safetensors", "size": "5.72GB"}, {"name": "LTX-Video 2B v0.9.5 Checkpoint", "type": "checkpoint", "base": "LTX-Video", "save_path": "checkpoints/LTXV", "description": "LTX-Video is the first DiT-based video generation model capable of generating high-quality videos in real-time. It produces 24 FPS videos at a 768x512 resolution faster than they can be watched. Trained on a large-scale dataset of diverse videos, the model generates high-resolution videos with realistic and varied content.", "reference": "https://huggingface.co/Lightricks/LTX-Video", "filename": "ltx-video-2b-v0.9.5.safetensors", "url": "https://huggingface.co/Lightricks/LTX-Video/resolve/main/ltx-video-2b-v0.9.5.safetensors", "size": "6.34GB"}, {"name": "XLabs-AI/flux-canny-controlnet-v3.safetensors", "type": "controlnet", "base": "FLUX.1", "save_path": "xlabs/controlnets", "description": "ControlNet checkpoints for FLUX.1-dev model by Black Forest Labs.", "reference": "https://huggingface.co/XLabs-AI/flux-controlnet-collections", "filename": "flux-canny-controlnet-v3.safetensors", "url": "https://huggingface.co/XLabs-AI/flux-controlnet-collections/resolve/main/flux-canny-controlnet-v3.safetensors", "size": "1.49GB"}, {"name": "XLabs-AI/flux-depth-controlnet-v3.safetensors", "type": "controlnet", "base": "FLUX.1", "save_path": "xlabs/controlnets", "description": "ControlNet checkpoints for FLUX.1-dev model by Black Forest Labs.", "reference": "https://huggingface.co/XLabs-AI/flux-controlnet-collections", "filename": "flux-depth-controlnet-v3.safetensors", "url": "https://huggingface.co/XLabs-AI/flux-controlnet-collections/resolve/main/flux-depth-controlnet-v3.safetensors", "size": "1.49GB"}, {"name": "XLabs-AI/flux-hed-controlnet-v3.safetensors", "type": "controlnet", "base": "FLUX.1", "save_path": "xlabs/controlnets", "description": "ControlNet checkpoints for FLUX.1-dev model by Black Forest Labs.", "reference": "https://huggingface.co/XLabs-AI/flux-controlnet-collections", "filename": "flux-hed-controlnet-v3.safetensors", "url": "https://huggingface.co/XLabs-AI/flux-controlnet-collections/resolve/main/flux-hed-controlnet-v3.safetensors", "size": "1.49GB"}, {"name": "XLabs-AI/realism_lora.safetensors", "type": "lora", "base": "FLUX.1", "save_path": "xlabs/loras", "description": "A checkpoint with trained LoRAs for FLUX.1-dev model by Black Forest Labs", "reference": "https://huggingface.co/XLabs-AI/flux-lora-collection", "filename": "realism_lora.safetensors", "url": "https://huggingface.co/XLabs-AI/flux-lora-collection/resolve/main/realism_lora.safetensors", "size": "44.8MB"}, {"name": "XLabs-AI/art_lora.safetensors", "type": "lora", "base": "FLUX.1", "save_path": "xlabs/loras", "description": "A checkpoint with trained LoRAs for FLUX.1-dev model by Black Forest Labs", "reference": "https://huggingface.co/XLabs-AI/flux-lora-collection", "filename": "art_lora.safetensors", "url": "https://huggingface.co/XLabs-AI/flux-lora-collection/resolve/main/scenery_lora.safetensors", "size": "44.8MB"}, {"name": "XLabs-AI/mjv6_lora.safetensors", "type": "lora", "base": "FLUX.1", "save_path": "xlabs/loras", "description": "A checkpoint with trained LoRAs for FLUX.1-dev model by Black Forest Labs", "reference": "https://huggingface.co/XLabs-AI/flux-lora-collection", "filename": "mjv6_lora.safetensors", "url": "https://huggingface.co/XLabs-AI/flux-lora-collection/resolve/main/mjv6_lora.safetensors", "size": "44.8MB"}, {"name": "XLabs-AI/flux-ip-adapter", "type": "lora", "base": "FLUX.1", "save_path": "xlabs/ipadapters", "description": "A checkpoint with trained LoRAs for FLUX.1-dev model by Black Forest Labs", "reference": "https://huggingface.co/XLabs-AI/flux-ip-adapter", "filename": "ip_adapter.safetensors", "url": "https://huggingface.co/XLabs-AI/flux-ip-adapter/resolve/main/ip_adapter.safetensors", "size": "982MB"}, {"name": "efficient_sam_s_cpu.jit [ComfyUI-YoloWorld-EfficientSAM]", "type": "efficient_sam", "base": "efficient_sam", "save_path": "yolo_world", "description": "Install efficient_sam_s_cpu.jit into ComfyUI-YoloWorld-EfficientSAM", "reference": "https://huggingface.co/camenduru/YoloWorld-EfficientSAM/tree/main", "filename": "efficient_sam_s_cpu.jit", "url": "https://huggingface.co/camenduru/YoloWorld-EfficientSAM/resolve/main/efficient_sam_s_cpu.jit", "size": "106.0MB"}, {"name": "efficient_sam_s_gpu.jit [ComfyUI-YoloWorld-EfficientSAM]", "type": "efficient_sam", "base": "efficient_sam", "save_path": "yolo_world", "description": "Install efficient_sam_s_gpu.jit into ComfyUI-YoloWorld-EfficientSAM", "reference": "https://huggingface.co/camenduru/YoloWorld-EfficientSAM/tree/main", "filename": "efficient_sam_s_gpu.jit", "url": "https://huggingface.co/camenduru/YoloWorld-EfficientSAM/resolve/main/efficient_sam_s_gpu.jit", "size": "106.0MB"}, {"name": "TencentARC/CustomNet V1", "type": "CustomNet", "base": "CustomNet", "save_path": "checkpoints/customnet", "description": "CustomNet pretrained model for ComfyUI_CustomNet", "reference": "https://huggingface.co/TencentARC/CustomNet/tree/main", "filename": "customnet_v1.pt", "url": "https://huggingface.co/TencentARC/CustomNet/resolve/main/customnet_v1.pt", "size": "5.71GB"}, {"name": "TencentARC/CustomNet Inpaint V1", "type": "CustomNet", "base": "CustomNet", "save_path": "checkpoints/customnet", "description": "CustomNet Inpaint pretrained model for ComfyUI_CustomNet", "reference": "https://huggingface.co/TencentARC/CustomNet/tree/main", "filename": "customnet_inpaint_v1.pt", "url": "https://huggingface.co/TencentARC/CustomNet/resolve/main/customnet_inpaint_v1.pt", "size": "5.71GB"}, {"name": "deepseek-ai/Janus-Pro-1B", "type": "<PERSON><PERSON><PERSON><PERSON>", "base": "<PERSON><PERSON><PERSON><PERSON>", "save_path": "<PERSON><PERSON><PERSON><PERSON>", "description": "[SNAPSHOT] Janus-Pro-1B model.[w/You cannot download this item on ComfyUI-Manager versions below V3.18]", "reference": "https://huggingface.co/deepseek-ai/<PERSON><PERSON>-Pro-1B", "filename": "<huggingface>", "url": "deepseek-ai/Janus-Pro-1B", "size": "7.8GB"}, {"name": "deepseek-ai/Janus-Pro-7B", "type": "<PERSON><PERSON><PERSON><PERSON>", "base": "<PERSON><PERSON><PERSON><PERSON>", "save_path": "<PERSON><PERSON><PERSON><PERSON>", "description": "[SNAPSHOT] Janus-Pro-7B model.[w/You cannot download this item on ComfyUI-Manager versions below V3.18]", "reference": "https://huggingface.co/deepseek-ai/<PERSON><PERSON>-<PERSON>-7B", "filename": "<huggingface>", "url": "deepseek-ai/Janus-Pro-7B", "size": "14.85GB"}, {"name": "kolors/vae/diffusion_pytorch_model.fp16.safetensors", "type": "VAE", "base": "<PERSON><PERSON><PERSON>", "save_path": "vae/kolors", "description": "Kolors VAE", "reference": "https://huggingface.co/<PERSON><PERSON>-<PERSON>lor<PERSON>/Kolors", "filename": "diffusion_pytorch_model.fp16.safetensors", "url": "https://huggingface.co/<PERSON><PERSON>-Kolors/Kolors/resolve/main/vae/diffusion_pytorch_model.fp16.safetensors", "size": "167MB"}, {"name": "kolors/vae/diffusion_pytorch_model.safetensors", "type": "VAE", "base": "<PERSON><PERSON><PERSON>", "save_path": "vae/kolors", "description": "Kolors VAE", "reference": "https://huggingface.co/<PERSON><PERSON>-<PERSON>lor<PERSON>/Kolors", "filename": "diffusion_pytorch_model.safetensors", "url": "https://huggingface.co/<PERSON><PERSON>-Kolors/Kolors/resolve/main/vae/diffusion_pytorch_model.safetensors", "size": "335MB"}, {"name": "Comfy-Org/Wan2.1 i2v 480p 14B (bf16)", "type": "diffusion_model", "base": "Wan2.1", "save_path": "diffusion_models/Wan2.1", "description": "Wan2.1 difussion model for i2v 480p 14B (bf16)", "reference": "https://huggingface.co/Comfy-Org/Wan_2.1_ComfyUI_repackaged", "filename": "wan2.1_i2v_480p_14B_bf16.safetensors", "url": "https://huggingface.co/Comfy-Org/Wan_2.1_ComfyUI_repackaged/resolve/main/split_files/diffusion_models/wan2.1_i2v_480p_14B_bf16.safetensors", "size": "32.8GB"}, {"name": "Comfy-Org/Wan2.1 i2v 480p 14B (fp16)", "type": "diffusion_model", "base": "Wan2.1", "save_path": "diffusion_models/Wan2.1", "description": "Wan2.1 difussion model for i2v 480p 14B (fp16)", "reference": "https://huggingface.co/Comfy-Org/Wan_2.1_ComfyUI_repackaged", "filename": "wan2.1_i2v_480p_14B_fp16.safetensors", "url": "https://huggingface.co/Comfy-Org/Wan_2.1_ComfyUI_repackaged/resolve/main/split_files/diffusion_models/wan2.1_i2v_480p_14B_fp16.safetensors", "size": "32.8GB"}, {"name": "Comfy-Org/Wan2.1 i2v 480p 14B (fp8_e4m3fn)", "type": "diffusion_model", "base": "Wan2.1", "save_path": "diffusion_models/Wan2.1", "description": "Wan2.1 difussion model for i2v 480p 14B (fp8_e4m3fn)", "reference": "https://huggingface.co/Comfy-Org/Wan_2.1_ComfyUI_repackaged", "filename": "wan2.1_i2v_480p_14B_fp8_e4m3fn.safetensors", "url": "https://huggingface.co/Comfy-Org/Wan_2.1_ComfyUI_repackaged/resolve/main/split_files/diffusion_models/wan2.1_i2v_480p_14B_fp8_e4m3fn.safetensors", "size": "16.4GB"}, {"name": "Comfy-Org/Wan2.1 i2v 480p 14B (fp8_scaled)", "type": "diffusion_model", "base": "Wan2.1", "save_path": "diffusion_models/Wan2.1", "description": "Wan2.1 difussion model for i2v 480p 14B (fp8_scaled)", "reference": "https://huggingface.co/Comfy-Org/Wan_2.1_ComfyUI_repackaged", "filename": "wan2.1_i2v_480p_14B_fp8_scaled.safetensors", "url": "https://huggingface.co/Comfy-Org/Wan_2.1_ComfyUI_repackaged/resolve/main/split_files/diffusion_models/wan2.1_i2v_480p_14B_fp8_scaled.safetensors", "size": "16.4GB"}, {"name": "Comfy-Org/Wan2.1 i2v 720p 14B (bf16)", "type": "diffusion_model", "base": "Wan2.1", "save_path": "diffusion_models/Wan2.1", "description": "Wan2.1 difussion model for i2v 720p 14B (bf16)", "reference": "https://huggingface.co/Comfy-Org/Wan_2.1_ComfyUI_repackaged", "filename": "wan2.1_i2v_720p_14B_bf16.safetensors", "url": "https://huggingface.co/Comfy-Org/Wan_2.1_ComfyUI_repackaged/resolve/main/split_files/diffusion_models/wan2.1_i2v_720p_14B_bf16.safetensors", "size": "32.8GB"}, {"name": "Comfy-Org/Wan2.1 i2v 720p 14B (fp16)", "type": "diffusion_model", "base": "Wan2.1", "save_path": "diffusion_models/Wan2.1", "description": "Wan2.1 difussion model for i2v 720p 14B (fp16)", "reference": "https://huggingface.co/Comfy-Org/Wan_2.1_ComfyUI_repackaged", "filename": "wan2.1_i2v_720p_14B_fp16.safetensors", "url": "https://huggingface.co/Comfy-Org/Wan_2.1_ComfyUI_repackaged/resolve/main/split_files/diffusion_models/wan2.1_i2v_720p_14B_fp16.safetensors", "size": "32.8GB"}, {"name": "Comfy-Org/Wan2.1 i2v 720p 14B (fp8_e4m3fn)", "type": "diffusion_model", "base": "Wan2.1", "save_path": "diffusion_models/Wan2.1", "description": "Wan2.1 difussion model for i2v 720p 14B (fp8_e4m3fn)", "reference": "https://huggingface.co/Comfy-Org/Wan_2.1_ComfyUI_repackaged", "filename": "wan2.1_i2v_720p_14B_fp8_e4m3fn.safetensors", "url": "https://huggingface.co/Comfy-Org/Wan_2.1_ComfyUI_repackaged/resolve/main/split_files/diffusion_models/wan2.1_i2v_720p_14B_fp8_e4m3fn.safetensors", "size": "16.4GB"}, {"name": "Comfy-Org/Wan2.1 i2v 720p 14B (fp8_scaled)", "type": "diffusion_model", "base": "Wan2.1", "save_path": "diffusion_models/Wan2.1", "description": "Wan2.1 difussion model for i2v 720p 14B (fp8_scaled)", "reference": "https://huggingface.co/Comfy-Org/Wan_2.1_ComfyUI_repackaged", "filename": "wan2.1_i2v_720p_14B_fp8_scaled.safetensors", "url": "https://huggingface.co/Comfy-Org/Wan_2.1_ComfyUI_repackaged/resolve/main/split_files/diffusion_models/wan2.1_i2v_720p_14B_fp8_scaled.safetensors", "size": "16.4GB"}, {"name": "Comfy-Org/Wan2.1 t2v 1.3B (bf16)", "type": "diffusion_model", "base": "Wan2.1", "save_path": "diffusion_models/Wan2.1", "description": "Wan2.1 difussion model for t2v 1.3B (bf16)", "reference": "https://huggingface.co/Comfy-Org/Wan_2.1_ComfyUI_repackaged", "filename": "wan2.1_t2v_1.3B_bf16.safetensors", "url": "https://huggingface.co/Comfy-Org/Wan_2.1_ComfyUI_repackaged/resolve/main/split_files/diffusion_models/wan2.1_t2v_1.3B_bf16.safetensors", "size": "2.84GB"}, {"name": "Comfy-Org/Wan2.1 t2v 1.3B (fp16)", "type": "diffusion_model", "base": "Wan2.1", "save_path": "diffusion_models/Wan2.1", "description": "Wan2.1 difussion model for t2v 1.3B (fp16)", "reference": "https://huggingface.co/Comfy-Org/Wan_2.1_ComfyUI_repackaged", "filename": "wan2.1_t2v_1.3B_fp16.safetensors", "url": "https://huggingface.co/Comfy-Org/Wan_2.1_ComfyUI_repackaged/resolve/main/split_files/diffusion_models/wan2.1_t2v_1.3B_fp16.safetensors", "size": "2.84GB"}, {"name": "Comfy-Org/Wan2.1 t2v 14B (bf16)", "type": "diffusion_model", "base": "Wan2.1", "save_path": "diffusion_models/Wan2.1", "description": "Wan2.1 difussion model for t2v 14B (bf16)", "reference": "https://huggingface.co/Comfy-Org/Wan_2.1_ComfyUI_repackaged", "filename": "wan2.1_t2v_14B_bf16.safetensors", "url": "https://huggingface.co/Comfy-Org/Wan_2.1_ComfyUI_repackaged/resolve/main/split_files/diffusion_models/wan2.1_t2v_14B_bf16.safetensors", "size": "28.6GB"}, {"name": "Comfy-Org/Wan2.1 t2v 14B (fp16)", "type": "diffusion_model", "base": "Wan2.1", "save_path": "diffusion_models/Wan2.1", "description": "Wan2.1 difussion model for t2v 14B (fp16)", "reference": "https://huggingface.co/Comfy-Org/Wan_2.1_ComfyUI_repackaged", "filename": "wan2.1_t2v_14B_fp16.safetensors", "url": "https://huggingface.co/Comfy-Org/Wan_2.1_ComfyUI_repackaged/resolve/main/split_files/diffusion_models/wan2.1_t2v_14B_fp16.safetensors", "size": "28.6GB"}, {"name": "Comfy-Org/Wan2.1 t2v 14B (fp8_e4m3fn)", "type": "diffusion_model", "base": "Wan2.1", "save_path": "diffusion_models/Wan2.1", "description": "Wan2.1 difussion model for t2v 14B (fp8_e4m3fn)", "reference": "https://huggingface.co/Comfy-Org/Wan_2.1_ComfyUI_repackaged", "filename": "wan2.1_t2v_14B_fp8_e4m3fn.safetensors", "url": "https://huggingface.co/Comfy-Org/Wan_2.1_ComfyUI_repackaged/resolve/main/split_files/diffusion_models/wan2.1_t2v_14B_fp8_e4m3fn.safetensors", "size": "14.3GB"}, {"name": "Comfy-Org/Wan2.1 t2v 14B (fp8_scaled)", "type": "diffusion_model", "base": "Wan2.1", "save_path": "diffusion_models/Wan2.1", "description": "Wan2.1 difussion model for t2v 14B (fp8_scaled)", "reference": "https://huggingface.co/Comfy-Org/Wan_2.1_ComfyUI_repackaged", "filename": "wan2.1_t2v_14B_fp8_scaled.safetensors", "url": "https://huggingface.co/Comfy-Org/Wan_2.1_ComfyUI_repackaged/resolve/main/split_files/diffusion_models/wan2.1_t2v_14B_fp8_scaled.safetensors", "size": "14.3GB"}, {"name": "Comfy-Org/Wan2.1 VAE", "type": "vae", "base": "Wan2.1", "save_path": "vae", "description": "Wan2.1 VAE model", "reference": "https://huggingface.co/Comfy-Org/Wan_2.1_ComfyUI_repackaged", "filename": "wan_2.1_vae.safetensors", "url": "https://huggingface.co/Comfy-Org/Wan_2.1_ComfyUI_repackaged/resolve/main/split_files/vae/wan_2.1_vae.safetensors", "size": "254MB"}, {"name": "Comfy-Org/clip_vision_h.safetensors", "type": "clip_vision", "base": "clip_vision_h", "save_path": "clip_vision", "description": "clip_vision_h model for Wan2.1", "reference": "https://huggingface.co/Comfy-Org/Wan_2.1_ComfyUI_repackaged", "filename": "clip_vision_h.safetensors", "url": "https://huggingface.co/Comfy-Org/Wan_2.1_ComfyUI_repackaged/resolve/main/split_files/clip_vision/clip_vision_h.safetensors", "size": "1.26GB"}, {"name": "Comfy-Org/umt5_xxl_fp16.safetensors", "type": "clip", "base": "umt5_xxl", "save_path": "text_encoders", "description": "umt5_xxl_fp16 text encoder for Wan2.1", "reference": "https://huggingface.co/Comfy-Org/Wan_2.1_ComfyUI_repackaged", "filename": "umt5_xxl_fp16.safetensors", "url": "https://huggingface.co/Comfy-Org/Wan_2.1_ComfyUI_repackaged/resolve/main/split_files/text_encoders/umt5_xxl_fp16.safetensors", "size": "11.4GB"}, {"name": "Comfy-Org/umt5_xxl_fp8_e4m3fn_scaled.safetensors", "type": "clip", "base": "umt5_xxl", "save_path": "text_encoders", "description": "umt5_xxl_fp8_e4m3fn_scaled text encoder for Wan2.1", "reference": "https://huggingface.co/Comfy-Org/Wan_2.1_ComfyUI_repackaged", "filename": "umt5_xxl_fp8_e4m3fn_scaled.safetensors", "url": "https://huggingface.co/Comfy-Org/Wan_2.1_ComfyUI_repackaged/resolve/main/split_files/text_encoders/umt5_xxl_fp8_e4m3fn_scaled.safetensors", "size": "6.74GB"}, {"name": "lllyasviel/FramePackI2V_HY", "type": "FramePackI2V", "base": "FramePackI2V", "save_path": "diffusers/lllyasviel", "description": "[SNAPSHOT] This is the f1k1_x_g9_f1k1f2k2f16k4_td FramePack for HY. [w/You cannot download this item on ComfyUI-Manager versions below V3.18]", "reference": "https://huggingface.co/lllyasviel/FramePackI2V_HY", "filename": "<huggingface>", "url": "lllyasviel/FramePackI2V_HY", "size": "25.75GB"}, {"name": "LTX-Video Spatial Upscaler v0.9.7", "type": "upscale", "base": "upscale", "save_path": "default", "description": "Spatial upscaler model for LTX-Video. This model enhances the spatial resolution of generated videos.", "reference": "https://huggingface.co/Lightricks/LTX-Video", "filename": "ltxv-spatial-upscaler-0.9.7.safetensors", "url": "https://huggingface.co/Lightricks/LTX-Video/resolve/main/ltxv-spatial-upscaler-0.9.7.safetensors", "size": "505MB"}, {"name": "LTX-Video Temporal Upscaler v0.9.7", "type": "upscale", "base": "upscale", "save_path": "default", "description": "Temporal upscaler model for LTX-Video. This model enhances the temporal resolution and smoothness of generated videos.", "reference": "https://huggingface.co/Lightricks/LTX-Video", "filename": "ltxv-temporal-upscaler-0.9.7.safetensors", "url": "https://huggingface.co/Lightricks/LTX-Video/resolve/main/ltxv-temporal-upscaler-0.9.7.safetensors", "size": "524MB"}, {"name": "LTX-Video 13B v0.9.7", "type": "checkpoint", "base": "LTX-Video", "save_path": "checkpoints/LTXV", "description": "High-resolution quality LTX-Video 13B model.", "reference": "https://huggingface.co/Lightricks/LTX-Video", "filename": "ltxv-13b-0.9.7-dev.safetensors", "url": "https://huggingface.co/Lightricks/LTX-Video/resolve/main/ltxv-13b-0.9.7-dev.safetensors", "size": "28.6GB"}, {"name": "LTX-Video 13B FP8 v0.9.7", "type": "checkpoint", "base": "LTX-Video", "save_path": "checkpoints/LTXV", "description": "Quantized version of the LTX-Video 13B model, optimized for lower VRAM usage while maintaining high quality.", "reference": "https://huggingface.co/Lightricks/LTX-Video", "filename": "ltxv-13b-0.9.7-dev-fp8.safetensors", "url": "https://huggingface.co/Lightricks/LTX-Video/resolve/main/ltxv-13b-0.9.7-dev-fp8.safetensors", "size": "15.7GB"}, {"name": "LTX-Video 13B Distilled v0.9.7", "type": "checkpoint", "base": "LTX-Video", "save_path": "checkpoints/LTXV", "description": "Distilled version of the LTX-Video 13B model, providing improved efficiency while maintaining high-resolution quality.", "reference": "https://huggingface.co/Lightricks/LTX-Video", "filename": "ltxv-13b-0.9.7-distilled.safetensors", "url": "https://huggingface.co/Lightricks/LTX-Video/resolve/main/ltxv-13b-0.9.7-distilled.safetensors", "size": "28.6GB"}, {"name": "LTX-Video 13B Distilled FP8 v0.9.7", "type": "checkpoint", "base": "LTX-Video", "save_path": "checkpoints/LTXV", "description": "Quantized distilled version of the LTX-Video 13B model, optimized for even lower VRAM usage while maintaining quality.", "reference": "https://huggingface.co/Lightricks/LTX-Video", "filename": "ltxv-13b-0.9.7-distilled-fp8.safetensors", "url": "https://huggingface.co/Lightricks/LTX-Video/resolve/main/ltxv-13b-0.9.7-distilled-fp8.safetensors", "size": "15.7GB"}, {"name": "LTX-Video 13B Distilled LoRA v0.9.7", "type": "lora", "base": "LTX-Video", "save_path": "loras", "description": "A LoRA adapter that transforms the standard LTX-Video 13B model into a distilled version when loaded.", "reference": "https://huggingface.co/Lightricks/LTX-Video", "filename": "ltxv-13b-0.9.7-distilled-lora128.safetensors", "url": "https://huggingface.co/Lightricks/LTX-Video/resolve/main/ltxv-13b-0.9.7-distilled-lora128.safetensors", "size": "1.33GB"}, {"name": "Latent Bridge Matching for Image Relighting", "type": "diffusion_model", "base": "LBM", "save_path": "diffusion_models/LBM", "description": "Latent Bridge Matching (LBM) Relighting model", "reference": "https://huggingface.co/jasperai/LBM_relighting", "filename": "LBM_relighting.safetensors", "url": "https://huggingface.co/jasperai/LBM_relighting/resolve/main/model.safetensors", "size": "5.02GB"}]}