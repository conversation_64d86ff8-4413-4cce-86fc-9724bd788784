{"https://github.com/123jimin/ComfyUI-MobileForm": {"stars": 9, "last_update": "2025-04-06 13:36:29", "author_account_age_days": 5132}, "https://github.com/17Retoucher/ComfyUI_Fooocus": {"stars": 57, "last_update": "2024-02-24 07:33:29", "author_account_age_days": 538}, "https://github.com/1hew/ComfyUI-1hewNodes": {"stars": 3, "last_update": "2025-06-25 09:27:10", "author_account_age_days": 815}, "https://github.com/3dmindscapper/ComfyUI-PartField": {"stars": 30, "last_update": "2025-05-01 02:50:39", "author_account_age_days": 771}, "https://github.com/3dmindscapper/ComfyUI-Sam-Mesh": {"stars": 31, "last_update": "2025-05-07 12:42:13", "author_account_age_days": 771}, "https://github.com/*********/ComfyUI-SanMian-Nodes": {"stars": 27, "last_update": "2025-04-29 10:29:07", "author_account_age_days": 781}, "https://github.com/5x00/ComfyUI-Prompt-Plus": {"stars": 1, "last_update": "2025-01-08 15:54:08", "author_account_age_days": 1338}, "https://github.com/7BEII/Comfyui_PDuse": {"stars": 5, "last_update": "2025-06-16 13:56:22", "author_account_age_days": 180}, "https://github.com/A4P7J1N7M05OT/ComfyUI-ManualSigma": {"stars": 1, "last_update": "2024-12-30 10:45:23", "author_account_age_days": 839}, "https://github.com/A4P7J1N7M05OT/ComfyUI-VAELoaderSDXLmod": {"stars": 0, "last_update": "2025-06-23 23:42:45", "author_account_age_days": 839}, "https://github.com/A719689614/ComfyUI_AC_FUNV8Beta1": {"stars": 13, "last_update": "2025-06-17 02:04:04", "author_account_age_days": 681}, "https://github.com/AICodeFactory/ComfyUI-Viva": {"stars": 1, "last_update": "2025-05-15 08:07:12", "author_account_age_days": 435}, "https://github.com/AIFSH/ComfyUI-OpenDIT": {"stars": 0, "last_update": "2024-06-30 09:33:55", "author_account_age_days": 598}, "https://github.com/AIFSH/ComfyUI-ViViD": {"stars": 5, "last_update": "2024-06-25 08:16:53", "author_account_age_days": 598}, "https://github.com/AIFSH/HivisionIDPhotos-ComfyUI": {"stars": 149, "last_update": "2024-09-16 14:16:06", "author_account_age_days": 598}, "https://github.com/AIFSH/IMAGDressing-ComfyUI": {"stars": 61, "last_update": "2024-11-14 01:44:02", "author_account_age_days": 598}, "https://github.com/AIFSH/UltralightDigitalHuman-ComfyUI": {"stars": 128, "last_update": "2024-11-25 11:39:23", "author_account_age_days": 598}, "https://github.com/AIFSH/UtilNodes-ComfyUI": {"stars": 14, "last_update": "2024-12-19 06:44:25", "author_account_age_days": 598}, "https://github.com/AJO-reading/ComfyUI-AjoNodes": {"stars": 7, "last_update": "2025-06-16 08:10:10", "author_account_age_days": 216}, "https://github.com/ALatentPlace/ComfyUI_yanc": {"stars": 63, "last_update": "2025-01-22 14:44:17", "author_account_age_days": 1827}, "https://github.com/APZmedia/comfyui-textools": {"stars": 4, "last_update": "2024-09-02 09:17:36", "author_account_age_days": 2844}, "https://github.com/AhBumm/ComfyUI-Upscayl": {"stars": 0, "last_update": "2025-02-19 09:41:02", "author_account_age_days": 1176}, "https://github.com/AhBumm/ComfyUI_MangaLineExtraction-hf": {"stars": 0, "last_update": "2025-05-02 18:47:09", "author_account_age_days": 1176}, "https://github.com/AkiEvansDev/ComfyUI-Tools": {"stars": 0, "last_update": "2025-06-01 19:15:57", "author_account_age_days": 2685}, "https://github.com/Alazuaka/comfyui-lora-stack-node": {"stars": 0, "last_update": "2025-06-12 23:14:31", "author_account_age_days": 1153}, "https://github.com/AlejandroTuzzi/TUZZI-ByPass": {"stars": 5, "last_update": "2025-05-13 14:04:56", "author_account_age_days": 1611}, "https://github.com/AlexXi19/ComfyUI-OpenAINode": {"stars": 1, "last_update": "2025-01-13 18:43:22", "author_account_age_days": 1797}, "https://github.com/AlexYez/comfyui-timesaver": {"stars": 0, "last_update": "2025-06-23 09:07:06", "author_account_age_days": 1522}, "https://github.com/AllenEdgarPoe/ComfyUI-Xorbis-nodes": {"stars": 3, "last_update": "2025-06-12 23:48:01", "author_account_age_days": 2472}, "https://github.com/Alvaroeai/ComfyUI-SunoAI-Mds": {"stars": 0, "last_update": "2025-01-11 21:13:41", "author_account_age_days": 4068}, "https://github.com/Anonymzx/ComfyUI-Indonesia-TTS": {"stars": 0, "last_update": "2025-05-07 14:33:50", "author_account_age_days": 2196}, "https://github.com/Anze-/ComfyUI-OIDN": {"stars": 8, "last_update": "2024-11-27 18:05:41", "author_account_age_days": 4321}, "https://github.com/Anze-/ComfyUI_deepDeband": {"stars": 3, "last_update": "2024-11-12 19:13:59", "author_account_age_days": 4321}, "https://github.com/ArmandAlbert/Kwai_font_comfyui": {"stars": 1, "last_update": "2025-01-14 04:02:21", "author_account_age_days": 2359}, "https://github.com/ArthusLiang/comfyui-face-remap": {"stars": 4, "last_update": "2024-11-30 12:34:28", "author_account_age_days": 4383}, "https://github.com/Aryan185/ComfyUI-ReplicateFluxKontext": {"stars": 0, "last_update": "2025-06-20 06:02:51", "author_account_age_days": 1539}, "https://github.com/AustinMroz/ComfyUI-MinCache": {"stars": 2, "last_update": "2024-12-25 18:52:07", "author_account_age_days": 4441}, "https://github.com/AustinMroz/ComfyUI-WorkflowCheckpointing": {"stars": 11, "last_update": "2024-10-17 19:59:40", "author_account_age_days": 4441}, "https://github.com/BadCafeCode/execution-inversion-demo-comfyui": {"stars": 74, "last_update": "2025-03-09 00:44:37", "author_account_age_days": 797}, "https://github.com/BaronVonBoolean/ComfyUI-FileOps": {"stars": 0, "last_update": "2024-12-22 18:04:20", "author_account_age_days": 205}, "https://github.com/Beinsezii/comfyui-amd-go-fast": {"stars": 42, "last_update": "2025-04-21 19:37:22", "author_account_age_days": 2593}, "https://github.com/BenjaMITM/ComfyUI_On_The_Fly_Wildcards": {"stars": 0, "last_update": "2024-11-20 06:17:53", "author_account_age_days": 314}, "https://github.com/BetaDoggo/ComfyUI-LogicGates": {"stars": 3, "last_update": "2024-07-21 06:31:25", "author_account_age_days": 1164}, "https://github.com/Big-Idea-Technology/ComfyUI-Movie-Tools": {"stars": 3, "last_update": "2024-11-29 11:13:57", "author_account_age_days": 1238}, "https://github.com/BigStationW/flowmatch_scheduler-comfyui": {"stars": 12, "last_update": "2025-06-17 13:31:03", "author_account_age_days": 53}, "https://github.com/BinglongLi/ComfyUI_ToolsForAutomask": {"stars": 1, "last_update": "2025-06-04 11:56:53", "author_account_age_days": 2052}, "https://github.com/BlueDangerX/ComfyUI-BDXNodes": {"stars": 1, "last_update": "2023-12-10 04:01:19", "author_account_age_days": 613}, "https://github.com/BobRandomNumber/ComfyUI-DiaTTS": {"stars": 8, "last_update": "2025-06-02 03:02:19", "author_account_age_days": 210}, "https://github.com/Brandelan/ComfyUI_bd_customNodes": {"stars": 2, "last_update": "2024-09-08 01:04:38", "author_account_age_days": 4518}, "https://github.com/BuffMcBigHuge/ComfyUI-Buff-Nodes": {"stars": 2, "last_update": "2025-05-21 02:59:22", "author_account_age_days": 3279}, "https://github.com/Burgstall-labs/ComfyUI-BS_FalAi-API-Video": {"stars": 3, "last_update": "2025-06-19 06:47:25", "author_account_age_days": 158}, "https://github.com/COcisuts/CObot-ComfyUI-WhisperToTranscription": {"stars": 0, "last_update": "2025-06-08 13:32:25", "author_account_age_days": 2983}, "https://github.com/CY-CHENYUE/ComfyUI-FramePack-HY": {"stars": 18, "last_update": "2025-05-08 09:38:09", "author_account_age_days": 565}, "https://github.com/CeeVeeR/ComfyUi-Text-Tiler": {"stars": 0, "last_update": "2025-03-25 20:26:18", "author_account_age_days": 1440}, "https://github.com/Chargeuk/ComfyUI-vts-nodes": {"stars": 0, "last_update": "2025-06-22 17:05:09", "author_account_age_days": 4474}, "https://github.com/Charonartist/ComfyUI-send-eagle-pro_2": {"stars": 0, "last_update": "2025-05-26 12:12:47", "author_account_age_days": 361}, "https://github.com/ChrisColeTech/ComfyUI-Get-Random-File": {"stars": 3, "last_update": "2025-06-19 03:10:17", "author_account_age_days": 2779}, "https://github.com/Clelstyn/ComfyUI-Inpaint_with_Detailer": {"stars": 1, "last_update": "2024-11-02 12:04:53", "author_account_age_days": 685}, "https://github.com/Clybius/ComfyUI-FluxDeCLIP": {"stars": 1, "last_update": "2024-11-17 20:06:29", "author_account_age_days": 2100}, "https://github.com/Comfy-Org/ComfyUI_devtools": {"stars": 19, "last_update": "2025-05-10 16:23:35", "author_account_age_days": 442}, "https://github.com/ComfyUI-Workflow/ComfyUI-OpenAI": {"stars": 26, "last_update": "2024-10-07 08:25:18", "author_account_age_days": 264}, "https://github.com/D1-3105/ComfyUI-VideoStream": {"stars": 0, "last_update": "2025-02-17 04:02:01", "author_account_age_days": 1870}, "https://github.com/DDDDEEP/ComfyUI-DDDDEEP": {"stars": 0, "last_update": "2025-06-22 03:03:32", "author_account_age_days": 2870}, "https://github.com/DataCTE/ComfyUI-DataVoid-nodes": {"stars": 0, "last_update": "2024-11-20 14:20:31", "author_account_age_days": 1144}, "https://github.com/DeTK/ComfyUI-Switch": {"stars": 0, "last_update": "2024-03-04 11:52:04", "author_account_age_days": 2398}, "https://github.com/DoctorDiffusion/ComfyUI-Flashback": {"stars": 0, "last_update": "2024-11-11 01:37:43", "author_account_age_days": 708}, "https://github.com/DonutsDelivery/ComfyUI-DonutNodes": {"stars": 6, "last_update": "2025-06-17 17:36:23", "author_account_age_days": 92}, "https://github.com/DrMWeigand/ComfyUI_LineBreakInserter": {"stars": 0, "last_update": "2024-04-19 11:37:19", "author_account_age_days": 1399}, "https://github.com/DraconicDragon/ComfyUI_e621_booru_toolkit": {"stars": 5, "last_update": "2025-06-09 19:31:11", "author_account_age_days": 1740}, "https://github.com/DreamsInAutumn/ComfyUI-Autumn-LLM-Nodes": {"stars": 0, "last_update": "2025-06-14 06:14:05", "author_account_age_days": 1233}, "https://github.com/Dreamshot-io/ComfyUI-Extend-Resolution": {"stars": 0, "last_update": "2025-06-02 07:15:00", "author_account_age_days": 219}, "https://github.com/ELiZswe/ComfyUI-ELiZTools": {"stars": 0, "last_update": "2025-06-24 06:14:44", "author_account_age_days": 2190}, "https://github.com/EQXai/ComfyUI_EQX": {"stars": 0, "last_update": "2025-06-18 14:21:53", "author_account_age_days": 394}, "https://github.com/Eagle-CN/ComfyUI-Addoor": {"stars": 50, "last_update": "2025-04-25 01:03:58", "author_account_age_days": 2994}, "https://github.com/Elawphant/ComfyUI-MusicGen": {"stars": 6, "last_update": "2024-05-11 13:33:24", "author_account_age_days": 2955}, "https://github.com/Elypha/ComfyUI-Prompt-Helper": {"stars": 0, "last_update": "2025-03-03 21:42:14", "author_account_age_days": 2899}, "https://github.com/EmanueleUniroma2/ComfyUI-FLAC-to-WAV": {"stars": 0, "last_update": "2025-01-26 11:25:43", "author_account_age_days": 3012}, "https://github.com/EmilioPlumed/ComfyUI-Math": {"stars": 1, "last_update": "2025-01-11 14:28:42", "author_account_age_days": 2345}, "https://github.com/EricRollei/Comfy-Metadata-System": {"stars": 0, "last_update": "2025-04-28 23:42:26", "author_account_age_days": 1261}, "https://github.com/ExponentialML/ComfyUI_LiveDirector": {"stars": 37, "last_update": "2024-04-09 19:01:49", "author_account_age_days": 1990}, "https://github.com/Extraltodeus/Conditioning-token-experiments-for-ComfyUI": {"stars": 18, "last_update": "2024-03-10 01:04:02", "author_account_age_days": 3516}, "https://github.com/FaberVS/MultiModel": {"stars": 1, "last_update": "2025-05-06 14:27:08", "author_account_age_days": 2138}, "https://github.com/Fannovel16/ComfyUI-AppIO": {"stars": 0, "last_update": "2024-12-01 16:37:19", "author_account_age_days": 3498}, "https://github.com/FinetunersAI/comfyui-fast-group-link": {"stars": 0, "last_update": "2024-12-09 17:35:50", "author_account_age_days": 388}, "https://github.com/FinetunersAI/finetuners": {"stars": 1, "last_update": "2025-01-06 16:29:33", "author_account_age_days": 388}, "https://github.com/FoundD-oka/ComfyUI-kisekae-OOTD": {"stars": 0, "last_update": "2024-06-02 06:13:42", "author_account_age_days": 804}, "https://github.com/Fucci-Mateo/ComfyUI-Airtable": {"stars": 1, "last_update": "2024-06-25 13:35:18", "author_account_age_days": 1245}, "https://github.com/GalactusX31/ComfyUI-FileBrowserAPI": {"stars": 4, "last_update": "2025-06-13 20:53:11", "author_account_age_days": 2693}, "https://github.com/GentlemanHu/ComfyUI-Notifier": {"stars": 4, "last_update": "2024-07-14 15:38:44", "author_account_age_days": 2750}, "https://github.com/George0726/ComfyUI-video-accessory": {"stars": 1, "last_update": "2025-05-19 14:18:22", "author_account_age_days": 2618}, "https://github.com/Good-Dream-Studio/ComfyUI-Connect": {"stars": 15, "last_update": "2025-05-18 09:30:10", "author_account_age_days": 103}, "https://github.com/Grant-CP/ComfyUI-LivePortraitKJ-MPS": {"stars": 12, "last_update": "2024-07-11 22:04:16", "author_account_age_days": 1540}, "https://github.com/Grey3016/Save2Icon": {"stars": 2, "last_update": "2025-01-06 15:18:57", "author_account_age_days": 695}, "https://github.com/GrindHouse66/ComfyUI-GH_Tools": {"stars": 0, "last_update": "2024-03-10 13:27:14", "author_account_age_days": 991}, "https://github.com/Hapseleg/ComfyUI-This-n-That": {"stars": 0, "last_update": "2025-06-03 20:26:27", "author_account_age_days": 3662}, "https://github.com/HuangYuChuh/ComfyUI-DeepSeek-Toolkit": {"stars": 12, "last_update": "2025-06-23 09:42:46", "author_account_age_days": 431}, "https://github.com/HuangYuChuh/ComfyUI-LLMs-Toolkit": {"stars": 12, "last_update": "2025-06-23 09:42:46", "author_account_age_days": 431}, "https://github.com/IfnotFr/ComfyUI-Ifnot-Pack": {"stars": 1, "last_update": "2025-02-05 08:51:23", "author_account_age_days": 4951}, "https://github.com/IgPoly/ComfyUI-igTools": {"stars": 0, "last_update": "2024-09-11 08:48:57", "author_account_age_days": 294}, "https://github.com/IsItDanOrAi/ComfyUI-exLoadout": {"stars": 0, "last_update": "2025-06-26 02:19:42", "author_account_age_days": 478}, "https://github.com/IuvenisSapiens/ComfyUI_MiniCPM-V-2_6-int4": {"stars": 184, "last_update": "2025-04-02 16:32:54", "author_account_age_days": 778}, "https://github.com/IvanZhd/comfyui-codeformer": {"stars": 0, "last_update": "2023-12-02 20:51:52", "author_account_age_days": 2945}, "https://github.com/Jaxkr/comfyui-terminal-command": {"stars": 1, "last_update": "2023-12-03 10:31:40", "author_account_age_days": 4994}, "https://github.com/JayLyu/ComfyUI_BaiKong_Node": {"stars": 8, "last_update": "2025-03-31 14:22:51", "author_account_age_days": 3635}, "https://github.com/Jiffies-64/ComfyUI-SaveImagePlus": {"stars": 0, "last_update": "2024-04-01 10:52:59", "author_account_age_days": 1258}, "https://github.com/Jingwen-genies/comfyui-genies-nodes": {"stars": 0, "last_update": "2025-05-13 19:36:45", "author_account_age_days": 700}, "https://github.com/JissiChoi/ComfyUI-Jissi-List": {"stars": 0, "last_update": "2024-12-24 08:24:27", "author_account_age_days": 2585}, "https://github.com/JoeAu/ComfyUI-PythonNode": {"stars": 3, "last_update": "2025-03-16 13:05:38", "author_account_age_days": 4553}, "https://github.com/Jordach/comfy-consistency-vae": {"stars": 69, "last_update": "2023-11-06 20:50:40", "author_account_age_days": 4880}, "https://github.com/Junst/ComfyUI-PNG2SVG2PNG": {"stars": 0, "last_update": "2024-12-04 02:25:04", "author_account_age_days": 2905}, "https://github.com/KERRY-YUAN/ComfyUI_Python_Executor": {"stars": 1, "last_update": "2025-04-07 07:49:03", "author_account_age_days": 1621}, "https://github.com/Kayarte/Time-Series-Nodes-for-ComfyUI": {"stars": 1, "last_update": "2025-01-29 02:33:25", "author_account_age_days": 420}, "https://github.com/KihongK/comfyui-roysnodes": {"stars": 0, "last_update": "2025-01-23 09:11:02", "author_account_age_days": 1929}, "https://github.com/KoreTeknology/ComfyUI-Nai-Production-Nodes-Pack": {"stars": 10, "last_update": "2024-11-24 15:55:30", "author_account_age_days": 3557}, "https://github.com/Krish-701/RK_Comfyui": {"stars": 0, "last_update": "2025-04-17 17:18:52", "author_account_age_days": 223}, "https://github.com/Kur0butiMegane/Comfyui-StringUtils2": {"stars": 0, "last_update": "2025-05-04 16:34:13", "author_account_age_days": 2019}, "https://github.com/KurtHokke/ComfyUI_KurtHokke_Nodes": {"stars": 1, "last_update": "2025-03-27 19:04:42", "author_account_age_days": 192}, "https://github.com/LAOGOU-666/Comfyui_StartPatch": {"stars": 47, "last_update": "2025-02-24 17:22:34", "author_account_age_days": 462}, "https://github.com/LLMCoder2023/ComfyUI-LLMCoder2023Nodes": {"stars": 0, "last_update": "2025-04-30 02:42:58", "author_account_age_days": 3374}, "https://github.com/LZpenguin/ComfyUI-Text": {"stars": 23, "last_update": "2024-06-20 13:38:16", "author_account_age_days": 2346}, "https://github.com/LarryJane491/ComfyUI-ModelUnloader": {"stars": 4, "last_update": "2024-01-14 08:22:39", "author_account_age_days": 531}, "https://github.com/Laser-one/ComfyUI-align-pose": {"stars": 0, "last_update": "2024-11-01 09:34:31", "author_account_age_days": 1199}, "https://github.com/Lilien86/Comfyui_Latent_Interpolation": {"stars": 1, "last_update": "2024-09-03 21:00:49", "author_account_age_days": 861}, "https://github.com/Linsoo/ComfyUI-Linsoo-Custom-Nodes": {"stars": 0, "last_update": "2025-04-25 09:23:18", "author_account_age_days": 4458}, "https://github.com/Looking-Glass/LKG-ComfyUI": {"stars": 4, "last_update": "2024-10-30 17:02:54", "author_account_age_days": 3352}, "https://github.com/LotzF/ComfyUI-Simple-Chat-GPT-completion": {"stars": 0, "last_update": "2025-02-27 15:07:36", "author_account_age_days": 1296}, "https://github.com/LucianGnn/ComfyUI-Lucian": {"stars": 0, "last_update": "2025-06-18 06:47:37", "author_account_age_days": 2246}, "https://github.com/LucipherDev/ComfyUI-Sentinel": {"stars": 29, "last_update": "2025-04-07 14:53:13", "author_account_age_days": 1864}, "https://github.com/LyazS/ComfyUI-aznodes": {"stars": 0, "last_update": "2025-06-03 14:57:29", "author_account_age_days": 3224}, "https://github.com/LykosAI/ComfyUI-Inference-Core-Nodes": {"stars": 34, "last_update": "2025-04-05 22:22:31", "author_account_age_days": 745}, "https://github.com/M4lF3s/comfy-tif-support": {"stars": 0, "last_update": "2025-02-12 09:29:11", "author_account_age_days": 3591}, "https://github.com/MakkiShizu/ComfyUI-MakkiTools": {"stars": 2, "last_update": "2025-06-23 22:43:19", "author_account_age_days": 679}, "https://github.com/Malloc-pix/comfyui-QwenVL": {"stars": 0, "last_update": "2025-06-24 09:35:32", "author_account_age_days": 15}, "https://github.com/ManuShamil/ComfyUI_BodyEstimation_Nodes": {"stars": 0, "last_update": "2025-02-28 19:23:24", "author_account_age_days": 2520}, "https://github.com/Matrix-King-Studio/ComfyUI-MoviePy": {"stars": 0, "last_update": "2024-12-10 01:50:42", "author_account_age_days": 1830}, "https://github.com/Maxim-Dey/ComfyUI-MaksiTools": {"stars": 3, "last_update": "2025-02-08 08:04:03", "author_account_age_days": 779}, "https://github.com/Mervent/comfyui-telegram-send": {"stars": 0, "last_update": "2025-05-22 17:57:36", "author_account_age_days": 3186}, "https://github.com/Mervent/comfyui-yaml-prompt": {"stars": 0, "last_update": "2025-06-01 06:55:16", "author_account_age_days": 3186}, "https://github.com/MickeyJ/ComfyUI_mickster_nodes": {"stars": 0, "last_update": "2025-02-07 02:29:12", "author_account_age_days": 3581}, "https://github.com/MockbaTheBorg/ComfyUI-Mockba": {"stars": 0, "last_update": "2025-05-20 17:39:21", "author_account_age_days": 3446}, "https://github.com/MrAdamBlack/CheckProgress": {"stars": 1, "last_update": "2024-01-10 08:02:18", "author_account_age_days": 3087}, "https://github.com/MuAIGC/ComfyUI-DMXAPI_mmx": {"stars": 2, "last_update": "2025-05-26 06:58:45", "author_account_age_days": 289}, "https://github.com/MythicalChu/ComfyUI-APG_ImYourCFGNow": {"stars": 32, "last_update": "2024-11-29 17:45:03", "author_account_age_days": 1861}, "https://github.com/NEZHA625/ComfyUI-tools-by-dong": {"stars": 1, "last_update": "2025-06-05 03:40:06", "author_account_age_days": 825}, "https://github.com/Nambi24/ComfyUI-Save_Image": {"stars": 0, "last_update": "2025-05-05 15:05:27", "author_account_age_days": 1252}, "https://github.com/NicholasKao1029/comfyui-hook": {"stars": 0, "last_update": "2024-03-07 05:50:56", "author_account_age_days": 2397}, "https://github.com/No-22-Github/ComfyUI_SaveImageCustom": {"stars": 0, "last_update": "2025-06-26 06:33:38", "author_account_age_days": 787}, "https://github.com/Northerner1/ComfyUI_North_Noise": {"stars": 1, "last_update": "2025-03-01 12:32:29", "author_account_age_days": 808}, "https://github.com/Novavision0313/ComfyUI-NVVS": {"stars": 1, "last_update": "2025-06-12 03:27:13", "author_account_age_days": 35}, "https://github.com/OSAnimate/ComfyUI-SpriteSheetMaker": {"stars": 0, "last_update": "2025-03-12 04:22:34", "author_account_age_days": 813}, "https://github.com/Oct7/ComfyUI-LaplaMask": {"stars": 0, "last_update": "2025-06-03 07:45:26", "author_account_age_days": 1969}, "https://github.com/OgreLemonSoup/ComfyUI-Notes-manager": {"stars": 0, "last_update": "2025-06-25 07:24:16", "author_account_age_days": 320}, "https://github.com/PATATAJEC/Patatajec-Nodes": {"stars": 2, "last_update": "2025-02-26 16:26:39", "author_account_age_days": 2302}, "https://github.com/Pablerdo/ComfyUI-Sa2VAWrapper": {"stars": 3, "last_update": "2025-03-27 22:58:39", "author_account_age_days": 3167}, "https://github.com/PabloGrant/comfyui-giraffe-test-panel": {"stars": 0, "last_update": "2025-05-18 16:38:09", "author_account_age_days": 653}, "https://github.com/Poseidon-fan/ComfyUI-fileCleaner": {"stars": 1, "last_update": "2024-11-19 02:42:29", "author_account_age_days": 949}, "https://github.com/Poukpalaova/ComfyUI-FRED-Nodes": {"stars": 4, "last_update": "2025-06-08 17:29:57", "author_account_age_days": 688}, "https://github.com/QingLuanWithoutHeart/comfyui-file-image-utils": {"stars": 1, "last_update": "2025-04-08 11:13:50", "author_account_age_days": 2392}, "https://github.com/Quasimondo/ComfyUI-QuasimondoNodes": {"stars": 13, "last_update": "2025-06-09 08:58:42", "author_account_age_days": 5642}, "https://github.com/RLW-Chars/comfyui-promptbymood": {"stars": 1, "last_update": "2025-01-25 11:21:59", "author_account_age_days": 152}, "https://github.com/RUFFY-369/ComfyUI-FeatureBank": {"stars": 0, "last_update": "2025-03-07 19:30:55", "author_account_age_days": 1844}, "https://github.com/Raidez/comfyui-kuniklo-collection": {"stars": 0, "last_update": "2025-05-02 19:44:45", "author_account_age_days": 4041}, "https://github.com/RedmondAI/comfyui-tools": {"stars": 0, "last_update": "2025-06-26 22:15:25", "author_account_age_days": 401}, "https://github.com/RicherdLee/comfyui-oss-image-save": {"stars": 0, "last_update": "2024-12-10 09:08:39", "author_account_age_days": 4010}, "https://github.com/RobeSantoro/ComfyUI-RobeNodes": {"stars": 0, "last_update": "2025-06-14 10:29:07", "author_account_age_days": 4983}, "https://github.com/RoyKillington/miscomfy-nodes": {"stars": 0, "last_update": "2025-03-06 19:36:33", "author_account_age_days": 2772}, "https://github.com/S4MUEL-404/ComfyUI-Folder-Images-Preview": {"stars": 3, "last_update": "2025-03-09 11:29:04", "author_account_age_days": 3454}, "https://github.com/SKBv0/ComfyUI-RetroEngine": {"stars": 4, "last_update": "2025-05-10 14:29:43", "author_account_age_days": 1922}, "https://github.com/SS-snap/ComfyUI-Snap_Processing": {"stars": 61, "last_update": "2025-04-25 04:54:44", "author_account_age_days": 667}, "https://github.com/SS-snap/Comfyui_SSsnap_pose-Remapping": {"stars": 21, "last_update": "2025-06-18 07:25:25", "author_account_age_days": 667}, "https://github.com/SXQBW/ComfyUI-Qwen3": {"stars": 0, "last_update": "2025-04-18 06:06:49", "author_account_age_days": 3159}, "https://github.com/SadaleNet/ComfyUI-Prompt-To-Prompt": {"stars": 24, "last_update": "2024-03-17 04:30:01", "author_account_age_days": 4407}, "https://github.com/Sai-ComfyUI/ComfyUI-MS-Nodes": {"stars": 2, "last_update": "2024-02-22 08:34:44", "author_account_age_days": 576}, "https://github.com/Sakura-nee/ComfyUI_Save2Discord": {"stars": 0, "last_update": "2024-08-27 19:01:46", "author_account_age_days": 1678}, "https://github.com/SanDiegoDude/ComfyUI-HiDream-Sampler": {"stars": 97, "last_update": "2025-05-09 15:17:23", "author_account_age_days": 998}, "https://github.com/Scaryplasmon/ComfTrellis": {"stars": 7, "last_update": "2025-02-18 11:34:33", "author_account_age_days": 1392}, "https://github.com/SeedV/ComfyUI-SeedV-Nodes": {"stars": 1, "last_update": "2025-04-25 07:37:36", "author_account_age_days": 1493}, "https://github.com/Sephrael/comfyui_caption-around-image": {"stars": 0, "last_update": "2025-06-02 19:16:34", "author_account_age_days": 830}, "https://github.com/ShahFaisalWani/ComfyUI-Mojen-Nodeset": {"stars": 0, "last_update": "2025-05-03 08:29:40", "author_account_age_days": 777}, "https://github.com/Shinsplat/ComfyUI-Shinsplat": {"stars": 45, "last_update": "2025-03-15 00:02:11", "author_account_age_days": 1391}, "https://github.com/ShmuelRonen/ComfyUI-FreeMemory": {"stars": 108, "last_update": "2025-03-20 11:25:12", "author_account_age_days": 1572}, "https://github.com/Simlym/comfyui-prompt-helper": {"stars": 2, "last_update": "2025-04-17 15:20:34", "author_account_age_days": 2549}, "https://github.com/SirVeggie/comfyui-sv-nodes": {"stars": 5, "last_update": "2025-05-03 19:46:49", "author_account_age_days": 2827}, "https://github.com/Slix-M-Lestragg/comfyui-enhanced": {"stars": 0, "last_update": "2025-04-11 21:32:23", "author_account_age_days": 1681}, "https://github.com/SoftMeng/ComfyUI-PIL": {"stars": 6, "last_update": "2024-10-13 10:02:17", "author_account_age_days": 3890}, "https://github.com/Solankimayursinh/PMSnodes": {"stars": 0, "last_update": "2025-04-26 07:47:15", "author_account_age_days": 232}, "https://github.com/Sophylax/ComfyUI-ReferenceMerge": {"stars": 0, "last_update": "2025-04-30 21:48:18", "author_account_age_days": 4312}, "https://github.com/Soppatorsk/comfyui_img_to_ascii": {"stars": 0, "last_update": "2024-09-07 15:39:28", "author_account_age_days": 1507}, "https://github.com/SpaceWarpStudio/ComfyUI_Remaker_FaceSwap": {"stars": 0, "last_update": "2024-07-15 11:57:20", "author_account_age_days": 3323}, "https://github.com/Stable-X/ComfyUI-Hi3DGen": {"stars": 163, "last_update": "2025-04-04 03:48:36", "author_account_age_days": 389}, "https://github.com/StableDiffusionVN/SDVN_Comfy_node": {"stars": 48, "last_update": "2025-06-26 08:18:00", "author_account_age_days": 325}, "https://github.com/StaffsGull/comfyui_scene_builder": {"stars": 0, "last_update": "2025-04-27 12:40:57", "author_account_age_days": 3307}, "https://github.com/StartHua/Comfyui_CSDMT_CXH": {"stars": 20, "last_update": "2024-07-11 15:36:03", "author_account_age_days": 3202}, "https://github.com/StartHua/Comfyui_CXH_CRM": {"stars": 45, "last_update": "2024-06-06 14:15:14", "author_account_age_days": 3202}, "https://github.com/StartHua/Comfyui_CXH_joy_caption": {"stars": 581, "last_update": "2025-02-06 02:35:10", "author_account_age_days": 3202}, "https://github.com/StartHua/Comfyui_Flux_Style_Ctr": {"stars": 97, "last_update": "2024-11-22 09:25:11", "author_account_age_days": 3202}, "https://github.com/StartHua/Comfyui_leffa": {"stars": 227, "last_update": "2024-12-18 03:04:54", "author_account_age_days": 3202}, "https://github.com/StoryWalker/comfyui_flux_collection_advanced": {"stars": 0, "last_update": "2025-04-28 02:49:48", "author_account_age_days": 177}, "https://github.com/Symbiomatrix/Comfyui-Sort-Files": {"stars": 1, "last_update": "2025-04-22 22:24:00", "author_account_age_days": 2541}, "https://github.com/TSFSean/ComfyUI-TSFNodes": {"stars": 6, "last_update": "2024-05-18 00:59:06", "author_account_age_days": 3839}, "https://github.com/Tawbaware/ComfyUI-Tawbaware": {"stars": 1, "last_update": "2025-04-20 22:23:11", "author_account_age_days": 1638}, "https://github.com/Temult/TWanSigmaSampler": {"stars": 2, "last_update": "2025-04-17 08:53:41", "author_account_age_days": 633}, "https://github.com/ThatGlennD/ComfyUI-Image-Analysis-Tools": {"stars": 12, "last_update": "2025-05-27 11:49:48", "author_account_age_days": 3212}, "https://github.com/TheJorseman/IntrinsicCompositingClean-ComfyUI": {"stars": 0, "last_update": "2025-05-07 17:07:51", "author_account_age_days": 3649}, "https://github.com/ThisModernDay/ComfyUI-InstructorOllama": {"stars": 7, "last_update": "2024-08-20 00:30:24", "author_account_age_days": 4096}, "https://github.com/V-woodpecker-V/comfyui-stiffy-nodes": {"stars": 1, "last_update": "2025-04-12 22:36:51", "author_account_age_days": 1623}, "https://github.com/Velour-Fog/comfy-latent-nodes": {"stars": 6, "last_update": "2025-02-24 00:34:41", "author_account_age_days": 1328}, "https://github.com/VictorLopes643/ComfyUI-Video-Dataset-Tools": {"stars": 1, "last_update": "2025-05-09 22:47:52", "author_account_age_days": 2676}, "https://github.com/Video3DGenResearch/comfyui-batch-input-node": {"stars": 1, "last_update": "2024-04-28 15:21:17", "author_account_age_days": 473}, "https://github.com/VisionExp/ve_custom_comfyui_nodes": {"stars": 0, "last_update": "2024-07-17 11:51:54", "author_account_age_days": 372}, "https://github.com/WASasquatch/ASTERR": {"stars": 30, "last_update": "2024-10-27 01:48:56", "author_account_age_days": 4992}, "https://github.com/WSJUSA/Comfyui-StableSR": {"stars": 50, "last_update": "2023-10-18 12:40:30", "author_account_age_days": 1791}, "https://github.com/WaiyanLing/ComfyUI-Tracking": {"stars": 1, "last_update": "2025-04-18 04:36:33", "author_account_age_days": 4488}, "https://github.com/WilliamStanford/ComfyUI-VisualLabs": {"stars": 1, "last_update": "2024-04-16 21:53:02", "author_account_age_days": 2136}, "https://github.com/WozStudios/ComfyUI-WozNodes": {"stars": 0, "last_update": "2025-06-25 14:29:29", "author_account_age_days": 4476}, "https://github.com/Yeonri/ComfyUI_LLM_Are_You_Listening": {"stars": 0, "last_update": "2025-02-21 00:35:03", "author_account_age_days": 904}, "https://github.com/Yukinoshita-Yukinoe/ComfyUI-KontextOfficialNode": {"stars": 2, "last_update": "2025-06-06 09:23:19", "author_account_age_days": 1773}, "https://github.com/ZHO-ZHO-ZHO/ComfyUI-AuraSR-ZHO": {"stars": 95, "last_update": "2024-07-11 07:33:30", "author_account_age_days": 707}, "https://github.com/ZHO-ZHO-ZHO/ComfyUI-BiRefNet-ZHO": {"stars": 354, "last_update": "2024-07-30 23:24:24", "author_account_age_days": 707}, "https://github.com/ZHO-ZHO-ZHO/ComfyUI-Gemini": {"stars": 762, "last_update": "2024-05-22 14:15:11", "author_account_age_days": 707}, "https://github.com/ZHO-ZHO-ZHO/ComfyUI-Llama-3-2": {"stars": 18, "last_update": "2024-09-26 18:08:01", "author_account_age_days": 707}, "https://github.com/ZHO-ZHO-ZHO/ComfyUI-PuLID-ZHO": {"stars": 235, "last_update": "2024-05-22 13:38:23", "author_account_age_days": 707}, "https://github.com/ZHO-ZHO-ZHO/ComfyUI-Qwen": {"stars": 110, "last_update": "2024-09-20 21:27:47", "author_account_age_days": 707}, "https://github.com/ZHO-ZHO-ZHO/ComfyUI-Wan-ZHO": {"stars": 10, "last_update": "2025-02-26 05:46:42", "author_account_age_days": 707}, "https://github.com/ZenAI-Vietnam/ComfyUI-gemini-IG": {"stars": 1, "last_update": "2025-03-26 14:49:13", "author_account_age_days": 550}, "https://github.com/ZenAI-Vietnam/ComfyUI_InfiniteYou": {"stars": 230, "last_update": "2025-03-31 07:56:02", "author_account_age_days": 550}, "https://github.com/a-One-Fan/ComfyUI-Blenderesque-Nodes": {"stars": 3, "last_update": "2025-05-22 12:36:15", "author_account_age_days": 1222}, "https://github.com/a-und-b/ComfyUI_Output_as_Input": {"stars": 2, "last_update": "2025-05-08 08:35:02", "author_account_age_days": 807}, "https://github.com/aa-parky/pipemind-comfyui": {"stars": 0, "last_update": "2025-06-25 09:26:18", "author_account_age_days": 2211}, "https://github.com/abuzreq/ComfyUI-Model-Bending": {"stars": 11, "last_update": "2025-06-20 04:31:48", "author_account_age_days": 4223}, "https://github.com/aiden1020/ComfyUI_Artcoder": {"stars": 2, "last_update": "2025-01-11 08:31:32", "author_account_age_days": 836}, "https://github.com/ainanoha/etm_comfyui_nodes": {"stars": 0, "last_update": "2024-10-31 05:45:59", "author_account_age_days": 4621}, "https://github.com/akatz-ai/ComfyUI-Execution-Inversion": {"stars": 1, "last_update": "2025-06-18 04:06:55", "author_account_age_days": 402}, "https://github.com/aklevecz/ComfyUI-AutoPrompt": {"stars": 0, "last_update": "2025-05-26 18:36:34", "author_account_age_days": 2639}, "https://github.com/alexgenovese/ComfyUI-Diffusion-4k": {"stars": 6, "last_update": "2025-05-22 20:48:23", "author_account_age_days": 5381}, "https://github.com/alexgenovese/ComfyUI-Reica": {"stars": 0, "last_update": "2025-06-20 17:08:23", "author_account_age_days": 5381}, "https://github.com/alexisrolland/ComfyUI-AuraSR": {"stars": 28, "last_update": "2025-04-01 14:20:42", "author_account_age_days": 3653}, "https://github.com/alt-key-project/comfyui-dream-painter": {"stars": 2, "last_update": "2025-02-23 10:19:26", "author_account_age_days": 1027}, "https://github.com/alt-key-project/comfyui-dream-video-batches": {"stars": 72, "last_update": "2025-02-23 10:28:40", "author_account_age_days": 1027}, "https://github.com/amamisonlyuser/MixvtonComfyui": {"stars": 0, "last_update": "2025-05-31 14:14:10", "author_account_age_days": 802}, "https://github.com/ammahmoudi/ComfyUI-Legendary-Nodes": {"stars": 0, "last_update": "2025-03-15 07:26:17", "author_account_age_days": 1309}, "https://github.com/animEEEmpire/ComfyUI-Animemory-Loader": {"stars": 2, "last_update": "2025-01-20 08:02:58", "author_account_age_days": 212}, "https://github.com/apetitbois/nova_utils": {"stars": 0, "last_update": "2025-04-02 20:01:49", "author_account_age_days": 3464}, "https://github.com/aria1th/ComfyUI-CairoSVG": {"stars": 0, "last_update": "2025-01-07 19:40:19", "author_account_age_days": 2712}, "https://github.com/aria1th/ComfyUI-SkipCFGSigmas": {"stars": 3, "last_update": "2025-03-05 07:50:45", "author_account_age_days": 2712}, "https://github.com/aria1th/ComfyUI-camietagger-onnx": {"stars": 0, "last_update": "2025-03-06 01:55:51", "author_account_age_days": 2712}, "https://github.com/armandgw84/comfyui-custom-v8": {"stars": 0, "last_update": "2025-06-24 15:24:02", "author_account_age_days": 818}, "https://github.com/artem-konevskikh/comfyui-split-merge-video": {"stars": 3, "last_update": "2024-11-19 00:11:17", "author_account_age_days": 4738}, "https://github.com/artifyfun/ComfyUI-JS": {"stars": 2, "last_update": "2025-06-23 15:19:20", "author_account_age_days": 458}, "https://github.com/artisanalcomputing/ComfyUI-Custom-Nodes": {"stars": 0, "last_update": "2024-10-13 05:55:33", "author_account_age_days": 2642}, "https://github.com/ashishsaini/comfyui-segment-clothing-sleeves": {"stars": 2, "last_update": "2024-09-23 19:09:15", "author_account_age_days": 4315}, "https://github.com/ashllay/ComfyUI_MoreComfy": {"stars": 0, "last_update": "2025-04-14 12:27:22", "author_account_age_days": 4337}, "https://github.com/avocadori/ComfyUI-AudioAmplitudeConverter": {"stars": 0, "last_update": "2025-05-29 07:57:22", "author_account_age_days": 440}, "https://github.com/ayaoayaoayaoaya/ComfyUI-KLUT-DeepSeek-API": {"stars": 0, "last_update": "2025-03-27 15:38:59", "author_account_age_days": 386}, "https://github.com/backearth1/Comfyui-MiniMax-Video": {"stars": 19, "last_update": "2025-03-12 15:26:35", "author_account_age_days": 619}, "https://github.com/badmike/comfyui-prompt-factory": {"stars": 0, "last_update": "2025-02-18 09:28:53", "author_account_age_days": 5047}, "https://github.com/baicai99/ComfyUI-FrameSkipping": {"stars": 10, "last_update": "2025-06-23 02:50:12", "author_account_age_days": 1198}, "https://github.com/bananasss00/Comfyui-PyExec": {"stars": 1, "last_update": "2025-02-26 12:01:18", "author_account_age_days": 2901}, "https://github.com/bandido37/comfyui-kaggle-local-save": {"stars": 0, "last_update": "2025-04-23 16:20:30", "author_account_age_days": 2109}, "https://github.com/barakapa/barakapa-nodes": {"stars": 0, "last_update": "2025-05-13 20:47:52", "author_account_age_days": 47}, "https://github.com/benda1989/WaterMarkRemover_ComfyUI": {"stars": 1, "last_update": "2025-05-01 22:31:19", "author_account_age_days": 2480}, "https://github.com/benmizrahi/ComfyGCS": {"stars": 1, "last_update": "2025-05-05 15:18:40", "author_account_age_days": 3609}, "https://github.com/beyastard/ComfyUI_BeySoft": {"stars": 0, "last_update": "2024-05-26 22:44:55", "author_account_age_days": 4653}, "https://github.com/bheins/ComfyUI-glb-to-stl": {"stars": 0, "last_update": "2025-05-31 17:41:31", "author_account_age_days": 4031}, "https://github.com/birnam/ComfyUI-GenData-Pack": {"stars": 0, "last_update": "2024-03-25 01:25:23", "author_account_age_days": 5376}, "https://github.com/bleash-dev/ComfyUI-Auth-Manager": {"stars": 0, "last_update": "2025-06-26 13:33:37", "author_account_age_days": 1430}, "https://github.com/bleash-dev/Comfyui-Idle-Checker": {"stars": 0, "last_update": "2025-06-21 21:44:47", "author_account_age_days": 1430}, "https://github.com/blib-la/ComfyUI-Captain-Extensions": {"stars": 0, "last_update": "2024-05-17 23:27:25", "author_account_age_days": 640}, "https://github.com/blueraincoatli/ComfyUI-Model-Cleaner": {"stars": 1, "last_update": "2025-05-29 08:55:38", "author_account_age_days": 679}, "https://github.com/blurymind/cozy-fireplace": {"stars": 4, "last_update": "2024-11-08 19:42:20", "author_account_age_days": 4170}, "https://github.com/bmad4ever/comfyui_bmad_nodes": {"stars": 63, "last_update": "2025-03-17 14:50:46", "author_account_age_days": 3903}, "https://github.com/brace-great/comfyui-eim": {"stars": 0, "last_update": "2025-05-14 06:09:18", "author_account_age_days": 1455}, "https://github.com/brace-great/comfyui-mc": {"stars": 0, "last_update": "2025-06-12 07:56:33", "author_account_age_days": 1455}, "https://github.com/bruce007lee/comfyui-cleaner": {"stars": 3, "last_update": "2024-04-20 15:36:03", "author_account_age_days": 4879}, "https://github.com/bruce007lee/comfyui-tiny-utils": {"stars": 1, "last_update": "2024-08-31 13:34:57", "author_account_age_days": 4879}, "https://github.com/brycegoh/comfyui-custom-nodes": {"stars": 0, "last_update": "2024-06-05 09:30:06", "author_account_age_days": 3475}, "https://github.com/bulldog68/ComfyUI_FMJ": {"stars": 4, "last_update": "2025-06-21 13:54:04", "author_account_age_days": 471}, "https://github.com/c0ffymachyne/ComfyUI_SignalProcessing": {"stars": 11, "last_update": "2025-05-14 01:41:00", "author_account_age_days": 4882}, "https://github.com/casterpollux/MiniMax-bmo": {"stars": 31, "last_update": "2025-06-24 19:22:18", "author_account_age_days": 40}, "https://github.com/catboxanon/ComfyUI-Pixelsmith": {"stars": 4, "last_update": "2025-01-22 03:02:05", "author_account_age_days": 898}, "https://github.com/celll1/cel_sampler": {"stars": 1, "last_update": "2024-11-20 13:04:54", "author_account_age_days": 597}, "https://github.com/cesilk10/cesilk-comfyui-nodes": {"stars": 0, "last_update": "2025-06-20 08:20:32", "author_account_age_days": 50}, "https://github.com/chaojie/ComfyUI-DynamiCrafter": {"stars": 129, "last_update": "2024-06-14 10:23:59", "author_account_age_days": 5200}, "https://github.com/chaojie/ComfyUI-mobvoi-openapi": {"stars": 2, "last_update": "2024-05-29 09:02:52", "author_account_age_days": 5200}, "https://github.com/chenbaiyujason/ComfyUI_StepFun": {"stars": 6, "last_update": "2024-12-05 14:45:27", "author_account_age_days": 2101}, "https://github.com/chengzeyi/Comfy-WaveSpeed": {"stars": 1072, "last_update": "2025-03-27 08:10:29", "author_account_age_days": 3144}, "https://github.com/chetusangolgi/Comfyui-supabase": {"stars": 0, "last_update": "2025-04-30 12:33:21", "author_account_age_days": 760}, "https://github.com/chrisdreid/ComfyUI_EnvAutopsyAPI": {"stars": 4, "last_update": "2024-08-29 03:54:28", "author_account_age_days": 3485}, "https://github.com/christian-byrne/infinite-zoom-parallax-nodes": {"stars": 5, "last_update": "2024-07-08 15:07:05", "author_account_age_days": 1715}, "https://github.com/christian-byrne/python-interpreter-node": {"stars": 58, "last_update": "2025-04-02 02:06:27", "author_account_age_days": 1715}, "https://github.com/chuge26/ComfyUI_seal_migration": {"stars": 0, "last_update": "2025-04-21 07:23:45", "author_account_age_days": 2733}, "https://github.com/cidiro/cid-node-pack": {"stars": 0, "last_update": "2025-03-23 23:26:00", "author_account_age_days": 1996}, "https://github.com/ciga2011/ComfyUI-AppGen": {"stars": 2, "last_update": "2025-01-02 17:00:32", "author_account_age_days": 4566}, "https://github.com/coVISIONSld/ComfyUI-OmniGen2": {"stars": 0, "last_update": "2025-06-26 18:08:46", "author_account_age_days": 265}, "https://github.com/comfyanonymous/ComfyUI": {"stars": 80797, "last_update": "2025-06-26 07:39:12", "author_account_age_days": 916}, "https://github.com/comfyanonymous/ComfyUI_bitsandbytes_NF4": {"stars": 409, "last_update": "2024-08-16 18:06:10", "author_account_age_days": 916}, "https://github.com/comfypod/ComfyUI-Comflow": {"stars": 0, "last_update": "2024-06-17 08:44:08", "author_account_age_days": 390}, "https://github.com/comfyuiblog/deepseek_prompt_generator_comfyui": {"stars": 2, "last_update": "2025-01-28 21:28:11", "author_account_age_days": 257}, "https://github.com/corbin-hayden13/ComfyUI-Better-Dimensions": {"stars": 7, "last_update": "2024-06-12 17:45:21", "author_account_age_days": 2182}, "https://github.com/cubiq/Comfy_Dungeon": {"stars": 259, "last_update": "2024-04-26 11:00:58", "author_account_age_days": 5378}, "https://github.com/cwebbi1/VoidCustomNodes": {"stars": 0, "last_update": "2024-10-07 02:23:02", "author_account_age_days": 372}, "https://github.com/cyberhirsch/seb_nodes": {"stars": 0, "last_update": "2025-06-12 18:30:24", "author_account_age_days": 2234}, "https://github.com/daracazamea/comfyUI-DCNodes": {"stars": 0, "last_update": "2025-04-03 14:38:27", "author_account_age_days": 2326}, "https://github.com/denislov/Comfyui_AutoSurvey": {"stars": 1, "last_update": "2024-08-03 06:50:57", "author_account_age_days": 2354}, "https://github.com/dfl/comfyui-stylegan": {"stars": 0, "last_update": "2024-12-29 18:35:27", "author_account_age_days": 6341}, "https://github.com/dhpdong/ComfyUI-IPAdapter-Flux-Repair": {"stars": 4, "last_update": "2025-05-23 08:51:34", "author_account_age_days": 2277}, "https://github.com/dihan/comfyui-random-kps": {"stars": 3, "last_update": "2025-01-01 22:48:11", "author_account_age_days": 4663}, "https://github.com/diodiogod/Comfy-Inpainting-Works": {"stars": 24, "last_update": "2025-06-24 01:23:56", "author_account_age_days": 495}, "https://github.com/dogcomplex/ComfyUI-LOKI": {"stars": 1, "last_update": "2025-05-07 08:10:12", "author_account_age_days": 4433}, "https://github.com/doucx/ComfyUI_WcpD_Utility_Kit": {"stars": 1, "last_update": "2024-01-06 19:07:45", "author_account_age_days": 2690}, "https://github.com/dowands/ComfyUI-AddMaskForICLora": {"stars": 1, "last_update": "2024-11-26 09:40:06", "author_account_age_days": 2906}, "https://github.com/downlifted/ComfyUI_BWiZ_Nodes": {"stars": 1, "last_update": "2024-12-27 17:03:52", "author_account_age_days": 2614}, "https://github.com/eigenpunk/ComfyUI-audio": {"stars": 88, "last_update": "2024-03-03 21:14:14", "author_account_age_days": 1292}, "https://github.com/ejektaflex/ComfyUI-Ty": {"stars": 0, "last_update": "2024-06-12 16:08:16", "author_account_age_days": 3139}, "https://github.com/emranemran/ComfyUI-FasterLivePortrait": {"stars": 0, "last_update": "2024-12-18 20:03:19", "author_account_age_days": 4552}, "https://github.com/endman100/ComfyUI-SaveAndLoadPromptCondition": {"stars": 0, "last_update": "2024-07-03 09:35:02", "author_account_age_days": 2840}, "https://github.com/endman100/ComfyUI-augmentation": {"stars": 0, "last_update": "2024-07-23 09:06:24", "author_account_age_days": 2840}, "https://github.com/ericbeyer/guidance_interval": {"stars": 2, "last_update": "2024-04-16 03:24:01", "author_account_age_days": 2961}, "https://github.com/erosDiffusion/ComfyUI-enricos-json-file-load-and-value-selector": {"stars": 2, "last_update": "2025-06-04 16:32:17", "author_account_age_days": 363}, "https://github.com/esciron/ComfyUI-HunyuanVideoWrapper-Extended": {"stars": 4, "last_update": "2025-01-04 22:27:09", "author_account_age_days": 3361}, "https://github.com/etng/ComfyUI-Heartbeat": {"stars": 2, "last_update": "2025-06-03 09:32:40", "author_account_age_days": 1431}, "https://github.com/exectails/comfyui-et_scripting": {"stars": 1, "last_update": "2024-11-29 17:23:07", "author_account_age_days": 4285}, "https://github.com/eyekayem/comfyui_runway_gen3": {"stars": 0, "last_update": "2025-01-27 06:59:45", "author_account_age_days": 979}, "https://github.com/fablestudio/ComfyUI-Showrunner-Utils": {"stars": 0, "last_update": "2025-06-04 04:34:09", "author_account_age_days": 2415}, "https://github.com/facok/ComfyUI-FokToolset": {"stars": 5, "last_update": "2025-04-24 19:29:57", "author_account_age_days": 825}, "https://github.com/fangg2000/ComfyUI-SenseVoice": {"stars": 0, "last_update": "2025-05-06 06:42:52", "author_account_age_days": 795}, "https://github.com/fangg2000/ComfyUI-StableAudioFG": {"stars": 0, "last_update": "2025-06-15 11:49:34", "author_account_age_days": 795}, "https://github.com/fangziheng2321/comfyuinode_chopmask": {"stars": 0, "last_update": "2025-02-17 03:16:50", "author_account_age_days": 1539}, "https://github.com/filipemeneses/ComfyUI_html": {"stars": 0, "last_update": "2025-06-10 10:53:55", "author_account_age_days": 3843}, "https://github.com/filliptm/ComfyUI_Fill-Node-Loader": {"stars": 2, "last_update": "2025-06-25 01:25:38", "author_account_age_days": 2103}, "https://github.com/flowtyone/comfyui-flowty-lcm": {"stars": 63, "last_update": "2023-10-23 12:08:55", "author_account_age_days": 640}, "https://github.com/flyingdogsoftware/gyre_for_comfyui": {"stars": 1, "last_update": "2024-11-18 22:35:37", "author_account_age_days": 2380}, "https://github.com/foglerek/comfyui-cem-tools": {"stars": 1, "last_update": "2024-01-13 23:22:07", "author_account_age_days": 4405}, "https://github.com/franky519/comfyui-redux-style": {"stars": 0, "last_update": "2025-02-13 10:04:45", "author_account_age_days": 644}, "https://github.com/franky519/comfyui_fnckc_Face_analysis": {"stars": 0, "last_update": "2025-06-16 02:09:00", "author_account_age_days": 644}, "https://github.com/fritzprix/ComfyUI-LLM-Utils": {"stars": 1, "last_update": "2025-01-04 23:25:38", "author_account_age_days": 5094}, "https://github.com/ftechmax/ComfyUI-NovaKit-Pack": {"stars": 0, "last_update": "2025-04-26 13:27:06", "author_account_age_days": 2945}, "https://github.com/ftf001-tech/ComfyUI-ExternalLLMDetector": {"stars": 1, "last_update": "2025-06-22 03:43:09", "author_account_age_days": 2063}, "https://github.com/futureversecom/ComfyUI-JEN": {"stars": 1, "last_update": "2024-08-06 00:24:56", "author_account_age_days": 1086}, "https://github.com/fuzr0dah/comfyui-sceneassembly": {"stars": 0, "last_update": "2025-05-18 12:27:05", "author_account_age_days": 3471}, "https://github.com/gabe-init/ComfyUI-LM-Studio": {"stars": 0, "last_update": "2025-05-26 22:10:36", "author_account_age_days": 31}, "https://github.com/gabe-init/ComfyUI-Repo-Eater": {"stars": 0, "last_update": "2025-05-27 01:09:24", "author_account_age_days": 31}, "https://github.com/gabe-init/comfyui_ui_render": {"stars": 1, "last_update": "2025-05-27 00:27:32", "author_account_age_days": 31}, "https://github.com/gagaprince/ComfyUI_gaga_utils": {"stars": 0, "last_update": "2025-05-12 09:54:34", "author_account_age_days": 4227}, "https://github.com/galoreware/ComfyUI-GaloreNodes": {"stars": 0, "last_update": "2024-10-24 05:47:23", "author_account_age_days": 1792}, "https://github.com/gameltb/ComfyUI_paper_playground": {"stars": 10, "last_update": "2025-05-14 16:18:43", "author_account_age_days": 4422}, "https://github.com/gameltb/ComfyUI_stable_fast": {"stars": 208, "last_update": "2024-08-04 09:25:33", "author_account_age_days": 4422}, "https://github.com/gameltb/io_comfyui": {"stars": 6, "last_update": "2025-02-04 15:14:01", "author_account_age_days": 4422}, "https://github.com/gamtruliar/ComfyUI-N_SwapInput": {"stars": 0, "last_update": "2025-05-08 19:08:30", "author_account_age_days": 4491}, "https://github.com/gilons/ComfyUI-GoogleDrive-Downloader": {"stars": 0, "last_update": "2025-06-13 20:43:59", "author_account_age_days": 2915}, "https://github.com/gioferreira/ComfyUI-Molde-Utils": {"stars": 0, "last_update": "2025-02-27 20:53:33", "author_account_age_days": 3327}, "https://github.com/gitadmini/comfyui_extractstoryboards": {"stars": 1, "last_update": "2025-06-11 02:01:24", "author_account_age_days": 3409}, "https://github.com/githubYiheng/comfyui_median_filter": {"stars": 0, "last_update": "2024-07-03 11:38:39", "author_account_age_days": 4270}, "https://github.com/gitmylo/FlowNodes": {"stars": 11, "last_update": "2025-04-03 08:17:47", "author_account_age_days": 2674}, "https://github.com/glamorfleet0i/ComfyUI-Firewall": {"stars": 0, "last_update": "2024-12-30 02:14:57", "author_account_age_days": 185}, "https://github.com/gmorks/ComfyUI-Animagine-Prompt": {"stars": 9, "last_update": "2025-02-21 08:34:05", "author_account_age_days": 2662}, "https://github.com/go-package-lab/ComfyUI-Tools-Video-Combine": {"stars": 1, "last_update": "2024-09-24 03:54:00", "author_account_age_days": 1751}, "https://github.com/godric8/ComfyUI_Step1X-Edit": {"stars": 0, "last_update": "2025-06-02 12:14:14", "author_account_age_days": 1811}, "https://github.com/gold24park/loki-comfyui-node": {"stars": 0, "last_update": "2025-02-07 01:55:07", "author_account_age_days": 3657}, "https://github.com/gondar-software/ComfyUI-Affine-Transform": {"stars": 3, "last_update": "2024-10-05 17:42:40", "author_account_age_days": 326}, "https://github.com/gondar-software/ComfyUI-Simple-Image-Tools": {"stars": 0, "last_update": "2024-10-12 18:29:58", "author_account_age_days": 326}, "https://github.com/gordon123/ComfyUI_DreamBoard": {"stars": 2, "last_update": "2025-05-18 09:53:50", "author_account_age_days": 5462}, "https://github.com/gordon123/ComfyUI_srt2speech": {"stars": 3, "last_update": "2025-04-27 13:00:13", "author_account_age_days": 5462}, "https://github.com/grimli333/ComfyUI_Grim": {"stars": 0, "last_update": "2024-12-01 18:10:07", "author_account_age_days": 5131}, "https://github.com/grinlau18/ComfyUI_XISER_Nodes": {"stars": 15, "last_update": "2025-06-26 11:29:33", "author_account_age_days": 671}, "https://github.com/grokuku/ComfyUI-Holaf": {"stars": 1, "last_update": "2025-06-10 13:53:54", "author_account_age_days": 2824}, "https://github.com/grokuku/ComfyUI-Holaf-Utilities": {"stars": 1, "last_update": "2025-06-25 21:30:44", "author_account_age_days": 2824}, "https://github.com/hananbeer/node_dev": {"stars": 6, "last_update": "2024-08-19 08:08:39", "author_account_age_days": 1911}, "https://github.com/haodman/ComfyUI_Rain": {"stars": 1, "last_update": "2024-09-01 10:41:20", "author_account_age_days": 2506}, "https://github.com/haofanwang/ComfyUI-InstantStyle": {"stars": 8, "last_update": "2024-05-23 16:11:13", "author_account_age_days": 3344}, "https://github.com/haomole/Comfyui-SadTalker": {"stars": 20, "last_update": "2024-08-05 02:44:26", "author_account_age_days": 668}, "https://github.com/hay86/ComfyUI_AceNodes": {"stars": 62, "last_update": "2025-05-01 03:08:58", "author_account_age_days": 5034}, "https://github.com/hayden-fr/ComfyUI-Image-Browsing": {"stars": 16, "last_update": "2025-04-21 02:35:46", "author_account_age_days": 2304}, "https://github.com/hdfhssg/ComfyUI_pxtool": {"stars": 4, "last_update": "2025-03-02 06:23:44", "author_account_age_days": 1611}, "https://github.com/hdfhssg/comfyui_EvoSearch": {"stars": 6, "last_update": "2025-06-15 11:05:48", "author_account_age_days": 1611}, "https://github.com/hiusdev/ComfyUI_Lah_Toffee": {"stars": 0, "last_update": "2025-02-14 12:40:14", "author_account_age_days": 1711}, "https://github.com/hnmr293/ComfyUI-SamOne": {"stars": 0, "last_update": "2025-04-16 08:07:42", "author_account_age_days": 922}, "https://github.com/horidream/ComfyUI-Horidream": {"stars": 0, "last_update": "2024-09-08 08:57:57", "author_account_age_days": 5410}, "https://github.com/hotpizzatactics/ComfyUI-WaterMark-Detector": {"stars": 0, "last_update": "2024-07-23 14:36:35", "author_account_age_days": 343}, "https://github.com/hotpot-killer/ComfyUI_AlexNodes": {"stars": 0, "last_update": "2024-12-06 09:09:03", "author_account_age_days": 2593}, "https://github.com/houdinii/comfy-magick": {"stars": 5, "last_update": "2024-03-11 06:40:54", "author_account_age_days": 3887}, "https://github.com/huizhang0110/ComfyUI_Easy_Nodes_hui": {"stars": 2, "last_update": "2024-02-27 08:22:49", "author_account_age_days": 2822}, "https://github.com/hunterssl/ComfyUI_SSLNodes": {"stars": 0, "last_update": "2025-01-20 07:23:52", "author_account_age_days": 3203}, "https://github.com/hunzmusic/ComfyUI-Hunyuan3DTools": {"stars": 4, "last_update": "2025-06-19 18:11:36", "author_account_age_days": 95}, "https://github.com/hunzmusic/Comfyui-CraftsMan3DWrapper": {"stars": 14, "last_update": "2025-05-09 10:46:59", "author_account_age_days": 95}, "https://github.com/hunzmusic/comfyui-hnznodes": {"stars": 1, "last_update": "2025-03-24 21:53:50", "author_account_age_days": 95}, "https://github.com/hy134300/comfyui-hb-node": {"stars": 0, "last_update": "2024-04-09 09:56:22", "author_account_age_days": 2128}, "https://github.com/hy134300/comfyui-hydit": {"stars": 9, "last_update": "2024-06-07 09:52:15", "author_account_age_days": 2128}, "https://github.com/hylarucoder/comfyui-copilot": {"stars": 26, "last_update": "2024-06-28 04:43:18", "author_account_age_days": 4280}, "https://github.com/iacoposk8/xor_pickle_nodes": {"stars": 1, "last_update": "2025-06-24 17:46:56", "author_account_age_days": 4521}, "https://github.com/if-ai/ComfyUI-IF_Zonos": {"stars": 1, "last_update": "2025-02-18 01:28:04", "author_account_age_days": 3229}, "https://github.com/ilovejohnwhite/Tracer": {"stars": 0, "last_update": "2024-11-26 03:39:33", "author_account_age_days": 1240}, "https://github.com/immersiveexperience/ie-comfyui-color-nodes": {"stars": 2, "last_update": "2024-06-18 10:54:55", "author_account_age_days": 636}, "https://github.com/io-club/ComfyUI-LuminaNext": {"stars": 0, "last_update": "2024-09-23 12:02:22", "author_account_age_days": 1006}, "https://github.com/jammyfu/ComfyUI_PaintingCoderUtils": {"stars": 12, "last_update": "2025-02-26 05:03:05", "author_account_age_days": 4845}, "https://github.com/jax-explorer/ComfyUI-DreamO": {"stars": 66, "last_update": "2025-05-22 08:07:02", "author_account_age_days": 944}, "https://github.com/jcomeme/ComfyUI-AsunaroTools": {"stars": 1, "last_update": "2025-03-21 03:57:39", "author_account_age_days": 5217}, "https://github.com/jerryname2022/ComfyUI-Real-ESRGAN": {"stars": 0, "last_update": "2025-04-19 10:54:34", "author_account_age_days": 3646}, "https://github.com/jgbrblmd/ComfyUI-ComfyFluxSize": {"stars": 0, "last_update": "2024-08-30 06:42:39", "author_account_age_days": 823}, "https://github.com/jgbyte/ComfyUI-RandomCube": {"stars": 0, "last_update": "2025-02-19 23:13:05", "author_account_age_days": 322}, "https://github.com/jiafuzeng/comfyui-fishSpeech": {"stars": 0, "last_update": "2025-06-26 10:50:03", "author_account_age_days": 2580}, "https://github.com/jimmm-ai/TimeUi-a-ComfyUi-Timeline-Node": {"stars": 227, "last_update": "2024-07-04 11:44:03", "author_account_age_days": 387}, "https://github.com/jimstudt/ComfyUI-Jims-Nodes": {"stars": 0, "last_update": "2025-01-21 17:36:29", "author_account_age_days": 5314}, "https://github.com/jinchanz/ComfyUI-AliCloud-Bailian": {"stars": 1, "last_update": "2025-06-06 11:50:34", "author_account_age_days": 2432}, "https://github.com/jn-jairo/jn_node_suite_comfyui": {"stars": 6, "last_update": "2024-06-08 05:15:33", "author_account_age_days": 4351}, "https://github.com/jordancoult/ComfyUI_HelpfulNodes": {"stars": 0, "last_update": "2025-05-17 01:04:37", "author_account_age_days": 2792}, "https://github.com/jschoormans/Comfy-InterestingPixels": {"stars": 1, "last_update": "2025-02-05 08:34:17", "author_account_age_days": 3906}, "https://github.com/jtscmw01/ComfyUI-DiffBIR": {"stars": 290, "last_update": "2024-05-21 05:28:34", "author_account_age_days": 869}, "https://github.com/jtydhr88/ComfyUI-Unique3D": {"stars": 212, "last_update": "2024-10-18 10:37:10", "author_account_age_days": 5120}, "https://github.com/jtydhr88/ComfyUI_frontend_vue_basic": {"stars": 3, "last_update": "2025-06-13 00:22:50", "author_account_age_days": 5120}, "https://github.com/kadirnar/ComfyUI-Adapter": {"stars": 3, "last_update": "2024-04-03 12:05:39", "author_account_age_days": 2696}, "https://github.com/kandy/ComfyUI-KAndy": {"stars": 0, "last_update": "2025-04-08 01:42:33", "author_account_age_days": 5840}, "https://github.com/kappa54m/ComfyUI_Usability": {"stars": 0, "last_update": "2024-08-08 15:31:47", "author_account_age_days": 1878}, "https://github.com/karthikg-09/ComfyUI-3ncrypt": {"stars": 0, "last_update": "2024-12-27 09:09:07", "author_account_age_days": 563}, "https://github.com/kevin314/ComfyUI-FastVideo": {"stars": 2, "last_update": "2025-05-25 10:25:28", "author_account_age_days": 2499}, "https://github.com/kijai/ComfyUI-CV-VAE": {"stars": 11, "last_update": "2024-06-03 21:46:49", "author_account_age_days": 2550}, "https://github.com/kijai/ComfyUI-DeepSeek-VL": {"stars": 46, "last_update": "2024-05-21 16:43:40", "author_account_age_days": 2550}, "https://github.com/kijai/ComfyUI-DiffSynthWrapper": {"stars": 61, "last_update": "2024-06-22 00:16:46", "author_account_age_days": 2550}, "https://github.com/kijai/ComfyUI-DiffusersSD3Wrapper": {"stars": 10, "last_update": "2024-06-17 13:03:43", "author_account_age_days": 2550}, "https://github.com/kijai/ComfyUI-EasyAnimateWrapper": {"stars": 85, "last_update": "2024-08-14 02:20:18", "author_account_age_days": 2550}, "https://github.com/kijai/ComfyUI-FollowYourEmojiWrapper": {"stars": 63, "last_update": "2025-04-18 10:50:26", "author_account_age_days": 2550}, "https://github.com/kijai/ComfyUI-FramePackWrapper": {"stars": 1356, "last_update": "2025-06-03 21:48:59", "author_account_age_days": 2550}, "https://github.com/kijai/ComfyUI-Hunyuan3DWrapper": {"stars": 759, "last_update": "2025-06-15 09:52:41", "author_account_age_days": 2550}, "https://github.com/kijai/ComfyUI-HunyuanVideoWrapper": {"stars": 2487, "last_update": "2025-05-12 13:31:36", "author_account_age_days": 2550}, "https://github.com/kijai/ComfyUI-MMAudio": {"stars": 358, "last_update": "2025-01-23 17:06:52", "author_account_age_days": 2550}, "https://github.com/kijai/ComfyUI-MochiWrapper": {"stars": 787, "last_update": "2024-11-11 13:54:57", "author_account_age_days": 2550}, "https://github.com/kijai/ComfyUI-VEnhancer": {"stars": 73, "last_update": "2024-11-02 00:24:36", "author_account_age_days": 2550}, "https://github.com/kijai/ComfyUI-VideoNoiseWarp": {"stars": 155, "last_update": "2025-03-30 13:39:03", "author_account_age_days": 2550}, "https://github.com/kijai/ComfyUI-WanVideoWrapper": {"stars": 3079, "last_update": "2025-06-20 14:31:02", "author_account_age_days": 2550}, "https://github.com/kimara-ai/ComfyUI-Kimara-AI-Advanced-Watermarks": {"stars": 16, "last_update": "2025-04-03 17:22:59", "author_account_age_days": 223}, "https://github.com/kimara-ai/ComfyUI-Kimara-AI-Image-From-URL": {"stars": 0, "last_update": "2025-05-06 07:50:34", "author_account_age_days": 223}, "https://github.com/kk8bit/KayTool": {"stars": 151, "last_update": "2025-05-25 03:46:23", "author_account_age_days": 724}, "https://github.com/krich-cto/ComfyUI-Flow-Control": {"stars": 1, "last_update": "2025-06-01 03:38:17", "author_account_age_days": 1632}, "https://github.com/krisshen2021/comfyui_OpenRouterNodes": {"stars": 0, "last_update": "2025-02-22 02:29:36", "author_account_age_days": 1555}, "https://github.com/kuschanow/ComfyUI-SD-Slicer": {"stars": 0, "last_update": "2024-12-08 16:59:31", "author_account_age_days": 1748}, "https://github.com/kxh/ComfyUI-ImageUpscaleWithModelMultipleTimes": {"stars": 0, "last_update": "2024-10-16 13:53:50", "author_account_age_days": 4914}, "https://github.com/kxh/ComfyUI-sam2": {"stars": 1, "last_update": "2024-10-10 18:06:11", "author_account_age_days": 4914}, "https://github.com/kycg/comfyui-Kwtoolset": {"stars": 0, "last_update": "2024-11-04 21:14:07", "author_account_age_days": 1307}, "https://github.com/kylegrover/comfyui-python-cowboy": {"stars": 1, "last_update": "2024-11-04 18:37:04", "author_account_age_days": 3019}, "https://github.com/l1yongch1/ComfyUI-YcNodes": {"stars": 1, "last_update": "2025-05-05 04:00:28", "author_account_age_days": 1143}, "https://github.com/laksjdjf/ssd-1b-comfyui": {"stars": 1, "last_update": "2023-10-27 20:05:06", "author_account_age_days": 3198}, "https://github.com/laubsauger/comfyui-storyboard": {"stars": 6, "last_update": "2025-06-14 23:33:25", "author_account_age_days": 4922}, "https://github.com/lcolok/ComfyUI-MagicAI": {"stars": 7, "last_update": "2024-11-14 08:21:40", "author_account_age_days": 2780}, "https://github.com/leadbreak/comfyui-faceaging": {"stars": 85, "last_update": "2024-10-31 08:25:21", "author_account_age_days": 1740}, "https://github.com/leeguandong/ComfyUI_AliControlnetInpainting": {"stars": 3, "last_update": "2024-09-25 10:44:58", "author_account_age_days": 3161}, "https://github.com/leoleelxh/ComfyUI-MidjourneyNode-leoleexh": {"stars": 23, "last_update": "2024-08-01 03:37:17", "author_account_age_days": 4442}, "https://github.com/leon-etienne/ComfyUI_Scoring-Nodes": {"stars": 0, "last_update": "2025-04-21 11:48:26", "author_account_age_days": 719}, "https://github.com/lgldlk/ComfyUI-img-tiler": {"stars": 1, "last_update": "2024-10-17 07:56:42", "author_account_age_days": 2057}, "https://github.com/lichenhao/Comfyui_Ryota": {"stars": 0, "last_update": "2024-09-07 08:25:54", "author_account_age_days": 4734}, "https://github.com/linhusyung/comfyui-Build-and-train-your-network": {"stars": 106, "last_update": "2024-06-26 05:44:43", "author_account_age_days": 1044}, "https://github.com/littleowl/ComfyUI-MV-HECV": {"stars": 1, "last_update": "2025-06-04 12:42:47", "author_account_age_days": 5028}, "https://github.com/logtd/ComfyUI-Fluxtapoz": {"stars": 1344, "last_update": "2025-01-09 02:38:40", "author_account_age_days": 489}, "https://github.com/logtd/ComfyUI-HunyuanLoom": {"stars": 476, "last_update": "2025-02-21 21:01:57", "author_account_age_days": 489}, "https://github.com/logtd/ComfyUI-Veevee": {"stars": 61, "last_update": "2024-08-12 03:04:12", "author_account_age_days": 489}, "https://github.com/longgui0318/comfyui-one-more-step": {"stars": 1, "last_update": "2024-05-07 08:40:56", "author_account_age_days": 4532}, "https://github.com/longzoho/ComfyUI-Qdrant-Saver": {"stars": 0, "last_update": "2025-03-07 13:44:52", "author_account_age_days": 1886}, "https://github.com/lord-lethris/ComfyUI-RPG-Characters": {"stars": 1, "last_update": "2025-06-18 23:08:15", "author_account_age_days": 4791}, "https://github.com/lordwedggie/xcpNodes": {"stars": 0, "last_update": "2024-11-15 06:24:48", "author_account_age_days": 920}, "https://github.com/lrzjason/Comfyui-Condition-Utils": {"stars": 6, "last_update": "2025-05-18 17:09:17", "author_account_age_days": 4030}, "https://github.com/ltdrdata/ComfyUI-Workflow-Component": {"stars": 243, "last_update": "2024-07-30 08:08:28", "author_account_age_days": 829}, "https://github.com/ltdrdata/comfyui-unsafe-torch": {"stars": 23, "last_update": "2025-06-10 22:31:29", "author_account_age_days": 829}, "https://github.com/lu64k/SK-Nodes": {"stars": 0, "last_update": "2024-11-18 03:47:34", "author_account_age_days": 758}, "https://github.com/lucafoscili/lf-nodes": {"stars": 11, "last_update": "2025-06-23 07:17:31", "author_account_age_days": 2401}, "https://github.com/lum3on/comfyui_LLM_Polymath": {"stars": 63, "last_update": "2025-06-12 22:24:36", "author_account_age_days": 141}, "https://github.com/lum3on/comfyui_RollingDepth": {"stars": 1, "last_update": "2025-06-01 18:46:56", "author_account_age_days": 141}, "https://github.com/m-ai-studio/mai-prompt-progress": {"stars": 0, "last_update": "2025-04-14 19:13:55", "author_account_age_days": 415}, "https://github.com/machinesarenotpeople/comfyui-energycost": {"stars": 0, "last_update": "2025-05-03 21:22:23", "author_account_age_days": 1900}, "https://github.com/maekawataiki/ComfyUI-ALB-Login": {"stars": 3, "last_update": "2025-01-17 02:10:49", "author_account_age_days": 3030}, "https://github.com/maizerrr/comfyui-code-nodes": {"stars": 0, "last_update": "2025-06-24 12:28:51", "author_account_age_days": 3437}, "https://github.com/majorsauce/comfyui_indieTools": {"stars": 0, "last_update": "2024-06-25 08:59:57", "author_account_age_days": 2172}, "https://github.com/mamorett/ComfyUI-SmolVLM": {"stars": 5, "last_update": "2024-11-30 14:31:14", "author_account_age_days": 1118}, "https://github.com/mamorett/comfyui_minicpm_vision": {"stars": 0, "last_update": "2025-06-17 13:25:18", "author_account_age_days": 1118}, "https://github.com/marcueberall/ComfyUI-BuildPath": {"stars": 0, "last_update": "2024-02-06 07:57:33", "author_account_age_days": 2149}, "https://github.com/marduk191/comfyui-marnodes": {"stars": 3, "last_update": "2025-03-27 13:26:45", "author_account_age_days": 4782}, "https://github.com/maruhidd/ComfyUI_Transparent-Background": {"stars": 4, "last_update": "2024-06-14 07:02:56", "author_account_age_days": 2622}, "https://github.com/mashb1t/comfyui-nodes-mashb1t": {"stars": 0, "last_update": "2024-06-11 15:55:53", "author_account_age_days": 3902}, "https://github.com/masmullin2000/ComfyUI-MMYolo": {"stars": 0, "last_update": "2025-02-22 22:23:02", "author_account_age_days": 4445}, "https://github.com/matDobek/ComfyUI_duck": {"stars": 0, "last_update": "2025-05-21 13:12:40", "author_account_age_days": 4444}, "https://github.com/maurorilla/ComfyUI-MisterMR-Nodes": {"stars": 0, "last_update": "2025-05-09 13:18:07", "author_account_age_days": 4383}, "https://github.com/mehbebe/ComfyLoraGallery": {"stars": 1, "last_update": "2024-12-29 12:44:29", "author_account_age_days": 725}, "https://github.com/melMass/ComfyUI-Lygia": {"stars": 0, "last_update": "2024-07-14 09:59:10", "author_account_age_days": 4112}, "https://github.com/mikebilly/Transparent-background-comfyUI": {"stars": 2, "last_update": "2025-01-29 16:29:23", "author_account_age_days": 2931}, "https://github.com/mikeymcfish/FishTools": {"stars": 26, "last_update": "2024-07-13 20:51:17", "author_account_age_days": 3770}, "https://github.com/mikheys/ComfyUI-mikheys": {"stars": 0, "last_update": "2025-06-21 15:35:56", "author_account_age_days": 2768}, "https://github.com/minhtuannhn/comfyui-gemini-studio": {"stars": 0, "last_update": "2024-11-19 16:05:05", "author_account_age_days": 1548}, "https://github.com/miragecoa/ComfyUI-LLM-Evaluation": {"stars": 1, "last_update": "2024-11-21 01:29:48", "author_account_age_days": 933}, "https://github.com/mliand/ComfyUI-Calendar-Node": {"stars": 0, "last_update": "2025-01-10 07:33:40", "author_account_age_days": 753}, "https://github.com/mm-akhtar/comfyui-mask-selector-node": {"stars": 0, "last_update": "2025-04-18 10:06:17", "author_account_age_days": 1860}, "https://github.com/mohamedsobhi777/ComfyUI-FramerComfy": {"stars": 0, "last_update": "2025-01-25 14:39:17", "author_account_age_days": 2786}, "https://github.com/molbal/comfy-url-fetcher": {"stars": 0, "last_update": "2025-02-02 13:37:48", "author_account_age_days": 4264}, "https://github.com/moonwhaler/comfyui-moonpack": {"stars": 0, "last_update": "2025-06-20 16:22:16", "author_account_age_days": 4791}, "https://github.com/mr-krak3n/ComfyUI-Qwen": {"stars": 21, "last_update": "2025-03-08 12:12:29", "author_account_age_days": 2408}, "https://github.com/mut-ex/comfyui-gligengui-node": {"stars": 51, "last_update": "2024-02-28 02:46:05", "author_account_age_days": 3225}, "https://github.com/muvich3n/ComfyUI-Claude-I2T": {"stars": 0, "last_update": "2025-01-15 07:50:46", "author_account_age_days": 1665}, "https://github.com/muvich3n/ComfyUI-Crop-Border": {"stars": 0, "last_update": "2025-02-24 10:01:53", "author_account_age_days": 1665}, "https://github.com/naderzare/comfyui-inodes": {"stars": 0, "last_update": "2025-02-05 04:32:29", "author_account_age_days": 3057}, "https://github.com/nat-chan/comfyui-exec": {"stars": 3, "last_update": "2024-05-28 11:56:37", "author_account_age_days": 3360}, "https://github.com/nat-chan/comfyui-paint": {"stars": 3, "last_update": "2024-06-14 11:01:38", "author_account_age_days": 3360}, "https://github.com/nat-chan/transceiver": {"stars": 1, "last_update": "2024-05-01 10:03:01", "author_account_age_days": 3360}, "https://github.com/neeltheninja/ComfyUI-TempFileDeleter": {"stars": 0, "last_update": "2024-10-26 19:25:43", "author_account_age_days": 2245}, "https://github.com/neeltheninja/ComfyUI-TextOverlay": {"stars": 0, "last_update": "2024-07-31 18:40:19", "author_account_age_days": 2245}, "https://github.com/neo0801/my-comfy-node": {"stars": 0, "last_update": "2024-09-20 07:49:04", "author_account_age_days": 4147}, "https://github.com/netanelben/comfyui-camera2image-customnode": {"stars": 1, "last_update": "2024-09-29 15:14:57", "author_account_age_days": 4249}, "https://github.com/netanelben/comfyui-image2image-customnode": {"stars": 1, "last_update": "2024-09-29 12:50:53", "author_account_age_days": 4249}, "https://github.com/netanelben/comfyui-photobooth-customnode": {"stars": 0, "last_update": "2024-10-02 08:00:05", "author_account_age_days": 4249}, "https://github.com/netanelben/comfyui-text2image-customnode": {"stars": 4, "last_update": "2024-09-29 15:19:37", "author_account_age_days": 4249}, "https://github.com/neverbiasu/ComfyUI-ControlNeXt": {"stars": 3, "last_update": "2024-08-15 08:15:43", "author_account_age_days": 1386}, "https://github.com/neverbiasu/ComfyUI-DeepSeek": {"stars": 0, "last_update": "2025-02-01 04:17:59", "author_account_age_days": 1386}, "https://github.com/neverbiasu/ComfyUI-Show-o": {"stars": 0, "last_update": "2025-06-24 06:33:20", "author_account_age_days": 1386}, "https://github.com/neverbiasu/ComfyUI-StereoCrafter": {"stars": 4, "last_update": "2024-12-30 13:32:43", "author_account_age_days": 1386}, "https://github.com/newraina/ComfyUI-Remote-Save-Image": {"stars": 0, "last_update": "2025-04-18 10:50:44", "author_account_age_days": 3804}, "https://github.com/nidefawl/ComfyUI-nidefawl": {"stars": 0, "last_update": "2024-01-16 18:16:41", "author_account_age_days": 5235}, "https://github.com/nikkuexe/ComfyUI-ListDataHelpers": {"stars": 0, "last_update": "2024-09-21 16:15:57", "author_account_age_days": 4916}, "https://github.com/nkchocoai/ComfyUI-PromptUtilities": {"stars": 19, "last_update": "2025-03-30 08:19:25", "author_account_age_days": 527}, "https://github.com/nobandegani/comfyui_ino_nodes": {"stars": 1, "last_update": "2025-06-25 12:08:40", "author_account_age_days": 1654}, "https://github.com/nomcycle/ComfyUI_Cluster": {"stars": 3, "last_update": "2025-05-28 03:57:01", "author_account_age_days": 4705}, "https://github.com/norgeous/ComfyUI-UI-Builder": {"stars": 9, "last_update": "2024-08-11 22:22:04", "author_account_age_days": 4396}, "https://github.com/osuiso-depot/comfyui-keshigom_custom": {"stars": 0, "last_update": "2025-02-27 10:01:17", "author_account_age_days": 1477}, "https://github.com/owengillett/ComfyUI-tilefusion": {"stars": 0, "last_update": "2025-02-19 11:05:53", "author_account_age_days": 2099}, "https://github.com/oxysoft/Comfy-Compel": {"stars": 0, "last_update": "2025-04-08 13:12:20", "author_account_age_days": 4478}, "https://github.com/oxysoft/ComfyUI-uiapi": {"stars": 0, "last_update": "2025-01-27 18:29:08", "author_account_age_days": 4478}, "https://github.com/oyvindg/ComfyUI-TrollSuite": {"stars": 4, "last_update": "2024-08-15 10:37:43", "author_account_age_days": 2696}, "https://github.com/oztrkoguz/ComfyUI_Kosmos2_BBox_Cutter": {"stars": 16, "last_update": "2024-07-25 05:50:01", "author_account_age_days": 1208}, "https://github.com/p1atdev/comfyui-aesthetic-predictor": {"stars": 0, "last_update": "2025-05-10 08:03:13", "author_account_age_days": 1982}, "https://github.com/pacchikAI/ImagePromptBatch": {"stars": 0, "last_update": "2025-05-26 12:48:05", "author_account_age_days": 45}, "https://github.com/pamparamm/ComfyUI-ppm": {"stars": 195, "last_update": "2025-06-13 16:23:47", "author_account_age_days": 2498}, "https://github.com/papcorns/ComfyUI-Papcorns-Node-UploadToGCS": {"stars": 0, "last_update": "2025-05-28 09:31:23", "author_account_age_days": 1879}, "https://github.com/parmarjh/ComfyUI-MochiWrapper-I2V": {"stars": 0, "last_update": "2025-01-10 14:28:51", "author_account_age_days": 1928}, "https://github.com/paulhoux/Smart-Prompting": {"stars": 0, "last_update": "2025-03-10 09:16:44", "author_account_age_days": 5490}, "https://github.com/phamngoctukts/ComyUI-Tupham": {"stars": 1, "last_update": "2025-01-09 04:02:54", "author_account_age_days": 4254}, "https://github.com/pictorialink/ComfyUI-static-resource": {"stars": 0, "last_update": "2025-05-29 09:04:55", "author_account_age_days": 43}, "https://github.com/pinkpixel-dev/comfyui-llm-prompt-enhancer": {"stars": 6, "last_update": "2025-01-28 12:43:25", "author_account_age_days": 156}, "https://github.com/pixuai/ComfyUI-PixuAI": {"stars": 0, "last_update": "2025-03-01 13:56:56", "author_account_age_days": 117}, "https://github.com/pmarmotte2/Comfyui-VibeVoiceSelector": {"stars": 1, "last_update": "2025-04-08 11:18:55", "author_account_age_days": 431}, "https://github.com/poisenbery/NudeNet-Detector-Provider": {"stars": 1, "last_update": "2024-02-26 02:11:27", "author_account_age_days": 1604}, "https://github.com/pomelyu/cy-prompt-tools": {"stars": 0, "last_update": "2025-06-13 15:09:26", "author_account_age_days": 4622}, "https://github.com/power88/ComfyUI-PDiD-Nodes": {"stars": 0, "last_update": "2025-01-04 11:21:29", "author_account_age_days": 3098}, "https://github.com/prabinpebam/anyPython": {"stars": 13, "last_update": "2025-02-15 06:56:01", "author_account_age_days": 4609}, "https://github.com/prodogape/ComfyUI-clip-interrogator": {"stars": 58, "last_update": "2024-07-27 18:33:22", "author_account_age_days": 1397}, "https://github.com/pschroedl/ComfyUI-StreamDiffusion": {"stars": 5, "last_update": "2025-05-21 01:33:15", "author_account_age_days": 4354}, "https://github.com/pzzmyc/comfyui-sd3-simple-simpletuner": {"stars": 1, "last_update": "2024-06-19 12:48:18", "author_account_age_days": 2466}, "https://github.com/qlikpetersen/ComfyUI-AI_Tools": {"stars": 0, "last_update": "2025-06-20 15:46:20", "author_account_age_days": 1395}, "https://github.com/rakki194/ComfyUI_WolfSigmas": {"stars": 4, "last_update": "2025-05-21 13:02:21", "author_account_age_days": 144}, "https://github.com/ralonsobeas/ComfyUI-HDRConversion": {"stars": 5, "last_update": "2024-12-12 20:21:26", "author_account_age_days": 2425}, "https://github.com/redhottensors/ComfyUI-ODE": {"stars": 49, "last_update": "2024-08-01 06:57:05", "author_account_age_days": 506}, "https://github.com/retech995/Save_Florence2_Bulk_Prompts": {"stars": 0, "last_update": "2025-06-03 18:27:37", "author_account_age_days": 2347}, "https://github.com/rhinoflavored/comfyui_QT": {"stars": 0, "last_update": "2025-03-18 08:35:59", "author_account_age_days": 363}, "https://github.com/ricklove/ComfyUI-AutoSeg-SAM2": {"stars": 0, "last_update": "2025-03-15 20:46:14", "author_account_age_days": 5199}, "https://github.com/rickyars/sd-cn-animation": {"stars": 0, "last_update": "2025-05-18 22:33:04", "author_account_age_days": 4571}, "https://github.com/rishipandey125/ComfyUI-FramePacking": {"stars": 9, "last_update": "2025-06-09 21:51:46", "author_account_age_days": 2717}, "https://github.com/risunobushi/ComfyUI_FaceMesh_Eyewear_Mask": {"stars": 0, "last_update": "2025-05-14 07:13:26", "author_account_age_days": 1014}, "https://github.com/risunobushi/ComfyUI_FocusMask": {"stars": 4, "last_update": "2024-12-09 11:52:53", "author_account_age_days": 1014}, "https://github.com/risunobushi/ComfyUI_HEXtoRGB": {"stars": 1, "last_update": "2025-01-28 14:37:42", "author_account_age_days": 1014}, "https://github.com/ritikvirus/comfyui-terminal-modal-node": {"stars": 0, "last_update": "2025-03-01 20:03:57", "author_account_age_days": 2546}, "https://github.com/robertvoy/ComfyUI-Distributed": {"stars": 1, "last_update": "2025-06-25 22:55:00", "author_account_age_days": 4472}, "https://github.com/romeobuilderotti/ComfyUI-EZ-Pipes": {"stars": 3, "last_update": "2023-11-15 22:00:49", "author_account_age_days": 657}, "https://github.com/ronalds-eu/comfyui-plus-integrations": {"stars": 0, "last_update": "2025-05-02 17:38:19", "author_account_age_days": 4148}, "https://github.com/rouxianmantou/comfyui-rxmt-nodes": {"stars": 0, "last_update": "2025-04-03 09:55:50", "author_account_age_days": 3543}, "https://github.com/rphmeier/comfyui-videodepthanything": {"stars": 1, "last_update": "2025-04-14 18:53:06", "author_account_age_days": 3852}, "https://github.com/ruka-game/rukalib_comfyui": {"stars": 0, "last_update": "2024-10-03 23:59:55", "author_account_age_days": 272}, "https://github.com/ryanontheinside/ComfyUI-Livepeer": {"stars": 1, "last_update": "2025-04-21 22:53:14", "author_account_age_days": 4056}, "https://github.com/ryanontheinside/ComfyUI-MineWorld": {"stars": 2, "last_update": "2025-04-16 18:59:09", "author_account_age_days": 4056}, "https://github.com/ryanontheinside/ComfyUI_YoloNasObjectDetection_Tensorrt": {"stars": 1, "last_update": "2024-12-31 17:43:33", "author_account_age_days": 4056}, "https://github.com/sangeet/testui": {"stars": 2, "last_update": "2024-05-15 00:55:17", "author_account_age_days": 5447}, "https://github.com/sdfxai/SDFXBridgeForComfyUI": {"stars": 11, "last_update": "2024-06-14 10:26:56", "author_account_age_days": 602}, "https://github.com/seancheung/comfyui-creative-nodes": {"stars": 0, "last_update": "2024-09-13 06:22:45", "author_account_age_days": 4305}, "https://github.com/shadowcz007/ComfyUI-PuLID-Test": {"stars": 9, "last_update": "2024-05-12 14:37:28", "author_account_age_days": 3681}, "https://github.com/shadowcz007/Comfyui-EzAudio": {"stars": 1, "last_update": "2024-09-22 03:17:40", "author_account_age_days": 3681}, "https://github.com/shadowcz007/comfyui-CLIPSeg": {"stars": 3, "last_update": "2024-02-08 02:16:24", "author_account_age_days": 3681}, "https://github.com/shadowcz007/comfyui-hydit-lowvram": {"stars": 1, "last_update": "2024-07-31 10:04:09", "author_account_age_days": 3681}, "https://github.com/shadowcz007/comfyui-sd-prompt-mixlab": {"stars": 16, "last_update": "2024-05-21 19:47:56", "author_account_age_days": 3681}, "https://github.com/shinich39/comfyui-nothing-happened": {"stars": 0, "last_update": "2025-05-25 10:18:24", "author_account_age_days": 679}, "https://github.com/shinich39/comfyui-run-js": {"stars": 0, "last_update": "2025-06-05 13:34:15", "author_account_age_days": 679}, "https://github.com/shinich39/comfyui-textarea-is-shit": {"stars": 0, "last_update": "2025-06-03 11:52:52", "author_account_age_days": 679}, "https://github.com/shirazdesigner/CLIPTextEncodeAndEnhancev4": {"stars": 1, "last_update": "2024-04-27 13:25:08", "author_account_age_days": 4329}, "https://github.com/shuanshuan/ComfyUI_CheckPointLoader_Ext": {"stars": 0, "last_update": "2024-08-27 02:24:05", "author_account_age_days": 4485}, "https://github.com/silent-rain/ComfyUI-SilentRain": {"stars": 1, "last_update": "2025-06-26 11:21:49", "author_account_age_days": 2510}, "https://github.com/silveroxides/ComfyUI_ReduxEmbedToolkit": {"stars": 1, "last_update": "2025-05-16 03:24:41", "author_account_age_days": 1868}, "https://github.com/simonjaq/ComfyUI-sjnodes": {"stars": 0, "last_update": "2025-06-20 08:23:01", "author_account_age_days": 2902}, "https://github.com/smthemex/ComfyUI_GPT_SoVITS_Lite": {"stars": 5, "last_update": "2025-03-17 06:45:58", "author_account_age_days": 721}, "https://github.com/smthemex/ComfyUI_MangaNinjia": {"stars": 53, "last_update": "2025-04-09 14:21:57", "author_account_age_days": 721}, "https://github.com/sofakid/dandy": {"stars": 50, "last_update": "2024-05-27 21:46:18", "author_account_age_days": 4420}, "https://github.com/songtianhui/ComfyUI-DMM": {"stars": 2, "last_update": "2025-04-27 12:38:20", "author_account_age_days": 1612}, "https://github.com/sorption-dev/mycraft-comfyui": {"stars": 3, "last_update": "2025-04-12 18:08:12", "author_account_age_days": 89}, "https://github.com/sourceful-official/ComfyUI_InstructPixToPixConditioningLatent": {"stars": 3, "last_update": "2025-01-03 13:20:33", "author_account_age_days": 1850}, "https://github.com/sourceful-official/comfyui-sourceful-official": {"stars": 0, "last_update": "2025-01-27 14:58:03", "author_account_age_days": 1850}, "https://github.com/spawner1145/comfyui-spawner-nodes": {"stars": 2, "last_update": "2025-06-09 11:07:31", "author_account_age_days": 305}, "https://github.com/springjk/ComfyUI-Psutil-Container-Memory-Patch": {"stars": 0, "last_update": "2025-04-23 15:12:34", "author_account_age_days": 4028}, "https://github.com/sswink/comfyui-lingshang": {"stars": 0, "last_update": "2024-11-06 15:04:22", "author_account_age_days": 2903}, "https://github.com/stalkervr/comfyui-custom-path-nodes": {"stars": 1, "last_update": "2025-06-21 07:29:47", "author_account_age_days": 2734}, "https://github.com/stavsap/ComfyUI-React-SDK": {"stars": 12, "last_update": "2024-03-17 21:54:21", "author_account_age_days": 4451}, "https://github.com/steelan9199/ComfyUI-Teeth": {"stars": 9, "last_update": "2025-03-03 01:44:23", "author_account_age_days": 1221}, "https://github.com/strhwste/comfyui_csv_utils": {"stars": 0, "last_update": "2025-06-03 23:04:43", "author_account_age_days": 839}, "https://github.com/stutya/ComfyUI-Terminal": {"stars": 0, "last_update": "2025-05-11 21:29:49", "author_account_age_days": 4205}, "https://github.com/sugarkwork/comfyui_image_crop": {"stars": 0, "last_update": "2025-03-14 01:43:03", "author_account_age_days": 1242}, "https://github.com/sugarkwork/comfyui_my_img_util": {"stars": 0, "last_update": "2025-04-04 15:51:26", "author_account_age_days": 1242}, "https://github.com/sugarkwork/comfyui_psd": {"stars": 6, "last_update": "2025-01-14 04:33:37", "author_account_age_days": 1242}, "https://github.com/suncat2ps/ComfyUI-SaveImgNextcloud": {"stars": 0, "last_update": "2025-06-23 04:03:38", "author_account_age_days": 4492}, "https://github.com/takoyaki1118/ComfyUI_PromptExtractor": {"stars": 0, "last_update": "2025-06-05 07:01:24", "author_account_age_days": 2466}, "https://github.com/talesofai/comfyui-supersave": {"stars": 2, "last_update": "2023-12-27 02:05:53", "author_account_age_days": 923}, "https://github.com/talon468/ComfyUI-Rpg-Architect": {"stars": 4, "last_update": "2024-08-31 14:47:47", "author_account_age_days": 785}, "https://github.com/tankenyuen-ola/comfyui-env-variable-reader": {"stars": 0, "last_update": "2025-06-19 06:21:23", "author_account_age_days": 171}, "https://github.com/tanmoy-it/comfyuiCustomNode": {"stars": 0, "last_update": "2025-05-10 07:45:32", "author_account_age_days": 299}, "https://github.com/tc888/ComfyUI_Save_Flux_Image": {"stars": 0, "last_update": "2025-02-09 17:21:22", "author_account_age_days": 2627}, "https://github.com/techidsk/comfyui_molook_nodes": {"stars": 0, "last_update": "2025-03-31 02:17:02", "author_account_age_days": 2555}, "https://github.com/techtruth/ComfyUI-Dreambooth": {"stars": 0, "last_update": "2025-04-06 02:57:20", "author_account_age_days": 2998}, "https://github.com/techzuhaib/ComfyUI-CacheImageNode": {"stars": 0, "last_update": "2024-11-29 07:31:49", "author_account_age_days": 536}, "https://github.com/thderoo/ComfyUI-_topfun_s_nodes": {"stars": 6, "last_update": "2024-07-03 14:39:28", "author_account_age_days": 3233}, "https://github.com/thot-experiment/comfy-live-preview": {"stars": 2, "last_update": "2025-02-19 20:30:13", "author_account_age_days": 1318}, "https://github.com/threadedblue/MLXnodes": {"stars": 2, "last_update": "2025-02-15 13:41:14", "author_account_age_days": 4331}, "https://github.com/tjorbogarden/my-useful-comfyui-custom-nodes": {"stars": 0, "last_update": "2024-03-05 13:31:31", "author_account_age_days": 480}, "https://github.com/tom-doerr/dspy_nodes": {"stars": 190, "last_update": "2024-12-01 20:14:37", "author_account_age_days": 3147}, "https://github.com/tracerstar/comfyui-p5js-node": {"stars": 37, "last_update": "2024-07-05 23:47:57", "author_account_age_days": 5565}, "https://github.com/trampolin/comfy-ui-scryfall": {"stars": 0, "last_update": "2025-05-20 11:46:54", "author_account_age_days": 4612}, "https://github.com/trashgraphicard/Albedo-Sampler-for-ComfyUI": {"stars": 4, "last_update": "2024-12-04 23:50:38", "author_account_age_days": 1042}, "https://github.com/truebillyblue/lC.ComfyUI_epistemic_nodes": {"stars": 0, "last_update": "2025-05-29 14:43:38", "author_account_age_days": 122}, "https://github.com/tuckerdarby/ComfyUI-TDNodes": {"stars": 3, "last_update": "2024-02-19 17:00:55", "author_account_age_days": 3305}, "https://github.com/turskeli/comfyui-SetWallpaper": {"stars": 0, "last_update": "2025-04-23 22:46:46", "author_account_age_days": 4984}, "https://github.com/tzsoulcap/ComfyUI-SaveImg-W-MetaData": {"stars": 0, "last_update": "2025-04-11 15:28:03", "author_account_age_days": 2189}, "https://github.com/umisetokikaze/comfyui_mergekit": {"stars": 0, "last_update": "2024-04-28 07:21:00", "author_account_age_days": 2190}, "https://github.com/unanan/ComfyUI-Dist": {"stars": 6, "last_update": "2024-02-28 10:03:50", "author_account_age_days": 3265}, "https://github.com/usman2003/ComfyUI-Classifiers": {"stars": 0, "last_update": "2025-05-21 12:44:01", "author_account_age_days": 1904}, "https://github.com/usman2003/ComfyUI-RaceDetect": {"stars": 0, "last_update": "2025-05-23 12:23:39", "author_account_age_days": 1904}, "https://github.com/usrname0/ComfyUI-AllergicPack": {"stars": 0, "last_update": "2025-06-14 00:52:42", "author_account_age_days": 2780}, "https://github.com/var1ableX/ComfyUI_Accessories": {"stars": 1, "last_update": "2025-02-09 14:31:19", "author_account_age_days": 5131}, "https://github.com/vchopine/ComfyUI_Toolbox": {"stars": 2, "last_update": "2025-03-18 16:12:09", "author_account_age_days": 3957}, "https://github.com/virallover/comfyui-virallover": {"stars": 0, "last_update": "2025-06-02 13:49:38", "author_account_age_days": 59}, "https://github.com/visualbruno/ComfyUI-Hunyuan3d-2-1": {"stars": 33, "last_update": "2025-06-25 18:43:58", "author_account_age_days": 5406}, "https://github.com/vladp0727/Comfyui-with-Furniture": {"stars": 0, "last_update": "2025-04-18 08:58:04", "author_account_age_days": 96}, "https://github.com/vovler/ComfyUI-vovlerTools": {"stars": 0, "last_update": "2025-06-25 17:36:07", "author_account_age_days": 2094}, "https://github.com/wTechArtist/ComfyUI_VVL_Segmentation": {"stars": 0, "last_update": "2025-05-29 05:25:00", "author_account_age_days": 1728}, "https://github.com/wTechArtist/ComfyUI_VVL_VideoCamera": {"stars": 0, "last_update": "2025-06-12 02:11:03", "author_account_age_days": 1728}, "https://github.com/wTechArtist/ComfyUI_vvl_BBOX": {"stars": 0, "last_update": "2025-05-21 12:14:07", "author_account_age_days": 1728}, "https://github.com/walterFeng/ComfyUI-Image-Utils": {"stars": 3, "last_update": "2025-03-25 14:36:37", "author_account_age_days": 3143}, "https://github.com/warshanks/Shank-Tools": {"stars": 0, "last_update": "2025-01-26 03:39:09", "author_account_age_days": 3850}, "https://github.com/watarika/ComfyUI-Text-Utility": {"stars": 1, "last_update": "2025-04-22 14:16:27", "author_account_age_days": 2100}, "https://github.com/watarika/ComfyUI-exit": {"stars": 0, "last_update": "2025-01-05 03:24:05", "author_account_age_days": 2100}, "https://github.com/waynepimpzhang/comfyui-opencv-brightestspot": {"stars": 0, "last_update": "2025-01-05 06:04:53", "author_account_age_days": 4157}, "https://github.com/whmc76/ComfyUI-LongTextTTSSuite": {"stars": 8, "last_update": "2025-06-09 11:08:21", "author_account_age_days": 819}, "https://github.com/wildminder/ComfyUI-MagCache": {"stars": 5, "last_update": "2025-06-13 20:56:49", "author_account_age_days": 4600}, "https://github.com/willblaschko/ComfyUI-Unload-Models": {"stars": 21, "last_update": "2024-06-30 10:07:40", "author_account_age_days": 4958}, "https://github.com/wilzamguerrero/Comfyui-zZzZz": {"stars": 2, "last_update": "2025-01-02 00:35:50", "author_account_age_days": 1055}, "https://github.com/wordbrew/comfyui-wan-control-nodes": {"stars": 6, "last_update": "2025-06-19 23:37:04", "author_account_age_days": 980}, "https://github.com/wormley/comfyui-wormley-nodes": {"stars": 0, "last_update": "2023-11-12 19:05:11", "author_account_age_days": 2840}, "https://github.com/x3bits/ComfyUI-Power-Flow": {"stars": 2, "last_update": "2025-01-14 14:20:35", "author_account_age_days": 3750}, "https://github.com/xiaoyumu/ComfyUI-XYNodes": {"stars": 0, "last_update": "2024-12-05 07:07:30", "author_account_age_days": 4383}, "https://github.com/xinyiSS/CombineMasksNode": {"stars": 0, "last_update": "2025-02-08 04:35:18", "author_account_age_days": 818}, "https://github.com/xl0/q_tools": {"stars": 0, "last_update": "2025-05-28 06:09:00", "author_account_age_days": 5368}, "https://github.com/xmarked-ai/ComfyUI_misc": {"stars": 2, "last_update": "2025-06-07 16:26:56", "author_account_age_days": 240}, "https://github.com/xqqe/honey_nodes": {"stars": 0, "last_update": "2025-05-03 20:59:53", "author_account_age_days": 2068}, "https://github.com/xzuyn/ComfyUI-xzuynodes": {"stars": 0, "last_update": "2025-06-23 05:03:37", "author_account_age_days": 3487}, "https://github.com/y4my4my4m/ComfyUI_Direct3DS2": {"stars": 7, "last_update": "2025-06-01 04:29:47", "author_account_age_days": 4001}, "https://github.com/yamanacn/comfyui_qwen_object": {"stars": 0, "last_update": "2025-06-20 12:24:28", "author_account_age_days": 1687}, "https://github.com/yamanacn/comfyui_qwenbbox": {"stars": 0, "last_update": "2025-06-21 03:00:01", "author_account_age_days": 1687}, "https://github.com/yanhuifair/ComfyUI-FairLab": {"stars": 1, "last_update": "2025-04-08 09:14:57", "author_account_age_days": 3929}, "https://github.com/yanhuifair/comfyui-deepseek": {"stars": 4, "last_update": "2025-04-08 09:14:25", "author_account_age_days": 3929}, "https://github.com/yanlang0123/ComfyUI_Lam": {"stars": 44, "last_update": "2025-05-24 12:20:05", "author_account_age_days": 3175}, "https://github.com/yichengup/ComfyUI-Transition": {"stars": 3, "last_update": "2025-06-13 03:03:10", "author_account_age_days": 491}, "https://github.com/yichengup/ComfyUI-YCNodes_Advance": {"stars": 5, "last_update": "2025-06-25 15:08:18", "author_account_age_days": 491}, "https://github.com/yichengup/Comfyui-NodeSpark": {"stars": 5, "last_update": "2025-01-20 14:20:36", "author_account_age_days": 491}, "https://github.com/yincangshiwei/ComfyUI-SEQLToolNode": {"stars": 0, "last_update": "2025-05-28 10:06:17", "author_account_age_days": 3997}, "https://github.com/yojimbodayne/ComfyUI-Dropbox-API": {"stars": 0, "last_update": "2024-08-30 05:29:07", "author_account_age_days": 317}, "https://github.com/zackabrams/ComfyUI-KeySyncWrapper": {"stars": 2, "last_update": "2025-06-21 17:46:04", "author_account_age_days": 2696}, "https://github.com/zhaorishuai/ComfyUI-StoryboardDistributor": {"stars": 1, "last_update": "2025-04-01 08:10:16", "author_account_age_days": 2606}, "https://github.com/zhengxyz123/ComfyUI-CLIPSeg": {"stars": 2, "last_update": "2025-05-20 12:40:03", "author_account_age_days": 2022}, "https://github.com/zhongpei/Comfyui_image2prompt": {"stars": 348, "last_update": "2025-06-06 23:41:46", "author_account_age_days": 3826}, "https://github.com/zjkhurry/comfyui_MetalFX": {"stars": 1, "last_update": "2025-03-05 07:07:17", "author_account_age_days": 3347}, "https://github.com/zml-ai/comfyui-hydit": {"stars": 3, "last_update": "2024-08-07 09:37:09", "author_account_age_days": 2350}, "https://github.com/zyd232/ComfyUI-zyd232-Nodes": {"stars": 0, "last_update": "2025-04-12 01:13:21", "author_account_age_days": 3991}, "https://github.com/zyquon/ComfyUI-Stash": {"stars": 0, "last_update": "2025-06-21 03:32:52", "author_account_age_days": 87}}