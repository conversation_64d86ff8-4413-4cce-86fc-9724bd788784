#!/usr/bin/env python3
"""
ComfyUI API client for generating videos with Wan2.1
"""

import json
import requests
import time
import websocket
import uuid
import threading
from urllib.parse import urljoin

class ComfyUIClient:
    def __init__(self, server_address="127.0.0.1:8189"):
        self.server_address = server_address
        self.client_id = str(uuid.uuid4())
        self.ws = None
        
    def connect_websocket(self):
        """Connect to ComfyUI WebSocket for real-time updates"""
        ws_url = f"ws://{self.server_address}/ws?clientId={self.client_id}"
        self.ws = websocket.WebSocket()
        try:
            self.ws.connect(ws_url)
            print("✅ WebSocket connected")
            return True
        except Exception as e:
            print(f"❌ WebSocket connection failed: {e}")
            return False
    
    def queue_prompt(self, workflow):
        """Queue a workflow for execution"""
        url = f"http://{self.server_address}/prompt"
        data = {
            "prompt": workflow,
            "client_id": self.client_id
        }
        
        try:
            response = requests.post(url, json=data)
            if response.status_code == 200:
                result = response.json()
                prompt_id = result.get("prompt_id")
                print(f"✅ Workflow queued successfully! Prompt ID: {prompt_id}")
                return prompt_id
            else:
                print(f"❌ Failed to queue workflow: {response.status_code}")
                print(response.text)
                return None
        except Exception as e:
            print(f"❌ Error queuing workflow: {e}")
            return None
    
    def monitor_progress(self, prompt_id):
        """Monitor workflow execution progress"""
        if not self.ws:
            print("❌ WebSocket not connected")
            return False
            
        print("🔄 Monitoring progress...")
        
        while True:
            try:
                message = self.ws.recv()
                data = json.loads(message)
                
                if data["type"] == "progress":
                    node = data["data"]["node"]
                    value = data["data"]["value"]
                    max_value = data["data"]["max"]
                    print(f"📊 Progress: {node} - {value}/{max_value} ({value/max_value*100:.1f}%)")
                
                elif data["type"] == "executed":
                    node = data["data"]["node"]
                    print(f"✅ Node completed: {node}")
                
                elif data["type"] == "execution_start":
                    print("🚀 Execution started!")
                
                elif data["type"] == "execution_success":
                    print("🎉 Execution completed successfully!")
                    return True
                
                elif data["type"] == "execution_error":
                    print("❌ Execution failed!")
                    print(data["data"])
                    return False
                    
            except websocket.WebSocketTimeoutException:
                continue
            except Exception as e:
                print(f"❌ WebSocket error: {e}")
                return False
    
    def generate_video(self, workflow_path):
        """Generate video using the specified workflow"""
        print("🎬 Starting video generation with Wan2.1...")
        
        # Load workflow
        try:
            with open(workflow_path, 'r') as f:
                workflow = json.load(f)
            print(f"📄 Loaded workflow: {workflow_path}")
        except Exception as e:
            print(f"❌ Failed to load workflow: {e}")
            return False
        
        # Connect WebSocket
        if not self.connect_websocket():
            return False
        
        # Queue workflow
        prompt_id = self.queue_prompt(workflow)
        if not prompt_id:
            return False
        
        # Monitor progress
        success = self.monitor_progress(prompt_id)
        
        # Close WebSocket
        if self.ws:
            self.ws.close()
        
        return success

def main():
    print("🎯 ComfyUI Simple Video Generation")
    print("=" * 50)
    
    # Initialize client
    client = ComfyUIClient()
    
    # Generate video
    workflow_path = "simple_video_workflow.json"
    success = client.generate_video(workflow_path)
    
    if success:
        print("\n🎉 Video generation completed!")
        print("📁 Check the ComfyUI/output/ folder for your generated video")
        print("🎬 Filename: wan_t2v_cherry_blossom_*.mp4")
    else:
        print("\n❌ Video generation failed!")

if __name__ == "__main__":
    main()
