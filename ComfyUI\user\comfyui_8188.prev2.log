## ComfyUI-Manager: installing dependencies. (GitPython)
[2025-06-28 20:03:31.706] ## ComfyUI-Manager: installing dependencies done.
[2025-06-28 20:03:31.707] ** ComfyUI startup time: 2025-06-28 20:03:31.707
[2025-06-28 20:03:31.707] ** Platform: Windows
[2025-06-28 20:03:31.707] ** Python version: 3.12.10 (tags/v3.12.10:0cc8128, Apr  8 2025, 12:21:36) [MSC v.1943 64 bit (AMD64)]
[2025-06-28 20:03:31.707] ** Python executable: C:\Users\<USER>\.pyenv\pyenv-win\versions\3.12.10\python.exe
[2025-06-28 20:03:31.708] ** ComfyUI Path: C:\Users\<USER>\ai\ComfiGit\ComfyUI
[2025-06-28 20:03:31.708] ** ComfyUI Base Folder Path: C:\Users\<USER>\ai\ComfiGit\ComfyUI
[2025-06-28 20:03:31.709] ** User directory: C:\Users\<USER>\ai\ComfiGit\ComfyUI\user
[2025-06-28 20:03:31.709] ** ComfyUI-Manager config path: C:\Users\<USER>\ai\ComfiGit\ComfyUI\user\default\ComfyUI-Manager\config.ini
[2025-06-28 20:03:31.709] ** Log path: C:\Users\<USER>\ai\ComfiGit\ComfyUI\user\comfyui.log
[ComfyUI-Manager] 'numpy' dependency were fixed
[2025-06-28 20:03:37.302] 
Prestartup times for custom nodes:
[2025-06-28 20:03:37.302]   19.7 seconds: C:\Users\<USER>\ai\ComfiGit\ComfyUI\custom_nodes\ComfyUI-Manager
[2025-06-28 20:03:37.302] 
[2025-06-28 20:03:52.579] Checkpoint files will always be loaded safely.
[2025-06-28 20:03:52.624] Traceback (most recent call last):
[2025-06-28 20:03:52.624]   File "C:\Users\<USER>\ai\ComfiGit\ComfyUI\main.py", line 129, in <module>
[2025-06-28 20:03:52.625]     import execution
[2025-06-28 20:03:52.625]   File "C:\Users\<USER>\ai\ComfiGit\ComfyUI\execution.py", line 14, in <module>
[2025-06-28 20:03:52.625]     import comfy.model_management
[2025-06-28 20:03:52.626]   File "C:\Users\<USER>\ai\ComfiGit\ComfyUI\comfy\model_management.py", line 221, in <module>
[2025-06-28 20:03:52.627]     total_vram = get_total_memory(get_torch_device()) / (1024 * 1024)
[2025-06-28 20:03:52.627]                                   ^^^^^^^^^^^^^^^^^^
[2025-06-28 20:03:52.632]   File "C:\Users\<USER>\ai\ComfiGit\ComfyUI\comfy\model_management.py", line 172, in get_torch_device
[2025-06-28 20:03:52.632]     return torch.device(torch.cuda.current_device())
[2025-06-28 20:03:52.633]                         ^^^^^^^^^^^^^^^^^^^^^^^^^^^
[2025-06-28 20:03:52.637]   File "C:\Users\<USER>\.pyenv\pyenv-win\versions\3.12.10\Lib\site-packages\torch\cuda\__init__.py", line 1026, in current_device
[2025-06-28 20:03:52.638]     _lazy_init()
[2025-06-28 20:03:52.638]   File "C:\Users\<USER>\.pyenv\pyenv-win\versions\3.12.10\Lib\site-packages\torch\cuda\__init__.py", line 363, in _lazy_init
[2025-06-28 20:03:52.639]     raise AssertionError("Torch not compiled with CUDA enabled")
[2025-06-28 20:03:52.639] AssertionError: Torch not compiled with CUDA enabled
