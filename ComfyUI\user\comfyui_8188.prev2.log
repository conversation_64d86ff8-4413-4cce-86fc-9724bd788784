## ComfyUI-Manager: installing dependencies done.
[2025-06-28 19:55:24.481] ** ComfyUI startup time: 2025-06-28 19:55:24.481
[2025-06-28 19:55:24.481] ** Platform: Windows
[2025-06-28 19:55:24.481] ** Python version: 3.10.11 (tags/v3.10.11:7d4cc5a, Apr  5 2023, 00:38:17) [MSC v.1929 64 bit (AMD64)]
[2025-06-28 19:55:24.482] ** Python executable: C:\Users\<USER>\.pyenv\pyenv-win\versions\3.10.11\python.exe
[2025-06-28 19:55:24.482] ** ComfyUI Path: C:\Users\<USER>\ai\ComfiGit\ComfyUI
[2025-06-28 19:55:24.482] ** ComfyUI Base Folder Path: C:\Users\<USER>\ai\ComfiGit\ComfyUI
[2025-06-28 19:55:24.482] ** User directory: C:\Users\<USER>\ai\ComfiGit\ComfyUI\user
[2025-06-28 19:55:24.482] ** ComfyUI-Manager config path: C:\Users\<USER>\ai\ComfiGit\ComfyUI\user\default\ComfyUI-Manager\config.ini
[2025-06-28 19:55:24.483] ** Log path: C:\Users\<USER>\ai\ComfiGit\ComfyUI\user\comfyui.log

Prestartup times for custom nodes:
[2025-06-28 19:55:25.424]    2.0 seconds: C:\Users\<USER>\ai\ComfiGit\ComfyUI\custom_nodes\ComfyUI-Manager
[2025-06-28 19:55:25.424] 
[2025-06-28 19:55:26.640] Checkpoint files will always be loaded safely.
[2025-06-28 19:55:26.668] C:\Users\<USER>\.pyenv\pyenv-win\versions\3.10.11\lib\site-packages\torch\cuda\__init__.py:235: UserWarning: 
NVIDIA GeForce RTX 5090 Laptop GPU with CUDA capability sm_120 is not compatible with the current PyTorch installation.
The current PyTorch install supports CUDA capabilities sm_50 sm_60 sm_61 sm_70 sm_75 sm_80 sm_86 sm_90.
If you want to use the NVIDIA GeForce RTX 5090 Laptop GPU GPU with PyTorch, please check the instructions at https://pytorch.org/get-started/locally/

  warnings.warn(
[2025-06-28 19:55:26.750] Total VRAM 24463 MB, total RAM 64957 MB
[2025-06-28 19:55:26.750] pytorch version: 2.5.1+cu121
[2025-06-28 19:55:26.750] Set vram state to: NORMAL_VRAM
[2025-06-28 19:55:26.751] Device: cuda:0 NVIDIA GeForce RTX 5090 Laptop GPU : cudaMallocAsync
[2025-06-28 19:55:27.638] Using pytorch attention
[2025-06-28 19:55:28.887] Python version: 3.10.11 (tags/v3.10.11:7d4cc5a, Apr  5 2023, 00:38:17) [MSC v.1929 64 bit (AMD64)]
[2025-06-28 19:55:28.887] ComfyUI version: 0.3.43
[2025-06-28 19:55:28.921] ComfyUI frontend version: 1.23.4
[2025-06-28 19:55:28.922] [Prompt Server] web root: C:\Users\<USER>\.pyenv\pyenv-win\versions\3.10.11\lib\site-packages\comfyui_frontend_package\static
[2025-06-28 19:55:29.606] ### Loading: ComfyUI-Impact-Pack (V8.17.1)
[2025-06-28 19:55:30.604] [Impact Pack] Failed to import due to several dependencies are missing!!!!
[2025-06-28 19:55:30.605] Traceback (most recent call last):
  File "C:\Users\<USER>\ai\ComfiGit\ComfyUI\nodes.py", line 2124, in load_custom_node
    module_spec.loader.exec_module(module)
  File "<frozen importlib._bootstrap_external>", line 883, in exec_module
  File "<frozen importlib._bootstrap>", line 241, in _call_with_frames_removed
  File "C:\Users\<USER>\ai\ComfiGit\ComfyUI\custom_nodes\ComfyUI-Impact-Pack\__init__.py", line 46, in <module>
    raise e
  File "C:\Users\<USER>\ai\ComfiGit\ComfyUI\custom_nodes\ComfyUI-Impact-Pack\__init__.py", line 37, in <module>
    import piexif
ModuleNotFoundError: No module named 'piexif'

[2025-06-28 19:55:30.605] Cannot import C:\Users\<USER>\ai\ComfiGit\ComfyUI\custom_nodes\ComfyUI-Impact-Pack module for custom nodes: No module named 'piexif'
[2025-06-28 19:55:30.609] ### Loading: ComfyUI-Manager (V3.33.3)
[2025-06-28 19:55:30.610] [ComfyUI-Manager] network_mode: public
[2025-06-28 19:55:30.806] ### ComfyUI Version: v0.3.43 | Released on '2025-06-27'
[2025-06-28 19:55:31.093] [ComfyUI-Manager] default cache updated: https://raw.githubusercontent.com/ltdrdata/ComfyUI-Manager/main/alter-list.json
[2025-06-28 19:55:31.098] [ComfyUI-Manager] default cache updated: https://raw.githubusercontent.com/ltdrdata/ComfyUI-Manager/main/model-list.json
[2025-06-28 19:55:31.137] [ComfyUI-Manager] default cache updated: https://raw.githubusercontent.com/ltdrdata/ComfyUI-Manager/main/github-stats.json
[2025-06-28 19:55:31.181] [ComfyUI-Manager] default cache updated: https://raw.githubusercontent.com/ltdrdata/ComfyUI-Manager/main/extension-node-map.json
[2025-06-28 19:55:31.209] [ComfyUI-Manager] default cache updated: https://raw.githubusercontent.com/ltdrdata/ComfyUI-Manager/main/custom-node-list.json
[2025-06-28 19:55:32.100] Warning: Could not load sageattention: No module named 'sageattention'
[2025-06-28 19:55:32.100] sageattention package is not installed
[2025-06-28 19:55:32.107] Traceback (most recent call last):
  File "C:\Users\<USER>\ai\ComfiGit\ComfyUI\nodes.py", line 2124, in load_custom_node
    module_spec.loader.exec_module(module)
  File "<frozen importlib._bootstrap_external>", line 883, in exec_module
  File "<frozen importlib._bootstrap>", line 241, in _call_with_frames_removed
  File "C:\Users\<USER>\ai\ComfiGit\ComfyUI\custom_nodes\ComfyUI-WanVideoWrapper\__init__.py", line 1, in <module>
    from .nodes import NODE_CLASS_MAPPINGS, NODE_DISPLAY_NAME_MAPPINGS
  File "C:\Users\<USER>\ai\ComfiGit\ComfyUI\custom_nodes\ComfyUI-WanVideoWrapper\nodes.py", line 10, in <module>
    from .wanvideo.modules.clip import CLIPModel
  File "C:\Users\<USER>\ai\ComfiGit\ComfyUI\custom_nodes\ComfyUI-WanVideoWrapper\wanvideo\modules\__init__.py", line 2, in <module>
    from .t5 import T5Decoder, T5Encoder, T5EncoderModel, T5Model
  File "C:\Users\<USER>\ai\ComfiGit\ComfyUI\custom_nodes\ComfyUI-WanVideoWrapper\wanvideo\modules\t5.py", line 10, in <module>
    from .tokenizers import HuggingfaceTokenizer
  File "C:\Users\<USER>\ai\ComfiGit\ComfyUI\custom_nodes\ComfyUI-WanVideoWrapper\wanvideo\modules\tokenizers.py", line 5, in <module>
    import ftfy
ModuleNotFoundError: No module named 'ftfy'

[2025-06-28 19:55:32.107] Cannot import C:\Users\<USER>\ai\ComfiGit\ComfyUI\custom_nodes\ComfyUI-WanVideoWrapper module for custom nodes: No module named 'ftfy'
[2025-06-28 19:55:32.121] 
Import times for custom nodes:
[2025-06-28 19:55:32.121]    0.0 seconds: C:\Users\<USER>\ai\ComfiGit\ComfyUI\custom_nodes\websocket_image_save.py
[2025-06-28 19:55:32.122]    0.0 seconds: C:\Users\<USER>\ai\ComfiGit\ComfyUI\custom_nodes\ComfyUI-WanVideoKsampler
[2025-06-28 19:55:32.122]    0.0 seconds: C:\Users\<USER>\ai\ComfiGit\ComfyUI\custom_nodes\ComfyUI-Custom-Scripts
[2025-06-28 19:55:32.122]    0.0 seconds: C:\Users\<USER>\ai\ComfiGit\ComfyUI\custom_nodes\ComfyUI-Advanced-ControlNet
[2025-06-28 19:55:32.122]    0.0 seconds: C:\Users\<USER>\ai\ComfiGit\ComfyUI\custom_nodes\efficiency-nodes-comfyui
[2025-06-28 19:55:32.122]    0.1 seconds: C:\Users\<USER>\ai\ComfiGit\ComfyUI\custom_nodes\ComfyUI-AnimateDiff-Evolved
[2025-06-28 19:55:32.122]    0.2 seconds: C:\Users\<USER>\ai\ComfiGit\ComfyUI\custom_nodes\ComfyUI-VideoHelperSuite
[2025-06-28 19:55:32.122]    0.4 seconds: C:\Users\<USER>\ai\ComfiGit\ComfyUI\custom_nodes\ComfyUI-Manager
[2025-06-28 19:55:32.122]    0.9 seconds (IMPORT FAILED): C:\Users\<USER>\ai\ComfiGit\ComfyUI\custom_nodes\ComfyUI-WanVideoWrapper
[2025-06-28 19:55:32.123]    1.0 seconds (IMPORT FAILED): C:\Users\<USER>\ai\ComfiGit\ComfyUI\custom_nodes\ComfyUI-Impact-Pack
[2025-06-28 19:55:32.123] 
[2025-06-28 19:55:32.314] Context impl SQLiteImpl.
[2025-06-28 19:55:32.314] Will assume non-transactional DDL.
[2025-06-28 19:55:32.315] No target revision found.
[2025-06-28 19:55:32.329] Starting server

[2025-06-28 19:55:32.330] To see the GUI go to: http://127.0.0.1:8188
[2025-06-28 19:55:34.963] FETCH ComfyRegistry Data: 5/90
[2025-06-28 19:55:38.686] FETCH ComfyRegistry Data: 10/90
[2025-06-28 19:55:42.820] FETCH ComfyRegistry Data: 15/90
[2025-06-28 19:55:46.532] FETCH ComfyRegistry Data: 20/90
[2025-06-28 19:55:50.355] FETCH ComfyRegistry Data: 25/90
[2025-06-28 19:55:54.142] FETCH ComfyRegistry Data: 30/90
[2025-06-28 19:55:57.905] FETCH ComfyRegistry Data: 35/90
[2025-06-28 19:56:01.488] FETCH ComfyRegistry Data: 40/90
[2025-06-28 19:56:05.089] FETCH ComfyRegistry Data: 45/90
[2025-06-28 19:56:08.879] FETCH ComfyRegistry Data: 50/90
