## ComfyUI-Manager: installing dependencies done.
[2025-06-28 19:57:11.335] ** ComfyUI startup time: 2025-06-28 19:57:11.335
[2025-06-28 19:57:11.336] ** Platform: Windows
[2025-06-28 19:57:11.336] ** Python version: 3.10.11 (tags/v3.10.11:7d4cc5a, Apr  5 2023, 00:38:17) [MSC v.1929 64 bit (AMD64)]
[2025-06-28 19:57:11.336] ** Python executable: C:\Users\<USER>\.pyenv\pyenv-win\versions\3.10.11\python.exe
[2025-06-28 19:57:11.336] ** ComfyUI Path: C:\Users\<USER>\ai\ComfiGit\ComfyUI
[2025-06-28 19:57:11.337] ** ComfyUI Base Folder Path: C:\Users\<USER>\ai\ComfiGit\ComfyUI
[2025-06-28 19:57:11.337] ** User directory: C:\Users\<USER>\ai\ComfiGit\ComfyUI\user
[2025-06-28 19:57:11.337] ** ComfyUI-Manager config path: C:\Users\<USER>\ai\ComfiGit\ComfyUI\user\default\ComfyUI-Manager\config.ini
[2025-06-28 19:57:11.337] ** Log path: C:\Users\<USER>\ai\ComfiGit\ComfyUI\user\comfyui.log

Prestartup times for custom nodes:
[2025-06-28 19:57:12.258]    2.0 seconds: C:\Users\<USER>\ai\ComfiGit\ComfyUI\custom_nodes\ComfyUI-Manager
[2025-06-28 19:57:12.259] 
[2025-06-28 19:57:13.627] Checkpoint files will always be loaded safely.
[2025-06-28 19:57:13.653] C:\Users\<USER>\.pyenv\pyenv-win\versions\3.10.11\lib\site-packages\torch\cuda\__init__.py:235: UserWarning: 
NVIDIA GeForce RTX 5090 Laptop GPU with CUDA capability sm_120 is not compatible with the current PyTorch installation.
The current PyTorch install supports CUDA capabilities sm_50 sm_60 sm_61 sm_70 sm_75 sm_80 sm_86 sm_90.
If you want to use the NVIDIA GeForce RTX 5090 Laptop GPU GPU with PyTorch, please check the instructions at https://pytorch.org/get-started/locally/

  warnings.warn(
[2025-06-28 19:57:13.737] Total VRAM 24463 MB, total RAM 64957 MB
[2025-06-28 19:57:13.737] pytorch version: 2.5.1+cu121
[2025-06-28 19:57:13.737] Set vram state to: NORMAL_VRAM
[2025-06-28 19:57:13.738] Device: cuda:0 NVIDIA GeForce RTX 5090 Laptop GPU : cudaMallocAsync
[2025-06-28 19:57:14.645] Using pytorch attention
[2025-06-28 19:57:15.882] Python version: 3.10.11 (tags/v3.10.11:7d4cc5a, Apr  5 2023, 00:38:17) [MSC v.1929 64 bit (AMD64)]
[2025-06-28 19:57:15.882] ComfyUI version: 0.3.43
[2025-06-28 19:57:15.905] ComfyUI frontend version: 1.23.4
[2025-06-28 19:57:15.907] [Prompt Server] web root: C:\Users\<USER>\.pyenv\pyenv-win\versions\3.10.11\lib\site-packages\comfyui_frontend_package\static
[2025-06-28 19:57:16.574] ### Loading: ComfyUI-Impact-Pack (V8.17.1)
[2025-06-28 19:57:16.618] Traceback (most recent call last):
  File "C:\Users\<USER>\ai\ComfiGit\ComfyUI\nodes.py", line 2124, in load_custom_node
    module_spec.loader.exec_module(module)
  File "<frozen importlib._bootstrap_external>", line 883, in exec_module
  File "<frozen importlib._bootstrap>", line 241, in _call_with_frames_removed
  File "C:\Users\<USER>\ai\ComfiGit\ComfyUI\custom_nodes\ComfyUI-Impact-Pack\__init__.py", line 49, in <module>
    import impact.impact_server  # to load server api
  File "C:\Users\<USER>\ai\ComfiGit\ComfyUI\custom_nodes\ComfyUI-Impact-Pack\modules\impact\impact_server.py", line 12, in <module>
    import impact.core as core
  File "C:\Users\<USER>\ai\ComfiGit\ComfyUI\custom_nodes\ComfyUI-Impact-Pack\modules\impact\core.py", line 7, in <module>
    from segment_anything import SamPredictor
ModuleNotFoundError: No module named 'segment_anything'

[2025-06-28 19:57:16.618] Cannot import C:\Users\<USER>\ai\ComfiGit\ComfyUI\custom_nodes\ComfyUI-Impact-Pack module for custom nodes: No module named 'segment_anything'
[2025-06-28 19:57:16.622] ### Loading: ComfyUI-Manager (V3.33.3)
[2025-06-28 19:57:16.623] [ComfyUI-Manager] network_mode: public
[2025-06-28 19:57:16.820] ### ComfyUI Version: v0.3.43 | Released on '2025-06-27'
[2025-06-28 19:57:17.149] [ComfyUI-Manager] default cache updated: https://raw.githubusercontent.com/ltdrdata/ComfyUI-Manager/main/alter-list.json
[2025-06-28 19:57:17.240] [ComfyUI-Manager] default cache updated: https://raw.githubusercontent.com/ltdrdata/ComfyUI-Manager/main/github-stats.json
[2025-06-28 19:57:17.246] [ComfyUI-Manager] default cache updated: https://raw.githubusercontent.com/ltdrdata/ComfyUI-Manager/main/model-list.json
[2025-06-28 19:57:17.350] [ComfyUI-Manager] default cache updated: https://raw.githubusercontent.com/ltdrdata/ComfyUI-Manager/main/extension-node-map.json
[2025-06-28 19:57:17.390] [ComfyUI-Manager] default cache updated: https://raw.githubusercontent.com/ltdrdata/ComfyUI-Manager/main/custom-node-list.json
[2025-06-28 19:57:18.067] Warning: Could not load sageattention: No module named 'sageattention'
[2025-06-28 19:57:18.067] sageattention package is not installed
[2025-06-28 19:57:18.156] 
Import times for custom nodes:
[2025-06-28 19:57:18.156]    0.0 seconds: C:\Users\<USER>\ai\ComfiGit\ComfyUI\custom_nodes\websocket_image_save.py
[2025-06-28 19:57:18.156]    0.0 seconds: C:\Users\<USER>\ai\ComfiGit\ComfyUI\custom_nodes\ComfyUI-WanVideoKsampler
[2025-06-28 19:57:18.156]    0.0 seconds: C:\Users\<USER>\ai\ComfiGit\ComfyUI\custom_nodes\ComfyUI-Custom-Scripts
[2025-06-28 19:57:18.156]    0.0 seconds: C:\Users\<USER>\ai\ComfiGit\ComfyUI\custom_nodes\efficiency-nodes-comfyui
[2025-06-28 19:57:18.156]    0.0 seconds: C:\Users\<USER>\ai\ComfiGit\ComfyUI\custom_nodes\ComfyUI-Advanced-ControlNet
[2025-06-28 19:57:18.157]    0.0 seconds (IMPORT FAILED): C:\Users\<USER>\ai\ComfiGit\ComfyUI\custom_nodes\ComfyUI-Impact-Pack
[2025-06-28 19:57:18.157]    0.1 seconds: C:\Users\<USER>\ai\ComfiGit\ComfyUI\custom_nodes\ComfyUI-AnimateDiff-Evolved
[2025-06-28 19:57:18.157]    0.2 seconds: C:\Users\<USER>\ai\ComfiGit\ComfyUI\custom_nodes\ComfyUI-VideoHelperSuite
[2025-06-28 19:57:18.157]    0.4 seconds: C:\Users\<USER>\ai\ComfiGit\ComfyUI\custom_nodes\ComfyUI-Manager
[2025-06-28 19:57:18.157]    0.9 seconds: C:\Users\<USER>\ai\ComfiGit\ComfyUI\custom_nodes\ComfyUI-WanVideoWrapper
[2025-06-28 19:57:18.157] 
[2025-06-28 19:57:18.486] Context impl SQLiteImpl.
[2025-06-28 19:57:18.486] Will assume non-transactional DDL.
[2025-06-28 19:57:18.488] No target revision found.
[2025-06-28 19:57:18.501] Starting server

[2025-06-28 19:57:18.502] To see the GUI go to: http://127.0.0.1:8188
[2025-06-28 19:57:21.158] FETCH ComfyRegistry Data: 5/90
[2025-06-28 19:57:25.037] FETCH ComfyRegistry Data: 10/90
[2025-06-28 19:57:28.781] FETCH ComfyRegistry Data: 15/90
[2025-06-28 19:57:33.754] FETCH ComfyRegistry Data: 20/90
[2025-06-28 19:57:37.934] FETCH ComfyRegistry Data: 25/90
[2025-06-28 19:57:42.006] FETCH ComfyRegistry Data: 30/90
[2025-06-28 19:57:47.589] FETCH ComfyRegistry Data: 35/90
[2025-06-28 19:57:51.325] FETCH ComfyRegistry Data: 40/90
[2025-06-28 19:57:54.987] FETCH ComfyRegistry Data: 45/90
[2025-06-28 19:57:58.706] FETCH ComfyRegistry Data: 50/90
[2025-06-28 19:58:02.538] FETCH ComfyRegistry Data: 55/90
[2025-06-28 19:58:06.822] FETCH ComfyRegistry Data: 60/90
[2025-06-28 19:58:10.665] FETCH ComfyRegistry Data: 65/90
[2025-06-28 19:58:14.504] FETCH ComfyRegistry Data: 70/90
[2025-06-28 19:58:18.214] FETCH ComfyRegistry Data: 75/90
[2025-06-28 19:58:22.283] FETCH ComfyRegistry Data: 80/90
[2025-06-28 19:58:26.144] FETCH ComfyRegistry Data: 85/90
[2025-06-28 19:58:29.918] FETCH ComfyRegistry Data: 90/90
[2025-06-28 19:58:30.433] FETCH ComfyRegistry Data [DONE]
[2025-06-28 19:58:30.523] [ComfyUI-Manager] default cache updated: https://api.comfy.org/nodes
[2025-06-28 19:58:30.538] FETCH DATA from: https://raw.githubusercontent.com/ltdrdata/ComfyUI-Manager/main/custom-node-list.json [DONE]
[2025-06-28 19:58:30.729] [ComfyUI-Manager] All startup tasks have been completed.
