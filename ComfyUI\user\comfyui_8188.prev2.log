## ComfyUI-Manager: installing dependencies done.
[2025-06-28 19:46:47.626] ** ComfyUI startup time: 2025-06-28 19:46:47.626
[2025-06-28 19:46:47.626] ** Platform: Windows
[2025-06-28 19:46:47.626] ** Python version: 3.10.11 (tags/v3.10.11:7d4cc5a, Apr  5 2023, 00:38:17) [MSC v.1929 64 bit (AMD64)]
[2025-06-28 19:46:47.626] ** Python executable: C:\Users\<USER>\.pyenv\pyenv-win\versions\3.10.11\python.exe
[2025-06-28 19:46:47.626] ** ComfyUI Path: C:\Users\<USER>\ai\ComfiGit\ComfyUI
[2025-06-28 19:46:47.626] ** ComfyUI Base Folder Path: C:\Users\<USER>\ai\ComfiGit\ComfyUI
[2025-06-28 19:46:47.627] ** User directory: C:\Users\<USER>\ai\ComfiGit\ComfyUI\user
[2025-06-28 19:46:47.627] ** ComfyUI-Manager config path: C:\Users\<USER>\ai\ComfiGit\ComfyUI\user\default\ComfyUI-Manager\config.ini
[2025-06-28 19:46:47.627] ** Log path: C:\Users\<USER>\ai\ComfiGit\ComfyUI\user\comfyui.log

Prestartup times for custom nodes:
[2025-06-28 19:46:48.496]    1.9 seconds: C:\Users\<USER>\ai\ComfiGit\ComfyUI\custom_nodes\ComfyUI-Manager
[2025-06-28 19:46:48.496] 
[2025-06-28 19:46:49.563] Checkpoint files will always be loaded safely.
[2025-06-28 19:46:49.572] Traceback (most recent call last):
[2025-06-28 19:46:49.573]   File "C:\Users\<USER>\ai\ComfiGit\ComfyUI\main.py", line 129, in <module>
[2025-06-28 19:46:49.573]     import execution
[2025-06-28 19:46:49.574]   File "C:\Users\<USER>\ai\ComfiGit\ComfyUI\execution.py", line 14, in <module>
[2025-06-28 19:46:49.574]     import comfy.model_management
[2025-06-28 19:46:49.574]   File "C:\Users\<USER>\ai\ComfiGit\ComfyUI\comfy\model_management.py", line 221, in <module>
[2025-06-28 19:46:49.574]     total_vram = get_total_memory(get_torch_device()) / (1024 * 1024)
[2025-06-28 19:46:49.575]   File "C:\Users\<USER>\ai\ComfiGit\ComfyUI\comfy\model_management.py", line 172, in get_torch_device
[2025-06-28 19:46:49.575]     return torch.device(torch.cuda.current_device())
[2025-06-28 19:46:49.575]   File "C:\Users\<USER>\.pyenv\pyenv-win\versions\3.10.11\lib\site-packages\torch\cuda\__init__.py", line 1026, in current_device
[2025-06-28 19:46:49.575]     _lazy_init()
[2025-06-28 19:46:49.575]   File "C:\Users\<USER>\.pyenv\pyenv-win\versions\3.10.11\lib\site-packages\torch\cuda\__init__.py", line 363, in _lazy_init
[2025-06-28 19:46:49.576]     raise AssertionError("Torch not compiled with CUDA enabled")
[2025-06-28 19:46:49.576] AssertionError: Torch not compiled with CUDA enabled
