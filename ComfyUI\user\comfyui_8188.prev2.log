## ComfyUI-Manager: installing dependencies done.
[2025-06-28 17:37:24.515] ** ComfyUI startup time: 2025-06-28 17:37:24.515
[2025-06-28 17:37:24.516] ** Platform: Windows
[2025-06-28 17:37:24.516] ** Python version: 3.13.5 (tags/v3.13.5:6cb20a2, Jun 11 2025, 16:15:46) [MSC v.1943 64 bit (AMD64)]
[2025-06-28 17:37:24.516] ** Python executable: C:\Users\<USER>\ai\ComfiGit\comfyui_env\Scripts\python.exe
[2025-06-28 17:37:24.516] ** ComfyUI Path: C:\Users\<USER>\ai\ComfiGit\ComfyUI
[2025-06-28 17:37:24.516] ** ComfyUI Base Folder Path: C:\Users\<USER>\ai\ComfiGit\ComfyUI
[2025-06-28 17:37:24.516] ** User directory: C:\Users\<USER>\ai\ComfiGit\ComfyUI\user
[2025-06-28 17:37:24.517] ** ComfyUI-Manager config path: C:\Users\<USER>\ai\ComfiGit\ComfyUI\user\default\ComfyUI-Manager\config.ini
[2025-06-28 17:37:24.517] ** Log path: C:\Users\<USER>\ai\ComfiGit\ComfyUI\user\comfyui.log
[ComfyUI-Manager] In Python 3.13 and above, PIP Fixer does not downgrade `numpy` below version 2.0. If you need to force a downgrade of `numpy`, please use `pip_auto_fix.list`.
[2025-06-28 17:37:25.216] 
Prestartup times for custom nodes:
[2025-06-28 17:37:25.216]    1.9 seconds: C:\Users\<USER>\ai\ComfiGit\ComfyUI\custom_nodes\ComfyUI-Manager
[2025-06-28 17:37:25.216] 
[2025-06-28 17:37:26.493] Checkpoint files will always be loaded safely.
[2025-06-28 17:37:27.434] Total VRAM 24463 MB, total RAM 64957 MB
[2025-06-28 17:37:27.435] pytorch version: 2.7.1+cu128
[2025-06-28 17:37:27.435] Set vram state to: NORMAL_VRAM
[2025-06-28 17:37:27.436] Device: cuda:0 NVIDIA GeForce RTX 5090 Laptop GPU : cudaMallocAsync
[2025-06-28 17:37:28.406] Using pytorch attention
[2025-06-28 17:37:29.951] Python version: 3.13.5 (tags/v3.13.5:6cb20a2, Jun 11 2025, 16:15:46) [MSC v.1943 64 bit (AMD64)]
[2025-06-28 17:37:29.951] ComfyUI version: 0.3.43
[2025-06-28 17:37:29.994] ComfyUI frontend version: 1.23.4
[2025-06-28 17:37:29.995] [Prompt Server] web root: C:\Users\<USER>\ai\ComfiGit\comfyui_env\Lib\site-packages\comfyui_frontend_package\static
[2025-06-28 17:37:30.688] ### Loading: ComfyUI-Impact-Pack (V8.17.1)
[2025-06-28 17:37:30.748] [Impact Pack] Wildcards loading done.
[2025-06-28 17:37:30.753] ### Loading: ComfyUI-Manager (V3.33.3)
[2025-06-28 17:37:30.754] [ComfyUI-Manager] network_mode: public
[2025-06-28 17:37:30.963] ### ComfyUI Version: v0.3.43 | Released on '2025-06-27'
[2025-06-28 17:37:31.323] [ComfyUI-Manager] default cache updated: https://raw.githubusercontent.com/ltdrdata/ComfyUI-Manager/main/alter-list.json
[2025-06-28 17:37:31.334] [ComfyUI-Manager] default cache updated: https://raw.githubusercontent.com/ltdrdata/ComfyUI-Manager/main/model-list.json
[2025-06-28 17:37:31.346] [ComfyUI-Manager] default cache updated: https://raw.githubusercontent.com/ltdrdata/ComfyUI-Manager/main/github-stats.json
[2025-06-28 17:37:31.377] [ComfyUI-Manager] default cache updated: https://raw.githubusercontent.com/ltdrdata/ComfyUI-Manager/main/extension-node-map.json
[2025-06-28 17:37:31.404] [ComfyUI-Manager] default cache updated: https://raw.githubusercontent.com/ltdrdata/ComfyUI-Manager/main/custom-node-list.json
[2025-06-28 17:37:31.477] 
Import times for custom nodes:
[2025-06-28 17:37:31.478]    0.0 seconds: C:\Users\<USER>\ai\ComfiGit\ComfyUI\custom_nodes\websocket_image_save.py
[2025-06-28 17:37:31.478]    0.0 seconds: C:\Users\<USER>\ai\ComfiGit\ComfyUI\custom_nodes\ComfyUI-Custom-Scripts
[2025-06-28 17:37:31.478]    0.0 seconds: C:\Users\<USER>\ai\ComfiGit\ComfyUI\custom_nodes\efficiency-nodes-comfyui
[2025-06-28 17:37:31.478]    0.0 seconds: C:\Users\<USER>\ai\ComfiGit\ComfyUI\custom_nodes\ComfyUI-Advanced-ControlNet
[2025-06-28 17:37:31.478]    0.1 seconds: C:\Users\<USER>\ai\ComfiGit\ComfyUI\custom_nodes\ComfyUI-Impact-Pack
[2025-06-28 17:37:31.478]    0.2 seconds: C:\Users\<USER>\ai\ComfiGit\ComfyUI\custom_nodes\ComfyUI-VideoHelperSuite
[2025-06-28 17:37:31.478]    0.5 seconds: C:\Users\<USER>\ai\ComfiGit\ComfyUI\custom_nodes\ComfyUI-Manager
[2025-06-28 17:37:31.478] 
[2025-06-28 17:37:31.681] Context impl SQLiteImpl.
[2025-06-28 17:37:31.681] Will assume non-transactional DDL.
[2025-06-28 17:37:31.682] No target revision found.
[2025-06-28 17:37:31.695] Starting server

[2025-06-28 17:37:31.695] To see the GUI go to: http://127.0.0.1:8188
[2025-06-28 17:37:35.942] FETCH ComfyRegistry Data: 5/90
[2025-06-28 17:37:39.510] FETCH ComfyRegistry Data: 10/90
[2025-06-28 17:37:43.609] FETCH ComfyRegistry Data: 15/90
[2025-06-28 17:37:47.198] FETCH ComfyRegistry Data: 20/90
[2025-06-28 17:37:50.703] FETCH ComfyRegistry Data: 25/90
[2025-06-28 17:37:54.343] FETCH ComfyRegistry Data: 30/90
[2025-06-28 17:37:57.907] FETCH ComfyRegistry Data: 35/90
[2025-06-28 17:38:02.202] FETCH ComfyRegistry Data: 40/90
[2025-06-28 17:38:05.891] FETCH ComfyRegistry Data: 45/90
[2025-06-28 17:38:09.563] FETCH ComfyRegistry Data: 50/90
[2025-06-28 17:38:13.175] FETCH ComfyRegistry Data: 55/90
[2025-06-28 17:38:17.403] FETCH ComfyRegistry Data: 60/90
[2025-06-28 17:38:21.080] FETCH ComfyRegistry Data: 65/90
[2025-06-28 17:38:24.987] FETCH ComfyRegistry Data: 70/90
[2025-06-28 17:38:28.783] FETCH ComfyRegistry Data: 75/90
[2025-06-28 17:38:32.507] FETCH ComfyRegistry Data: 80/90
[2025-06-28 17:38:36.091] FETCH ComfyRegistry Data: 85/90
[2025-06-28 17:38:41.121] FETCH ComfyRegistry Data: 90/90
[2025-06-28 17:38:41.622] FETCH ComfyRegistry Data [DONE]
[2025-06-28 17:38:41.829] [ComfyUI-Manager] default cache updated: https://api.comfy.org/nodes
[2025-06-28 17:38:41.856] FETCH DATA from: https://raw.githubusercontent.com/ltdrdata/ComfyUI-Manager/main/custom-node-list.json [DONE]
[2025-06-28 17:38:41.970] [ComfyUI-Manager] All startup tasks have been completed.
[2025-06-28 17:39:21.760] got prompt
[2025-06-28 17:39:21.885] model weight dtype torch.float16, manual cast: None
[2025-06-28 17:39:21.897] model_type V_PREDICTION_EDM
[2025-06-28 17:39:22.607] Using pytorch attention in VAE
[2025-06-28 17:39:22.612] Using pytorch attention in VAE
[2025-06-28 17:39:22.719] VAE load device: cuda:0, offload device: cpu, dtype: torch.bfloat16
[2025-06-28 17:39:23.220] !!! Exception during processing !!! ERROR: clip input is invalid: None

If the clip is from a checkpoint loader node your checkpoint does not contain a valid clip or text encoder model.
[2025-06-28 17:39:23.224] Traceback (most recent call last):
  File "C:\Users\<USER>\ai\ComfiGit\ComfyUI\execution.py", line 361, in execute
    output_data, output_ui, has_subgraph = get_output_data(obj, input_data_all, execution_block_cb=execution_block_cb, pre_execute_cb=pre_execute_cb)
                                           ~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\ai\ComfiGit\ComfyUI\execution.py", line 236, in get_output_data
    return_values = _map_node_over_list(obj, input_data_all, obj.FUNCTION, allow_interrupt=True, execution_block_cb=execution_block_cb, pre_execute_cb=pre_execute_cb)
  File "C:\Users\<USER>\ai\ComfiGit\ComfyUI\execution.py", line 208, in _map_node_over_list
    process_inputs(input_dict, i)
    ~~~~~~~~~~~~~~^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\ai\ComfiGit\ComfyUI\execution.py", line 197, in process_inputs
    results.append(getattr(obj, func)(**inputs))
                   ~~~~~~~~~~~~~~~~~~^^^^^^^^^^
  File "C:\Users\<USER>\ai\ComfiGit\ComfyUI\nodes.py", line 67, in encode
    raise RuntimeError("ERROR: clip input is invalid: None\n\nIf the clip is from a checkpoint loader node your checkpoint does not contain a valid clip or text encoder model.")
RuntimeError: ERROR: clip input is invalid: None

If the clip is from a checkpoint loader node your checkpoint does not contain a valid clip or text encoder model.

[2025-06-28 17:39:23.227] Prompt executed in 1.46 seconds
[2025-06-28 17:41:17.102] got prompt
[2025-06-28 17:41:17.104] !!! Exception during processing !!! ERROR: clip input is invalid: None

If the clip is from a checkpoint loader node your checkpoint does not contain a valid clip or text encoder model.
[2025-06-28 17:41:17.107] Traceback (most recent call last):
  File "C:\Users\<USER>\ai\ComfiGit\ComfyUI\execution.py", line 361, in execute
    output_data, output_ui, has_subgraph = get_output_data(obj, input_data_all, execution_block_cb=execution_block_cb, pre_execute_cb=pre_execute_cb)
                                           ~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\ai\ComfiGit\ComfyUI\execution.py", line 236, in get_output_data
    return_values = _map_node_over_list(obj, input_data_all, obj.FUNCTION, allow_interrupt=True, execution_block_cb=execution_block_cb, pre_execute_cb=pre_execute_cb)
  File "C:\Users\<USER>\ai\ComfiGit\ComfyUI\execution.py", line 208, in _map_node_over_list
    process_inputs(input_dict, i)
    ~~~~~~~~~~~~~~^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\ai\ComfiGit\ComfyUI\execution.py", line 197, in process_inputs
    results.append(getattr(obj, func)(**inputs))
                   ~~~~~~~~~~~~~~~~~~^^^^^^^^^^
  File "C:\Users\<USER>\ai\ComfiGit\ComfyUI\nodes.py", line 67, in encode
    raise RuntimeError("ERROR: clip input is invalid: None\n\nIf the clip is from a checkpoint loader node your checkpoint does not contain a valid clip or text encoder model.")
RuntimeError: ERROR: clip input is invalid: None

If the clip is from a checkpoint loader node your checkpoint does not contain a valid clip or text encoder model.

[2025-06-28 17:41:17.110] Prompt executed in 0.01 seconds
[2025-06-28 17:41:38.314] ERROR: To use this feature, you must either set '--listen' to a local IP and set the security level to 'normal-' or lower, or set the security level to 'middle' or 'weak'. Please contact the administrator.
Reference: https://github.com/ltdrdata/ComfyUI-Manager#security-policy
[2025-06-28 17:41:45.066] ERROR: To use this feature, you must either set '--listen' to a local IP and set the security level to 'normal-' or lower, or set the security level to 'middle' or 'weak'. Please contact the administrator.
Reference: https://github.com/ltdrdata/ComfyUI-Manager#security-policy
[2025-06-28 17:46:54.485] got prompt
[2025-06-28 17:46:54.488] !!! Exception during processing !!! ERROR: clip input is invalid: None

If the clip is from a checkpoint loader node your checkpoint does not contain a valid clip or text encoder model.
[2025-06-28 17:46:54.489] Traceback (most recent call last):
  File "C:\Users\<USER>\ai\ComfiGit\ComfyUI\execution.py", line 361, in execute
    output_data, output_ui, has_subgraph = get_output_data(obj, input_data_all, execution_block_cb=execution_block_cb, pre_execute_cb=pre_execute_cb)
                                           ~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\ai\ComfiGit\ComfyUI\execution.py", line 236, in get_output_data
    return_values = _map_node_over_list(obj, input_data_all, obj.FUNCTION, allow_interrupt=True, execution_block_cb=execution_block_cb, pre_execute_cb=pre_execute_cb)
  File "C:\Users\<USER>\ai\ComfiGit\ComfyUI\execution.py", line 208, in _map_node_over_list
    process_inputs(input_dict, i)
    ~~~~~~~~~~~~~~^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\ai\ComfiGit\ComfyUI\execution.py", line 197, in process_inputs
    results.append(getattr(obj, func)(**inputs))
                   ~~~~~~~~~~~~~~~~~~^^^^^^^^^^
  File "C:\Users\<USER>\ai\ComfiGit\ComfyUI\nodes.py", line 67, in encode
    raise RuntimeError("ERROR: clip input is invalid: None\n\nIf the clip is from a checkpoint loader node your checkpoint does not contain a valid clip or text encoder model.")
RuntimeError: ERROR: clip input is invalid: None

If the clip is from a checkpoint loader node your checkpoint does not contain a valid clip or text encoder model.

[2025-06-28 17:46:54.491] Prompt executed in 0.01 seconds
