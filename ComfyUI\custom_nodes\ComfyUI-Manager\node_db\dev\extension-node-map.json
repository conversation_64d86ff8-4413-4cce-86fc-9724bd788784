{"https://github.com/17Retoucher/ComfyUI_Fooocus": [["BasicScheduler", "CLIPLoader", "CLIPMergeSimple", "CLIPSave", "CLIPSetLastLayer", "CLIPTextEncode", "CLIPTextEncodeSDXL", "CLIPTextEncodeSDXLRefiner", "CLIPVisionEncode", "CLIPVisionLoader", "<PERSON><PERSON>", "CheckpointLoader", "CheckpointLoaderSimple", "CheckpointSave", "ConditioningAverage", "Conditioning<PERSON><PERSON><PERSON>", "ConditioningConcat", "ConditioningSetArea", "ConditioningSetAreaPercentage", "ConditioningSetMask", "ConditioningSetTimestepRange", "ConditioningZeroOut", "ControlNetApply", "ControlNetApplyAdvanced", "ControlNetLoader", "CropMask", "DiffControlNetLoader", "Diff<PERSON><PERSON><PERSON><PERSON><PERSON>", "DualCLIPLoader", "EmptyImage", "EmptyLatentImage", "ExponentialScheduler", "FeatherMask", "FlipSigmas", "Fooocus Controlnet", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "Fooocus negative", "Fooocus positive", "Fooocus stylesSelector", "FreeU", "FreeU_V2", "GLIGEN<PERSON><PERSON>der", "GLIGENTextBoxApply", "GrowMask", "HyperTile", "HypernetworkLoader", "ImageBatch", "ImageBlend", "ImageBlur", "ImageColorToMask", "ImageCompositeMasked", "ImageCrop", "ImageInvert", "ImageOnlyCheckpointLoader", "ImagePadForOutpaint", "ImageQuantize", "ImageScale", "ImageScaleBy", "ImageScaleToTotalPixels", "ImageSharpen", "ImageToMask", "ImageUpscaleWithModel", "InvertMask", "JoinImageWithAlpha", "K<PERSON><PERSON><PERSON>", "KSamplerAdvanced", "KSamplerSelect", "KarrasScheduler", "LatentAdd", "LatentBatch", "LatentBlend", "LatentComposite", "LatentCompositeMasked", "LatentCrop", "LatentFlip", "LatentFromBatch", "LatentInterpolate", "Latent<PERSON><PERSON><PERSON>ly", "LatentRotate", "LatentSubtract", "LatentUpscale", "LatentUpscaleBy", "LoadImage", "LoadImageMask", "LoadLatent", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "LoraLoaderModelOnly", "MaskComposite", "MaskToImage", "ModelMergeAdd", "ModelMergeBlocks", "ModelMergeSimple", "ModelMergeSubtract", "ModelSamplingContinuousEDM", "ModelSamplingDiscrete", "PatchModelAddDownscale", "PerpNeg", "PolyexponentialScheduler", "PorterDuffImageComposite", "PreviewImage", "RebatchImages", "RebatchLatents", "RepeatImageBatch", "RepeatLatentBatch", "RescaleCFG", "SDTurboScheduler", "SVD_img2vid_Conditioning", "SamplerCustom", "SamplerDPMPP_2M_SDE", "SamplerDPMPP_SDE", "SaveAnimatedPNG", "SaveAnimatedWEBP", "SaveImage", "SaveLatent", "SelfAttentionGuidance", "SetLatentNoiseMask", "SolidMask", "SplitImageWithAlpha", "SplitSigmas", "StableZero123_Conditioning", "StyleModelApply", "StyleModelLoader", "TomePatchModel", "UNETLoader", "UpscaleModelLoader", "VAEDecode", "VAEDecodeTiled", "VAEEncode", "VAEEncodeForInpaint", "VAEEncodeTiled", "VAELoader", "VAESave", "VPScheduler", "VideoLinearCFGGuidance", "unCLIPCheckpointLoader", "unCLIPConditioning"], {"title_aux": "ComfyUI_Fooocus"}], "https://github.com/1hew/ComfyUI-1hewNodes": [["ImageAddLabel", "ImageBatchToList", "ImageBlendModesByAlpha", "ImageBlendModesByCSS", "ImageCropByMaskAlpha", "ImageCropEdge", "ImageCropSquare", "ImageCropWithBBoxMask", "ImageDetailHLFreqSeparation", "ImageEditStitch", "ImageListAppend", "ImageListToBatch", "ImageLumaMatte", "ImagePasteByBBoxMask", "ImagePlot", "ImageResizeUniversal", "ImageSolid", "ImageTileMerge", "ImageTileSplit", "ListCustomFloat", "ListCustomInt", "ListCustomString", "MaskBatchMathOps", "MaskBatchToList", "MaskCropByBBoxMask", "MaskListToBatch", "MaskMathOps", "PathBuild", "RangeMapping", "StringCoordinateToBBoxMask", "StringCoordinateToBBoxes", "TextCustomExtract"], {"title_aux": "ComfyUI-1hewNodes [WIP]"}], "https://github.com/3dmindscapper/ComfyUI-PartField": [["PartFieldClustering", "PartFieldExportParts", "PartFieldInference", "PartFieldModelDownLoader", "PartFieldSplitter", "PartFieldViewer"], {"title_aux": "ComfyUI-PartField [WIP]"}], "https://github.com/3dmindscapper/ComfyUI-Sam-Mesh": [["SamMeshExporter", "SamMeshExporter+SamMesh", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "SamMeshLoader+SamMesh", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "SamMeshRenderer+Sam<PERSON>esh", "SamMeshSegmenter", "SamMeshSegmenter+SamMesh", "SamModelDownloader", "SamModelDownloader+SamMesh"], {"title_aux": "ComfyUI-<PERSON> [WIP]"}], "https://github.com/438443467/ComfyUI-SanMian-Nodes": [["FaceAlignPro", "FaceAlignProRestore", "SANMIN Adapt Coordinates", "SanmiKSampler", "sanmi AddTextToImage", "sanmi Adjust Transparency By Mask", "sanmi AdjustHexBrightness", "sanmi Align Images with Mask", "sanmi BinarizeMask", "sanmi BlendICLight", "sanmi Chinese To Character", "sanmi ColorOverlayOnMask", "sanmi Compare", "sanmi CompareV2", "sanmi Counter", "sanmi CreateTxtForImages", "sanmi Filter Prompt Words", "sanmi Float", "sanmi Florence2toCoordinates", "sanmi Get Content From Excel", "sanmi Get LastPathComponent", "sanmi Get Mask White Region Size", "san<PERSON>", "sanmi GetMostCommonColor", "sanmi ImageBatchSplitter", "sanmi Image_Rotate", "sanmi Int90", "sanmi IntToBOOLEAN", "sanmi Load Image Batch", "sanmi <PERSON>rom<PERSON>", "sanmi Load<PERSON>mi", "sanmi Mask To Box", "sanmi MaskToBboxes", "sanmi MaskWhiteRatioAnalyzer", "sanmi Path Captioner", "sanmi Path Change", "sanmi Read Image Prompt", "sanmi RectMaskAnalyzer", "sanmi Reduce Mask", "san<PERSON>", "sanmi Sanmi_Text_Concatenate", "sanmi Save Image To Local", "sanmi SimpleWildcards", "sanmi SortTheMasksLeftRight", "sanmi SortTheMasksSize", "sanmi Special Counter", "sanmi StrToPinYin", "sanmi String Counter", "sanmi String Counter V2", "sanmi StringToBox", "sanmi Time", "sanmi Upscale And Keep Original Size"], {"title_aux": "ComfyUI-SanMian-Nodes"}], "https://github.com/5x00/ComfyUI-Prompt-Plus": [["LoadAPI", "LoadCustomModel", "LoadFlorenceModel", "Prompt", "RunAPIVLM", "RunCustomVLM", "TriggerToPromptAPI", "TriggerToPromptCustom", "TriggerToPromptSimple"], {"title_aux": "ComfyUI-Prompt-Plus [WIP]"}], "https://github.com/7BEII/Comfyui_PDuse": [["Empty_Line", "ImageBlendText", "ImageBlendV1", "ImageRatioCrop", "Load_Images", "PDFile_name_fix", "PDIMAGE_ImageCombine", "PDIMAGE_LongerSize", "PDIMAGE_Rename", "PDImageConcante", "PDImageResize", "PDImageResizeV2", "PDJSON_BatchJsonIncremental", "PDJSON_Group", "PD_CustomImageProcessor", "PD_GetImageSize", "PD_ImageBatchSplitter", "PD_ImageInfo", "PD_Image_Crop_Location", "PD_Image_Rotate_v1", "PD_Image_centerCrop", "PD_MASK_SELECTION", "PD_RemoveColorWords", "PD_SimpleTest", "PD_Text Overlay Node", "PD_imagesave_path", "PD_random_prompt", "PDstring_Save", "mask_edge_selector"], {"title_aux": "comfyui-promptbymood [WIP]"}], "https://github.com/A4P7J1N7M05OT/ComfyUI-ManualSigma": [["ManualSigma"], {"title_aux": "ComfyUI-ManualSigma"}], "https://github.com/A4P7J1N7M05OT/ComfyUI-VAELoaderSDXLmod": [["EmptyLatentImageVariable", "ModifiedSDXLVAELoader"], {"title_aux": "ComfyUI-VAELoaderSDXLmod"}], "https://github.com/A719689614/ComfyUI_AC_FUNV8Beta1": [["⬛(TODO)AC_Super_Come_Ckpt", "⬜(TODO)AC_Super_Come_Lora", "⭕AC_FUN_SUPER_LARGE", "🈵AC_Super_Checkpoint", "🈵AC_Super_Loras", "🎫AC_Super_PreviewImage", "🎰AC_Super_Controlnet", "💶AC_Super_EmptLatent", "💼AC_Super_Lora&LCM", "💾AC_Super_SaveImage", "📄AC_Super_CLIPEN", "📈AC_Super_UpKSampler", "📟AC_Super_CKPT&LCM", "🚀AC_Super_KSampler"], {"title_aux": "ComfyUI_AC_FUNV8Beta1"}], "https://github.com/AICodeFactory/ComfyUI-Viva": [["HttpTrigger_Common", "HttpTrigger_Image", "HttpTrigger_Viva"], {"title_aux": "ComfyUI-Viva"}], "https://github.com/AIFSH/ComfyUI-OpenDIT": [["DITModelLoader", "DITPromptNode", "Diff<PERSON><PERSON><PERSON><PERSON>", "LattePipeLineNode", "OpenSoraNode", "OpenSoraPlanPipeLineNode", "PABConfigNode", "PreViewVideo", "<PERSON>r<PERSON><PERSON><PERSON>", "T5EncoderLoader", "T5TokenizerLoader"], {"title_aux": "ComfyUI-OpenDIT [WIP]"}], "https://github.com/AIFSH/ComfyUI-ViViD": [["LoadImagePath", "LoadVideo", "PreViewVideo", "ViViD_Node"], {"title_aux": "ComfyUI-ViViD"}], "https://github.com/AIFSH/HivisionIDPhotos-ComfyUI": [["AddBackgroundNode", "AddWaterMarkNode", "ENHivisionParamsNode", "HivisionLayOutNode", "HivisionNode", "LaterProcessNode", "ZHHivisionParamsNode"], {"author": "cuny", "description": "", "title_aux": "HivisionIDPhotos-ComfyUI"}], "https://github.com/AIFSH/IMAGDressing-ComfyUI": [["IMAGDressingNode", "TextNode"], {"title_aux": "IMAGDressing-ComfyUI"}], "https://github.com/AIFSH/UltralightDigitalHuman-ComfyUI": [["InferUltralightDigitalHumanNode", "TrainUltralightDigitalHumanNode"], {"title_aux": "UltralightDigitalHuman-ComfyUI"}], "https://github.com/AIFSH/UtilNodes-ComfyUI": [["GetRGBEmptyImgae", "LoadVideo", "PreViewVideo", "PromptTextNode"], {"title_aux": "UtilNodes-ComfyUI [WIP]"}], "https://github.com/ALatentPlace/ComfyUI_yanc": [["> Bloom", "> Blur", "> Brightness", "> Clear Text", "> Combine Channels", "> Contrast", "> Divide Channels", "> <PERSON> Enhance", "> Film Grain", "> Float to Int", "> Fog", "> Get Mean Color", "> HUE", "> Int", "> Int to Text", "> Layer Weights (for IPAMS)", "> Lens Distortion", "> Light Source Mask", "> Load Image", "> Load Image From Folder", "> <PERSON>", "> NIKSampler", "> Noise From Image", "> Normal Map Lighting", "> RGB Color", "> RGB Shift", "> Resolution by Aspect Ratio", "> Rotate Image", "> Saturation", "> Save Image", "> Save Text", "> Scale Image to Side", "> Scanlines", "> Sharpen", "> Text", "> Text Combine", "> Text Count", "> Text Pick Line by Index", "> Text Pick Random Line", "> Text Random Weights", "> Text Replace", "> Vignette"], {"title_aux": "YANC- Yet Another Node Collection"}], "https://github.com/APZmedia/comfyui-textools": [["APZmediaImageRichTextOverlay"], {"author": "<PERSON>", "description": "This extension provides rich text overlay functionalities, color management, and text parsing utilities for ComfyUI.", "nickname": "ComfyUI Text Tools", "title": "ComfyUI APZmedia Text Tools", "title_aux": "comfyui-textools [WIP]"}], "https://github.com/AhBumm/ComfyUI-Upscayl": [["Upscayl Upscaler"], {"nodename_pattern": "\\(BillBum\\)$", "title_aux": "ComfyUI-Upscayl"}], "https://github.com/AhBumm/ComfyUI_MangaLineExtraction-hf": [["MangaLineExtraction-hf"], {"title_aux": "ComfyUI_MangaLineExtraction"}], "https://github.com/AkiEvansDev/ComfyUI-Tools": [["AE.AnySwitch", "AE.AnyTypeSwitch", "AE.BRIARemBG", "AE.BRIARemBGAdvanced", "AE.ChangeSamplerConfig", "AE.CheckpointList", "AE.CheckpointLoader", "AE.CompareFloat", "AE.CompareInt", "AE.ControlNetApplyWithConfig", "AE.ControlNetConfig", "AE.DisplayAny", "AE.ExtractControlNetConfig", "AE.ExtractHiresFixConfig", "AE.ExtractImg2ImgConfig", "AE.ExtractOutpaintConfig", "AE.ExtractSamplerConfig", "AE.Float", "AE.FloatList", "AE.FloatSwitch", "AE.FloatToInt", "A<PERSON><PERSON>", "AE.GetImageSize", "AE.GetLatentSize", "AE.GroupsMuter", "AE.HiresFixConfig", "AE.ImageAdjustment", "AE.ImageBlank", "AE.ImageBlendMask", "AE.ImageBlendMode", "AE.ImageCannyFilter", "AE.ImageDragonFilter", "A<PERSON>.<PERSON>", "AE.ImageLevels", "AE.ImageLucySharpen", "AE.ImagePixelate", "AE.ImagePowerN<PERSON>", "AE.ImageStyleFilter", "AE.Img2ImgConfig", "AE.InpaintWithModel", "AE.Int", "AE.IntList", "AE.IntSwitch", "AE.IntToFloat", "AE.KSamplerHiresFixWithConfig", "AE.KSamplerImg2ImgWithConfig", "AE.KSamplerInpaintWithConfig", "AE.KSamplerInpaintWithConfigAndImage", "AE.KSamplerOutpaintWithConfig", "AE.KSamplerOutpaintWithConfigAndImage", "AE.KSamplerWithConfig", "AE.LoadImageFromPath", "AE.LoadInpaintModel", "A<PERSON><PERSON>", "AE.Lo<PERSON>List", "<PERSON><PERSON><PERSON>", "AE.MathFloat", "AE.MathInt", "AE.OutpaintConfig", "AE.OutpaintWithModel", "AE.OutpaintWithModelAndConfig", "AE.Range", "AE.RangeList", "AE.SDXLConfig", "AE.SDXLPrompt", "AE.SDXLPromptWithHires", "AE.SDXLRegionalPrompt", "AE.SDXLRegionalPromptWithHires", "AE.<PERSON>Config", "AE.<PERSON>plerList", "AE.SaveImage", "AE.SchedulerList", "<PERSON><PERSON><PERSON>Seed", "AE.String", "AE.StringConcat", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "AE.StringList", "<PERSON><PERSON><PERSON>", "AE.StringSwitch", "AE.Text", "AE.ToString", "AE.ToStringConcat", "AE.UpscaleLatentBy", "AE.VAEEncodeInpaintConditioning", "AE.XYRange"], {"title_aux": "ComfyUI-Tools"}], "https://github.com/Alazuaka/comfyui-lora-stack-node": [["AlazukaCheckpoint", "EsLoraSet"], {"title_aux": "ES_nodes for ComfyUI by Alazuka [WIP]"}], "https://github.com/AlejandroTuzzi/TUZZI-ByPass": [["LinkSuppressor", "SequentialTextReaderAuto", "TUZZI-Bypasser", "TUZZI-DataloungeScraper", "TUZZI-DirectoryImagePromptReader", "TUZZI-GeminiFlash25", "TUZZI-GroqNode", "TUZZI-ImageAudioToVideo", "TUZZI-ImageExtractorSaver", "TUZZI-LineCounter", "TUZZI-LinkSuppressor", "TUZZI-NumberLines", "TUZZI-PlosArticleScraper", "TUZZI-RangedSelectorText5", "TUZZI-RangedSelectorTitleURL10", "TUZZI-RangedSelectorTitleURL5", "TUZZI-RedditPostExtractor", "TUZZI-SaveVideo", "TUZZI-SequentialTextReader", "TUZZI-SequentialTextReaderAuto", "TUZZI-SmartAudioVisualComposer", "TUZZI-TVTropesScraper", "TUZZI-TextFormatter", "TUZZI-TextFormatterPlus", "TUZZI-TextTranslatorExporter", "TUZZI-TextTruncatorPlus", "TUZZI-YouTubeCommentExtractor", "TUZZI-YouTubeSubtitleExtractor"], {"title_aux": "TUZZI-ByPass [WIP]"}], "https://github.com/AlexXi19/ComfyUI-OpenAINode": [["ImageWithPrompt", "TextWithPrompt"], {"title_aux": "ComfyUI-OpenAINode"}], "https://github.com/AlexYez/comfyui-timesaver": [["TS Cube to Equirectangular", "TS Equirectangular to Cube", "TS Files Downloader", "TS Qwen2.5", "TS Youtube Chapters", "TSCropToMask", "TSRestoreFromCrop", "TSWhisper", "TS_DeflickerNode", "TS_FilePathLoader", "TS_Free_Video_Memory", "TS_ImageResize", "TS_MarianTranslator", "TS_Qwen3", "TS_VideoDepthNode", "TS_Video_Upscale_With_Model"], {"title_aux": "ComfyUI Timesaver Nodes"}], "https://github.com/AllenEdgarPoe/ComfyUI-Xorbis-nodes": [["Add Human Styler", "ConcaveHullImage", "Convert Monochrome", "ImageBWPostprocessor", "ImageBWPreprocessor", "Inpaint Crop Xo", "LoadData", "Mask Aligned bbox for ConcaveHull", "Mask Aligned bbox for Inpainting", "Mask Aligned bbox for Inpainting2", "Mask Square bbox for Inpainting", "One Image Compare", "RT4KSR Loader", "RandomPromptStyler", "Save Log Info", "Three Image Compare", "Upscale RT4SR"], {"title_aux": "ComfyUI-Xorbis-nodes [WIP]"}], "https://github.com/Alvaroeai/ComfyUI-SunoAI-Mds": [["Mideas_SunoAI_AudioManager", "Mideas_SunoAI_Generator", "Mideas_SunoAI_ProxyDownloadNode", "Mideas_SunoAI_ProxyNode"], {"title_aux": "ComfyUI-SunoAI-Mds"}], "https://github.com/Anonymzx/ComfyUI-Indonesia-TTS": [["Facebook MMS-TTS-IND Variants FX"], {"title_aux": "ComfyUI-Indonesia-TTS [WIP]"}], "https://github.com/Anze-/ComfyUI-OIDN": [["OIDN Denoise"], {"title_aux": "ComfyUI-OIDN [WIP]"}], "https://github.com/Anze-/ComfyUI_deepDeband": [["deepDeband Inference"], {"title_aux": "ComfyUI_deepDeband [WIP]"}], "https://github.com/ArmandAlbert/Kwai_font_comfyui": [["Kwaifont_Image_Cropper", "Kwaifont_Resnet101_Loader", "Kwaifont_Resnet101_Runner", "Kwaifont_Resnet50_Loader", "Kwaifont_Resnet50_Runner"], {"title_aux": "Kwai_font_comfyui"}], "https://github.com/ArthusLiang/comfyui-face-remap": [["FaceRemap"], {"title_aux": "comfyui-face-remap [WIP]"}], "https://github.com/Aryan185/ComfyUI-ReplicateFluxKontext": [["FluxKontextMaxNode", "FluxKontextProNode", "GeminiChatNode"], {"title_aux": "ComfyUI-ReplicateFluxKontext"}], "https://github.com/BadCafeCode/execution-inversion-demo-comfyui": [["AccumulateNode", "AccumulationGetItemNode", "AccumulationGetLengthNode", "AccumulationHeadNode", "AccumulationSetItemNode", "AccumulationTailNode", "AccumulationToListNode", "BoolOperationNode", "ComponentInput", "ComponentMetadata", "ComponentOutput", "DebugPrint", "ExecutionBlocker", "FloatConditions", "ForLoopClose", "ForLoopOpen", "IntConditions", "IntMathOperation", "InversionDemoAdvancedPromptNode", "InversionDemoLazyConditional", "InversionDemoLazyIndexSwitch", "InversionDemoLazyMixImages", "InversionDemoLazySwitch", "ListToAccumulationNode", "MakeListNode", "StringConditions", "ToBoolNode", "WhileLoopClose", "WhileLoopOpen"], {"title_aux": "execution-inversion-demo-comfyui"}], "https://github.com/BaronVonBoolean/ComfyUI-FileOps": [["File Mv", "File Path", "Make Dir"], {"title_aux": "ComfyUI-FileOps [UNSAFE]"}], "https://github.com/BenjaMITM/ComfyUI_On_The_Fly_Wildcards": [["Display String", "Wildcard Creator", "Wildcard Loader", "Wildcard Selector"], {"title_aux": "ComfyUI_On_The_Fly_Wildcards [WIP]"}], "https://github.com/BetaDoggo/ComfyUI-LogicGates": [["AND", "BitMemory", "BoolToString", "ByteMemory", "ByteToBits", "CreateByte", "NAND", "NOR", "NOT", "ON", "OR", "SWITCH", "XNOR", "XOR"], {"title_aux": "ComfyUI-LogicGates"}], "https://github.com/Big-Idea-Technology/ComfyUI-Movie-Tools": [["LoadImagesFromSubdirsBatch", "SaveImagesWithSubfolder"], {"title_aux": "ComfyUI-Movie-Tools [WIP]"}], "https://github.com/BigStationW/flowmatch_scheduler-comfyui": [["FlowMatchSigmas"], {"title_aux": "flowmatch_scheduler-comfyui"}], "https://github.com/BinglongLi/ComfyUI_ToolsForAutomask": [["Closing Mask", "Conditional Mask Selector", "Directional Mask Expansion", "Mask Fill Gaps Convex Hull", "Opening Mask", "Precise Add Mask", "Precise Subtract Mask", "<PERSON><PERSON>e Thin Branches Mask", "Remove Small Regions Mask"], {"title_aux": "ComfyUI_ToolsForAutomask"}], "https://github.com/BlueDangerX/ComfyUI-BDXNodes": [["BDXTestInt", "ColorMatch", "ColorToMask", "ConditioningMultiCombine", "ConditioningSetMaskAndCombine", "ConditioningSetMaskAndCombine3", "ConditioningSetMaskAndCombine4", "ConditioningSetMaskAndCombine5", "CreateAudioMask", "CreateFadeMask", "CreateFluidMask", "CreateGradientMask", "CreateTextMask", "CrossFadeImages", "EmptyLatentImagePresets", "GrowMaskWithBlur", "SomethingToString", "VRAM_Debug"], {"author": "BlueDangerX", "title": "BDXNodes", "title_aux": "ComfyUI-BDXNodes [WIP]"}], "https://github.com/BobRandomNumber/ComfyUI-DiaTTS": [["DiaGenerate", "<PERSON><PERSON><PERSON><PERSON><PERSON>"], {"title_aux": "ComfyUI DiaTest TTS Node [WIP]"}], "https://github.com/Brandelan/ComfyUI_bd_customNodes": [["BD Random Range", "BD Random Settings", "BD Sequencer", "BD Settings"], {"title_aux": "ComfyUI_bd_customNodes"}], "https://github.com/BuffMcBigHuge/ComfyUI-Buff-Nodes": [["ConsoleOutput", "FilePathSelectorFromDirectory", "MostRecentFileSelector", "RaftOpticalFlowNode", "StringProcessor", "TwoImageConcatenator"], {"title_aux": "ComfyUI-Buff-<PERSON>des [WIP]"}], "https://github.com/Burgstall-labs/ComfyUI-BS_FalAi-API-Video": [["FalAILipSyncNode", "FalAPIOmniProNode", "FalAPIVideoGeneratorI2V", "FalAPIVideoGeneratorT2V"], {"title_aux": "ComfyUI-BS_FalAi-API-Video [WIP]"}], "https://github.com/COcisuts/CObot-ComfyUI-WhisperToTranscription": [["CobotWhisperToTransciption"], {"title_aux": "CObot-ComfyUI-WhisperToTranscription [WIP]"}], "https://github.com/CY-CHENYUE/ComfyUI-FramePack-HY": [["CreateKeyframes_HY", "FramePackBucketResize_HY", "FramePackDiffusersSampler_HY", "LoadFramePackDiffusersPipeline_HY"], {"title_aux": "ComfyUI-FramePack-HY"}], "https://github.com/CeeVeeR/ComfyUi-Text-Tiler": [["Text Tiler"], {"title_aux": "ComfyUi-Text-Tiler"}], "https://github.com/Chargeuk/ComfyUI-vts-nodes": [["VTS Add Text To list", "VTS Calculate Upscale Amount", "VTS Clean Text", "VTS Clean Text List", "VTS Clear Ram", "VTS Clip Text Encode", "VTS Color Mask To Mask", "VTS Conditioning Set Batch Mask", "VTS Count Characters", "VTS Create Character Mask", "VTS Fix Image Tags", "VTS Image Upscale With Model", "VTS Images Crop From Masks", "VTS Images Scale", "VTS Images Scale To Min", "VTS Merge Delimited Text", "VTS Merge Text", "VTS Merge Text Lists", "VTS Reduce Batch Size", "VTS Render People Kps", "VTS Repeat Text As List", "VTS Replace Text In List", "VTS To Text", "VTS_Load_Pose_Keypoints", "Vts Text To Batch Prompt"], {"title_aux": "ComfyUI-vts-nodes [WIP]"}], "https://github.com/Charonartist/ComfyUI-send-eagle-pro_2": [["<PERSON>ch Send Media to Eagle", "Send Audio to Eagle", "Send Eagle with text", "Send Media to Eagle", "Send Media to Eagle (Advanced)", "Send Video to Eagle", "Send Webp Image to Eagle"], {"title_aux": "ComfyUI-send-eagle-pro"}], "https://github.com/ChrisColeTech/ComfyUI-Get-Random-File": [["Get Image File By Index", "Get Video File By Index", "Random File Path", "Random Image Path", "Random Video Path"], {"title_aux": "ComfyUI-Get-Random-File [UNSAFE]"}], "https://github.com/Clelstyn/ComfyUI-Inpaint_with_Detailer": [["FilterAndBlurMask", "MaskedResizeImage", "PasteMaskedImage"], {"title_aux": "ComfyUI-Inpaint_with_Detailer"}], "https://github.com/Clybius/ComfyUI-FluxDeCLIP": [["FluxDeCLIPCheckpointLoader"], {"title_aux": "ComfyUI-FluxDeCLIP"}], "https://github.com/Comfy-Org/ComfyUI_devtools": [["DevToolsDeprecatedNode", "DevToolsErrorRaiseNode", "DevToolsErrorRaiseNodeWithMessage", "DevToolsExperimentalNode", "DevToolsLoadAnimatedImageTest", "DevToolsLongComboDropdown", "DevToolsMultiSelectNode", "DevToolsNodeWithBooleanInput", "DevToolsNodeWithDefaultInput", "DevToolsNodeWithForceInput", "DevToolsNodeWithOnlyOptionalInput", "DevToolsNodeWithOptionalComboInput", "DevToolsNodeWithOptionalInput", "DevToolsNodeWithOutputCombo", "DevToolsNodeWithOutputList", "DevToolsNodeWithSeedInput", "DevToolsNodeWithStringInput", "DevToolsNodeWithUnionInput", "DevToolsNodeWithV2ComboInput", "DevToolsNodeWithValidation", "DevToolsObjectPatchNode", "DevToolsRemoteWidgetNode", "DevToolsRemoteWidgetNodeWithControlAfterRefresh", "DevToolsRemoteWidgetNodeWithParams", "DevToolsRemoteWidgetNodeWithRefresh", "DevToolsRemoteWidgetNodeWithRefreshButton", "DevToolsSimpleSlider"], {"title_aux": "ComfyUI_devtools [WIP]"}], "https://github.com/ComfyUI-Workflow/ComfyUI-OpenAI": [["OpenAI.CaptionImage"], {"title_aux": "ComfyUI OpenAI Nodes"}], "https://github.com/D1-3105/ComfyUI-VideoStream": [["FloWWeaverExportSingleFrameGRPC"], {"title_aux": "ComfyUI-VideoStream"}], "https://github.com/DataCTE/ComfyUI-DataVoid-nodes": [["IPAAdapterFaceIDBatch", "IPAdapter", "IPAdapterAdvanced", "IPAdapterBatch", "IPAdapterClipVisionEnhancer", "IPAdapterClipVisionEnhancerBatch", "IPAdapterCombineEmbeds", "IPAdapterCombineParams", "IPAdapterCombineWeights", "IPAdapterEmbeds", "IPAdapterEmbedsBatch", "IPAdapterEncoder", "IPAdapterFaceID", "IPAdapterFaceIDKolors", "IPAdapterFromParams", "IPAdapterInsightFaceLoader", "IPAdapterLoadEmbeds", "IPAdapterMS", "IPAdapterModelLoader", "IPAdapterNoise", "IPAdapterPreciseComposition", "IPAdapterPreciseCompositionBatch", "IPAdapterPreciseStyleTransfer", "IPAdapterPreciseStyleTransferBatch", "IPAdapterPromptScheduleFromWeightsStrategy", "IPAdapterRegionalConditioning", "IPAdapterSameEnergy", "IPAdapterSaveEmbeds", "IPAdapterStyleComposition", "IPAdapterStyleCompositionBatch", "IPAdapterTiled", "IPAdapterTiledBatch", "IPAdapterUnifiedLoader", "IPAdapterUnifiedLoaderCommunity", "IPAdapterUnifiedLoaderFaceID", "IPAdapterWeights", "IPAdapterWeightsFromStrategy", "MegaMergeSDXL", "PrepImageForClipVision"], {"title_aux": "ComfyUI-DataVoid-nodes [WIP]"}], "https://github.com/DeTK/ComfyUI-Switch": [["NodeSwitch"], {"title_aux": "ComfyUI Node Switcher"}], "https://github.com/DoctorDiffusion/ComfyUI-Flashback": [["LatentExport", "LatentImport", "LatentLoop"], {"title_aux": "ComfyUI-Flashback"}], "https://github.com/DonutsDelivery/ComfyUI-DonutNodes": [["APG", "AddNoise", "AlignYourStepsScheduler", "ApplyLBW //Inspire", "BasicGuider", "BasicScheduler", "BetaSamplingScheduler", "CFGGuider", "CFGZeroStar", "CLIPAttentionMultiply", "CLIPLoader", "CLIPMergeAdd", "CLIPMergeSimple", "CLIPMergeSubtract", "CLIPSave", "CLIPSetLastLayer", "CLIPTextEncode", "CLIPTextEncodeControlnet", "CLIPTextEncodeFlux", "CLIPTextEncodeHiDream", "CLIPTextEncodeHunyuanDiT", "CLIPTextEncodeLumina2", "CLIPTextEncodePixArtAlpha", "CLIPTextEncodeSD3", "CLIPTextEncodeSDXL", "CLIPTextEncodeSDXLRefiner", "CLIPVisionEncode", "CLIPVisionLoader", "<PERSON><PERSON>", "CaseConverter", "CheckpointLoader", "CheckpointLoaderSimple", "CheckpointSave", "ConditioningAverage", "Conditioning<PERSON><PERSON><PERSON>", "ConditioningConcat", "ConditioningSetArea", "ConditioningSetAreaPercentage", "ConditioningSetAreaPercentageVideo", "ConditioningSetAreaStrength", "ConditioningSetMask", "ConditioningSetTimestepRange", "ConditioningStableAudio", "ConditioningZeroOut", "ControlNetApply", "ControlNetApplyAdvanced", "ControlNetApplySD3", "ControlNetInpaintingAliMamaApply", "ControlNetLoader", "CosmosImageToVideoLatent", "CreateVideo", "CropMask", "DiffControlNetLoader", "DifferentialDiffusion", "Diff<PERSON><PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "Donut Detailer 2", "Donut Detailer 4", "Don<PERSON> Detailer LoRA 5", "Donut Detailer XL Blocks", "DonutApplyLoRAStack", "DonutClipEncode", "DonutFillerClip", "DonutFillerModel", "DonutLoRAStack", "DonutWidenMergeCLIP", "DonutWidenMergeUNet", "DualCFGGuider", "DualCLIPLoader", "EmptyAceStepLatentAudio", "EmptyCosmosLatentVideo", "EmptyHunyuanLatentVideo", "EmptyImage", "EmptyLTXVLatentVideo", "EmptyLatentAudio", "EmptyLatentHunyuan3Dv2", "EmptyLatentImage", "EmptyMochiLatentVideo", "EmptySD3LatentImage", "ExponentialScheduler", "ExtendIntermediateSigmas", "FeatherMask", "FlipSigmas", "FluxDisableGuidance", "FluxGuidance", "FluxProCannyNode", "FluxProDepthNode", "FluxProExpandNode", "FluxProFillNode", "FluxProImageNode", "FluxProUltraImageNode", "FreSca", "FreeU", "FreeU_V2", "GITSScheduler", "GLIGEN<PERSON><PERSON>der", "GLIGENTextBoxApply", "GetVideoComponents", "GrowMask", "Hunyuan3Dv2Conditioning", "Hunyuan3Dv2ConditioningMultiView", "HunyuanImageToVideo", "HyperTile", "HypernetworkLoader", "IdeogramV1", "IdeogramV2", "IdeogramV3", "ImageAddNoise", "ImageBatch", "ImageBlend", "ImageBlur", "ImageColorToMask", "ImageCompositeMasked", "ImageCrop", "ImageFromBatch", "ImageInvert", "ImageOnlyCheckpointLoader", "ImageOnlyCheckpointSave", "ImagePadForOutpaint", "ImageQuantize", "ImageRGBToYUV", "ImageScale", "ImageScaleBy", "ImageScaleToTotalPixels", "ImageSharpen", "ImageToMask", "ImageUpscaleWithModel", "ImageYUVToRGB", "InpaintModelConditioning", "InstructPixToPixConditioning", "InvertMask", "JoinImageWithAlpha", "K<PERSON><PERSON><PERSON>", "KSamplerAdvanced", "KSamplerSelect", "KarrasScheduler", "KlingCameraControlI2VNode", "KlingCameraControlT2VNode", "KlingCameraControls", "KlingDualCharacterVideoEffectNode", "KlingImage2VideoNode", "KlingImageGenerationNode", "KlingLipSyncAudioToVideoNode", "KlingLipSyncTextToVideoNode", "KlingSingleImageVideoEffectNode", "KlingStartEndFrameNode", "KlingTextToVideoNode", "KlingVideoExtendNode", "KlingVirtualTryOnNode", "LTXVAddGuide", "LTXVConditioning", "LTXVCropGuides", "LTXVImgToVideo", "LTXVPreprocess", "LTXVScheduler", "LaplaceScheduler", "LatentAdd", "LatentApplyOperation", "LatentApplyOperationCFG", "LatentBatch", "LatentBatchSeedBehavior", "LatentBlend", "LatentComposite", "LatentCompositeMasked", "LatentCrop", "LatentFlip", "LatentFromBatch", "LatentInterpolate", "Latent<PERSON><PERSON><PERSON>ly", "LatentOperationSharpen", "LatentOperationTonemapReinhard", "LatentRotate", "LatentSubtract", "LatentUpscale", "LatentUpscaleBy", "Load3D", "Load3DAnimation", "LoadAudio", "LoadImage", "LoadImageMask", "LoadImageOutput", "LoadLBW //Inspire", "LoadLatent", "LoadVideo", "LoraBlockInfo //Inspire", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "LoraLoaderBlockWeight //Inspire", "LoraLoaderModelOnly", "LoraSave", "LotusConditioning", "LumaConceptsNode", "LumaImageModifyNode", "LumaImageNode", "LumaImageToVideoNode", "LumaReferenceNode", "LumaVideoNode", "<PERSON><PERSON>", "MakeLBW //Inspire", "MaskComposite", "MaskPreview", "MaskToImage", "MinimaxImageToVideoNode", "MinimaxSubjectToVideoNode", "MinimaxTextToVideoNode", "ModelComputeDtype", "ModelMergeAdd", "ModelMergeAuraflow", "ModelMergeBlocks", "ModelMergeCosmos14B", "ModelMergeCosmos7B", "ModelMergeFlux1", "ModelMergeLTXV", "ModelMergeMochiPreview", "ModelMergeSD1", "ModelMergeSD2", "ModelMergeSD35_Large", "ModelMergeSD3_2B", "ModelMergeSDXL", "ModelMergeSimple", "ModelMergeSubtract", "ModelMergeWAN2_1", "ModelSamplingAuraFlow", "ModelSamplingContinuousEDM", "ModelSamplingContinuousV", "ModelSamplingDiscrete", "ModelSamplingFlux", "ModelSamplingLTXV", "ModelSamplingSD3", "ModelSamplingStableCascade", "ModelSave", "Morphology", "OpenAIDalle2", "OpenAIDalle3", "OpenAIGPTImage1", "OptimalStepsScheduler", "PatchModelAddDownscale", "PerpNeg", "PerpNegGuider", "PerturbedAttentionGuidance", "PhotoMakerEncode", "PhotoMaker<PERSON><PERSON>der", "PikaImageToVideoNode2_2", "PikaScenesV2_2", "PikaStartEndFrameNode2_2", "PikaTextToVideoNode2_2", "Pikadditions", "Pikaffects", "Pikaswaps", "PixverseImageToVideoNode", "PixverseTemplateNode", "PixverseTextToVideoNode", "PixverseTransitionVideoNode", "PolyexponentialScheduler", "PorterDuffImageComposite", "Preview3D", "Preview3DAnimation", "PreviewAny", "PreviewAudio", "PreviewImage", "PrimitiveBoolean", "PrimitiveFloat", "PrimitiveInt", "PrimitiveString", "PrimitiveStringMultiline", "QuadrupleCLIPLoader", "RandomNoise", "RebatchImages", "RebatchLatents", "RecraftColorRGB", "RecraftControls", "RecraftCreativeUpscaleNode", "RecraftCrispUpscaleNode", "RecraftImageInpaintingNode", "RecraftImageToImageNode", "RecraftRemoveBackgroundNode", "RecraftReplaceBackgroundNode", "RecraftStyleV3DigitalIllustration", "RecraftStyleV3InfiniteStyleLibrary", "RecraftStyleV3LogoRaster", "RecraftStyleV3RealisticImage", "RecraftTextToImageNode", "RecraftTextToVectorNode", "RecraftVectorizeImageNode", "RegexExtract", "RegexMatch", "RenormCFG", "RepeatImageBatch", "RepeatLatentBatch", "RescaleCFG", "SDTurboScheduler", "SD_4XUpscale_Conditioning", "SV3D_Conditioning", "SVD_img2vid_Conditioning", "SamplerCustom", "SamplerCustomAdvanced", "SamplerDPMAdaptative", "SamplerDPMPP_2M_SDE", "SamplerDPMPP_2S_Ancestral", "SamplerDPMPP_3M_SDE", "SamplerDPMPP_SDE", "SamplerEulerAncestral", "SamplerEulerAncestralCFGPP", "SamplerEulerCFGpp", "SamplerLCMUpscale", "SamplerLMS", "SaveAnimatedPNG", "SaveAnimatedWEBP", "SaveAudio", "SaveAudioMP3", "SaveAudioOpus", "SaveGLB", "SaveImage", "SaveImageWebsocket", "SaveLBW //Inspire", "SaveLatent", "SaveSVGNode", "SaveVideo", "SaveWEBM", "SelfAttentionGuidance", "SetFirstSigma", "SetLatentNoiseMask", "SetUnionControlNetType", "SkipLayerGuidanceDiT", "SkipLayerGuidanceSD3", "SolidMask", "SplitImageWithAlpha", "SplitSigmas", "SplitSigmasDenoise", "StabilityStableImageSD_3_5Node", "StabilityStableImageUltraNode", "StabilityUpscaleConservativeNode", "StabilityUpscaleCreativeNode", "StabilityUpscaleFastNode", "StableCascade_EmptyLatentImage", "StableCascade_StageB_Conditioning", "StableCascade_StageC_VAEEncode", "StableCascade_SuperResolutionControlnet", "StableZero123_Conditioning", "StableZero123_Conditioning_Batched", "StringCompare", "StringConcatenate", "StringContains", "<PERSON><PERSON><PERSON><PERSON>", "StringReplace", "StringSubstring", "StringTrim", "StubConstantImage", "StubFloat", "StubImage", "StubInt", "StubMask", "StyleModelApply", "StyleModelLoader", "T5TokenizerOptions", "TestAccumulateNode", "TestAccumulationGetItemNode", "TestAccumulationGetLengthNode", "TestAccumulationHeadNode", "TestAccumulationSetItemNode", "TestAccumulationTailNode", "TestAccumulationToListNode", "TestBoolOperationNode", "TestCustomIsChanged", "TestCustomValidation1", "TestCustomValidation2", "TestCustomValidation3", "TestCustomValidation4", "TestCustomValidation5", "TestDynamicDependencyCycle", "TestExecutionBlocker", "TestFloatConditions", "TestForLoopClose", "TestForLoopOpen", "TestIntConditions", "TestIntMathOperation", "TestIsChangedWithConstants", "TestLazyMixImages", "TestListToAccumulationNode", "TestMakeListNode", "TestMixedExpansionReturns", "TestStringConditions", "TestToBoolNode", "TestVariadicAverage", "TestWhileLoopClose", "TestWhileLoopOpen", "TextEncodeAceStepAudio", "TextEncodeHunyuanVideo_ImageToVideo", "ThresholdMask", "TomePatchModel", "TorchCompileModel", "TrimVideoLatent", "TripleCLIPLoader", "UNETLoader", "UNetCrossAttentionMultiply", "UNetSelfAttentionMultiply", "UNetTemporalAttentionMultiply", "UpscaleModelLoader", "VAEDecode", "VAEDecodeAudio", "VAEDecodeHunyuan3D", "VAEDecodeTiled", "VAEEncode", "VAEEncodeAudio", "VAEEncodeForInpaint", "VAEEncodeTiled", "VAELoader", "VAESave", "VPScheduler", "VeoVideoGenerationNode", "VideoLinearCFGGuidance", "VideoTriangleCFGGuidance", "VoxelToMesh", "VoxelToMeshBasic", "WanCamera<PERSON>dding", "WanCameraImageToVideo", "WanFirstLastFrameToVideo", "WanFunControlToVideo", "WanFunInpaintToVideo", "WanImageToVideo", "WanVaceToVideo", "WebcamCapture", "XY Input: Lora Block Weight //Inspire", "unCLIPCheckpointLoader", "unCLIPConditioning"], {"title_aux": "ComfyUI-DonutDetailer"}], "https://github.com/DrMWeigand/ComfyUI_LineBreakInserter": [["LineBreakInserter"], {"title_aux": "ComfyUI_LineBreakInserter"}], "https://github.com/DraconicDragon/ComfyUI_e621_booru_toolkit": [["GetAnyBooruPostAdv", "GetBooruPost", "TagWikiFetch"], {"title_aux": "ComfyUI e621 booru Toolkit"}], "https://github.com/DreamsInAutumn/ComfyUI-Autumn-LLM-Nodes": [["GeminiImageToPrompt", "GeminiPromptBuilder", "LLMPromptBuilder"], {"title_aux": "ComfyUI-Autumn-LLM-Nodes"}], "https://github.com/Dreamshot-io/ComfyUI-Extend-Resolution": [["ResolutionPadding"], {"title_aux": "ComfyUI-Extend-Resolution"}], "https://github.com/ELiZswe/ComfyUI-ELiZTools": [["ELiZMeshUVWrap"], {"title_aux": "ComfyUI-ELiZTools"}], "https://github.com/EQXai/ComfyUI_EQX": [["CountFaces_EQX", "Extract Filename - EQX", "Extract LORA name - EQX", "FaceDetectOut", "File Image Selector", "Load Prompt From File - EQX", "LoadRetinaFace_EQX", "LoraStackEQX_random", "NSFW Detector EQX", "SaveImage_EQX", "WorkFlow Check"], {"title_aux": "ComfyUI_EQX"}], "https://github.com/Eagle-CN/ComfyUI-Addoor": [["AD_AnyFileList", "AD_BatchImageLoadFromDir", "AD_CSVPromptStyler", "AD_CSVReader", "AD_CSVTranslator", "AD_DeleteLocalAny", "AD_FluxTrainStepMath", "AD_HFDownload", "AD_ImageDrawRectangleSimple", "AD_ImageIndexer", "AD_ImageSaver", "AD_LoadImageAdvanced", "AD_PromptReplace", "AD_TextIndexer", "AD_TextListToString", "AD_TextSaver", "AD_TxtToCSVCombiner", "AD_ZipSave", "AD_advanced-padding", "AD_color-image", "AD_image-concat", "AD_image-resize", "AD_mockup-maker", "AD_poster-maker", "AD_prompt-saver", "ImageCaptioner", "ImageResize", "Incrementer 🪴", "TextAppendNode", "Width and height for scaling image to ideal resolution 🪴", "Width and height from aspect ratio 🪴", "YANC.MultilineString", "comfyui-easy-padding", "image concat mask"], {"author": "ComfyUI Addoor", "description": "Save prompts to CSV file with customizable naming pattern", "title": "ComfyUI-PromptSaver", "title_aux": "ComfyUI-Addoor [UNSAFE]"}], "https://github.com/Elawphant/ComfyUI-MusicGen": [["AudioLoader", "MusicGen"], {"title_aux": "ComfyUI-MusicGen [WIP]"}], "https://github.com/Elypha/ComfyUI-Prompt-Helper": [["PromptHelper_CombineConditioning", "PromptHelper_ConcatConditioning", "PromptHelper_ConcatString", "PromptHelper_EncodeMultiStringCombine", "PromptHelper_FormatString", "PromptHelper_LoadPreset", "PromptHelper_LoadPresetAdvanced", "PromptHelper_String", "PromptHelper_StringMultiLine", "PromptHelper_WeightedPrompt"], {"title_aux": "ComfyUI-Prompt-Helper [WIP]"}], "https://github.com/EmanueleUniroma2/ComfyUI-FLAC-to-WAV": [["AudioToWavConverter"], {"title_aux": "ComfyUI-FLAC-to-WAV [WIP]"}], "https://github.com/EmilioPlumed/ComfyUI-Math": [["GreatestCommonDenominator", "LowestCommonMultiple"], {"title_aux": "ComfyUI-Math [WIP]"}], "https://github.com/EricRollei/Comfy-Metadata-System": [["EnhancedMetadataFilterNode_V2", "<PERSON><PERSON><PERSON><PERSON>_Image_Finder_v021", "Eric_Image_Sorter_V13", "<PERSON>_Keyword_Sorter_V6", "<PERSON><PERSON><PERSON><PERSON><PERSON>_Debugger_V2", "<PERSON><PERSON><PERSON><PERSON><PERSON>_Entry_V2", "<PERSON>_<PERSON><PERSON><PERSON>_Filter_V2", "<PERSON><PERSON><PERSON><PERSON><PERSON>_Query_V3", "MetadataAwareSaveImage_v099", "MetadataConsolidatorNode_V2", "PngInfoDiagnosticV3", "PngMetadataExtractorV3", "TextOverlayNode_v04"], {"title_aux": "Comfy-Metadata-System [WIP]"}], "https://github.com/ExponentialML/ComfyUI_LiveDirector": [["LiveDirector"], {"title_aux": "ComfyUI_LiveDirector (WIP)"}], "https://github.com/Extraltodeus/Conditioning-token-experiments-for-ComfyUI": [["Automatic wildcards", "Conditioning (Cosine similarities)", "Conditioning (Maximum absolute)", "Conditioning (Maximum absolute) text inputs", "Conditioning (Scale by absolute sum)", "Conditioning merge clip g/l", "Conditioning similar tokens recombine", "Conditioning to text", "Quick and dirty text encode", "encode_all_tokens_SDXL"], {"title_aux": "Conditioning-token-experiments-for-ComfyUI"}], "https://github.com/FaberVS/MultiModel": [["ActiveModel", "DenoiseSelector", "KSamplerPipe", "ListSelector", "ModelParamsPipe", "MySwitchIndex", "ParamsPipeUnpack", "PromptBuilder"], {"title_aux": "MultiModel"}], "https://github.com/Fannovel16/ComfyUI-AppIO": [["AppIO_FitResizeImage", "AppIO_ImageInput", "AppIO_ImageInputFromID", "AppIO_ImageOutput", "AppIO_IntegerInput", "AppIO_ResizeInstanceAndPaste", "AppIO_ResizeInstanceImageMask", "AppIO_StringInput", "AppIO_StringOutput"], {"title_aux": "ComfyUI-AppIO"}], "https://github.com/FinetunersAI/comfyui-fast-group-link": [["FastGroupLink"], {"title_aux": "Fast Group Link [WIP]"}], "https://github.com/FinetunersAI/finetuners": [["AutoImageResize", "GroupLink", "VariablesInjector"], {"title_aux": "ComfyUI Finetuners [WIP]"}], "https://github.com/Fucci-Mateo/ComfyUI-Airtable": [["Push pose to Airtable"], {"title_aux": "ComfyUI-Airtable [WIP]"}], "https://github.com/GalactusX31/ComfyUI-FileBrowserAPI": [["PathSelectorNode"], {"title_aux": "ComfyUI-FileBrowserAPI [UNSAFE]"}], "https://github.com/GentlemanHu/ComfyUI-Notifier": [["GentlemanHu_Notifier"], {"title_aux": "ComfyUI-Notifier"}], "https://github.com/George0726/ComfyUI-video-accessory": [["VideoAcc_CameraTrajectoryAdvance", "VideoAcc_CameraTrajectoryRecam", "VideoAcc_ImageResizeAdvanced", "VideoAcc_ImageUpscaleVideo", "VideoAcc_LoadImage", "VideoAcc_LoadVideo", "VideoAcc_SaveMP4", "VideoAcc_imageSize"], {"title_aux": "ComfyUI-video-accessory [WIP]"}], "https://github.com/Grant-CP/ComfyUI-LivePortraitKJ-MPS": [["DownloadAndLoadLivePortraitModels", "LivePortraitProcess"], {"title_aux": "ComfyUI-LivePortraitKJ-MPS"}], "https://github.com/Grey3016/Save2Icon": [["ConvertToIconNode"], {"title_aux": "Save2Icon"}], "https://github.com/GrindHouse66/ComfyUI-GH_Tools": [["GHImg_Sizer", "GHSimple_Scale"], {"title_aux": "GH Tools for ComfyUI"}], "https://github.com/Hapseleg/ComfyUI-This-n-That": [["Show Prompt (<PERSON><PERSON>)", "Show Prompt TnT", "Simple Ratio Selector (Hapse)", "Simple Ratio Selector TnT", "Simple Seed Selector TnT"], {"title_aux": "This n that (<PERSON><PERSON>)"}], "https://github.com/HuangYuChuh/ComfyUI-LLMs-Toolkit": [["DeepSeekImageAnalyst", "DeepSeekImageGeneration", "DeepSeekModelLoader", "ImagePreprocessor", "<PERSON><PERSON>_Loader", "OpenAICompatibleLoader", "VideoFileUploader"], {"title_aux": "ComfyUI-LLMs-Toolkit [WIP]"}], "https://github.com/IfnotFr/ComfyUI-Ifnot-Pack": [["<PERSON>", "Face Crop Mouth", "<PERSON>"], {"title_aux": "ComfyUI-Ifnot-Pack"}], "https://github.com/IgPoly/ComfyUI-igTools": [["IGT_SimpleTilesCalc"], {"title_aux": "ComfyUI-igTools"}], "https://github.com/IsItDanOrAi/ComfyUI-exLoadout": [["dropdowns", "exCheckpoint<PERSON><PERSON>der", "exLoadoutEditCell", "exLoadoutReadColumn", "exSeg", "exSeg2"], {"title_aux": "ComfyUI-exLoadout [WIP]"}], "https://github.com/IuvenisSapiens/ComfyUI_MiniCPM-V-2_6-int4": [["DisplayText", "LoadVideo", "MiniCPM_VQA", "MiniCPM_VQA_Polished", "MultipleImagesInput", "PreviewVideo"], {"title_aux": "ComfyUI_MiniCPM-V-2_6-int4"}], "https://github.com/IvanZhd/comfyui-codeformer": [["RedBeanie_CustomImageInverter"], {"title_aux": "comfyui-codeformer [WIP]"}], "https://github.com/Jaxkr/comfyui-terminal-command": [["Terminal"], {"title_aux": "comfyui-terminal-command [UNSAFE]"}], "https://github.com/Jiffies-64/ComfyUI-SaveImagePlus": [["SaveImagePlus"], {"title_aux": "ComfyUI-SaveImagePlus"}], "https://github.com/Jingwen-genies/comfyui-genies-nodes": [["GeniesPoseEstimation", "GeniesRGBToHSV", "GeniesScaleFaceByKeypoints", "GeniesSelectRGBByMask"], {"title_aux": "comfyui-genies-nodes"}], "https://github.com/JioJe/comfyui_video_BC": [["VideoCombine", "VideoSequenceProcessor"], {"title_aux": "comfyui_video_BC [WIP]"}], "https://github.com/JissiChoi/ComfyUI-Jissi-List": [["JissiFloatList", "JissiList", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "JissiMultiplePrompts", "JissiText", "JissiTextFileToListDisplay", "JissiTextTemplate", "<PERSON><PERSON><PERSON><PERSON><PERSON>"], {"title_aux": "ComfyUI-Jissi-List [WIP]"}], "https://github.com/Jordach/comfy-consistency-vae": [["Comfy_ConsistencyVAE"], {"title_aux": "comfy-consistency-vae"}], "https://github.com/Junst/ComfyUI-PNG2SVG2PNG": [["PNG2SVG2PNG"], {"title_aux": "ComfyUI-PNG2SVG2PNG"}], "https://github.com/KERRY-YUAN/ComfyUI_Python_Executor": [["NodePython"], {"title_aux": "Python_Executor [UNSAFE]"}], "https://github.com/Kayarte/Time-Series-Nodes-for-ComfyUI": [["DomainTimeSeriesPrep", "TimeSeriesLoader", "TimeSeriesPredictor"], {"title_aux": "Time Series Nodes for ComfyUI [Experimental]"}], "https://github.com/KihongK/comfyui-roysnodes": [["CLIPMultiTextEncode", "Create_ConditionLoRA", "Create_ConditionLoRA_MainPrompt", "Load_ConditionLoRA", "OpenAI_Summarize", "Unzip_ConditionLoRA"], {"title_aux": "ComfyUI-RoysNodes [WIP]"}], "https://github.com/KoreTeknology/ComfyUI-Nai-Production-Nodes-Pack": [["Brightness Image", "ColorMatch2", "Contrast Image", "Get Text", "Image Difference", "ImageConcatenate", "ImageDesaturate", "ImageExtend", "ImageFlip", "ImageRotate", "LoadImageNai", "Math Operation", "NoteAdvanced", "Set Text"], {"title_aux": "ComfyUI Production Nodes Pack [WIP]"}], "https://github.com/Krish-701/RK_Comfyui": [["RK_Accumulate_Text_Multiline", "RK_Accumulate_Text_Multiline_Numbered", "R<PERSON>_Advanced_Script_Finder", "RK_CSV_File_State_Looper_v01", "RK_CSV_File_State_Looper_v02", "RK_Calc", "RK_Concatenate_Text", "RK_Excel_File_State_Looper", "RK_ImageViewer", "RK_Read_Excel_Row", "RK_Write_Text", "RK_seed", "rk_save_image", "rk_save_image_v01"], {"title_aux": "R<PERSON><PERSON>m<PERSON>ui"}], "https://github.com/KurtHokke/ComfyUI_KurtHokke_Nodes": [["<PERSON><PERSON>_Tu<PERSON>_<PERSON><PERSON>", "ApplyCondsExtraOpts", "BashScriptNode", "BooleanFromPipe", "BooleanToPipe", "COND_ExtraOpts", "COND_ExtraOpts_2", "COND_SET_STRENGTH_ExtraOpts", "ChainTextEncode", "CkptPipe", "CompareTorch", "DynamicThresholding", "DynamicThresholdingBasic", "EmptyLatentSize", "EmptyLatentSize64", "ExecutePythonNode", "ExpMath", "ExpMathDual", "ExpMathQuad", "InspectNode", "LoadUnetAndClip", "LoraFluxParams", "MergeExtraOpts", "ModelPipe1", "ModelPipe2", "NoModel_CkptLoader", "NoNegExtraOpts", "Node_BOOL", "Node_Float", "Node_INT", "Node_RandomRange", "Node_String", "Node_StringMultiline", "SEED_ExtraOpts", "SamplerCustomAdvanced_Pipe", "SamplerSel", "SchedulerSel", "SedOnString", "UnetClipLoraLoader", "UnetClipLoraLoaderBasic", "VAE_ExtraOpts", "ViewExtraOpts", "batchsize_ExtraOpts", "get_lora_metadata", "mycombine", "re_sub_str", "splitcond", "str_str", "str_str_str_str"], {"title_aux": "ComfyUI-VLMStudio"}], "https://github.com/LLMCoder2023/ComfyUI-LLMCoder2023Nodes": [["DisplayLoraTriggersNode", "LoraAndTriggerWordsLoader", "MulticlipPromptCombinator", "TemplateInterpolation", "Variable", "WeightedAttributesFormatter"], {"title_aux": "ComfyUI-LLMCoderNodes"}], "https://github.com/LZpenguin/ComfyUI-Text": [["Add_text_by_mask"], {"title_aux": "ComfyUI-Text"}], "https://github.com/LarryJane491/ComfyUI-ModelUnloader": [["Model Unloader"], {"title_aux": "ComfyUI-ModelUnloader"}], "https://github.com/Laser-one/ComfyUI-align-pose": [["Align_Pose"], {"title_aux": "ComfyUI-align-pose"}], "https://github.com/Lilien86/Comfyui_Latent_Interpolation": [["Latent Interpolator Multi"], {"title_aux": "Comfyui_Latent_Interpolation [WIP]"}], "https://github.com/Linsoo/ComfyUI-Linsoo-Custom-Nodes": [["LinsooEmptyLatentImage", "LinsooLoadImage", "LinsooMultiInputs", "LinsooMultiOutputs", "LinsooSaveImage"], {"title_aux": "ComfyUI-Linsoo-Custom-Nodes"}], "https://github.com/Looking-Glass/LKG-ComfyUI": [["BridgePreview", "LoadFolder", "ScaleAndMaintainAspect", "SideBySide"], {"title_aux": "LKG-ComfyUI"}], "https://github.com/LotzF/ComfyUI-Simple-Chat-GPT-completion": [["AzureChatGptCompletion", "ChatGPTCompletion"], {"title_aux": "ComfyUI simple ChatGPT completion [UNSAFE]"}], "https://github.com/LucianGnn/ComfyUI-Lucian": [["AudioDurationCalculator"], {"title_aux": "ComfyUI-Lucian [WIP]"}], "https://github.com/LyazS/ComfyUI-aznodes": [["CrossFadeImageSequence", "ImageGrayscaleAZ", "SaveImageAZ"], {"title_aux": "ComfyUI-aznodes"}], "https://github.com/LykosAI/ComfyUI-Inference-Core-Nodes": [["AIO_Preprocessor", "AnimalPosePreprocessor", "AnimeFace_SemSegPreprocessor", "AnimeLineArtPreprocessor", "AnyLineArtPreprocessor_aux", "BAE-NormalMapPreprocessor", "BinaryPreprocessor", "CannyEdgePreprocessor", "ColorPreprocessor", "ControlNetAuxSimpleAddText", "ControlNetPreprocessorSelector", "DSINE-NormalMapPreprocessor", "DWPreprocessor", "DensePosePreprocessor", "DepthAnythingPreprocessor", "DepthAnythingV2Preprocessor", "DiffusionEdge_Preprocessor", "ExecuteAllControlNetPreprocessors", "FacialPartColoringFromPoseKps", "FakeScribblePreprocessor", "HEDPreprocessor", "HintImageEnchance", "ImageGenResolutionFromImage", "ImageGenResolutionFromLatent", "ImageIntensityDetector", "ImageLuminanceDetector", "InpaintPreprocessor", "LayeredDiffusionApply", "LayeredDiffusionCondApply", "LayeredDiffusionCondJointApply", "LayeredDiffusionDecode", "LayeredDiffusionDecodeRGBA", "LayeredDiffusionDecodeSplit", "LayeredDiffusionDiffApply", "LayeredDiffusionJointApply", "LeReS-DepthMapPreprocessor", "LineArtPreprocessor", "LineartStandardPreprocessor", "M-LSDPreprocessor", "Manga2Anime_LineArt_Preprocessor", "MaskOptFlow", "MediaPipe-FaceMeshPreprocessor", "MeshGraphormer+ImpactDetector-DepthMapPreprocessor", "MeshGraphormer-DepthMapPreprocessor", "Metric3D-DepthMapPreprocessor", "Metric3D-NormalMapPreprocessor", "Metric_DepthAnythingV2Preprocessor", "MiDaS-DepthMapPreprocessor", "MiDaS-NormalMapPreprocessor", "ModelMergeBlockNumber", "ModelMergeSDXL", "ModelMergeSDXLDetailedTransformers", "ModelMergeSDXLTransformers", "ModelSamplerTonemapNoiseTest", "OneFormer-ADE20K-SemSegPreprocessor", "OneFormer-COCO-SemSegPreprocessor", "OpenposePreprocessor", "PiDiNetPreprocessor", "PixelPerfectResolution", "PromptExpansion", "PyraCannyPreprocessor", "Reference<PERSON>n<PERSON><PERSON><PERSON><PERSON>", "RenderAnimalKps", "RenderPeopleKps", "RescaleClassifierFreeGuidanceTest", "SAMPreprocessor", "SavePoseKpsAsJsonFile", "ScribblePreprocessor", "Scribble_PiDiNet_Preprocessor", "Scribble_XDoG_Preprocessor", "SemSegPreprocessor", "ShufflePreprocessor", "TEEDPreprocessor", "TTPlanet_TileGF_Preprocessor", "TTPlanet_TileSimple_Preprocessor", "TilePreprocessor", "TonemapNoiseWithRescaleCFG", "UniFormer-SemSegPreprocessor", "Unimatch_OptFlowPreprocessor", "UpperBodyTrackingFromPoseKps", "Zoe-DepthMapPreprocessor", "Zoe_DepthAnythingPreprocessor"], {"author": "tstandley", "title_aux": "ComfyUI Nodes for Inference.Core"}], "https://github.com/M4lF3s/comfy-tif-support": [["Load TIFF", "Save TIFF"], {"title_aux": "comfy-tif-support"}], "https://github.com/MakkiShizu/ComfyUI-MakkiTools": [["AnyImageStitch", "AnyImagetoConditioning_flux_kontext", "AutoLoop_create_pseudo_loop_video", "Environment_INFO", "GetImageNthCount", "ImageChannelSeparate", "ImageCountConcatenate", "ImageHeigthStitch", "ImageWidthStitch", "MergeImageChannels", "random_any", "translator_m2m100", "translators"], {"title_aux": "ComfyUI-MakkiTools"}], "https://github.com/Malloc-pix/comfyui-QwenVL": [["Qwen2.5", "Qwen2.5VL"], {"title_aux": "comfyui-QwenVL"}], "https://github.com/ManuShamil/ComfyUI_BodyEstimation_Nodes": [["CogitareLabsPoseIDExtractor"], {"title_aux": "ComfyUI_BodyEstimation_Nodes"}], "https://github.com/Matrix-King-Studio/ComfyUI-MoviePy": [["AudioDurationNode", "ImageClipNode", "SaveVideoNode"], {"title_aux": "ComfyUI-MoviePy"}], "https://github.com/Maxim-Dey/ComfyUI-MaksiTools": [["🔢 Return Boolean", "🔢 Return Float", "🔢 Return Integer", "🔢 Return Multiline String", "🔧 Time Measure Node"], {"title_aux": "ComfyUI-MS_Tools [WIP]"}], "https://github.com/Mervent/comfyui-telegram-send": [["TelegramReply", "TelegramSend"], {"title_aux": "comfyui-telegram-send"}], "https://github.com/Mervent/comfyui-yaml-prompt": [["<PERSON><PERSON><PERSON><PERSON>pt<PERSON><PERSON><PERSON>"], {"title_aux": "comfyui-yaml-prompt"}], "https://github.com/MickeyJ/ComfyUI_mickster_nodes": [["Image Size Scaled", "ImageSwitchSelect"], {"title_aux": "ComfyUI_mickster_nodes [WIP]"}], "https://github.com/MockbaTheBorg/ComfyUI-Mockba": [["mb Barcode", "mb CLIP Text Encoder", "mb Debug", "mb <PERSON><PERSON><PERSON>", "mb Empty Latent Image", "mb <PERSON><PERSON>", "mb Exec", "mb File to Image", "mb File to Text", "mb <PERSON>h Generator", "mb <PERSON>ch", "mb Image Dimensions", "mb <PERSON>ther", "mb Image Flip", "mb Image Load", "mb <PERSON> Load from URL", "mb Image Preview", "mb Image Rotate", "mb <PERSON> Size", "mb Image Subtract", "mb Image to File", "mb <PERSON><PERSON><PERSON><PERSON>", "mb Select", "mb String", "mb Text", "mb Text or File", "mb Text to File", "mb Textbox"], {"title_aux": "ComfyUI-Mockba"}], "https://github.com/MrAdamBlack/CheckProgress": [["CHECK_PROGRESS"], {"title_aux": "CheckProgress [WIP]"}], "https://github.com/MuAIGC/ComfyUI-DMXAPI_mmx": [["DMXAPIClient", "ImageEdit", "ImageMerge", "PreviewImageFromUrl", "TextToImage"], {"title_aux": "DMXAPI Nodes [WIP]"}], "https://github.com/MythicalChu/ComfyUI-APG_ImYourCFGNow": [["APG_ImYourCFGNow"], {"title_aux": "ComfyUI-APG_ImYourCFGNow"}], "https://github.com/NEZHA625/ComfyUI-tools-by-dong": [["A1111_FLUX_DATA_NODE", "AudioDurationNode", "AudioPathToAudioNode", "CategorizeNode", "CountFilesFromFolderNode", "Data_handle_Node", "DeepSeek_Node", "Delay_node", "Delete_folder_Node", "DongShowTextNode", "Dong_Pixelate_Node", "Dong_Text_Node", "DownloadNode", "Downloader", "FileMoveNode", "FolderIteratorNODE", "GetImageListFromFloderNode", "Get_cookies_Node", "Get_json_value_Node", "Get_video_Node", "HashCalculationsNode", "HuggingFaceUploadNode", "IMG2URLNode", "INTNODE", "Image2GIFNode", "ImageDownloader", "ImageResizeNode", "LibLib_upload_Node", "LogicToolsNode", "LoraIterator", "Notice_Node", "PromptConcatNode", "RandomNumbersNode", "RenameNode", "ResolutionNode", "SaveTXTNode", "SetAppidNode", "TextToJsonNode", "TranslateAPINode", "Wan21_get_Node", "Wan21_post_Node", "ZIPwith7zNode", "bailian_model_select_Node", "cogvideox_flash_get_Node", "cogvideox_flash_post_Node", "cogview_3_flash_Node", "douyin_remove_watermark_Node", "file_analysis_Node", "find_files_by_extension_Node", "get_video_from_url_Node", "img2url_v2_Node", "img_understanding_Node", "klingai_video_Node", "path_join_Node", "save_img_NODE", "set_api_Node", "text_replace_node"], {"title_aux": "ComfyUI-tools-by-dong [UNSAFE]"}], "https://github.com/Nambi24/ComfyUI-Save_Image": [["ExtractLastPathComponent", "ListSubfoldersNode", "SaveImageNode"], {"title_aux": "ComfyUI-Save_Image"}], "https://github.com/No-22-Github/ComfyUI_SaveImageCustom": [["SaveUtility: SaveImageCustom"], {"title_aux": "ComfyUI_SaveImageCustom"}], "https://github.com/Northerner1/ComfyUI_North_Noise": [["North_Noise"], {"title_aux": "ComfyUI_North_Noise [WIP]"}], "https://github.com/Novavision0313/ComfyUI-NVVS": [["AllBlackMaskValidator", "DirectionSelector", "FullBodyDetection", "HighlightIndexSelector", "MaskCoverageAnalysis", "StringSplit", "StringStrip"], {"title_aux": "ComfyUI-NVVS [WIP]"}], "https://github.com/OSAnimate/ComfyUI-SpriteSheetMaker": [["SpriteSheetMaker"], {"title_aux": "ComfyUI-SpriteSheetMaker [WIP]"}], "https://github.com/Oct7/ComfyUI-LaplaMask": [["BlurMask"], {"title_aux": "ComfyUI-LaplaMask"}], "https://github.com/PATATAJEC/Patatajec-Nodes": [["FilePrefixSwitcher", "HyvidSwitcher", "ImageSequenceFromBatch", "MidiReader", "MidiToFrameSequences", "VideoCounter", "VideoSequencer"], {"title_aux": "Patatajec-Nodes [WIP]"}], "https://github.com/Pablerdo/ComfyUI-Sa2VAWrapper": [["GetCaptionFromImages"], {"title_aux": "ComfyUI-Sa2VAWrapper [WIP]"}], "https://github.com/PabloGrant/comfyui-giraffe-test-panel": [["DebugHelloNode", "GiraffeTestPanel"], {"title_aux": "comfyui-giraffe-test-panel"}], "https://github.com/PeterMikhai/Doom_Flux_NodePack": [["DoomFluxInpaintSampler", "DoomFluxLoader", "DoomFluxSampler"], {"title_aux": "DoomFLUX Nodes [WIP]"}], "https://github.com/Poseidon-fan/ComfyUI-fileCleaner": [["Clean input and output file"], {"title_aux": "ComfyUI-fileCleaner [UNSAFE]"}], "https://github.com/Poukpalaova/ComfyUI-FRED-Nodes": [["FRED_AutoCropImage_Native_Ratio_v5", "FRED_AutoCropImage_SDXL_Ratio_V3", "FRED_AutoCropImage_SDXL_Ratio_V4", "FRED_AutoImageTile_from_Mask_v1", "FRED_CropFace", "FRED_FolderSelector", "FRED_<PERSON><PERSON><PERSON>er_Dress", "FRED_ImageBrowser_Eyes_Color", "FRED_ImageBrowser_Generic", "FRED_ImageBrowser_Hair_Color", "FRED_<PERSON>Browser_Hair_Style", "FRED_ImageBrowser_Top", "FRED_JoinImages", "FRED_LoadImage_V2", "FRED_LoadImage_V3", "FRED_LoadImage_V4", "FRED_LoadImage_V5", "FRED_LoadImage_V6", "FRED_LoadPathImagesPreview", "FRED_LoadPathImagesPreview_v2", "FRED_LoadRetinaFace", "FRED_LoraInfos", "FRED_PreviewOnly", "FRED_TextMultiline", "FRED_Text_to_XMP", "FRED_photo_prompt"], {"title_aux": "ComfyUI-FRED-Nodes [WIP]"}], "https://github.com/QingLuanWithoutHeart/comfyui-file-image-utils": [["FileManagerV2", "IfTextEquals", "LoadImageFromPath"], {"title_aux": "ComfyUI File/Image Utils Nodes [UNSAFE]"}], "https://github.com/Quasimondo/ComfyUI-QuasimondoNodes": [["CPPN Generator", "Color Match", "Coordinates From Mask", "Custom Shader", "Distance Map", "Folder Queue Manager", "Image Blend by Mask (Batch)", "Image Noise Generator", "Image to Optical Flow", "Perlin Noise Generator", "Preview Mask", "Random Image Generator", "Shift Mask", "<PERSON><PERSON>", "Spring Mesh", "Temporal Blur", "Video Queue Manager"], {"title_aux": "ComfyUI-QuasimondoNodes [WIP]"}], "https://github.com/RLW-Chars/comfyui-promptbymood": [["Prompt By <PERSON><PERSON>"], {"title_aux": "comfyui-promptbymood [WIP]"}], "https://github.com/RUFFY-369/ComfyUI-FeatureBank": [["FeatureBankAttentionProcessor"], {"title_aux": "ComfyUI-FeatureBank"}], "https://github.com/Raidez/comfyui-kuniklo-collection": [["ApplySVG2Image", "Properties"], {"title_aux": "Kuniklo Collection"}], "https://github.com/RedmondAI/comfyui-tools": [["AutoName"], {"title_aux": "comfyui-tools [UNSAFE/NAME CONFLICT]"}], "https://github.com/RicherdLee/comfyui-oss-image-save": [["SaveImageOSS"], {"title_aux": "comfyui-oss-image-save [WIP]"}], "https://github.com/RobeSantoro/ComfyUI-RobeNodes": [["AudioWeights to FadeMask 🐤", "Boolean Primitive 🐤", "Image Input Switch 🐤", "Indices Generator 🐤", "List Image Path 🐤", "List Model Path 🐤", "List Video Path 🐤", "Peaks Weights Generator 🐤"], {"title_aux": "Comfy UI Robe Nodes [UNSAFE]"}], "https://github.com/RoyKillington/miscomfy-nodes": [["VeniceUpscale"], {"title_aux": "Miscomfy Nodes [WIP]"}], "https://github.com/SKBv0/ComfyUI-RetroEngine": [["RetroEngineNode"], {"title_aux": "Retro Engine Node for ComfyUI"}], "https://github.com/SS-snap/ComfyUI-Snap_Processing": [["AreaCalculator", "PyQtCanvasNode", "Snapload"], {"title_aux": "Snap Processing for Comfyui"}], "https://github.com/SS-snap/Comfyui_SSsnap_pose-Remapping": [["ApplyPoseDiff", "CalcScaledPoseDiff", "PoseDiffCalculator"], {"title_aux": "Com<PERSON>ui_SSsnap_pose-Remapping"}], "https://github.com/SXQBW/ComfyUI-Qwen3": [["QwenVisionParser"], {"title_aux": "ComfyUI-Qwen-VLM [WIP]"}], "https://github.com/SadaleNet/ComfyUI-Prompt-To-Prompt": [["CLIPTextEncodePromptToPrompt", "KSamplerPromptToPrompt", "KSamplerPromptToPromptAttentionMapLogger", "LocalBlendLayerPresetPromptToPrompt"], {"title_aux": "ComfyUI Port for Google's Prompt-to-Prompt"}], "https://github.com/Sai-ComfyUI/ComfyUI-MS-Nodes": [["FloatMath", "MS_Boolean", "MS_Float", "MS_GenerateSeed", "MS_NP_Vector3", "PowerFractalCrossHatchNode", "PowerFractalNoiseNode", "VectorMath"], {"title_aux": "ComfyUI-MS-Nodes [WIP]"}], "https://github.com/Sakura-nee/ComfyUI_Save2Discord": [["SendToWebhook"], {"title_aux": "ComfyUI_Save2Discord"}], "https://github.com/SanDiegoDude/ComfyUI-HiDream-Sampler": [["HiDreamImg2Img", "HiDreamResolutionSelect", "HiDreamSampler", "HiDreamSamplerAdvanced"], {"title_aux": "HiDreamSampler for ComfyUI [WIP]"}], "https://github.com/Scaryplasmon/ComfTrellis": [["LoadTrellisModel", "RembgSquare", "SaveGLBFile", "TrellisGrid", "TrellisInference"], {"title_aux": "<PERSON>mf<PERSON><PERSON><PERSON> [WIP]"}], "https://github.com/SeedV/ComfyUI-SeedV-Nodes": [["ALL_Model_UnLoader(SEEDV)", "AdvancedScript", "CheckpointLoaderSimpleShared //SeedV", "ControlNetLoaderAdvancedShared", "LoraLoader //SeedV", "<PERSON><PERSON><PERSON>", "Switch_Any(SEEDV)", "TCD_Sampler(SEEDV)", "nunchakuLoraAdapter(SEEDV)"], {"title_aux": "ComfyUI-SeedV-Nodes [UNSAFE]"}], "https://github.com/Sephrael/comfyui_caption-around-image": [["CaptionAroundImageSmart", "PrintPromptValues", "PromptProvider", "TextboxReferenceNode"], {"title_aux": "comfyui_caption-around-image"}], "https://github.com/ShahFaisalWani/ComfyUI-Mojen-Nodeset": [["MojenAnalyzeProcessor", "MojenAspectRatio", "MojenImageL<PERSON>der", "MojenNSFWClassifier", "MojenNSFWClassifierSave", "<PERSON><PERSON><PERSON><PERSON>", "MojenStyleExtractor", "MojenTagProcessor", "MojenTransparentBg"], {"title_aux": "ComfyUI-Mojen-Nodeset"}], "https://github.com/Shinsplat/ComfyUI-Shinsplat": [["Clip Text Encode (Shinsplat)", "Clip Text Encode ALT (Shinsplat)", "Clip Text Encode SD3 (Shinsplat)", "Clip Text Encode SDXL (Shinsplat)", "Clip Text Encode T5 (Shinsplat)", "<PERSON>lip <PERSON> Encode (Shinsplat)", "Green Box (Shinsplat)", "<PERSON><PERSON> (Shinsplat)", "KSampler (Shinsplat)", "<PERSON><PERSON> (Shinsplat)", "Nupoma (Shinsplat)", "Seed (Shinsplat)", "Shinsplat_CLIPTextEncodeFlux", "String Interpolated (Shinsplat)", "<PERSON><PERSON> (Shinsplat)", "Tensor Toys (Shinsplat)", "Test Node (Shinsplat)", "Text To Tokens (Shinsplat)", "Text To Tokens SD3 (Shinsplat)", "Variables (Shinsplat)"], {"author": "Shinsplat", "description": "", "nickname": "shin<PERSON><PERSON>", "title": "Shinsplat", "title_aux": "ComfyUI-Shinsplat [UNSAFE]"}], "https://github.com/ShmuelRonen/ComfyUI-FreeMemory": [["FreeMemoryCLIP", "FreeMemoryImage", "FreeMemoryLatent", "FreeMemoryModel", "FreeMemoryString"], {"title_aux": "ComfyUI-FreeMemory"}], "https://github.com/Simlym/comfyui-prompt-helper": [["PromptProcessor"], {"title_aux": "Simlym/comfyui-prompt-helper [WIP]"}], "https://github.com/Slix-M-Lestragg/comfyui-enhanced": [["Range Iterator"], {"title_aux": "comfyui-enhanced [WIP]"}], "https://github.com/SoftMeng/ComfyUI-PIL": [["PIL Effects (Mexx)", "PIL Merge Image (Mexx)", "PIL Remove Black Dots (Mexx)", "PIL TITLE (Mexx)"], {"title_aux": "ComfyUI-PIL"}], "https://github.com/Solankimayursinh/PMSnodes": [["InputAnalyzer", "LoadBase64Audio", "LoadImageBase64", "LoadMaskBase64", "PMSLoadText", "PM<PERSON><PERSON><PERSON><PERSON><PERSON>", "PMSSendImage"], {"title_aux": "PMSnodes [WIP]"}], "https://github.com/Sophylax/ComfyUI-ReferenceMerge": [["InpaintRegionRestitcher", "ReferenceInpaintComposite"], {"title_aux": "ComfyUI-ReferenceMerge"}], "https://github.com/Soppatorsk/comfyui_img_to_ascii": [["Img_to_ASCII"], {"title_aux": "comfyui_img_to_ascii [WIP]"}], "https://github.com/SpaceWarpStudio/ComfyUI_Remaker_FaceSwap": [["RemakerFaceSwap"], {"title_aux": "ComfyUI_Remaker_FaceSwap"}], "https://github.com/Stable-X/ComfyUI-Hi3DGen": [["DifferenceExtractorNode", "DownloadAndLoadStableXModel", "IF_TrellisCheckpointLoader", "IF_TrellisImageTo3D", "StableXProcessImage"], {"title_aux": "ComfyUI-Hi3DGen"}], "https://github.com/StableDiffusionVN/SDVN_Comfy_node": [["SDVM Image List Repeat", "SDVN API chatbot", "SDVN Any From List", "SDVN Any Input Type", "SDVN Any List", "SDVN Any Repeat", "SDVN Any Show", "SDVN AnyDownload List", "SDVN Apply Style Model", "SDVN Auto Generate", "SDVN AutoSwitch", "SDVN Boolean", "SDVN CLIP Download", "SDVN CLIP Text Encode", "SDVN CLIPVision Download", "SDVN Checkpoint Download", "SDVN Checkpoint Download List", "SDVN ControlNet Download", "SDVN Controlnet Apply", "SDVN Crop By Ratio", "SDVN DALL-E Generate Image", "SDVN Dall-E Generate Image 2", "SDVN Dic Convert", "SDVN DualCLIP Download", "SDVN Easy IPAdapter weight", "SDVN Exif check", "SDVN Fill Background", "SDVN Filter List", "SDVN Flip Image", "SDVN GPT Image", "SDVN Gemini Flash 2 Image", "SDVN Google Imagen", "SDVN IC Lora Layout", "SDVN IC Lora Layout Crop", "SDVN IC-Light v2", "SDVN IPAdapterModel Download", "SDVN Image Adjust", "SDVN Image Film Grain", "SDVN Image HSL", "SDVN Image Info", "SDVN Image Layout", "SDVN Image Repeat", "SDVN Image Scraper", "SDVN Image Size", "SDVN Image White Balance", "SDVN Inpaint", "SDVN Inpaint Crop", "SDVN InstantIDModel Download", "SDVN Join <PERSON>", "SDVN Joy Caption", "SDVN KSampler", "SDVN Load Checkpoint", "SDVN Load Google Sheet", "SDVN Load Image", "SDVN Load Image Folder", "SDVN Load Image From List", "SDVN Load Image Ultimate", "SDVN Load Image Url", "SDVN Load Lora", "SDVN Load Model", "SDVN Load Text", "SDVN LoadPinterest", "SDVN Logic", "SDVN Loop Inpaint Stitch", "SDVN Lora Download", "SDVN Lora info", "SDVN Mask Regions", "SDVN Menu Option", "SDVN Merge Flux", "SDVN Merge SD1", "SDVN Merge SDXL", "SDVN Metadata Check", "SDVN Model Merge", "SDVN Model info editor", "SDVN Pipe In", "SDVN Pipe Out", "SDVN Pipe Out All", "SDVN QuadrupleCLIP Download", "SDVN Quick Menu", "SDVN RGBA to RGB", "SDVN Run Python Code", "SDVN Run Test", "SDVN Save Text", "SDVN Seed", "SDVN Simple Any Input", "SDVN Slider 1", "SDVN Slider 100", "SDVN StyleModel Download", "SDVN Styles", "SDVN Switch", "SDVN Translate", "SDVN UNET Download", "SDVN UPscale Latent", "SDVN Upscale Image", "SDVN UpscaleModel Download", "SDVN VAE Download", "SDVN Yolo8 Seg"], {"title_aux": "SDVN Comfy node [UNSAFE]"}], "https://github.com/StaffsGull/comfyui_scene_builder": [["CharacterBuilderNode", "ClothingItemNode", "ClothingMergerNode", "EnvironmentBuilderNode", "MergeCharactersNode", "PhotoStyleBuilderNode", "SceneCombinerNode"], {"title_aux": "comfyui_scene_builder [WIP]"}], "https://github.com/StartHua/Comfyui_CSDMT_CXH": [["CSD"], {"title_aux": "Comfyui_CXH_CRM"}], "https://github.com/StartHua/Comfyui_CXH_CRM": [["CRM"], {"title_aux": "Comfyui_CXH_CRM"}], "https://github.com/StartHua/Comfyui_CXH_joy_caption": [["CXH_DownloadAndLoadFlorence2Model", "CXH_Florence2Run", "CXH_HG_Model_Load", "CXH_IC_Lora_Florence2Run", "CXH_IC_lora_reversal", "CXH_<PERSON><PERSON>_lora_Joy_batch", "CXH_Min2_6_classifiy", "CXH_Min2_6_prompt_Run", "CXH_MinCP3_4B_Chat", "CXH_MinCP3_4B_Load", "CXH_SmolVlm_Load", "CXH_SmolVlm_Run", "Joy_caption", "Joy_caption_alpha_batch", "Joy_caption_alpha_batch_Dirs", "Joy_caption_alpha_load", "Joy_caption_alpha_prompt", "Joy_caption_alpha_run", "Joy_caption_load"], {"title_aux": "Comfyui_CXH_joy_caption [SECURITY SCREENING]"}], "https://github.com/StartHua/Comfyui_Flux_Style_Ctr": [["CXH_StyleModelApply"], {"title_aux": "Comfyui_Flux_Style_Ctr [WIP]"}], "https://github.com/StartHua/Comfyui_leffa": [["CXH_Leffa_Viton_Load", "CXH_Leffa_Viton_Run"], {"title_aux": "Comfyui_leffa"}], "https://github.com/StoryWalker/comfyui_flux_collection_advanced": [["Example", "FluxControlNetApply", "FluxControlNetApplyPreview", "FluxControlNetLoader", "FluxImagePreview", "FluxImageUpscaler", "FluxLoader", "FluxSamplerParameters", "FluxTextPrompt"], {"title_aux": "comfyui_flux_collection_advanced [WIP]"}], "https://github.com/Symbiomatrix/Comfyui-Sort-Files": [["ImageSaverSBM", "SortControlSBM", "StringToFloatSBM", "VideoSeriesMergerSBM"], {"author": "SBM", "title_aux": "Comfyui-Sort-Files"}], "https://github.com/TSFSean/ComfyUI-TSFNodes": [["GyroOSC"], {"title_aux": "ComfyUI-TSFNodes"}], "https://github.com/Tawbaware/ComfyUI-Tawbaware": [["Example", "LatentBlendGradient", "ReverseLatentBatch", "WanVideoReCamMasterGenerateOrbitCameraEx"], {"title_aux": "ComfyUI-Tawbaware [WIP]"}], "https://github.com/Temult/TWanSigmaSampler": [["TWanVideoSigmaSampler"], {"title_aux": "TWanVideoSigmaSampler: EXPERIMENTAL [WIP]"}], "https://github.com/ThatGlennD/ComfyUI-Image-Analysis-Tools": [["Blur Detection", "Clipping Analysis", "Color Cast Detector", "Color Harmony Analyzer", "Color Temperature Estimator", "ColorTemperatureEstimator", "Contrast Analysis", "ContrastAnalysis", "Defocus Analysis", "Edge Density Analysis", "Entropy Analysis", "Noise Estimation", "RGB Histogram Renderer", "Sharpness / Focus Score"], {"title_aux": "ComfyUI Image Analysis Toolkit [WIP]"}], "https://github.com/TheJorseman/IntrinsicCompositingClean-ComfyUI": [["AlbedoHarmonizer", "AlbedoModelLoader", "CompleteRelighting", "CompositeNormalsCalculator", "<PERSON><PERSON><PERSON>Estimator", "DepthModelLoader", "ExtractSmallBgShd", "HarmonizedImageCreator", "ImageResizer", "ImageResizerNP", "ImageResizerNPMASK", "IntrinsicDecomposer", "IntrinsicModelLoader", "LightCoeffExtractor", "LoadImagePIL", "MaskApplier", "MaskGenerator", "NormalsExtractor", "NormalsModelLoader", "ReshadingModelLoader", "ReshadingProcessor"], {"title_aux": "IntrinsicCompositingClean-ComfyUI"}], "https://github.com/ThisModernDay/ComfyUI-InstructorOllama": [["OllamaInstructorNode"], {"title_aux": "ComfyUI Instructor Ollama"}], "https://github.com/Velour-Fog/comfy-latent-nodes": [["CustomLoadLatent", "CustomSaveLatent"], {"title_aux": "comfy-latent-nodes [UNSAFE]"}], "https://github.com/VictorLopes643/ComfyUI-Video-Dataset-Tools": [["VideoFrameExtractor", "VideoFrameSaver"], {"title_aux": "ComfyUI-Video-Dataset-Tools [WIP]"}], "https://github.com/Video3DGenResearch/comfyui-batch-input-node": [["BatchImageAndPrompt", "BatchInputCSV", "BatchInputText"], {"title_aux": "ComfyUI Batch Input Node"}], "https://github.com/VisionExp/ve_custom_comfyui_nodes": [["LoadImgFromInputUrl", "assets/Asset Image", "render3d/Render Node"], {"title_aux": "ve_custom_comfyui_nodes"}], "https://github.com/WASasquatch/ASTERR": [["ASTERR", "SaveASTERR"], {"title_aux": "ASTERR [UNSAFE]"}], "https://github.com/WSJUSA/Comfyui-StableSR": [["ColorFix", "StableSRUpscalerPipe"], {"author": "WSJUSA", "description": "This module enables StableSR in Comgfyui. Ported work of sd-webui-stablesr. Original work for Auotmaatic1111 version of this module and StableSR credit to <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> and <PERSON><PERSON><PERSON>.", "nickname": "StableSR", "title": "StableSR", "title_aux": "pre-comfyui-stablsr"}], "https://github.com/WaiyanLing/ComfyUI-Tracking": [["WorkflowStats"], {"title_aux": "ComfyUI-Tracking [WIP]"}], "https://github.com/WilliamStanford/ComfyUI-VisualLabs": [["CreateFadeMaskAdvancedVL", "PointStringFromFloatArray", "RescaleFloatArray", "StringFromFloatArray"], {"title_aux": "visuallabs_comfyui_nodes"}], "https://github.com/WozStudios/ComfyUI-WozNodes": [["CreateImageBatch", "ImageBatchSelectByMask", "ImageBatchTrim", "ImageBatcherByIndexProV2"], {"title_aux": "ComfyUI-WozNodes"}], "https://github.com/Yeonri/ComfyUI_LLM_Are_You_Listening": [["AYL_API_Node", "AYL_GGUF_Node", "AYL_Node"], {"title_aux": "ComfyUI_LLM_Are_You_Listening [WIP]"}], "https://github.com/Yukinoshita-Yukinoe/ComfyUI-KontextOfficialNode": [["KontextImageEditingOfficialAPI_Max", "KontextTextToImageOfficialAPI_Max"], {"title_aux": "ComfyUI-KontextOfficialNode"}], "https://github.com/ZHO-ZHO-ZHO/ComfyUI-AuraSR-ZHO": [["AuraSR_Lterative_Zho", "AuraSR_ModelLoader_Zho", "AuraSR_Zho"], {"title_aux": "ComfyUI-AuraSR-ZHO"}], "https://github.com/ZHO-ZHO-ZHO/ComfyUI-BiRefNet-ZHO": [["BiRefNet_ModelLoader_Zho", "BiRefNet_Zho"], {"title_aux": "ComfyUI-BiRefNet-ZHO [BROKEN]"}], "https://github.com/ZHO-ZHO-ZHO/ComfyUI-Gemini": [["ConcatText_Zho", "DisplayText_Zho", "Gemini_15P_API_S_Advance_Zho", "Gemini_15P_API_S_Chat_Advance_Zho", "Gemini_API_Chat_Zho", "Gemini_API_S_Chat_Zho", "Gemini_API_S_Vsion_ImgURL_Zho", "Gemini_API_S_Zho", "Gemini_API_Vsion_ImgURL_Zho", "Gemini_API_Zho", "Gemini_FileUpload_API_S_Zho", "Gemini_File_API_S_Zho"], {"title_aux": "ComfyUI-Gemini [NAME CONFLICT]"}], "https://github.com/ZHO-ZHO-ZHO/ComfyUI-PuLID-ZHO": [["PuLID_Zho"], {"title_aux": "ComfyUI-PuLID-ZHO [WIP]"}], "https://github.com/ZHO-ZHO-ZHO/ComfyUI-Wan-ZHO": [["WanT2V_Generation_Zho", "WanT2V_ModelLoader_Zho"], {"title_aux": "ComfyUI Wan2.1 [WIP]"}], "https://github.com/ZenAI-Vietnam/ComfyUI-gemini-IG": [["Gemini Image Generation", "Gemini Text Generation"], {"title_aux": "ComfyUI-gemini-IG"}], "https://github.com/ZenAI-Vietnam/ComfyUI_InfiniteYou": [["Face<PERSON><PERSON><PERSON>", "FaceSwap_InfiniteYou", "InfiniteYouApply"], {"title_aux": "ComfyUI_InfiniteYou [NAME CONFLICT]"}], "https://github.com/a-One-Fan/ComfyUI-Blenderesque-Nodes": [["BlenderAlphaConvert", "BlenderAlphaOver", "BlenderBlackbody", "BlenderBokehImage", "BlenderBrightnessContrast", "BlenderClamp", "BlenderCombineColor", "BlenderCombineXYZ", "BlenderConvertColorspace", "BlenderCornerPin", "BlenderCrop", "BlenderDisplace", "BlenderExposure", "BlenderFlip", "BlenderGamma", "BlenderHueSaturationValue", "BlenderInvertColor", "BlenderLensDistortion", "BlenderMapRange", "BlenderMapUV", "BlenderMath", "BlenderMix", "BlenderMovieDistortion", "BlenderRGB", "BlenderRGBtoBW", "BlenderRotate", "BlenderScale", "BlenderSeparateColor", "BlenderSeparateXYZ", "BlenderSetAlpha", "BlenderTonemap", "BlenderTransform", "BlenderTranslate", "BlenderUV", "BlenderValue", "BlenderVectorMath", "BlenderWavelength", "BlenderZCombine"], {"title_aux": "ComfyUI-Blenderesque-Nodes [WIP]"}], "https://github.com/a-und-b/ComfyUI_Output_as_Input": [["OutputAsInput"], {"title_aux": "ComfyUI_Output_as_Input"}], "https://github.com/aa-parky/pipemind-comfyui": [["BatchImageLoadInput", "BatchImageLoadOutput", "BooleanSwitchAny", "KeywordPromptComposer", "LoadTxtFile", "PipemindDisplayAny", "PipemindFlux2MAspectRatio", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "PipemindMultilineTextInput", "PipemindSDXL15AspectRatio", "PipemindSaveImageWTxt", "PipemindShowText", "PipemindShowTextFind", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "RandomLineFromDropdown", "SelectLineFromDropdown", "SimplePromptCombiner"], {"title_aux": "pipemind-comfyui"}], "https://github.com/abuzreq/ComfyUI-Model-Bending": [["Add Noise Module (Bending)", "<PERSON><PERSON> (Bending)", "Compute PCA", "ConditioningApplyOperation", "Custom Code Module", "Dilation Module (Bending)", "<PERSON><PERSON><PERSON> (Bending)", "<PERSON><PERSON><PERSON> (Bending)", "HSpace Bending", "Latent Operation (Add Noise)", "Latent Operation (<PERSON><PERSON>ala<PERSON>)", "Latent Operation (Custom)", "Latent Operation (<PERSON><PERSON><PERSON>)", "Latent Operation (Rotate)", "Latent Operation (Threshold)", "Latent Operation To Module", "LatentApplyOperationCFGToStep", "LoRA Bending", "Model Bending", "Model Bending (SD Layers)", "Model Inspector", "Model VAE Bending", "Model VAE Inspector", "<PERSON>ply <PERSON> (Bending)", "NoiseVariations", "<PERSON><PERSON><PERSON> (Bending)", "Scale Module (Bending)", "<PERSON><PERSON> (Bending)", "<PERSON><PERSON><PERSON><PERSON> (Bending)", "Visualize Feature Map"], {"title_aux": "ComfyUI Model Bending [UNSAFE]"}], "https://github.com/aiden1020/ComfyUI_Artcoder": [["ArtCoder"], {"title_aux": "ComfyUI_Artcoder [WIP]"}], "https://github.com/ainanoha/etm_comfyui_nodes": [["ETM_LoadImageFromLocal", "ETM_SaveImage"], {"title_aux": "etm_comfyui_nodes"}], "https://github.com/akatz-ai/ComfyUI-Execution-Inversion": [["AccumulateNode", "AccumulationGetItemNode", "AccumulationGetLengthNode", "AccumulationHeadNode", "AccumulationSetItemNode", "AccumulationTailNode", "AccumulationToListNode", "DebugPrint", "ExecutionBlocker", "ForLoopClose", "ForLoopOpen", "GetFloatFromList", "GetIntFromList", "IntegerListGeneratorNode", "LazyConditional", "LazyIndexSwitch", "LazyMixImages", "LazySwitch", "ListToAccumulationNode", "MakeListNode", "WhileLoopClose", "WhileLoopOpen", "_ForLoopCounter"], {"title_aux": "ComfyUI-Execution-Inversion"}], "https://github.com/aklevecz/ComfyUI-AutoPrompt": [["OllamaChat", "OllamaModelLister", "OllamaPromptGenerator", "TextDisplay"], {"title_aux": "ComfyUI-AutoPrompt [WIP]"}], "https://github.com/alexgenovese/ComfyUI-Diffusion-4k": [["FluxImageGenerator"], {"title_aux": "ComfyUI-Diffusion-4k [WIP]"}], "https://github.com/alexgenovese/ComfyUI-Reica": [["ReicaGCPReadImageNode", "ReicaGCPWriteImageNode", "ReicaHTTPNotification", "ReicaInsertAnythingNode", "ReicaLoadLoopImagesFromURLs", "ReicaLoadLoopImagesFromURLsSkipErrors", "ReicaTextImageDisplay", "ReicaTryOffDiffGenerator", "ReicaTryOffDiffLoader", "ReicaURLImageLoader"], {"title_aux": "ComfyUI-Reica"}], "https://github.com/alexisrolland/ComfyUI-AuraSR": [["LoadAuraSR", "RunAuraSR"], {"title_aux": "alexisrolland/ComfyUI-AuraSR"}], "https://github.com/alt-key-project/comfyui-dream-painter": [["Bitmap AND [<PERSON><PERSON><PERSON><PERSON>]", "Bitmap Crop Center [DPaint]", "Bitmap Dimensions [D<PERSON><PERSON>t]", "Bitmap Edge Detect [DPaint]", "Bitmap Expand Canvas [DPaint]", "Bitmap Invert [D<PERSON><PERSON><PERSON>]", "Bitmap OR [D<PERSON><PERSON><PERSON>]", "Bitmap Resize [D<PERSON><PERSON>t]", "Bitmap Rotate [D<PERSON>aint]", "Bitmap To Image & Mask [DPaint]", "Bitmap XOR [D<PERSON>aint]", "Draw Shape As Bitmap [D<PERSON><PERSON>t]", "Image To Bitmap [D<PERSON>aint]", "Random Number Generator [<PERSON><PERSON><PERSON><PERSON>]", "Shape Center & Fit [DPaint]", "<PERSON><PERSON><PERSON> [<PERSON><PERSON><PERSON><PERSON>]", "<PERSON><PERSON><PERSON> [<PERSON><PERSON><PERSON><PERSON>]", "<PERSON><PERSON><PERSON> Find Bounds [<PERSON><PERSON><PERSON><PERSON>]", "<PERSON><PERSON><PERSON> [<PERSON><PERSON><PERSON><PERSON>]", "<PERSON><PERSON><PERSON> [<PERSON><PERSON><PERSON><PERSON>]", "<PERSON><PERSON><PERSON> [<PERSON><PERSON><PERSON><PERSON>]", "<PERSON><PERSON><PERSON> [<PERSON><PERSON><PERSON><PERSON>]", "Shape of Circular Rays [D<PERSON><PERSON>t]", "<PERSON><PERSON><PERSON> of N-Polygon [<PERSON><PERSON><PERSON><PERSON>]", "<PERSON><PERSON>pe of Rectangle [<PERSON><PERSON><PERSON><PERSON>]", "<PERSON><PERSON>pe of Star [D<PERSON><PERSON><PERSON>]"], {"title_aux": "<PERSON> Painter [WIP]"}], "https://github.com/alt-key-project/comfyui-dream-video-batches": [["Blended Transition [DVB]", "Calculation [DVB]", "Create Frame Set [DVB]", "Divide [DVB]", "Fade From Black [DVB]", "Fade To Black [DVB]", "Float Input [DVB]", "For Each Done [DVB]", "For Each Filename [DVB]", "Frame Set Append [DVB]", "Frame Set Frame Dimensions Scaled [DVB]", "Frame Set Index Offset [DVB]", "Frame <PERSON> [DVB]", "Frame Set Reindex [DVB]", "Frame Set Repeat [DVB]", "Frame Set Reverse [DVB]", "Frame Set Split Beginning [DVB]", "Frame Set Split End [DVB]", "Frame Set Splitter [DVB]", "Generate Inbetween Frames [DVB]", "Int Input [DVB]", "Linear Camera Pan [DVB]", "Linear Camera Roll [DVB]", "Linear Camera Zoom [DVB]", "Load Image From Path [DVB]", "Multiply [DVB]", "Sine Camera Pan [DVB]", "Sine Camera Roll [DVB]", "Sine Camera Zoom [DVB]", "String Input [DVB]", "Text Input [DVB]", "Trace Memory Allocation [DVB]", "Unwrap Frame Set [DVB]"], {"title_aux": "Dream Project Video Batches [WIP]"}], "https://github.com/amamisonlyuser/MixvtonComfyui": [["CXH_Leffa_Viton_Load", "CXH_Leffa_Viton_Run"], {"title_aux": "MixvtonComfyui [WIP]"}], "https://github.com/ammahmoudi/ComfyUI-Legendary-Nodes": [["Legendary Dataset Saver", "Legendary Image URL Loader", "Legendary Lora URL Loader"], {"title_aux": "ComfyUI-Legendary-Nodes"}], "https://github.com/animEEEmpire/ComfyUI-Animemory-Loader": [["AnimemoryNode"], {"title_aux": "ComfyUI-Animemory-Loader"}], "https://github.com/apetitbois/nova_utils": [["floatList2Float", "<PERSON><PERSON><PERSON><PERSON><PERSON>"], {"title_aux": "nova_utils"}], "https://github.com/aria1th/ComfyUI-SkipCFGSigmas": [["CFGControl_SKIPCFG"], {"title_aux": "ComfyUI-SkipCFGSigmas"}], "https://github.com/aria1th/ComfyUI-camietagger-onnx": [["<PERSON><PERSON>"], {"title_aux": "ComfyUI-camietagger-onnx"}], "https://github.com/artem-konevskikh/comfyui-split-merge-video": [["VideoMerger", "VideoSplitter"], {"title_aux": "ComfyUI Video Processing Nodes [WIP]"}], "https://github.com/artifyfun/ComfyUI-JS": [["JavascriptExecutor"], {"title_aux": "ComfyUI-JS [UNSAFE]"}], "https://github.com/artisanalcomputing/ComfyUI-Custom-Nodes": [["RandomVideoMixer", "SpotifyCanvasGenerator", "VideoWriter"], {"title_aux": "artcpu-custom-nodes"}], "https://github.com/ashishsaini/comfyui-segment-clothing-sleeves": [["segformer_b2_sleeves"], {"title_aux": "comfyui_segformer_b2_sleeves"}], "https://github.com/ashllay/ComfyUI_MoreComfy": [["MC <PERSON><PERSON>", "MC Get Image Size", "MC Multi Concat", "MC Multi Concat(Advanced)", "MC Set T<PERSON>", "MC Switch Image", "MC Switch Latent", "MC Switch Model", "MC Switch Seed", "MC Switch String"], {"title_aux": "ComfyUI_MoreComfy"}], "https://github.com/avocadori/ComfyUI-AudioAmplitudeConverter": [["NormalizeAmpToFloatNode"], {"title_aux": "ComfyUI Audio Amplitude Converter [WIP]"}], "https://github.com/ayaoayaoayaoaya/ComfyUI-KLUT-DeepSeek-API": [["KLUTDeepSeekAPI"], {"title_aux": "ComfyUI-KLUT-DeepSeek-API [WIP]"}], "https://github.com/backearth1/Comfyui-MiniMax-Video": [["ImageToPrompt", "MiniMaxAIAPIClient", "MiniMaxImage2Video", "MiniMaxImageGenerator", "MiniMaxPreviewVideo"], {"title_aux": "Comfyui-MiniMax-Video [WIP]"}], "https://github.com/baicai99/ComfyUI-FrameSkipping": [["FrameSelector", "FrameSkipping", "FrameTruncating", "IntOperationsNode", "MaskFrameSkipping", "MaskGenerator", "MaskSelector"], {"title_aux": "ComfyUI-FrameSkipping"}], "https://github.com/bananasss00/Comfyui-PyExec": [["PyExec", "PyExec_Output", "PyExec_OutputIsList", "PyExec_OutputIsValue"], {"author": "SeniorPioner", "description": "Comfyui runtime python code execution", "nickname": "PyExec", "title": "PyExec", "title_aux": "Comfyui-PyExec [UNSAFE]"}], "https://github.com/bandido37/comfyui-kaggle-local-save": [["KaggleLocalSaveNode"], {"title_aux": "Kaggle ComfyUI Local Save Node [WIP]"}], "https://github.com/benda1989/WaterMarkRemover_ComfyUI": [["Remover", "VideoRemover"], {"title_aux": "Comfyui lama remover [WIP]"}], "https://github.com/benmizrahi/ComfyGCS": [["LoadImageGCS", "SaveImageGCS"], {"title_aux": "ComfyGCS [WIP]"}], "https://github.com/beyastard/ComfyUI_BeySoft": [["BeySoft"], {"title_aux": "ComfyUI_BeySoft"}], "https://github.com/bheins/ComfyUI-glb-to-stl": [["GLBToSTLNode"], {"title_aux": "ComfyUI-glb-to-stl [WIP]"}], "https://github.com/birnam/ComfyUI-GenData-Pack": [["Checkpoint From String 👩‍💻", "Checkpoint Rerouter 👩‍💻", "Checkpoint Selector Stacker 👩‍💻", "Checkpoint Selector 👩‍💻", "Checkpoint to String 👩‍💻", "Crop Recombine 👩‍💻", "Crop|IP|Inpaint 👩‍💻", "Crop|IP|Inpaint|SDXL 👩‍💻", "Decode GenData 👩‍💻", "Encode GenData 👩‍💻", "GenData Stacker 👩‍💻", "IPAdapterApply", "IPAdapterApplyEncoded", "IPAdapterApplyFaceID", "IPAdapterBatchEmbeds", "IPAdapterEncoder", "IPAdapterLoadEmbeds", "IPAdapterModelLoader", "IPAdapterSaveEmbeds", "IPAdapterTilesMasked", "InsightFaceLoader", "<PERSON><PERSON>ack to String 👩‍💻", "Lo<PERSON> Stacker From Prompt 👩‍💻", "Load Checkpoints From File 👩‍💻", "Load GenData From Dir 👩‍💻", "Parse GenData 👩‍💻", "PrepImageForClipVision", "PrepImageForInsightFace", "Provide GenData 👩‍💻", "Save Image From GenData 👩‍💻", "VAE From String 👩‍💻", "VAE to String 👩‍💻", "× Product CheckpointXGenDatas 👩‍💻"], {"title_aux": "Gen Data Tester [WIP]"}], "https://github.com/blueraincoatli/ComfyUI-Model-Cleaner": [["InteractiveModelCleanerNode", "ModelScannerNode"], {"title_aux": "ComfyModelCleaner [WIP]"}], "https://github.com/bmad4ever/comfyui_bmad_nodes": [["AdaptiveThresholding", "Add String To Many", "AddAlpha", "AdjustRect", "AnyToAny", "BoundingRect (contours)", "BuildColorRangeAdvanced (hsv)", "BuildColorRangeHSV (hsv)", "CLAHE", "CLIPEncodeMultiple", "CLIPEncodeMultipleAdvanced", "ChameleonMask", "Check<PERSON><PERSON><PERSON><PERSON> (dirty)", "Checkpoint<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> (dirty)", "Color (RGB)", "Color (hexadecimal)", "Color Clip", "Color Clip (advanced)", "Color Clip ADE20k", "ColorDictionary", "ColorDictionary (custom)", "Conditioning (combine multiple)", "Conditioning (combine selective)", "Conditioning Grid (cond)", "Conditioning Grid (string)", "Conditioning Grid (string) Advanced", "Contour To Mask", "Contours", "ControlNetHadamard", "ControlNetHadamard (manual)", "ConvertImg", "CopyMakeBorder", "CreateRequestMetadata", "DistanceTransform", "Draw Contour(s)", "EqualizeHistogram", "ExtendColorList", "ExtendCondList", "ExtendFloatList", "ExtendImageList", "ExtendIntList", "ExtendLatentList", "ExtendMaskList", "ExtendModelList", "ExtendStringList", "FadeMaskEdges", "<PERSON><PERSON>", "FindComplementaryColor", "Find<PERSON><PERSON><PERSON><PERSON>", "FlatLatentsIntoSingleGrid", "Framed Mask Grab <PERSON>", "Framed Mask Grab Cut 2", "FromListGet1Color", "FromListGet1Cond", "FromListGet1Float", "FromListGet1Image", "FromListGet1Int", "FromListGet1Latent", "FromListGet1Mask", "FromListGet1Model", "FromListGet1String", "FromListGetColors", "FromListGetConds", "FromListGetFloats", "FromListGetImages", "FromListGetInts", "FromListGetLatents", "FromListGetMasks", "FromListGetModels", "FromListGetStrings", "Get <PERSON><PERSON> from list", "Get Models", "Get Prompt", "Hue Mode (InRange hsv)", "HypernetworkLoader (dirty)", "ImageBatchToList", "InRange (hsv)", "Inpaint", "Input/String to Int Array", "KMeansColor", "Load 64 Encoded Image", "<PERSON><PERSON><PERSON><PERSON><PERSON> (dirty)", "MaskGrid N KSamplers Advanced", "MaskOuterBlur", "<PERSON><PERSON> Latent <PERSON>", "MonoMerge", "MorphologicOperation", "MorphologicSkeletoning", "NaiveAutoKMeansColor", "OtsuThreshold", "RGB to HSV", "Rect G<PERSON>", "Remap", "RemapBarrelDistortion", "RemapFromInsideParabolas", "RemapFromQuadrilateral (homography)", "RemapInsideParabolas", "RemapInsideParabolasAdvanced", "RemapPinch", "RemapReverseBarrelDistortion", "RemapStretch", "RemapToInnerCylinder", "RemapToOuterCylinder", "RemapToQuadrilateral", "RemapWarpPolar", "Repeat Into Grid (image)", "Repeat Into Grid (latent)", "RequestInputs", "SampleColorHSV", "Save Image (api)", "SeamlessClone", "SeamlessClone (simple)", "SetRequestStateToComplete", "String", "String to Float", "String to Integer", "ToColorList", "ToCondList", "ToFloatList", "ToImageList", "ToIntList", "ToLatentList", "ToMaskList", "ToModelList", "ToStringList", "UnGridify (image)", "VAEEncodeBatch"], {"title_aux": "Bmad <PERSON> [UNSAFE]"}], "https://github.com/brace-great/comfyui-eim": [["EncryptImage"], {"title_aux": "comfy<PERSON>-eim"}], "https://github.com/brace-great/comfyui-mc": [["IncrementCounterOnMatch"], {"title_aux": "comfyui-mc [WIP]"}], "https://github.com/bruce007lee/comfyui-cleaner": [["cleaner"], {"title_aux": "comfyui-cleaner"}], "https://github.com/bruce007lee/comfyui-tiny-utils": [["CropImageByMask", "FaceAlign", "FaceAlignImageProcess", "FaceAlignMaskProcess", "ImageFillColorByMask", "ImageSAMMask", "ImageTransposeAdvance", "LoadImageAdvance"], {"title_aux": "comfyui-tiny-utils"}], "https://github.com/brycegoh/comfyui-custom-nodes": [["CombineTwoImageIntoOne", "FillMaskedArea", "MaskAreaComparisonSegment", "OCRAndMask"], {"title_aux": "brycegoh/comfyui-custom-nodes"}], "https://github.com/bulldog68/ComfyUI_FMJ": [["FMJCreaPrompt"], {"title_aux": "ComfyUI_FMJ [WIP]"}], "https://github.com/c0ffymachyne/ComfyUI_SignalProcessing": [["SignalProcessingBaxandall3BandEQ", "SignalProcessingBaxandallEQ", "SignalProcessingCompressor", "SignalProcessingConvolutionReverb", "SignalProcessingFilter", "SignalProcessingHarmonicsEnhancer", "SignalProcessingLimiter", "SignalProcessingLoadAudio", "SignalProcessingLoudness", "SignalProcessingMixdown", "SignalProcessingNormalizer", "SignalProcessingPadSynth", "SignalProcessingPadSynthChoir", "SignalProcessingPaulStretch", "SignalProcessingPitchShifter", "SignalProcessingSaturation", "SignalProcessingSpectrogram", "SignalProcessingStereoWidening", "SignalProcessingWaveform"], {"title_aux": "ComfyUI Signal Processing [WIP]"}], "https://github.com/casterpollux/MiniMax-bmo": [["MinimaxRemoverBMO"], {"title_aux": "MiniMax-bmo"}], "https://github.com/catboxanon/ComfyUI-Pixelsmith": [["<PERSON><PERSON><PERSON><PERSON>"], {"title_aux": "ComfyUI-Pixelsmith [WIP]"}], "https://github.com/celll1/cel_sampler": [["latent_tracker"], {"title_aux": "cel_sampler [WIP]"}], "https://github.com/cesilk10/cesilk-comfyui-nodes": [["SaveAndUploadToS3", "SdxlImageSizes"], {"title_aux": "cesilk-comfyui-nodes"}], "https://github.com/chaojie/ComfyUI-DynamiCrafter": [["DynamiCrafter Simple", "DynamiCrafterInterp Simple", "DynamiCrafterInterpLoader", "DynamiCrafter<PERSON><PERSON>der"], {"title_aux": "ComfyUI DynamiCrafter"}], "https://github.com/chaojie/ComfyUI-mobvoi-openapi": [["HtmlViewer", "MobvoiOpenapiMetamanAudio", "MobvoiOpenapiMetamanText", "MobvoiOpenapiTts", "OssUploadAudio", "OssUploadImage"], {"title_aux": "ComfyUI-mobvoi-openapi"}], "https://github.com/chenbaiyujason/ComfyUI_StepFun": [["CombineStrings", "JSO<PERSON><PERSON><PERSON>", "StepFunClient", "TextImageChat", "VideoChat", "VideoFileUploader"], {"title_aux": "ComfyUI_StepFun"}], "https://github.com/chengzeyi/Comfy-WaveSpeed": [["ApplyFBCacheOnModel", "EnhancedCompileModel", "EnhancedLoadDiffusionModel", "VelocatorCompileModel", "VelocatorLoadAndQuantizeClip", "VelocatorLoadAndQuantizeDiffusionModel", "VelocatorQuantizeModel"], {"title_aux": "Comfy-WaveSpeed [WIP]"}], "https://github.com/chetusangolgi/Comfyui-supabase": [["SupabaseImageUploader", "SupabaseTableWatcherNode"], {"title_aux": "Comfyui-supabase"}], "https://github.com/christian-byrne/infinite-zoom-parallax-nodes": [["Create Parallax Video", "Layer Shifter for Parallax Outpainting", "Load Parallax Frame", "Parallax Config", "Save Parallax Frame", "<PERSON><PERSON> and Pa<PERSON> for Outpainting"], {"title_aux": "🌌 Infinite Parallax Nodes [WIP]"}], "https://github.com/christian-byrne/python-interpreter-node": [["Exec Python Code Script"], {"title_aux": "Python Interpreter ComfyUI Node [UNSAFE]"}], "https://github.com/chuge26/ComfyUI_seal_migration": [["PDFLoader", "PDFSaver", "SealMigration"], {"title_aux": "ComfyUI_seal_migration [WIP]"}], "https://github.com/cidiro/cid-node-pack": [["Cid<PERSON><PERSON><PERSON><PERSON><PERSON>", "CidAnySync", "CidLoadImageFromDir", "CidSaveImage", "CidWildcardProcessor"], {"title_aux": "cid-node-pack"}], "https://github.com/ciga2011/ComfyUI-AppGen": [["AG_APP_EDIT", "AG_APP_GEN", "AG_APP_SANDBOX", "AG_CODER_LLM"], {"title_aux": "ComfyUI-AppGen [UNSAFE]"}], "https://github.com/coVISIONSld/ComfyUI-OmniGen2": [["OmniGen2ModelLoader", "OmniGen2Sampler"], {"title_aux": "ComfyUI-OmniGen2 [NAME CONFLICT]"}], "https://github.com/comfyanonymous/ComfyUI": [["APG", "AddNoise", "AlignYourStepsScheduler", "BasicGuider", "BasicScheduler", "BetaSamplingScheduler", "CFGGuider", "CFGZeroStar", "CLIPAttentionMultiply", "CLIPLoader", "CLIPMergeAdd", "CLIPMergeSimple", "CLIPMergeSubtract", "CLIPSave", "CLIPSetLastLayer", "CLIPTextEncode", "CLIPTextEncodeControlnet", "CLIPTextEncodeFlux", "CLIPTextEncodeHiDream", "CLIPTextEncodeHunyuanDiT", "CLIPTextEncodeLumina2", "CLIPTextEncodePixArtAlpha", "CLIPTextEncodeSD3", "CLIPTextEncodeSDXL", "CLIPTextEncodeSDXLRefiner", "CLIPVisionEncode", "CLIPVisionLoader", "<PERSON><PERSON>", "CaseConverter", "CheckpointLoader", "CheckpointLoaderSimple", "CheckpointSave", "ConditioningAverage", "Conditioning<PERSON><PERSON><PERSON>", "ConditioningConcat", "ConditioningSetArea", "ConditioningSetAreaPercentage", "ConditioningSetAreaPercentageVideo", "ConditioningSetAreaStrength", "ConditioningSetMask", "ConditioningSetTimestepRange", "ConditioningStableAudio", "ConditioningZeroOut", "ControlNetApply", "ControlNetApplyAdvanced", "ControlNetApplySD3", "ControlNetInpaintingAliMamaApply", "ControlNetLoader", "CosmosImageToVideoLatent", "CosmosPredict2ImageToVideoLatent", "CreateVideo", "CropMask", "DiffControlNetLoader", "DifferentialDiffusion", "Diff<PERSON><PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "DualCFGGuider", "DualCLIPLoader", "EmptyAceStepLatentAudio", "EmptyCosmosLatentVideo", "EmptyHunyuanLatentVideo", "EmptyImage", "EmptyLTXVLatentVideo", "EmptyLatentAudio", "EmptyLatentHunyuan3Dv2", "EmptyLatentImage", "EmptyMochiLatentVideo", "EmptySD3LatentImage", "ExponentialScheduler", "ExtendIntermediateSigmas", "FeatherMask", "FlipSigmas", "FluxDisableGuidance", "FluxGuidance", "FluxKontextImageScale", "FluxKontextMaxImageNode", "FluxKontextProImageNode", "FluxProCannyNode", "FluxProDepthNode", "FluxProExpandNode", "FluxProFillNode", "FluxProImageNode", "FluxProUltraImageNode", "FreSca", "FreeU", "FreeU_V2", "GITSScheduler", "GLIGEN<PERSON><PERSON>der", "GLIGENTextBoxApply", "GeminiInputFiles", "GeminiNode", "GetImageSize", "GetVideoComponents", "GrowMask", "Hunyuan3Dv2Conditioning", "Hunyuan3Dv2ConditioningMultiView", "HunyuanImageToVideo", "HyperTile", "HypernetworkLoader", "IdeogramV1", "IdeogramV2", "IdeogramV3", "ImageAddNoise", "ImageBatch", "ImageBlend", "ImageBlur", "ImageColorToMask", "ImageCompositeMasked", "ImageCrop", "ImageFromBatch", "ImageInvert", "ImageOnlyCheckpointLoader", "ImageOnlyCheckpointSave", "ImagePadForOutpaint", "ImageQuantize", "ImageRGBToYUV", "ImageScale", "ImageScaleBy", "ImageScaleToTotalPixels", "ImageSharpen", "ImageStitch", "ImageToMask", "ImageUpscaleWithModel", "ImageYUVToRGB", "InpaintModelConditioning", "InstructPixToPixConditioning", "InvertMask", "JoinImageWithAlpha", "K<PERSON><PERSON><PERSON>", "KSamplerAdvanced", "KSamplerSelect", "KarrasScheduler", "KlingCameraControlI2VNode", "KlingCameraControlT2VNode", "KlingCameraControls", "KlingDualCharacterVideoEffectNode", "KlingImage2VideoNode", "KlingImageGenerationNode", "KlingLipSyncAudioToVideoNode", "KlingLipSyncTextToVideoNode", "KlingSingleImageVideoEffectNode", "KlingStartEndFrameNode", "KlingTextToVideoNode", "KlingVideoExtendNode", "KlingVirtualTryOnNode", "LTXVAddGuide", "LTXVConditioning", "LTXVCropGuides", "LTXVImgToVideo", "LTXVPreprocess", "LTXVScheduler", "LaplaceScheduler", "LatentAdd", "LatentApplyOperation", "LatentApplyOperationCFG", "LatentBatch", "LatentBatchSeedBehavior", "LatentBlend", "LatentComposite", "LatentCompositeMasked", "LatentCrop", "LatentFlip", "LatentFromBatch", "LatentInterpolate", "Latent<PERSON><PERSON><PERSON>ly", "LatentOperationSharpen", "LatentOperationTonemapReinhard", "LatentRotate", "LatentSubtract", "LatentUpscale", "LatentUpscaleBy", "Load3D", "Load3DAnimation", "LoadAudio", "LoadImage", "LoadImageMask", "LoadImageOutput", "LoadImageSetFromFolderNode", "LoadLatent", "LoadVideo", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "LoraLoaderModelOnly", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "LoraSave", "LossGraphNode", "LotusConditioning", "LumaConceptsNode", "LumaImageModifyNode", "LumaImageNode", "LumaImageToVideoNode", "LumaReferenceNode", "LumaVideoNode", "<PERSON><PERSON>", "MaskComposite", "MaskPreview", "MaskToImage", "MinimaxImageToVideoNode", "MinimaxSubjectToVideoNode", "MinimaxTextToVideoNode", "ModelComputeDtype", "ModelMergeAdd", "ModelMergeAuraflow", "ModelMergeBlocks", "ModelMergeCosmos14B", "ModelMergeCosmos7B", "ModelMergeCosmosPredict2_14B", "ModelMergeCosmosPredict2_2B", "ModelMergeFlux1", "ModelMergeLTXV", "ModelMergeMochiPreview", "ModelMergeSD1", "ModelMergeSD2", "ModelMergeSD35_Large", "ModelMergeSD3_2B", "ModelMergeSDXL", "ModelMergeSimple", "ModelMergeSubtract", "ModelMergeWAN2_1", "ModelSamplingAuraFlow", "ModelSamplingContinuousEDM", "ModelSamplingContinuousV", "ModelSamplingDiscrete", "ModelSamplingFlux", "ModelSamplingLTXV", "ModelSamplingSD3", "ModelSamplingStableCascade", "ModelSave", "Morphology", "OpenAIChatConfig", "OpenAIChatNode", "OpenAIDalle2", "OpenAIDalle3", "OpenAIGPTImage1", "OpenAIInputFiles", "OptimalStepsScheduler", "PatchModelAddDownscale", "PerpNeg", "PerpNegGuider", "PerturbedAttentionGuidance", "PhotoMakerEncode", "PhotoMaker<PERSON><PERSON>der", "PikaImageToVideoNode2_2", "PikaScenesV2_2", "PikaStartEndFrameNode2_2", "PikaTextToVideoNode2_2", "Pikadditions", "Pikaffects", "Pikaswaps", "PixverseImageToVideoNode", "PixverseTemplateNode", "PixverseTextToVideoNode", "PixverseTransitionVideoNode", "PolyexponentialScheduler", "PorterDuffImageComposite", "Preview3D", "Preview3DAnimation", "PreviewAny", "PreviewAudio", "PreviewImage", "PrimitiveBoolean", "PrimitiveFloat", "PrimitiveInt", "PrimitiveString", "PrimitiveStringMultiline", "QuadrupleCLIPLoader", "RandomNoise", "RebatchImages", "RebatchLatents", "RecraftColorRGB", "RecraftControls", "RecraftCreativeUpscaleNode", "RecraftCrispUpscaleNode", "RecraftImageInpaintingNode", "RecraftImageToImageNode", "RecraftRemoveBackgroundNode", "RecraftReplaceBackgroundNode", "RecraftStyleV3DigitalIllustration", "RecraftStyleV3InfiniteStyleLibrary", "RecraftStyleV3LogoRaster", "RecraftStyleV3RealisticImage", "RecraftTextToImageNode", "RecraftTextToVectorNode", "RecraftVectorizeImageNode", "ReferenceLatent", "RegexExtract", "RegexMatch", "RegexReplace", "RenormCFG", "RepeatImageBatch", "RepeatLatentBatch", "RescaleCFG", "ResizeAndPadImage", "Rodin3D_Detail", "Rodin3D_Regular", "Rodin3D_Sketch", "Rodin3D_Smooth", "RunwayFirstLastFrameNode", "RunwayImageToVideoNodeGen3a", "RunwayImageToVideoNodeGen4", "RunwayTextToImageNode", "SDTurboScheduler", "SD_4XUpscale_Conditioning", "SV3D_Conditioning", "SVD_img2vid_Conditioning", "SamplerCustom", "SamplerCustomAdvanced", "SamplerDPMAdaptative", "SamplerDPMPP_2M_SDE", "SamplerDPMPP_2S_Ancestral", "SamplerDPMPP_3M_SDE", "SamplerDPMPP_SDE", "SamplerEulerAncestral", "SamplerEulerAncestralCFGPP", "SamplerEulerCFGpp", "SamplerLCMUpscale", "SamplerLMS", "SaveAnimatedPNG", "SaveAnimatedWEBP", "SaveAudio", "SaveAudioMP3", "SaveAudioOpus", "SaveGLB", "SaveImage", "SaveImageWebsocket", "SaveLatent", "SaveLoRANode", "SaveSVGNode", "SaveVideo", "SaveWEBM", "SelfAttentionGuidance", "SetFirstSigma", "SetLatentNoiseMask", "SetUnionControlNetType", "SkipLayerGuidanceDiT", "SkipLayerGuidanceSD3", "SolidMask", "SplitImageWithAlpha", "SplitSigmas", "SplitSigmasDenoise", "StabilityStableImageSD_3_5Node", "StabilityStableImageUltraNode", "StabilityUpscaleConservativeNode", "StabilityUpscaleCreativeNode", "StabilityUpscaleFastNode", "StableCascade_EmptyLatentImage", "StableCascade_StageB_Conditioning", "StableCascade_StageC_VAEEncode", "StableCascade_SuperResolutionControlnet", "StableZero123_Conditioning", "StableZero123_Conditioning_Batched", "StringCompare", "StringConcatenate", "StringContains", "<PERSON><PERSON><PERSON><PERSON>", "StringReplace", "StringSubstring", "StringTrim", "StubConstantImage", "StubFloat", "StubImage", "StubInt", "StubMask", "StyleModelApply", "StyleModelLoader", "T5TokenizerOptions", "TestAccumulateNode", "TestAccumulationGetItemNode", "TestAccumulationGetLengthNode", "TestAccumulationHeadNode", "TestAccumulationSetItemNode", "TestAccumulationTailNode", "TestAccumulationToListNode", "TestBoolOperationNode", "TestCustomIsChanged", "TestCustomValidation1", "TestCustomValidation2", "TestCustomValidation3", "TestCustomValidation4", "TestCustomValidation5", "TestDynamicDependencyCycle", "TestExecutionBlocker", "TestFloatConditions", "TestForLoopClose", "TestForLoopOpen", "TestIntConditions", "TestIntMathOperation", "TestIsChangedWithConstants", "TestLazyMixImages", "TestListToAccumulationNode", "TestMakeListNode", "TestMixedExpansionReturns", "TestStringConditions", "TestToBoolNode", "TestVariadicAverage", "TestWhileLoopClose", "TestWhileLoopOpen", "TextEncodeAceStepAudio", "TextEncodeHunyuanVideo_ImageToVideo", "ThresholdMask", "TomePatchModel", "TorchCompileModel", "TrainLoraNode", "TrimVideoLatent", "TripleCLIPLoader", "TripoConversionNode", "TripoImageToModelNode", "TripoMultiviewToModelNode", "TripoRefineNode", "TripoRetargetNode", "TripoRigNode", "TripoTextToModelNode", "TripoTextureNode", "UNETLoader", "UNetCrossAttentionMultiply", "UNetSelfAttentionMultiply", "UNetTemporalAttentionMultiply", "UpscaleModelLoader", "VAEDecode", "VAEDecodeAudio", "VAEDecodeHunyuan3D", "VAEDecodeTiled", "VAEEncode", "VAEEncodeAudio", "VAEEncodeForInpaint", "VAEEncodeTiled", "VAELoader", "VAESave", "VPScheduler", "VeoVideoGenerationNode", "VideoLinearCFGGuidance", "VideoTriangleCFGGuidance", "VoxelToMesh", "VoxelToMeshBasic", "WanCamera<PERSON>dding", "WanCameraImageToVideo", "WanFirstLastFrameToVideo", "WanFunControlToVideo", "WanFunInpaintToVideo", "WanImageToVideo", "WanPhantomSubjectToVideo", "WanVaceToVideo", "WebcamCapture", "unCLIPCheckpointLoader", "unCLIPConditioning"], {"title_aux": "ComfyUI"}], "https://github.com/comfyanonymous/ComfyUI_bitsandbytes_NF4": [["CheckpointLoaderNF4"], {"title_aux": "ComfyUI_bitsandbytes_NF4 [EXPERIMENTAL]"}], "https://github.com/comfypod/ComfyUI-Comflow": [["ComflowInputBoolean", "ComflowInputCheckpoint", "ComflowInputImage", "ComflowInputImageAlpha", "ComflowInputImageBatch", "ComflowInputLora", "ComflowInputNumber", "ComflowInputNumberInt", "ComflowInputNumberSlider", "ComflowInputText", "ComflowInputVid", "ComflowInputVideo", "ComflowWebsocketImageInput", "ComflowWebsocketImageOutput"], {"description": "", "nickname": "Comflow", "title": "comflow", "title_aux": "ComfyUI-Comflow"}], "https://github.com/comfyuiblog/deepseek_prompt_generator_comfyui": [["DeepSeek_Prompt_Generator"], {"title_aux": "deepseek_prompt_generator_comfyui [WIP]"}], "https://github.com/corbin-hayden13/ComfyUI-Better-Dimensions": [["BetterImageDimensions", "PureRatio", "SDXLDimensions"], {"title_aux": "ComfyUI-Better-Dimensions"}], "https://github.com/cwebbi1/VoidCustomNodes": [["Prompt Parser", "String Combiner"], {"title_aux": "VoidCustomNodes"}], "https://github.com/cyberhirsch/seb_nodes": [["SaveImageSeb", "SwitchMasksSeb"], {"title_aux": "seb_nodes [WIP]"}], "https://github.com/daracazamea/comfyUI-DCNodes": [["FluxResolutionPicker", "GetGenerationTime", "ManualTrigger", "SDXLResolutionPicker", "StartTimerPassThrough"], {"title_aux": "DCNodess [WIP]"}], "https://github.com/denislov/Comfyui_AutoSurvey": [["AddDoc2Knowledge", "AutoSurvey", "ChatModel", "ComfyMilvus", "ComfyWeaviate", "ManageDatabase", "MilvusScheme", "<PERSON><PERSON><PERSON>", "QueryKnowledge", "WcProperty", "WcPropertyComb", "WriteOutline", "WriteSection"], {"title_aux": "Comfyui_AutoSurvey"}], "https://github.com/dfl/comfyui-stylegan": [["BatchAverageStyleGANLatents", "BlendStyleGANLatents", "GenerateStyleGANLatent", "LoadStyleGAN", "LoadStyleGANLatentImg", "SaveStyleGANLatentImg", "StyleGANInversion", "StyleGANLatentFromBatch", "StyleGANSampler"], {"title_aux": "comfyui-stylegan"}], "https://github.com/dhpdong/ComfyUI-IPAdapter-Flux-Repair": [["SamplerCustomAdvancedPlus", "SeedPlus", "UnetLoaderGGUFPlus"], {"title_aux": "ComfyUI-IPAdapter-Flux-Repair"}], "https://github.com/dihan/comfyui-random-kps": [["RandomFaceKeypoints"], {"title_aux": "ComfyUI Random Keypoints for InstantID [WIP]"}], "https://github.com/dogcomplex/ComfyUI-LOKI": [["EvaluateRelevanceLLM", "FilterNodesLLM", "LLMQueryAPI", "LLMQueryAPIBatch", "LokiGlamour", "LokiListAvailableNodes", "LokiListInstalledNodes", "LokiTweetScraper", "LokiTweetThreadScraper", "LokiTweetUserScraper", "TwitterScraper", "TwitterThreadScraper", "TwitterUserScraper", "✍️Scribe (LOKI)"], {"title_aux": "ComfyUI-LOKI [WIP]"}], "https://github.com/doucx/ComfyUI_WcpD_Utility_Kit": [["BlackImage", "CopyImage(Wayland)", "ExecStrAsCode", "MergeStrings", "YamlToPrompt"], {"title_aux": "ComfyUI_WcpD_Utility_Kit"}], "https://github.com/dowands/ComfyUI-AddMaskForICLora": [["AddMaskForICLora"], {"title_aux": "AddMaskForICLora"}], "https://github.com/downlifted/ComfyUI_BWiZ_Nodes": [["BWIZInteractiveLogMonitor", "BWIZ_AdvancedLoadImageBatch", "BWIZ_CaptainWeb<PERSON>ok", "BWIZ_ComfyEmail", "BWIZ_ErrorDetector", "BWIZ_HFRepoBatchLoader", "BWIZ_NotificationSound"], {"title_aux": "ComfyUI_BWiZ_Nodes [WIP]"}], "https://github.com/eigenpunk/ComfyUI-audio": [["ApplyVoiceFixer", "BatchAudio", "BlendAudio", "ClipAudioRegion", "CombineImageWithAudio", "ConcatAudio", "ConvertAudio", "FilterAudio", "FlattenAudioBatch", "HifiGANApply", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "HifiGANModelParams", "InvertAudioPhase", "LoadAudio", "MusicgenGenerate", "MusicgenHFGenerate", "MusicgenHFLoader", "MusicgenLoader", "NormalizeAudio", "PreviewAudio", "ResampleAudio", "SaveAudio", "SpectrogramImage", "Tacotron2Generate", "Tacotron2Loader", "ToMelSpectrogram", "TortoiseTTSGenerate", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "TrimAudio", "TrimAudioSamples", "TrimSilence", "VALLEXGenerator", "VALLEXLoader", "VALLEXVoicePromptFromAudio", "VALLEXVoicePromptLoader", "WaveGlowApply", "WaveGlowLoader"], {"title_aux": "ComfyUI-audio"}], "https://github.com/ejektaflex/ComfyUI-Ty": [["Lora Block Weight Regex Loader // Ty"], {"title_aux": "ComfyUI-Ty"}], "https://github.com/emranemran/ComfyUI-FasterLivePortrait": [["FasterLivePortraitProcess", "LoadFasterLivePortraitModels"], {"title_aux": "ComfyUI-FasterLivePortrait"}], "https://github.com/endman100/ComfyUI-SaveAndLoadPromptCondition": [["LoadContditioning", "SaveConditioning"], {"title_aux": "ComfyUI Nodes: SaveConditioning and LoadConditioning"}], "https://github.com/endman100/ComfyUI-augmentation": [["RamdomFlipImage (endman100)"], {"title_aux": "ComfyUI-augmentation"}], "https://github.com/ericbeyer/guidance_interval": [["Guidance Interval"], {"title_aux": "guidance_interval"}], "https://github.com/erosDiffusion/ComfyUI-enricos-json-file-load-and-value-selector": [["SelectorNode"], {"title_aux": "Select key from JSON (Alpha) [UNSAFE]"}], "https://github.com/esciron/ComfyUI-HunyuanVideoWrapper-Extended": [["DownloadAndLoadHyVideoTextEncoder", "HyVideoBlockSwap", "HyVideoDecode", "HyVideoEncode", "HyVideoModelLoader", "HyVideoSTG", "HyVideoSampler", "HyVideoTextEncode", "HyVideoTorchCompileSettings", "HyVideoVAELoader"], {"title_aux": "ComfyUI-HunyuanVideoWrapper-Extended [WIP]"}], "https://github.com/exectails/comfyui-et_scripting": [["ETPythonTextScript3Node"], {"title_aux": "Scripting"}], "https://github.com/eyekayem/comfyui_runway_gen3": [["RunwayVideoGenerator", "RunwayVideoPreview"], {"title_aux": "comfyui_runway_gen3"}], "https://github.com/fablestudio/ComfyUI-Showrunner-Utils": [["AlignFace", "Alpha Crop and Position Image", "GenerateTimestamp", "GetMostCommonColors", "ImageCompositeAbsoluteByContainer", "LoadImageFromUrl", "OpenAI Image 2 Text", "PadMask", "ReadImage", "Shrink Image"], {"title_aux": "ComfyUI-Showrunner-U<PERSON><PERSON>"}], "https://github.com/facok/ComfyUI-FokToolset": [["Fok_PreprocessRefImage"], {"title_aux": "ComfyUI-FokToolset"}], "https://github.com/fangg2000/ComfyUI-SenseVoice": [["STTNode", "ShowTextNode", "VoiceRecorderNode"], {"title_aux": "ComfyUI-SenseVoice [WIP]"}], "https://github.com/fangg2000/ComfyUI-StableAudioFG": [["LoadStableAudioModel", "StableAudioFG"], {"author": "lks-ai", "description": "A Simple integration of Stable Audio Diffusion with knobs and stuff!", "nickname": "stableaudio", "title": "StableAudioSampler", "title_aux": "ComfyUI-StableAudioFG [WIP]"}], "https://github.com/fangziheng2321/comfyuinode_chopmask": [["cus_chopmask"], {"title_aux": "comfyuinode_chopmask [WIP]"}], "https://github.com/filipemeneses/ComfyUI_html": [["HtmlDownload", "HtmlPreview", "LoadHtml", "SaveHtml", "SingleImageToBase64", "SingleImageToBase64KeepMetadata"], {"title_aux": "ComfyUI_html [UNSAFE]"}], "https://github.com/flowtyone/comfyui-flowty-lcm": [["LCMSampler"], {"title_aux": "comfyui-flowty-lcm"}], "https://github.com/flyingdogsoftware/gyre_for_comfyui": [["Background<PERSON><PERSON><PERSON><PERSON>", "GyreIfElse", "GyreLoopEnd", "GyreLoopStart"], {"title_aux": "<PERSON>yre for ComfyUI"}], "https://github.com/foglerek/comfyui-cem-tools": [["ProcessImageBatch"], {"title_aux": "comfyui-cem-tools"}], "https://github.com/franky519/comfyui-redux-style": [["StyleModelAdvanced", "StyleModelApplySimple", "StyleModelConditioner", "StyleModelGridVisualizer"], {"title_aux": "comfyui-redux-style"}], "https://github.com/franky519/comfyui_fnckc_Face_analysis": [["FaceAnalysisModels", "FaceFourImageMatcher"], {"title_aux": "ComfyUI Face Four Image Matcher [WIP]"}], "https://github.com/fritzprix/ComfyUI-LLM-Utils": [["WeightedDict", "WeightedDictConcat", "WeightedDictInput", "WeightedDictSelect", "WeightedDictSelectGroup", "WeightedDictToPrompt"], {"title_aux": "ComfyUI-LLM-Utils [WIP]"}], "https://github.com/ftechmax/ComfyUI-NovaKit-Pack": [["CountTokens"], {"title_aux": "ComfyUI-NovaKit-Pack"}], "https://github.com/ftf001-tech/ComfyUI-ExternalLLMDetector": [["ExternalLLMDetectorBboxesConvert", "ExternalLLMDetectorMainProcess", "ExternalLLMDetectorSettings"], {"title_aux": "ComfyUI-Lucian [WIP]"}], "https://github.com/gabe-init/ComfyUI-LM-Studio": [["LMStudioNode"], {"title_aux": "ComfyUI LM Studio Node [WIP]"}], "https://github.com/gabe-init/ComfyUI-Repo-Eater": [["RepoEaterNode"], {"title_aux": "gabe-init [WIP]"}], "https://github.com/gabe-init/comfyui_ui_render": [["HTMLRendererNode"], {"title_aux": "comfyui_ui_render [UNSAFE]"}], "https://github.com/gagaprince/ComfyUI_gaga_utils": [["GagaAddStringArray", "GagaBatchStringReplace", "GagaGetDirList", "GagaGetFileList", "GagaGetImageInfoByUpload", "GagaGetImageInfoWithUrl", "GagaGetImageWithPath", "GagaGetStringArrayByIndex", "GagaGetStringArraySize", "GagaGetStringListSize", "GagaPythonScript", "GagaSaveImageToPath", "GagaSaveImageWithInfo", "GagaSaveImagesToGif", "GagaSplitStringToList", "GagaStringListToArray", "GagaTest"], {"title_aux": "ComfyUI_gaga_utils"}], "https://github.com/galoreware/ComfyUI-GaloreNodes": [["GNI_HEX_TO_COLOR", "GNI_RGB_TO_COLOR", "GN_COLOR_TO_INT", "GN_IO_GET_FILENAME", "GN_MASK_TO_IMAGE", "GN_SNAP_RESIZE"], {"title_aux": "ComfyUI-GaloreNodes [WIP]"}], "https://github.com/gameltb/ComfyUI_stable_fast": [["ApplyStableFastUnet", "ApplyTensorRTControlNet", "ApplyTensorRTUnet", "ApplyTensorRTVaeDecoder"], {"title_aux": "ComfyUI_stable_fast"}], "https://github.com/gamtruliar/ComfyUI-N_SwapInput": [["N_SwapInput"], {"title_aux": "ComfyUI-N_SwapInput [UNSAFE]"}], "https://github.com/gilons/ComfyUI-GoogleDrive-Downloader": [["custom_nodes"], {"title_aux": "ComfyUI-GoogleDrive-Downloader [UNSAFE]"}], "https://github.com/gitadmini/comfyui_extractstoryboards": [["Example", "ExtractStoryboards_xuhuan1024", "IntBatchSize_xuhuan1024", "IntBatch_xuhuan1024"], {"title_aux": "ExtractStoryboards [WIP]"}], "https://github.com/githubYiheng/comfyui_median_filter": [["ImageMedianFilter"], {"title_aux": "comfyui_median_filter"}], "https://github.com/gmorks/ComfyUI-Animagine-Prompt": [["AnimaginePrompt", "MultilineTextInput", "TextFileLoader"], {"title_aux": "ComfyUI Animagine prompt [WIP]"}], "https://github.com/go-package-lab/ComfyUI-Tools-Video-Combine": [["Tools:CopyFile", "Tools:Image2video", "Tools:LoadAudioUrl", "Tools:PreviewVideo", "Tools:SetString", "Tools:SetV<PERSON>ue", "Tools:VideoWatermark"], {"title_aux": "ComfyUI-Tools-Video-Combine [WIP]"}], "https://github.com/godric8/ComfyUI_Step1X-Edit": [["Step1XEdit"], {"title_aux": "ComfyUI_Step1X-Edit [NAME CONFLICT]"}], "https://github.com/gold24park/loki-comfyui-node": [["Base64ToImage", "DominantColor", "ImageLuminance", "ImageToBase64", "OverlayText"], {"title_aux": "loki-comfyui-node"}], "https://github.com/gondar-software/ComfyUI-Affine-Transform": [["AffineTransform"], {"title_aux": "Affine Transform ComfyUI Node [WIP]"}], "https://github.com/gondar-software/ComfyUI-Simple-Image-Tools": [["GetMaskFromAlpha", "GetQuadrilateralOutfit"], {"title_aux": "ComfyUI-Simple-Image-Tools [WIP]"}], "https://github.com/gordon123/ComfyUI_DreamBoard": [["PromptExtraNode", "StoryboardNode"], {"title_aux": "ComfyUI_DreamBoard [WIP]"}], "https://github.com/gordon123/ComfyUI_srt2speech": [["GetSubtitleByIndex", "MergeAllWave", "SaveWavNode"], {"title_aux": "ComfyUI_srt2speech [WIP]"}], "https://github.com/grimli333/ComfyUI_Grim": [["GenerateFileName", "TwoStringsFormat"], {"title_aux": "ComfyUI_Grim"}], "https://github.com/grinlau18/ComfyUI_XISER_Nodes": [["CreatePointsString", "XISER_Canvas", "XIS_CanvasMaskProcessor", "XIS_CompositorProcessor", "XIS_CropImage", "XIS_DynamicBatchKSampler", "XIS_Float_Slider", "XIS_FromListGet1Color", "XIS_FromListGet1Cond", "XIS_FromListGet1Float", "XIS_FromListGet1Image", "XIS_FromListGet1Int", "XIS_FromListGet1Latent", "XIS_FromListGet1Mask", "XIS_FromListGet1Model", "XIS_FromListGet1String", "XIS_INT_Slider", "XIS_IPAStyleSettings", "XIS_IfDataIsNone", "XIS_ImageManager", "XIS_ImageMaskMirror", "XIS_ImageStitcher", "XIS_InvertMask", "XIS_IsThereAnyData", "XIS_KSamplerSettingsNode", "XIS_KSamplerSettingsUnpackNode", "XIS_Label", "XIS_LatentBlendNode", "XIS_LoadImage", "XIS_MaskBatchProcessor", "XIS_MaskCompositeOperation", "XIS_MergePackImages", "XIS_MultiPromptSwitch", "XIS_PSDLayerExtractor", "XIS_PackImages", "XIS_PromptProcessor", "XIS_PromptsWithSwitches", "XIS_ReorderImageMaskGroups", "XIS_ReorderImages", "XIS_ResizeImageOrMask", "XIS_ResizeToDivisible", "XIS_ResolutionSelector"], {"title_aux": "Xiser_Nodes [WIP]"}], "https://github.com/grokuku/ComfyUI-Holaf": [["HolafBenchmarkLoader", "HolafBenchmarkPlotter", "HolafBenchmarkRunner", "HolafImageComparer", "HolafInstagramResize", "<PERSON><PERSON><PERSON><PERSON>", "HolafLutApplier", "HolafLutGenerator", "HolafLutLoader", "HolafLutSaver", "HolafMaskToBoolean", "HolafOverlayNode", "HolafResolutionPreset", "HolafSaveImage", "HolafSliceCalculator", "HolafTileCalculator", "HolafTiledKSampler", "UpscaleImageHolaf"], {"title_aux": "Holaf Custom Nodes for ComfyUI"}], "https://github.com/haodman/ComfyUI_Rain": [["Rain_ImageSize", "Rain_IntToFloat", "Rain_Math", "Rain_ValueSwitch"], {"title_aux": "ComfyUI_Rain"}], "https://github.com/haofanwang/ComfyUI-InstantStyle": [["BaseModelLoader", "InstantStyleGenerationNode", "InstantStyleLoader", "Pro<PERSON><PERSON><PERSON><PERSON>"], {"title_aux": "ComfyUI-InstantStyle"}], "https://github.com/haomole/Comfyui-SadTalker": [["LoadRefVideo", "<PERSON><PERSON><PERSON><PERSON>", "ShowAudio", "ShowText", "ShowVideo"], {"title_aux": "Comfyui-<PERSON><PERSON><PERSON><PERSON>"}], "https://github.com/hay86/ComfyUI_AceNodes": [["ACE_AnyInputSwitchBool", "ACE_AnyInputToAny", "ACE_AudioCrop", "ACE_AudioLoad", "ACE_AudioPlay", "ACE_AudioSave", "ACE_Expression_Eval", "ACE_Float", "ACE_ImageColorFix", "ACE_ImageConstrain", "ACE_ImageFaceCrop", "ACE_ImageGetSize", "ACE_ImageLoadFromCloud", "ACE_ImageMakeSlideshow", "ACE_ImagePixelate", "ACE_ImageQA", "ACE_ImageRemoveBackground", "ACE_ImageSaveToCloud", "ACE_Integer", "ACE_MaskBlur", "ACE_OpenAI_GPT_Chat", "ACE_OpenAI_GPT_IMAGE", "ACE_OpenAI_GPT_TTS", "ACE_Seed", "ACE_Text", "ACE_TextConcatenate", "ACE_TextGoogleTranslate", "ACE_TextInputSwitch2Way", "ACE_TextInputSwitch4Way", "ACE_TextInputSwitch8Way", "ACE_TextList", "ACE_TextLoad", "ACE_TextPreview", "ACE_TextSave", "ACE_TextSelector", "ACE_TextToResolution", "ACE_TextTranslate", "ACE_VideoConcat", "ACE_VideoLoad", "ACE_VideoPreview"], {"title_aux": "ComfyUI AceNodes [UNSAFE]"}], "https://github.com/hdfhssg/ComfyUI_pxtool": [["<PERSON><PERSON><PERSON><PERSON>", "CivitaiHelper", "DanbooruCharacterTag", "E621CharacterTag", "NegativeTag", "PX_Seed", "QualityTag", "RandomArtists", "RandomArtistsAdvanced", "RandomTag"], {"title_aux": "ComfyUI_pxtool [WIP]"}], "https://github.com/hdfhssg/comfyui_EvoSearch": [["EvoSearch_FLUX", "EvoSearch_SD21", "EvoSearch_WAN", "EvolutionScheduleGenerator", "GuidanceRewardsGenerator"], {"title_aux": "comfyui_EvoSearch [WIP]"}], "https://github.com/hiusdev/ComfyUI_Lah_Toffee": [["LoadVideoRandom"], {"title_aux": "ComfyUI_Lah_Toffee"}], "https://github.com/hnmr293/ComfyUI-SamOne": [["Latent", "SamplerOne"], {"title_aux": "ComfyUI-SamOne - one-step sampling"}], "https://github.com/horidream/ComfyUI-Horidream": [["PassThroughWithSound"], {"title_aux": "ComfyUI-Horidream"}], "https://github.com/hotpizzatactics/ComfyUI-WaterMark-Detector": [["AdaptiveThresholding", "AdvancedWatermarkEnhancement", "AdvancedWaveletWatermarkEnhancement", "CLAHEEnhancement", "CombineEnhancements", "ComprehensiveImageEnhancement", "Denoising<PERSON><PERSON>er", "EdgeDetection", "FlexibleCombineEnhancements", "HighPassFilter", "ImprovedGrayColorEnhancement", "MorphologicalOperations", "TextureEnhancement", "WatermarkEnhancement"], {"title_aux": "ComfyUI-WaterMark-Detector"}], "https://github.com/hotpot-killer/ComfyUI_AlexNodes": [["InstructPG"], {"title_aux": "ComfyUI_AlexNodes"}], "https://github.com/houdinii/comfy-magick": [["AdaptiveBlur", "AdaptiveSharpen", "AddNoise", "BlueShift", "Blur", "Charc<PERSON>l", "Colorize", "CropByAspectRatio", "<PERSON><PERSON><PERSON>", "Edge", "<PERSON><PERSON><PERSON>", "FX", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Implode", "<PERSON><PERSON><PERSON>", "MotionBlur", "RotationalBlur", "SelectiveBlur", "Sepia", "Shade", "Sharpen", "Sketch", "Solarize", "Spread", "Stereogram", "Swirl", "<PERSON><PERSON>", "UnsharpMask", "Vignette", "WaveletDenoise"], {"title_aux": "comfy-magick [WIP]"}], "https://github.com/huizhang0110/ComfyUI_Easy_Nodes_hui": [["EasyBgRemover", "EasyBgRemover_ModelLoader", "EasyControlNetApply", "EasyControlNetLoader", "EasyEmptyLatentImage", "EasyLatentToCondition", "EasyLoadImage"], {"title_aux": "ComfyUI_Easy_Nodes_hui"}], "https://github.com/hunterssl/ComfyUI_SSLNodes": [["SSLGetJsonKeysCount", "SSLLoadCheckpointByName", "SSLLoad<PERSON>son", "SSLRandomNumInLoop", "SSLRandomSeedInLoop", "SSLSaveImageOutside"], {"title_aux": "ComfyUI_SSLNodes"}], "https://github.com/hunzmusic/ComfyUI-Hunyuan3DTools": [["Hy3DTools_BackProjectInpaint", "Hy3DTools_RenderSpecificView"], {"title_aux": "ComfyUI-Hunyuan3DTools [WIP]"}], "https://github.com/hunzmusic/Comfyui-CraftsMan3DWrapper": [["CraftsManDoraVAEGenerator", "DecodeCraftsManLatents", "LoadCraftsManPipeline", "PreprocessImageCraftsMan", "SampleCraftsManLatents", "SaveCraftsManMesh"], {"title_aux": "Comfyui-CraftsMan3DWrapper [WIP]"}], "https://github.com/hunzmusic/comfyui-hnznodes": [["CombineChannelsGrayscale", "ImageBatchReorder", "MaleCharacterPromptGenerator"], {"title_aux": "comfyui-h<PERSON>nodes"}], "https://github.com/hy134300/comfyui-hb-node": [["generate story", "hy save image", "latent to list", "movie batch", "movie generate", "sound voice", "text concat"], {"title_aux": "comfyui-hb-node"}], "https://github.com/hy134300/comfyui-hydit": [["DiffusersCLIPLoader", "DiffusersCheckpointLoader", "DiffusersClipTextEncode", "DiffusersControlNetLoader", "Diffusers<PERSON><PERSON><PERSON><PERSON><PERSON>", "DiffusersModelMakeup", "Diffusers<PERSON>ipeline<PERSON><PERSON>der", "DiffusersSampler", "DiffusersSchedulerLoader", "DiffusersVAELoader"], {"title_aux": "comfyui-hydit"}], "https://github.com/hylarucoder/comfyui-copilot": [["EagleImageNode", "SDXLPromptStyler", "SDXLPromptStylerAdvanced", "SDXLResolutionPresets"], {"title_aux": "comfyui-copilot"}], "https://github.com/iacoposk8/xor_pickle_nodes": [["Load XOR Pickle From File", "Save XOR Pickle To File"], {"title_aux": "ComfyUI XOR Pickle Nodes"}], "https://github.com/if-ai/ComfyUI-IF_Zonos": [["IF_ZonosTTS"], {"title_aux": "ComfyUI-IF_Zonos [WIP]"}], "https://github.com/ilovejohnwhite/Tracer": [["BillyGoatNode", "EcstaticNode", "HintImageEnchance", "Image Load TTK", "ImageGenResolutionFromImage", "ImageGenResolutionFromLatent", "KillMeNode", "LinkMasterNode", "OkayBuddyNode", "OutlineRealNode", "OutlineStandardNode", "PixelPerfectResolution", "SuckerPunch", "UWU_Preprocessor", "VooDooNode"], {"title_aux": "Ko<PERSON><PERSON> A<PERSON> Prompts [WIP]"}], "https://github.com/immersiveexperience/ie-comfyui-color-nodes": [["Average Color", "Complementary Color", "Hex Color to Image", "Hex to Color Name", "Random String"], {"title_aux": "ie-comfyui-color-nodes"}], "https://github.com/io-club/ComfyUI-LuminaNext": [["<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"], {"title_aux": "ComfyUI-LuminaNext [WIP]"}], "https://github.com/jammyfu/ComfyUI_PaintingCoderUtils": [["PaintingCoder::DynamicImageCombiner", "PaintingCoder::DynamicMaskCombiner", "PaintingCoder::ImageLatentCreator", "PaintingCoder::ImageLatentCreatorPlus", "PaintingCoder::ImageResolutionAdjuster", "PaintingCoder::ImageSizeCreator", "PaintingCoder::ImageSizeCreatorPlus", "PaintingCoder::ImageSwitch", "PaintingCoder::ImageToBase64", "PaintingCoder::LatentSwitch", "PaintingCoder::MaskPreview", "PaintingCoder::MaskSwitch", "PaintingCoder::MultilineTextInput", "PaintingCoder::OutputToTextConverter", "PaintingCoder::RemoveEmptyLinesAndLeadingSpaces", "PaintingCoder::ShowTextPlus", "PaintingCoder::SimpleTextInput", "PaintingCoder::<PERSON><PERSON><PERSON><PERSON>", "PaintingCoder::TextSwitch", "PaintingCoder::WebImageLoader"], {"title_aux": "ComfyUI PaintingCoderUtils Nodes [WIP]"}], "https://github.com/jax-explorer/ComfyUI-DreamO": [["BgRmModelLoad", "DreamOGenerate", "DreamOLoadModel", "DreamOLoadModelFromLocal", "FaceModelLoad"], {"title_aux": "ComfyUI-DreamO"}], "https://github.com/jcomeme/ComfyUI-AsunaroTools": [["AsunaroAnd", "AsunaroAutomaticSexPrompter", "AsunaroBatchImageLoader", "AsunaroIfBiggerThanZero", "AsunaroIfContain", "AsunaroIfSame", "AsunaroImageLoader", "AsunaroIntToStr", "AsunaroOr", "AsunaroPromptStripper", "AsunaroRandomDice", "AsunaroResolutions", "<PERSON><PERSON>roSave", "AsunaroTextConcatenator", "AsunaroWildCard"], {"title_aux": "AsunaroTools"}], "https://github.com/jerryname2022/ComfyUI-Real-ESRGAN": [["GFPGANImageGenerator", "GFPGANModelLoader", "RealESRGANImageGenerator", "RealESRGANModelLoader"], {"title_aux": "ComfyUI-Real-ESRGAN [WIP]"}], "https://github.com/jgbrblmd/ComfyUI-ComfyFluxSize": [["ComfyFluxSize"], {"title_aux": "ComfyUI-ComfyFluxSize [WIP]"}], "https://github.com/jgbyte/ComfyUI-RandomCube": [["RandomCubeGrid"], {"title_aux": "ComfyUI-RandomCube [WIP]"}], "https://github.com/jiafuzeng/comfyui-fishSpeech": [["FishSpeechAudioPreview", "FishSpeechLoader", "FishSpeechTTS"], {"title_aux": "comfyui-fishSpeech"}], "https://github.com/jimmm-ai/TimeUi-a-ComfyUi-Timeline-Node": [["jimmm.ai.TimelineUI"], {"title_aux": "TimeUi a ComfyUI Timeline Node System [WIP]"}], "https://github.com/jimstudt/ComfyUI-Jims-Nodes": [["ChooseFromStringList", "<PERSON><PERSON><PERSON>", "DefineWord", "DictFromJSON", "DictionaryToJSON", "ImageToSolidBackground", "JSONToDictionary", "LiftFromBackground", "LoadImageAndInfoFromPath", "LookupWord", "<PERSON>lace<PERSON><PERSON><PERSON>", "TextToStringList", "ZoomFocus"], {"title_aux": "Jim's ComfyUI Nodes [WIP]"}], "https://github.com/jinchanz/ComfyUI-AliCloud-Bailian": [["BailianAPI", "BailianAPIPoll", "BailianAPISubmit", "MaletteJSONExtractor", "MaletteJSONModifier"], {"title_aux": "ComfyUI-AliCloud-Bailian [WIP]"}], "https://github.com/jn-jairo/jn_node_suite_comfyui": [["JN_AreaInfo", "JN_AreaNormalize", "JN_AreaWidthHeight", "JN_AreaXY", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "JN_BooleanOperation", "JN_Condition", "JN_CoolDown", "JN_CoolDownOutput", "JN_CropFace", "JN_DatetimeFormat", "JN_DatetimeInfo", "JN_DatetimeNow", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "JN_DumpOutput", "JN_FaceRestoreModelLoader", "JN_FaceRestoreWithModel", "JN_FirstActive", "JN_ImageAddMask", "JN_ImageBatch", "JN_ImageCenterArea", "JN_ImageCrop", "JN_ImageGrid", "JN_ImageInfo", "JN_ImageSharpness", "JN_ImageSquare", "JN_ImageUncrop", "<PERSON><PERSON><PERSON>", "JN_KSamplerAdvancedParams", "JN_KSamplerFaceRestoreParams", "JN_KSamplerResizeInputParams", "JN_KSamplerResizeMaskAreaParams", "JN_KSamplerResizeOutputParams", "JN_KSamplerSeamlessParams", "JN_KSamplerTileParams", "JN_LoadImageDirectory", "JN_LogicOperation", "JN_MaskInfo", "JN_MathOperation", "JN_MathOperationArray", "JN_PrimitiveArrayInfo", "JN_PrimitiveBatchToArray", "JN_PrimitiveBoolean", "JN_PrimitiveFloat", "JN_PrimitiveInt", "JN_PrimitivePrompt", "JN_PrimitiveString", "JN_PrimitiveStringMultiline", "JN_PrimitiveStringToArray", "JN_PrimitiveToArray", "JN_PrimitiveToBoolean", "JN_PrimitiveToFloat", "JN_PrimitiveToInt", "JN_PrimitiveToString", "JN_RemoveBackground", "J<PERSON>_Seamless", "JN_SeamlessBorder", "JN_SeamlessBorderCrop", "JN_SelectItem", "JN_<PERSON>", "JN_SleepOutput", "JN_SliceOperation", "JN_StopIf", "JN_StopIfOutput", "JN_TextConcatenation", "J<PERSON>_<PERSON><PERSON><PERSON>lace", "JN_TimedeltaFormat", "JN_TimedeltaInfo", "JN_VAEPatch"], {"title_aux": "jn_node_suite_comfyui [WIP]"}], "https://github.com/jordancoult/ComfyUI_HelpfulNodes": [["JCo_CropAroundKPS"], {"title_aux": "ComfyUI_HelpfulNodes"}], "https://github.com/jschoormans/Comfy-InterestingPixels": [["Random Palette", "Shareable Image Slider"], {"title_aux": "ComfyUI-TexturePacker [WIP]"}], "https://github.com/jtscmw01/ComfyUI-DiffBIR": [["DiffBIR_sample", "DiffBIR_sample_advanced", "Simple_load", "Stage1_load", "Stage2_load"], {"title_aux": "ComfyUI-DiffBIR"}], "https://github.com/jtydhr88/ComfyUI-Unique3D": [["Unique3DLoadPipeline", "Unique3DRun - Geo Reconstruct", "Unique3DRun - MVPrediction"], {"title_aux": "ComfyUI-Unique3D [WIP]"}], "https://github.com/jtydhr88/ComfyUI_frontend_vue_basic": [["vue-basic"], {"title_aux": "ComfyUI Frontend Vue Basic [WIP]"}], "https://github.com/kadirnar/ComfyUI-Adapter": [["GarmentSeg<PERSON><PERSON>der"], {"title_aux": "ComfyUI-Adapter [WIP]"}], "https://github.com/kandy/ComfyUI-KAndy": [["KAndyLoadImageFromUrl", "KAndyTaggerModelLoader", "KAndyWD14Tagger", "KPromtGen", "KandySimplePrompt"], {"title_aux": "ComfyUI-KAndy"}], "https://github.com/kappa54m/ComfyUI_Usability": [["KLoadImageByPath", "KLoadImageByPathAdvanced", "KLoadImageDedup"], {"title_aux": "ComfyUI_Usability (WIP)"}], "https://github.com/karthikg-09/ComfyUI-3ncrypt": [["Enhanced Save Image", "Markdown Editor"], {"title_aux": "ComfyUI-KG09 [WIP]"}], "https://github.com/kevin314/ComfyUI-FastVideo": [["DITConfig", "InferenceArgs", "LoadImagePath", "TextEncoderConfig", "VAEConfig", "VideoGenerator"], {"title_aux": "ComfyUI-FastVideo"}], "https://github.com/kijai/ComfyUI-CV-VAE": [["CV_VAE_Decode", "CV_VAE_Encode", "CV_VAE_Load"], {"title_aux": "ComfyUI-CV-VAE"}], "https://github.com/kijai/ComfyUI-DeepSeek-VL": [["deepseek_vl_inference", "deepseek_vl_model_loader"], {"title_aux": "ComfyUI nodes to use DeepSeek-VL"}], "https://github.com/kijai/ComfyUI-DiffSynthWrapper": [["DiffSynthSampler", "DownloadAndLoadDiffSynthExVideoSVD"], {"title_aux": "ComfyUI DiffSynth wrapper nodes"}], "https://github.com/kijai/ComfyUI-DiffusersSD3Wrapper": [["LoadSD3DiffusersPipeline", "SD3ControlNetSampler"], {"title_aux": "ComfyUI-DiffusersSD3Wrapper"}], "https://github.com/kijai/ComfyUI-EasyAnimateWrapper": [["DownloadAndLoadEasyAnimateModel", "EasyAnimateDecode", "EasyAnimateImageEncoder", "EasyAnimateResize", "EasyAnimateSampler", "EasyAnimateTextEncode"], {"title_aux": "ComfyUI-EasyAnimateWrapper [WIP]"}], "https://github.com/kijai/ComfyUI-FollowYourEmojiWrapper": [["DownloadAndLoadFYEModel", "FYECLIPEncode", "FYEClipEmbedToComfy", "FYELandmarkEncode", "FYELandmarkToComfy", "FYEMediaPipe", "FYESampler", "FYESamplerLong"], {"title_aux": "ComfyUI-FollowYourEmojiWrapper [WIP]"}], "https://github.com/kijai/ComfyUI-FramePackWrapper": [["DownloadAndLoadFramePackModel", "FramePackFindNearestBucket", "FramePackLoraSelect", "FramePackSampler", "FramePackSingleFrameSampler", "FramePackTorchCompileSettings", "LoadFramePackModel"], {"title_aux": "ComfyUI-FramePackWrapper [WIP]"}], "https://github.com/kijai/ComfyUI-Hunyuan3DWrapper": [["CV2InpaintTexture", "DownloadAndLoadHy3DDelightModel", "DownloadAndLoadHy3DPaintModel", "Hy3DApplyTexture", "Hy3DBPT", "Hy3DBakeFromMultiview", "Hy3DCameraConfig", "Hy3DDelightImage", "Hy3DDiffusersSchedulerConfig", "Hy3DExportMesh", "Hy3DFastSimplifyMesh", "Hy3DGenerateMesh", "Hy3DGenerateMeshMultiView", "Hy3DGetMeshPBRTextures", "Hy3<PERSON><PERSON><PERSON><PERSON>", "Hy3DLoadMesh", "Hy3DMeshInfo", "Hy3DMeshUVWrap", "Hy3DMeshVerticeInpaintTexture", "Hy3DModelLoader", "Hy3DNvdiffrastRenderer", "Hy3DPostprocessMesh", "Hy3DRenderMultiView", "Hy3DRenderMultiViewDepth", "Hy3DRenderSingleView", "Hy3DSampleMultiView", "Hy3DSetMeshPBRAttributes", "Hy3DSetMeshPBRTextures", "Hy3DTorchCompileSettings", "Hy3DUploadMesh", "Hy3DVAEDecode", "Hy3DVAELoader", "Hy3D_2_1SimpleMeshGen", "MESHToTrimesh", "TrimeshToMESH"], {"title_aux": "ComfyUI-ComfyUI-Hunyuan3DWrapper [WIP]"}], "https://github.com/kijai/ComfyUI-HunyuanVideoWrapper": [["DownloadAndLoadHyVideoTextEncoder", "HunyuanVideoFresca", "HunyuanVideoSLG", "HyVideoBlockSwap", "HyVideoCFG", "HyVideoContextOptions", "HyVideoCustomPromptTemplate", "HyVideoDecode", "HyVideoEmptyTextEmbeds", "HyVideoEncode", "HyVideoEncodeKeyframes", "HyVideoEnhanceAVideo", "HyVideoGetClosestBucketSize", "HyVideoI2VEncode", "HyVideoInverseSampler", "HyVideoLatentPreview", "HyVideoLoopArgs", "HyVideoLoraBlockEdit", "HyVideoLoraSelect", "HyVideoModelLoader", "HyVideoPromptMixSampler", "HyVideoReSampler", "HyVideoSTG", "HyVideoSampler", "HyVideoTeaCache", "HyVideoTextEmbedBridge", "HyVideoTextEmbedsLoad", "HyVideoTextEmbedsSave", "HyVideoTextEncode", "HyVideoTextImageEncode", "HyVideoTorchCompileSettings", "HyVideoVAELoader"], {"title_aux": "ComfyUI-HunyuanVideoWrapper [WIP]"}], "https://github.com/kijai/ComfyUI-MMAudio": [["MMAudioFeatureUtilsLoader", "MMAudioModelLoader", "MMAudioSampler", "MMAudioVoCoderLoader"], {"title_aux": "ComfyUI-MMAudio"}], "https://github.com/kijai/ComfyUI-MochiWrapper": [["DownloadAndLoadMochiModel", "MochiDecode", "MochiDecodeSpatialTiling", "MochiFasterCache", "MochiImageEncode", "MochiLatentPreview", "<PERSON>chi<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "MochiSampler", "MochiSigmaSchedule", "MochiTextEncode", "MochiTorchCompileSettings", "MochiVAEEncoderLoader", "Mochi<PERSON><PERSON><PERSON>der"], {"title_aux": "ComfyUI-MochiWrapper [WIP]"}], "https://github.com/kijai/ComfyUI-VEnhancer": [["DownloadAndLoadVEnhancerModel", "VEnhancerSampler", "VEnhancerUnpad"], {"title_aux": "ComfyUI nodes for VEnhancer [WIP]"}], "https://github.com/kijai/ComfyUI-VideoNoiseWarp": [["GetWarpedNoiseFromVideo", "GetWarpedNoiseFromVideoAnimateDiff", "GetWarpedNoiseFromVideoCogVideoX", "GetWarpedNoiseFromVideoHunyuan"], {"title_aux": "ComfyUI-VideoNoiseWarp [WIP]"}], "https://github.com/kijai/ComfyUI-WanVideoWrapper": [["CreateCFGScheduleFloatList", "DownloadAndLoadWav2VecModel", "FantasyTalkingModelLoader", "FantasyTalkingWav2VecEmbeds", "LoadWanVideoClipTextEncoder", "LoadWanVideoT5TextEncoder", "ReCamMasterPoseVisualizer", "WanVideoATITracks", "WanVideoATITracksVisualize", "WanVideoATI_comfy", "WanVideoApplyNAG", "WanVideoBlockSwap", "WanVideoClipVisionEncode", "WanVideoContextOptions", "WanVideoControlEmbeds", "WanVideoControlnet", "WanVideoControlnetLoader", "WanVideoDecode", "WanVideoDiffusionForcingSampler", "WanVideoEmptyEmbeds", "WanVideoEncode", "WanVideoEnhanceAVideo", "WanVideoExperimentalArgs", "WanVideoFlowEdit", "WanVideoFunCameraEmbeds", "WanVideoImageClipEncode", "WanVideoImageResizeToClosest", "WanVideoImageToVideoEncode", "WanVideoLoopArgs", "WanVideoLoraBlockEdit", "WanVideoLoraSelect", "WanVideoLoraSelectMulti", "WanVideoMagCache", "WanVideoMiniMaxRemoverEmbeds", "WanVideoModelLoader", "WanVideoPhantomEmbeds", "WanVideoReCamMasterCameraEmbed", "WanVideoReCamMasterDefaultCamera", "WanVideoReCamMasterGenerateOrbitCamera", "WanVideoRealisDanceLatents", "WanVideoSLG", "WanVideoSampler", "WanVideoSetBlockSwap", "WanVideoTeaCache", "WanVideoTextEmbedBridge", "WanVideoTextEncode", "WanVideoTextEncodeSingle", "WanVideoTinyVAELoader", "WanVideoTorchCompileSettings", "WanVideoUni3C_ControlnetLoader", "WanVideoUni3C_embeds", "WanVideoUniAnimateDWPoseDetector", "WanVideoUniAnimatePoseInput", "WanVideoVACEEncode", "WanVideoVACEModelSelect", "WanVideoVACEStartToEndFrame", "WanVideoVAELoader", "WanVideoVRAMManagement"], {"title_aux": "ComfyUI-WanVideoWrapper [WIP]"}], "https://github.com/kimara-ai/ComfyUI-Kimara-AI-Advanced-Watermarks": [["KimaraAIBatchImages", "KimaraAIWatermarker"], {"title_aux": "Advanced Watermarking Tools [WIP]"}], "https://github.com/kimara-ai/ComfyUI-Kimara-AI-Image-From-URL": [["KimaraAIImageFromURL"], {"title_aux": "ComfyUI-Kimara-AI-Image-From-URL [WIP]"}], "https://github.com/kk8bit/KayTool": [["AB_Images", "AIO_Translater", "Abc_Math", "Baidu_Translater", "Color_Adjustment", "Custom_Save_Image", "Display_Any", "Image_Composer", "Image_Cropper", "Image_Mask_Composer", "Image_Resizer", "Image_Size_Extractor", "Kay_BiRefNet_Loader", "Load_Image_Folder", "Mask_Blur_Plus", "Mask_Filler", "Preview_Mask", "Preview_Mask_Plus", "RemBG_Loader", "Remove_BG", "Slider_10", "Slider_100", "Slider_1000", "Strong_Prompt", "<PERSON><PERSON>_Translater", "Text", "To_Int"], {"title_aux": "KayTool"}], "https://github.com/krich-cto/ComfyUI-Flow-Control": [["CLIPLoaderGGUF", "DualCLIPLoaderGGUF", "FlowCheckpointPresetLoader", "FlowClipCondition", "FlowClipTextEncode", "FlowConditioningAutoSwitch", "FlowFluxPresetLoader", "FlowGate", "FlowImageAutoBatch", "FlowImageCondition", "FlowKSampler", "FlowLatentAutoBatch", "FlowLatentCondition", "FlowLoraLoader", "FlowLoraLoaderModelOnly", "FlowModelManager", "FlowSaveImage", "QuadrupleCLIPLoaderGGUF", "TripleCLIPLoaderGGUF", "UnetLoaderGGUF", "UnetLoaderGGUFAdvanced"], {"title_aux": "ComfyUI Flow Control [UNSTABLE]"}], "https://github.com/krisshen2021/comfyui_OpenRouterNodes": [["OpenRouterOAINode_Infer", "OpenRouterOAINode_Models", "OpenRouterOAINode_hunyuanPrompt", "OpenRouterOAINode_txt2imgPrompt"], {"title_aux": "comfyui_OpenRouterNodes [WIP]"}], "https://github.com/kuschanow/ComfyUI-SD-Slicer": [["SdSlicer"], {"title_aux": "ComfyUI-SD-Slicer"}], "https://github.com/kxh/ComfyUI-ImageUpscaleWithModelMultipleTimes": [["ImageUpscaleWithModelMultipleTimes"], {"title_aux": "ComfyUI-ImageUpscaleWithModelMultipleTimes"}], "https://github.com/kxh/ComfyUI-sam2": [["Segment"], {"title_aux": "ComfyUI-sam2"}], "https://github.com/kycg/comfyui-Kwtoolset": [["KWImageResizeByLongerSide", "KWNagetiveString", "KWPositiveString", "KWShowAnything", "KWanywhereString", "KwtoolsetChangeOpenpose", "KwtoolsetCheckpointLoaderwithpreview", "KwtoolsetConditioningSelect", "KwtoolsetGetHipMask", "KwtoolsetGetHipMasktest", "KwtoolsetGetImageSize", "KwtoolsetGrowMaskPlus", "KwtoolsetImageSelect", "KwtoolsetLoadCheckpointsBatch", "KwtoolsetLoraLoaderwithpreview", "KwtoolsetMaskAdd", "KwtoolsetModelSelect", "LatentMatch"], {"title_aux": "comfyui-Kwtoolset"}], "https://github.com/kylegrover/comfyui-python-cowboy": [["PythonScript"], {"title_aux": "comfyui-python-cowboy [UNSAFE]"}], "https://github.com/laksjdjf/ssd-1b-comfyui": [["SSD-1B-Loader"], {"title_aux": "ssd-1b-comfyui"}], "https://github.com/leadbreak/comfyui-faceaging": [["AgeTransformationNode"], {"title_aux": "Face Aging [WIP]"}], "https://github.com/leeguandong/ComfyUI_AliControlnetInpainting": [["AliInpaintingsampler", "EcomXL_AddFG", "EcomXL_Condition", "EcomXL_Controlnet_ModelLoader", "EcomXL_LoadImage", "EcomXL_SDXL_Inpaint_ModelLoader", "Flux_Controlnet_ModelLoader", "Flux_Inpainting_ModelLoader", "SD3_Controlnet_ModelLoader", "SD3_Inpainting_ModelLoader"], {"title_aux": "ComfyUI_AliControlnetInpainting [WIP]"}], "https://github.com/leoleelxh/ComfyUI-MidjourneyNode-leoleexh": [["MidjourneyGenerateNode", "MidjourneyUpscaleNode"], {"title_aux": "ComfyUI-MidjourneyNode-leoleexh"}], "https://github.com/leon-etienne/ComfyUI_Scoring-Nodes": [["AestheticScore", "ImageSimilarity", "MultiAestheticScore", "MultiImageToTextSimilarity", "MultiTextToImageSimilarity", "TextSimilarity"], {"title_aux": "ComfyUI_Scoring-Nodes"}], "https://github.com/lgldlk/ComfyUI-img-tiler": [["PC ImageListTileMaker", "PC TileMaker", "PC TilerImage", "PC TilerSelect"], {"title_aux": "ComfyUI-img-tiler"}], "https://github.com/linhusyung/comfyui-Build-and-train-your-network": [["Conv_layer", "Normalization_layer", "activation_function", "create_dataset", "create_intput", "create_model", "create_training_task", "forward_test", "linear_layer", "pooling_layer", "pre_train_layer", "res_connect", "show_dimensions", "view_layer"], {"title_aux": "ComfyUI Build and Train Your Network [WIP]"}], "https://github.com/littleowl/ComfyUI-MV-HECV": [["CombineSideBySide", "DepthResize", "StereoShift_Fast"], {"title_aux": "ComfyUI-MV-HECV"}], "https://github.com/logtd/ComfyUI-Fluxtapoz": [["AddFluxFlow", "ApplyFluxRaveAttention", "ApplyRefFlux", "ApplyRegionalConds", "ConfigureModifiedFlux", "CreateRegionalCond", "FlowEditForwardSampler", "FlowEditGuider", "FlowEditReverseSampler", "FlowEditSampler", "FluxAttnOverride", "FluxDeGuidance", "FluxForwardODESampler", "FluxInverseSampler", "FluxNoiseMixer", "FluxReverseODESampler", "InFluxFlipSigmas", "InFluxModelSamplingPred", "OutFluxModelSamplingPred", "PAGAttention", "PrepareAttnBank", "RFDoubleBlocksOverride", "RFSingleBlocksOverride", "RegionalStyleModelApply", "SEGAttention"], {"title_aux": "ComfyUI-Fluxtapoz [WIP]"}], "https://github.com/logtd/ComfyUI-HunyuanLoom": [["ConfigureModifiedHY", "HYApplyRegionalConds", "HYAttnOverride", "HYCreateRegionalCond", "HYFetaEnhance", "HYFlowEditGuider", "HYFlowEditGuiderAdv", "HYFlowEditGuiderCFG", "HYFlowEditGuiderCFGAdv", "HYFlowEditSampler", "HYForwardODESampler", "HYInverseModelSamplingPred", "HYReverseModelSamplingPred", "HYReverseODESampler", "HyVideoFlowEditSamplerWrapper"], {"title_aux": "ComfyUI-HunyuanLoom [WIP]"}], "https://github.com/logtd/ComfyUI-Veevee": [["ApplyVVModel", "FlowConfig", "FlowGetFlow", "GetRaftFlow", "InjectionConfig", "PivotConfig", "RaveConfig", "SCAConfig", "TemporalConfig", "VVSamplerSampler", "VVUnsamplerSampler"], {"title_aux": "ComfyUI-Veevee [WIP]"}], "https://github.com/longgui0318/comfyui-one-more-step": [["Calculate More Step Latent", "<PERSON>ad More Step Model"], {"title_aux": "comfyui-one-more-step [WIP]"}], "https://github.com/longzoho/ComfyUI-Qdrant-Saver": [["QDrantSaver"], {"title_aux": "ComfyUI-Qdrant-Saver"}], "https://github.com/lordwedggie/xcpNodes": [["derpBaseAlpha", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "xcpDerpBool", "xcpDerpFloat", "xcpDerpINT", "xcpDerpSeed"], {"title_aux": "xcpNodes [WIP]"}], "https://github.com/lrzjason/Comfyui-Condition-Utils": [["LoadCondition", "LoadConditionFromLoras", "SaveCondition"], {"title_aux": "Comfyui-Condition-Utils [WIP]"}], "https://github.com/ltdrdata/ComfyUI-Workflow-Component": [["ComboToString", "ExecutionBlocker", "ExecutionControlString", "ExecutionOneOf", "ExecutionSwitch", "InputUnzip", "InputZip", "LoopControl", "LoopCounterCondition", "OptionalTest", "TensorToCPU"], {"title_aux": "ComfyUI-Workflow-Component [WIP]"}], "https://github.com/lu64k/SK-Nodes": [["Ask LLM", "Color Transfer", "Image Tracing Node", "Load LLM", "Load_Nemotron", "Natural Saturation", "OpenAI DAlle Node", "OpenAI Text Node", "SK Random File Name", "SK Save Text", "SK Text_String", "SK load text", "To<PERSON> Quantize", "grey_scale blend"], {"title_aux": "SK-Nodes"}], "https://github.com/lucafoscili/lf-nodes": [["LF_Blend", "LF_BlobToImage", "LF_Bloom", "LF_BlurImages", "<PERSON><PERSON><PERSON>", "LF_Brightness", "LF_CaptionImageWD14", "LF_CharacterImpersonator", "LF_CheckpointSelector", "LF_CivitAIMetadataSetup", "LF_Clarity", "LF_ColorAnalysis", "LF_CompareImages", "LF_Contrast", "LF_ControlPanel", "LF_CreateMask", "LF_Desaturation", "LF_DisplayBoolean", "LF_DisplayFloat", "LF_DisplayInteger", "LF_DisplayJSON", "LF_DisplayPrimitiveAsJSON", "LF_DisplayString", "LF_EmbeddingSelector", "LF_EmptyImage", "LF_ExtractPromptFromLoraTag", "LF_ExtractString", "LF_FilmGrain", "LF_Float", "<PERSON><PERSON>_<PERSON><PERSON><PERSON>", "LF_GetRandomKeyFromJSON", "LF_GetValueFromJSON", "LF_ImageClassifier", "LF_ImageHistogram", "LF_ImageListFromJSON", "LF_ImageToSVG", "LF_ImagesEditingBreakpoint", "LF_ImagesSlideshow", "LF_Integer", "LF_IsLandscape", "LF_KeywordCounter", "LF_KeywordToggleFromJSON", "LF_LLMChat", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "LF_LUTApplication", "LF_LUTGeneration", "LF_Line", "LF_LoadAndEditImages", "LF_LoadCLIPSegModel", "LF_LoadFileOnce", "LF_LoadImages", "LF_LoadLocalJSON", "LF_LoadLoraTags", "LF_LoadMetadata", "LF_LoadWD14Model", "LF_LoraAndEmbeddingSelector", "LF_LoraSelector", "LF_MarkdownDocGenerator", "LF_MathOperation", "LF_MultipleImageResizeForWeb", "LF_Notify", "LF_ParsePromptWithLoraTags", "LF_RandomBoolean", "LF_RegexReplace", "LF_RegionExtractor", "LF_ResizeImageByEdge", "LF_ResizeImageToDimension", "LF_ResizeImageToSquare", "LF_ResolutionSwitcher", "LF_SamplerSelector", "LF_Saturation", "LF_SaveImageForCivitAI", "LF_SaveJSON", "LF_SaveMarkdown", "LF_SaveText", "LF_SchedulerSelector", "LF_Sepia", "LF_SequentialSeedsGenerator", "LF_SetValueInJSON", "L<PERSON>_ShuffleJSO<PERSON><PERSON>ey<PERSON>", "LF_Something2Number", "LF_Something2String", "LF_SortJSONKeys", "LF_SortTags", "LF_SplitTone", "LF_String", "LF_StringReplace", "LF_StringTemplate", "LF_StringToJSON", "LF_SwitchFloat", "LF_SwitchImage", "LF_SwitchInteger", "LF_SwitchJSON", "LF_SwitchString", "LF_TiltShift", "LF_UpdateUsageStatistics", "LF_UpscaleModelSelector", "LF_UrandomSeedGenerator", "LF_UsageStatistics", "LF_VAESelector", "LF_Vibrance", "LF_ViewImages", "LF_Vignette", "LF_WallOfText", "LF_WriteJSON"], {"title_aux": "LF Nodes [UNSAFE]"}], "https://github.com/lum3on/comfyui_LLM_Polymath": [["ConceptEraserNode", "polymath_SaveAbsolute", "polymath_StringListPicker", "polymath_TextSplitter", "polymath_chat", "polymath_concept_eraser", "polymath_helper", "polymath_scraper", "polymath_settings", "polymath_template", "polymath_text_mask"], {"title_aux": "comfyui_<PERSON><PERSON>_Polymath [WIP]"}], "https://github.com/lum3on/comfyui_RollingDepth": [["RollingDepthNode"], {"title_aux": "comfyui_RollingDepth [WIP]"}], "https://github.com/machinesarenotpeople/comfyui-energycost": [["TimeCostEndNode", "TimeStartNode"], {"title_aux": "comfyui-energycost"}], "https://github.com/maizerrr/comfyui-code-nodes": [["BBoxDrawNode", "BBoxParseNode", "DummyNode", "ImageBatchNode", "MaskEditorNode", "OpenAIGPTImageNode", "OpenAIQueryNode"], {"title_aux": null}], "https://github.com/majorsauce/comfyui_indieTools": [["IndCutByMask", "IndLocalScale", "IndPastImage", "IndSolidify", "IndYoloDetector"], {"title_aux": "comfyui_indieTools [WIP]"}], "https://github.com/mamorett/ComfyUI-SmolVLM": [["Smolvlm_Caption_Analyzer", "Smolvlm_Flux_CLIPTextEncode", "Smolvlm_SaveTags", "Smolvlm_Tagger"], {"title_aux": "ComfyUI-SmolVLM [WIP]"}], "https://github.com/mamorett/comfyui_minicpm_vision": [["MiniCPMVisionGGUF"], {"title_aux": "comfyui_minicpm_vision"}], "https://github.com/marcueberall/ComfyUI-BuildPath": [["Build Path Adv"], {"title_aux": "ComfyUI-BuildPath"}], "https://github.com/marduk191/comfyui-marnodes": [["ImageToDevice", "marduk191_5_text_string", "marduk191_5way_text_switch", "marduk191_s_random_latent", "marduk191_workflow_settings"], {"author": "˶marduk191", "description": "marduk191s nodes.", "nickname": "marduk191 workflow settings", "title": "marduk191 workflow settings", "title_aux": "comfyui-marno<PERSON>"}], "https://github.com/maruhidd/ComfyUI_Transparent-Background": [["FillTransparentNode", "RemoveBackgroundNode"], {"title_aux": "Transparent Background for ComfyUI"}], "https://github.com/mashb1t/comfyui-nodes-mashb1t": [["mashb1t: LoadImage"], {"title_aux": "ComfyUI mashb1t nodes"}], "https://github.com/masmullin2000/ComfyUI-MMYolo": [["M<PERSON>ace_Finder"], {"title_aux": "ComfyUI-MMYolo"}], "https://github.com/matDobek/ComfyUI_duck": [["Combine Images (duck)"], {"title_aux": "ComfyUI_duck"}], "https://github.com/maurorilla/ComfyUI-MisterMR-Nodes": [["AddLogo", "AddSingleObject", "AddSingleText", "ColorNode"], {"title_aux": "ComfyUI-glb-to-stl [WIP]"}], "https://github.com/mehbebe/ComfyLoraGallery": [["LoraGallery"], {"title_aux": "ComfyLoraGallery [WIP]"}], "https://github.com/melMass/ComfyUI-Lygia": [["LygiaProgram", "LygiaUniforms"], {"title_aux": "ComfyUI-Lygia"}], "https://github.com/mikebilly/Transparent-background-comfyUI": [["Transparentbackground RemBg"], {"title_aux": "transparent-background-comfyui"}], "https://github.com/mikeymcfish/FishTools": [["AnaglyphCreator", "AnaglyphCreatorPro", "Deptherize", "LaserCutterFull", "ShadowMap"], {"author": "Fish", "description": "This extension provides tools for generating laser cutter ready files and other fun stuff", "nickname": "FishTools", "title": "FishTools", "title_aux": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> and <PERSON><PERSON><PERSON><PERSON> Nodes"}], "https://github.com/mikheys/ComfyUI-mikheys": [["WanImageDimensions", "WanOptimalResolution", "ИмяДляComfyUI"], {"title_aux": "ComfyUI-mikheys"}], "https://github.com/minhtuannhn/comfyui-gemini-studio": [["GetFileNameFromURL"], {"title_aux": "comfyui-gemini-studio [WIP]"}], "https://github.com/miragecoa/ComfyUI-LLM-Evaluation": [["AccuracyNode", "ClearVRAM", "DeleteFile", "DownloadHuggingFaceModel", "F1ScoreNode", "JSONToListNode", "JsonResultGenerator", "LL<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "LoadFileNode", "MathOperationNode", "MyNode", "PullOllamaModel", "SelectItemByIndexNode", "SelectItemByKeyNode", "<PERSON><PERSON><PERSON><PERSON>", "StringPatternEnforcer", "StringScraper", "UpdateLLMResultToJson", "WriteToJson"], {"title_aux": "ComfyUI-LLM-Evaluation [WIP]"}], "https://github.com/mliand/ComfyUI-Calendar-Node": [["Comfy Calendar Node"], {"title_aux": "ComfyUI-Calendar-Node [WIP]"}], "https://github.com/mm-akhtar/comfyui-mask-selector-node": [["Mask Selector"], {"title_aux": "comfyui-mask-selector-node"}], "https://github.com/mohamedsobhi777/ComfyUI-FramerComfy": [["FramerComfyBooleanInputNode", "FramerComfyFloatInputNode", "FramerComfyInputImageNode", "FramerComfyInputNumberNode", "FramerComfyInputStringNode", "FramerComfySaveImageNode"], {"title_aux": "ComfyUI-FramerComfy [WIP]"}], "https://github.com/molbal/comfy-url-fetcher": [["U<PERSON> Fetcher"], {"title_aux": "comfy-url-fetcher [WIP]"}], "https://github.com/moonwhaler/comfyui-moonpack": [["DynamicLoraStack", "DynamicStringConcat", "ProportionalDimension", "RegexStringReplace", "SimpleStringReplace", "VACELooperFrameMaskCreator"], {"title_aux": "comfyui-moonpack"}], "https://github.com/mr-krak3n/ComfyUI-Qwen": [["DeepSeekResponseParser", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON>"], {"title_aux": "ComfyUI-Qwen [CONFLICT]"}], "https://github.com/mut-ex/comfyui-gligengui-node": [["GLIGEN_GUI"], {"title_aux": "ComfyUI GLIGEN GUI Node"}], "https://github.com/muvich3n/ComfyUI-Claude-I2T": [["ClaudeImageToPrompt"], {"title_aux": "ComfyUI-Claude-I2T"}], "https://github.com/muvich3n/ComfyUI-Crop-Border": [["CropImageBorder"], {"title_aux": "ComfyUI-Crop-Border"}], "https://github.com/naderzare/comfyui-inodes": [["IAzureAiApi", "ICutStrings", "IFinalizeProject", "IIfElse", "ILLMExecute", "ILLMExecute2", "ILoadAzureAiApi", "ILoadOllamaApi", "IMergeImages", "IMultilineSplitToStrings", "IPassImage", "IPostProcessLLMResponse", "IPromptGenerator", "IRandomChoiceToStrings", "ISaveImage", "ISaveText", "IStringsCounter", "IStringsToFile", "IStringsToString", "ITimesToStrings", "IUploadToGoogleDrive", "IZipImages"], {"title_aux": "comfyui-inodes"}], "https://github.com/neeltheninja/ComfyUI-TempFileDeleter": [["Temp<PERSON><PERSON><PERSON>"], {"title_aux": "ComfyUI-TempFileDeleter [UNSAFE]"}], "https://github.com/neeltheninja/ComfyUI-TextOverlay": [["TextOverlay"], {"title_aux": "ComfyUI-TextOverlay"}], "https://github.com/neo0801/my-comfy-node": [["DeepMosaicGetImageMosaicMask", "DeepMosaicGetVideoMosaicMask", "DeepMosaicRemoveImageMosaic", "DeepMosaicRemoveVideoMosaic"], {"title_aux": "my-comfy-node"}], "https://github.com/neverbiasu/ComfyUI-ControlNeXt": [["ControlNextPipelineConfig", "ControlNextSDXL"], {"title_aux": "ComfyUI-ControlNeXt [WIP]"}], "https://github.com/neverbiasu/ComfyUI-DeepSeek": [["DeepSeekCaller"], {"title_aux": "ComfyUI-DeepSeek"}], "https://github.com/neverbiasu/ComfyUI-Show-o": [["ShowoImageCaptioning", "ShowoImageInpainting", "ShowoModelLoader", "ShowoTextToImage"], {"title_aux": "ComfyUI-Show-o [WIP]"}], "https://github.com/neverbiasu/ComfyUI-StereoCrafter": [["DepthSplattingModelLoader", "DepthSplattingNode", "InpaintingInferenceNode"], {"title_aux": "ComfyUI-StereoCrafter [WIP]"}], "https://github.com/newraina/ComfyUI-Remote-Save-Image": [["RemoteImageSaver"], {"title_aux": "ComfyUI-Remote-Save-Image [UNSAFE]"}], "https://github.com/nidefawl/ComfyUI-nidefawl": [["BlendImagesWithBoundedMasks", "CropImagesWithMasks", "CustomCallback", "DisplayAnyType", "EmptyImageWithColor", "ImageToLatent", "Latent<PERSON><PERSON><PERSON><PERSON><PERSON>", "LatentScaledNoise", "LatentToImage", "MaskFromColor", "ModelSamplerTonemapNoiseTest", "PythonScript", "Reference<PERSON>n<PERSON><PERSON><PERSON><PERSON>", "SamplerCustomCallback", "SamplerDPMPP_2M_SDE_nidefawl", "SetLatentCustomNoise", "SplitCustomSigmas", "VAELoaderDataType", "gcLatentTunnel"], {"title_aux": "ComfyUI-nidefawl [UNSAFE]"}], "https://github.com/nikkuexe/ComfyUI-ListDataHelpers": [["List Difference", "VHS Output Filter", "VHS_VideoOutputFilter"], {"title_aux": "List Data Helper Nodes"}], "https://github.com/nkchocoai/ComfyUI-PromptUtilities": [["PromptUtilitiesConstString", "PromptUtilitiesConstStringMultiLine", "PromptUtilitiesFormatString", "PromptUtilitiesJoinStringList", "PromptUtilitiesLoadPreset", "PromptUtilitiesLoadPresetAdvanced", "PromptUtilitiesPromptWeight", "PromptUtilitiesRandomPreset", "PromptUtilitiesRandomPresetAdvanced", "PromptUtilitiesReplaceOrInsertTag", "PromptUtilitiesRoundPromptWeight", "PromptUtilitiesSampleTags", "PromptUtilitiesSampleTagsWithWeight"], {"title_aux": "ComfyUI-PromptUtilities"}], "https://github.com/nobandegani/comfyui_ino_nodes": [["Ino_BranchImage", "Ino_CountFiles", "Ino_DateTimeAsString", "Ino_GetParentID", "Ino_IntEqual", "Ino_NotBoolean", "Ino_ParseFilePath", "Ino_RandomCharacterPrompt", "Ino_SaveFile", "Ino_SaveImage", "Ino_VideoConvert"], {"title_aux": "Ino Custom Nodes"}], "https://github.com/nomcycle/ComfyUI_Cluster": [["ClusterBroadcastLoadedImage", "ClusterBroadcastTensor", "ClusterEndSubgraph", "ClusterExecuteCurrentWorkflow", "ClusterExecuteWorkflow", "ClusterFanInImages", "ClusterFanOutImage", "ClusterFanOutLatent", "ClusterFanOutMask", "ClusterFinallyFree", "ClusterFlattenBatchedImageList", "ClusterFreeNow", "ClusterGatherImages", "ClusterGatherLatents", "ClusterGatherMasks", "ClusterGetInstanceWorkItemFromBatch", "ClusterInfo", "ClusterInsertAtIndex", "ClusterListenTensorBroadcast", "ClusterSplitBatchToList", "ClusterStartSubgraph", "ClusterStridedReorder", "ClusterUseSubgraph"], {"title_aux": "ComfyUI_Cluster [WIP]"}], "https://github.com/osuiso-depot/comfyui-keshigom_custom": [["KANI_Checkpoint_Loader_From_String", "KANI_MathExpression", "KANI_Multiplexer", "KANI_ShowAnything", "KANI_TextFind", "KANI_TrueorFalse", "RegExTextChopper", "ResolutionSelector", "ResolutionSelectorConst", "StringNodeClass"], {"title_aux": "comfyui-keshigom_custom"}], "https://github.com/owengillett/ComfyUI-tilefusion": [["VideoGridCombine"], {"title_aux": "ComfyUI-tilefusion"}], "https://github.com/oxysoft/Comfy-Compel": [["CLIPEmbedCompel"], {"title_aux": "Comfy-Compel"}], "https://github.com/oyvindg/ComfyUI-TrollSuite": [["BinaryImageMask", "ImagePadding", "LoadLastImage", "RandomMask", "TransparentImage"], {"title_aux": "ComfyUI-TrollSuite"}], "https://github.com/oztrkoguz/ComfyUI_Kosmos2_BBox_Cutter": [["Kosmos2SamplerSimple", "KosmosLoader", "Write"], {"title_aux": "Kosmos2_BBox_Cutter Models"}], "https://github.com/p1atdev/comfyui-aesthetic-predictor": [["LoadAestheticPredictorNode", "PredictAestheticScore"], {"title_aux": "comfyui-aesthetic-predictor"}], "https://github.com/pacchikAI/ImagePromptBatch": [["LoadImageandPrompt"], {"title_aux": "ImagePromptBatch [UNSAFE]"}], "https://github.com/pamparamm/ComfyUI-ppm": [["AttentionCouplePPM", "CFGLimiterGuider", "CFGPPSamplerSelect", "CLIPMicroConditioning", "CLIPNegPip", "CLIPTextEncodeBREAK", "CLIPTextEncodeInvertWeights", "CLIPTokenCounter", "ConditioningZeroOutCombine", "ConvertTimestepToSigma", "DynSamplerSelect", "DynamicThresholdingPost", "DynamicThresholdingSimplePost", "EmptyLatentImageAR", "FreeU2PPM", "Guidance Limiter", "LatentOperationTonemapLuminance", "LatentToMaskBB", "LatentToWidthHeight", "MaskCompositePPM", "PPMSamplerSelect", "RenormCFGPost", "RescaleCFGPost"], {"title_aux": "ComfyUI-ppm"}], "https://github.com/papcorns/ComfyUI-Papcorns-Node-UploadToGCS": [["UploadImageToGCS"], {"title_aux": "ComfyUI-Papcorns-Node-UploadToGCS"}], "https://github.com/parmarjh/ComfyUI-MochiWrapper-I2V": [["DownloadAndLoadMochiModel", "MochiDecode", "MochiDecodeSpatialTiling", "MochiFasterCache", "MochiImageEncode", "MochiLatentPreview", "<PERSON>chi<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "MochiSampler", "MochiSigmaSchedule", "MochiTextEncode", "MochiTorchCompileSettings", "MochiVAEEncoderLoader", "Mochi<PERSON><PERSON><PERSON>der"], {"title_aux": "ComfyUI-MochiWrapper-I2V [WIP]"}], "https://github.com/paulhoux/Smart-Prompting": [["SaveImageWithPrefix", "TextAppend", "TextCharacterSelector", "TextEncodeReusable", "TextFile", "TextNegatives", "TextSearchReplace", "TextString", "TextStyleSelector"], {"title_aux": "List Data Helper Nodes"}], "https://github.com/phamngoctukts/ComyUI-Tupham": [["AreaCondition_v2", "ConditionUpscale", "MultiLatent", "Runnodeselected", "<PERSON><PERSON><PERSON><PERSON>"], {"title_aux": "ComyUI-Tupham"}], "https://github.com/pictorialink/ComfyUI-static-resource": [["StaticResource"], {"title_aux": "comfyui-static-resource[UNSAFE]"}], "https://github.com/pinkpixel-dev/comfyui-llm-prompt-enhancer": [["PromptEnhancer"], {"title_aux": "ComfyUI LLM Prompt Enhancer [WIP]"}], "https://github.com/pixuai/ComfyUI-PixuAI": [["PromptSearch"], {"title_aux": "ComfyUI-PixuAI"}], "https://github.com/pmarmotte2/Comfyui-VibeVoiceSelector": [["VibeVoiceSelector"], {"title_aux": "VibeVoiceSelector [WIP]"}], "https://github.com/poisenbery/NudeNet-Detector-Provider": [["NudeNetDetectorProvider"], {"title_aux": "NudeNet-Detector-Provider [WIP]"}], "https://github.com/pomelyu/cy-prompt-tools": [["CY_LLM", "CY_LoadPrompt", "CY_LoadPrompt4", "CY_LoadPromptPro", "CY_PromptComposer", "CY_TextBox"], {"title_aux": "cy-prompt-tools"}], "https://github.com/power88/ComfyUI-PDiD-Nodes": [["Blend Images", "Check Character Tag", "Get Image Colors", "Get image size", "List Operations", "Make Image Gray", "Nearest SDXL Resolution divided by 64"], {"title_aux": "ComfyUI-PDiD-Nodes [WIP]"}], "https://github.com/prabinpebam/anyPython": [["Any Python"], {"author": "prabinpebam", "description": "This node can take any input and use that to run a python script in ComfyUI", "nickname": "AnyPython", "title": "AnyPython v0.1", "title_aux": "anyPython [UNSAFE]"}], "https://github.com/prodogape/ComfyUI-clip-interrogator": [["ComfyUIClipInterrogator", "ShowText"], {"title_aux": "ComfyUI-clip-interrogator [WIP]"}], "https://github.com/pschroedl/ComfyUI-StreamDiffusion": [["StreamDiffusionAdvancedConfig", "StreamDiffusionCheckpointLoader", "StreamDiffusionConfig", "StreamDiffusionLPCheckpointLoader", "StreamDiffusionLoraLoader", "StreamDiffusionPrebuiltConfig", "StreamDiffusionSampler", "StreamDiffusionTensorRTEngineLoader"], {"title_aux": "ComfyUI-StreamDiffusion"}], "https://github.com/pzzmyc/comfyui-sd3-simple-simpletuner": [["sd not very simple simpletuner by hhy"], {"title_aux": "comfyui-sd3-simple-simpletuner"}], "https://github.com/qlikpetersen/ComfyUI-AI_Tools": [["CreateListJSON", "CreateListString", "<PERSON><PERSON><PERSON><PERSON>", "FixLinksAndRevLinks", "HttpRequest", "Image_Attachment", "IncludeInSpiderData", "JSON_Attachment", "Json2String", "LoadSpiderData", "PNGtoImage", "Query_OpenAI", "RemoveCircularReferences", "RunPython", "RunPythonGriptapeTool", "SaveSpiderData", "SpiderCrawl", "SpiderSplit", "String2Json", "String_Attachment", "TextMultiSave"], {"author": "k<PERSON><PERSON>n", "description": "Tools for agentic testing", "nickname": "ai_tools", "title": "AI_Tools", "title_aux": "ComfyUI-AI_Tools [UNSAFE]"}], "https://github.com/rakki194/ComfyUI_WolfSigmas": [["GetImageSize", "LatentVisualizeDirect", "ListModelBlocks", "ModifyActivationsSVD", "VisualizeActivation", "WolfDCTNoise", "WolfPlotSamplerStatsNode", "WolfProbeGetData", "WolfProbeSetup", "WolfSamplerCustomAdvancedPlotter", "WolfSamplerScriptEvaluator", "WolfScriptableEmptyLatent", "WolfScriptableLatentAnalyzer", "WolfScriptableNoise", "WolfSigmaAdd<PERSON><PERSON>", "WolfSigmaClampT0", "Wolf<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "WolfSigmaGeometricProgression", "WolfSigmaInsertValue", "WolfSigmaNormalizeRange", "WolfSigmaPolynomial", "WolfSigmaPowerTransform", "WolfSigmaQuantize", "WolfSigmaRespaceLogCosine", "WolfSigmaReverse", "WolfSigmaReverseAndRescale", "WolfSigmaScriptEvaluator", "WolfSigmaShiftAndScale", "WolfSigmaSlice", "WolfSigmaTanhGenerator", "WolfSigmasGet", "WolfSigmasSet", "WolfSigmasToJSON", "WolfSimpleSamplerScriptEvaluator", "WolfSimpleScriptableEmptyLatent"], {"title_aux": "ComfyUI_WolfSigmas [UNSAFE]"}], "https://github.com/ralonsobeas/ComfyUI-HDRConversion": [["HDRConversion"], {"title_aux": "ComfyUI-HDRConversion [WIP]"}], "https://github.com/redhottensors/ComfyUI-ODE": [["ODESamplerSelect"], {"author": "RedHotTensors", "description": "Adaptive ODE Solvers for ComfyUI", "nickname": "ComfyUI-ODE", "title": "ComfyUI-ODE", "title_aux": "ComfyUI-ODE"}], "https://github.com/retech995/Save_Florence2_Bulk_Prompts": [["SaveTextFlorence"], {"title_aux": "ComfyUI_SaveImageBulk [UNSAFE]"}], "https://github.com/rhinoflavored/comfyui_QT": [["CSVDataMatcher", "QTAutoCropByNPS", "QTExcelImageReader", "QTExcelReader", "QTRandomSelectString", "QTStringWrappingByNumber", "QT_Alpha_Yaxis_Node", "QT_AntiAliasing_Node", "QT_Batch_Anything_Node", "QT_Center_Rotation", "QT_Character_Height_Difference", "QT_Character_Size_Node", "QT_Color_Image_Loop", "QT_Content_Location_Node", "QT_Crop_Alpha", "QT_Crop_Alpha_V2", "QT_Curves_Node", "QT_Dictionary_Node", "QT_Elements_Into_List_Node", "QT_Float_To_Int", "QT_Image_Array", "QT_Image_Array_Circle", "QT_Image_Array_Rectangle", "QT_Image_Overlay", "QT_Image_Overlay_BOOLEAN", "QT_Image_Overlay_Rotation", "QT_Image_Overlay_V2", "QT_Image_Overlay_V3", "QT_Image_Sorting_Node", "QT_Image_Upscale_And_Crop_Node", "QT_Image_Upscale_And_Crop_Node_V2", "QT_Image_Upscale_And_Crop_Node_V3", "QT_Image_Upscale_Node", "QT_Image_Vision_Center_Node", "QT_Join_Image_List_Node", "QT_Line_Break", "QT_Line_Break_V2", "QT_List_Length", "QT_List_Picker", "QT_List_To_String", "QT_Mask_Mix_Node", "QT_Merge_Into_List_Node", "QT_Pageturn_Node", "QT_Pattern_Fill", "QT_Piecewise_Function_Node", "QT_Polar_Coordinate_Conversion_Node", "QT_Rounded_Corner", "QT_SUPIR_Upscale", "QT_Simple_Text_Image_V2", "QT_Sorting_Node", "QT_Split_List_Node", "QT_Split_List_Node_V2", "QT_Split_Mask_Node", "QT_Split_String", "QT_String_Horizontal_To_Vertical", "QT_String_To_List", "QT_Text_Input_Switch_Node", "QT_Text_Overlay_V2", "QT_Text_To_Bool_Node", "QT_Tilt_Transform", "QT_Translucent_Node", "QT_Vertical_Text_Overlay", "QT_Video_Combine_Node"], {"title_aux": "comfyui_QT"}], "https://github.com/ricklove/ComfyUI-AutoSeg-SAM2": [["AutoSegSAM2Node"], {"title_aux": "ComfyUI-AutoSeg-SAM2"}], "https://github.com/rickyars/sd-cn-animation": [["SDCNAnimation", "SDCNAnimationAdvanced"], {"title_aux": "sd-cn-animation"}], "https://github.com/rishipandey125/ComfyUI-FramePacking": [["Batch Keyframes", "Get Image Dimensions", "Image Mix RGB", "Pa<PERSON> to 4n+1", "Resize Frame", "Slot Frame", "Threshold Image", "<PERSON><PERSON>"], {"title_aux": "ComfyUI-FramePacking [WIP]"}], "https://github.com/risunobushi/ComfyUI_FaceMesh_Eyewear_Mask": [["FaceMeshEyewearMask", "MaskFromFacialKeypoints", "OpenPoseEyewearMask"], {"title_aux": "ComfyUI_FaceMesh_Eyewear_Mask"}], "https://github.com/risunobushi/ComfyUI_FocusMask": [["FocusMaskExtractor", "FocusOutlineExtractor"], {"title_aux": "ComfyUI_FocusMask"}], "https://github.com/risunobushi/ComfyUI_HEXtoRGB": [["HexToRGB"], {"title_aux": "ComfyUI_HEXtoRGB"}], "https://github.com/ritikvirus/comfyui-terminal-modal-node": [["terminal_node"], {"title_aux": "ComfyUI Terminal Command Node [UNSAFE]"}], "https://github.com/robertvoy/ComfyUI-Distributed": [["MultiGPUCollector"], {"title_aux": "ComfyUI-Distributed [WIP]"}], "https://github.com/ronalds-eu/comfyui-plus-integrations": [["ImagePassThrough", "ImageToS3"], {"title_aux": "comfyui-plus-integrations [WIP]"}], "https://github.com/rouxianmantou/comfyui-rxmt-nodes": [["CheckValueTypeNode", "TextCombineWithCommaNode", "WhyPromptTextNode"], {"title_aux": "comfyui-rxmt-nodes"}], "https://github.com/rphmeier/comfyui-videodepthanything": [["VideoDepthAnythingLoader", "VideoDepthAnythingProcess"], {"title_aux": "comfyui-videodepthanything"}], "https://github.com/ruka-game/rukalib_comfyui": [["RukaDebugProbe", "RukaPromptEnhancer"], {"title_aux": "ComfyUI RukaLib [WIP]"}], "https://github.com/ryanontheinside/ComfyUI-Livepeer": [["BatchInfo", "BatchIterator", "LivepeerA2T", "LivepeerAudioJobGetter", "LivepeerI2I", "LivepeerI2T", "LivepeerI2V", "LivepeerImageJobGetter", "LivepeerLLM", "LivepeerLive2Video", "LivepeerSegment", "LivepeerT2I", "LivepeerT2S", "LivepeerTextJobGetter", "LivepeerUpscale", "LivepeerVideoJobGetter"], {"title_aux": "ComfyUI-Livepeer [WIP]"}], "https://github.com/ryanontheinside/ComfyUI-MineWorld": [["MineWorldGenerateFrame", "MineWorldGenerateSequence", "MineWorldInitialState", "MineWorldModelLoader"], {"title_aux": "ComfyUI MineWorld Nodes [WIP]"}], "https://github.com/ryanontheinside/ComfyUI_YoloNasObjectDetection_Tensorrt": [["YoloNasDetectionTensorrt"], {"title_aux": "ComfyUI_YoloNasObjectDetection_Tensorrt [WIP]"}], "https://github.com/sdfxai/SDFXBridgeForComfyUI": [["SDFXClipTextEncode"], {"title_aux": "SDFXBridgeForComfyUI - ComfyUI Custom Node for SDFX Integration"}], "https://github.com/seancheung/comfyui-creative-nodes": [["CreativeSkipFromFlow", "CreativeSkipToFlow", "CreativeStopFlow", "ResolutionSelector", "ResolutionXLSelector"], {"title_aux": "comfyui-creative-nodes"}], "https://github.com/shadowcz007/ComfyUI-PuLID-Test": [["A<PERSON>ly<PERSON><PERSON><PERSON>", "PulidEvaClipLoader", "PulidInsightFaceLoader", "PulidMode<PERSON><PERSON><PERSON><PERSON>"], {"title_aux": "ComfyUI-PuLID [TEST]"}], "https://github.com/shadowcz007/Comfyui-EzAudio": [["EZGenerateAudioNode", "EZLoadModelNode"], {"title_aux": "Comfyui-EzAudio [WIP]"}], "https://github.com/shadowcz007/comfyui-CLIPSeg": [["CLIPSeg_", "CombineMasks_"], {"title_aux": "comfyui-CLIPSeg"}], "https://github.com/shadowcz007/comfyui-hydit-lowvram": [["DiffusersCLIPLoader", "DiffusersCheckpointLoader", "DiffusersClipTextEncode", "DiffusersControlNetLoader", "Diffusers<PERSON><PERSON><PERSON><PERSON><PERSON>", "DiffusersModelMakeup", "Diffusers<PERSON>ipeline<PERSON><PERSON>der", "DiffusersSampler", "DiffusersSchedulerLoader", "DiffusersVAELoader"], {"title_aux": "comfyui-hydit"}], "https://github.com/shinich39/comfyui-nothing-happened": [["NothingHappened"], {"author": "shinich39", "description": "Save image and keep metadata.", "nickname": "comfyui-nothing-happened", "title": "comfyui-nothing-happened", "title_aux": "comfyui-nothing-happened"}], "https://github.com/shinich39/comfyui-run-js": [["RunJS"], {"author": "shinich39", "description": "Manipulate workflow via javascript on node.", "nickname": "comfyui-run-js", "title": "comfyui-run-js", "title_aux": "comfyui-run-js [UNSAFE]"}], "https://github.com/shirazdesigner/CLIPTextEncodeAndEnhancev4": [["CLIPTextEncodeAndEnhance"], {"title_aux": "CLIPTextEncodeAndEnhancev4 (shirazdesigner)"}], "https://github.com/shuanshuan/ComfyUI_CheckPointLoader_Ext": [["CheckpointLoaderExt"], {"title_aux": "ComfyUI_CheckPointLoader_Ext [WIP]"}], "https://github.com/silent-rain/ComfyUI-SilentRain": [["Example"], {"title_aux": "ComfyUI-SilentRain"}], "https://github.com/silveroxides/ComfyUI_ReduxEmbedToolkit": [["LoadReduxEmb", "LoadT5XXLEmb", "SaveCondsEmb", "SaveReduxEmb"], {"title_aux": "ComfyUI_ReduxEmbedToolkit"}], "https://github.com/simonjaq/ComfyUI-sjnodes": [["CrossFadeVideo", "InpaintCropImprovedGPU", "InpaintStitchImprovedGPU", "LoadStitcherFromFile", "LogCRec709Convert", "SaveStitcherToFile", "SmoothTemporalMask", "WanVideoVACEExtend"], {"title_aux": "ComfyUI-sjnodes"}], "https://github.com/smthemex/ComfyUI_GPT_SoVITS_Lite": [["GPT_SoVITS_LoadModel", "GPT_SoVITS_Sampler"], {"title_aux": "ComfyUI_GPT_SoVITS_Lite"}], "https://github.com/smthemex/ComfyUI_MangaNinjia": [["MangaN<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "MangaNinjiaSampler", "MarkImageNode"], {"title_aux": "ComfyUI_MangaNinjia [WIP]"}], "https://github.com/sofakid/dandy": [["DandyBooleanCollector", "DandyBooleanPreview", "DandyBooleanSplitter", "Dandy<PERSON>s", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "DandyEditorSettings", "DandyFloatCollector", "DandyFloatPreview", "DandyFloatSplitter", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "DandyHtml", "DandyHtmlLoader", "DandyImageCollector", "DandyIntCollector", "DandyIntPreview", "DandyIntSplitter", "DandyJs", "Dandy<PERSON><PERSON><PERSON><PERSON>der", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "DandyLand", "DandyMaskCollector", "DandyP5JsDraw", "DandyP5JsLoader", "DandyP5JsSetup", "DandyPixelsJs", "DandyPixiJs", "DandyPrompt", "DandyString", "DandyStringArrayCollector", "DandyStringArraySplitter", "DandyStringCatCollector", "DandyStringPreview", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "DandyWasmLoader", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"], {"title_aux": "dandy [UNSAFE]"}], "https://github.com/songtianhui/ComfyUI-DMM": [["DMMApply", "DMMLoader"], {"title_aux": "ComfyUI-DMM [WIP]"}], "https://github.com/sourceful-official/ComfyUI_InstructPixToPixConditioningLatent": [["InstructPixToPixConditioningLatent"], {"title_aux": "ComfyUI_InstructPixToPixConditioningLatent [WIP]"}], "https://github.com/sourceful-official/comfyui-sourceful-official": [["FalFluxLoraSourcefulOfficial", "FalIcLightV2SourcefulOfficial", "SourcefulOfficialComfyuiIncontextThreePanels"], {"title_aux": "comfyui-sourceful-official"}], "https://github.com/spawner1145/comfyui-spawner-nodes": [["ImageMetadataReader", "TextEncoderDecoder", "json_process"], {"title_aux": "comfyui-spawner-nodes"}], "https://github.com/sswink/comfyui-lingshang": [["LS_ALY_Seg_Body_Utils", "LS_ALY_Seg_Body_Utils_Return_crop", "LS_<PERSON><PERSON>_Seg_Clothes_Utils", "LS_ALY_Seg_Common_Utils", "LS_ALY_Seg_Utils", "LS_ALY_UploadToOssAndGetUrl", "LS_DigImageByMask", "LS_GrowMaskWithBlur", "LS_LoadMaskFromUrl", "LS_SaveImageToOss"], {"title_aux": "comfy<PERSON>-l<PERSON><PERSON>"}], "https://github.com/stalkervr/comfyui-custom-path-nodes": [["BatchImageCrop", "ContextPipeIn", "ContextPipeOut", "ContextPipeReroute", "ImageGridCropper", "PathPipeIn", "PathPipeOut", "PathPipeReroute", "PromptPartConcatenation", "Prompt<PERSON><PERSON><PERSON><PERSON><PERSON>", "SavePath", "StringConcatenation"], {"title_aux": "comfyui-custom-path-nodes [UNSAFE]"}], "https://github.com/steelan9199/ComfyUI-Teeth": [["teeth FindContours", "teeth Gemini2", "teeth GetFirstSeg", "teeth GetValueByIndexFromList", "teeth ImageGridLines", "teeth LoadTextFile", "teeth RunPythonCode", "teeth SaveTextFile", "teeth SplitGridImage", "teeth TextSplitByDelimiter"], {"title_aux": "ComfyUI-Teeth [UNSAFE]"}], "https://github.com/strhwste/comfyui_csv_utils": [["ExtractFromJSON", "SearchCSVByRow", "WriteCSVByRow"], {"title_aux": "CSV Utils [WIP]"}], "https://github.com/stutya/ComfyUI-Terminal": [["Terminal"], {"title_aux": "ComfyUI-Terminal [UNSAFE]"}], "https://github.com/sugarkwork/comfyui_image_crop": [["CropReapply", "CropTransparent", "ExpandMultiple", "RestoreCrop"], {"title_aux": "comfyui_image_crop"}], "https://github.com/sugarkwork/comfyui_my_img_util": [["Auto Image Selector", "Simple Image Rotate"], {"title_aux": "comfyui_my_img_util"}], "https://github.com/sugarkwork/comfyui_psd": [["Convert PSD to Image", "PSDLayer", "Save PSD"], {"title_aux": "comfyui_psd [WIP]"}], "https://github.com/suncat2ps/ComfyUI-SaveImgNextcloud": [["SaveImageNextcloud"], {"title_aux": "ComfyUI-SaveImgNextcloud"}], "https://github.com/takoyaki1118/ComfyUI_PromptExtractor": [["CustomLoadImageWithPathNode", "PromptExtractorNode"], {"title_aux": "ComfyUI_PromptExtractor"}], "https://github.com/talon468/ComfyUI-Rpg-Architect": [["ComfyUI Rpg Architect 🪄"], {"title_aux": "ComfyUI-Rpg-Architect [WIP]"}], "https://github.com/tankenyuen-ola/comfyui-env-variable-reader": [["EnvironmentVariableNode"], {"title_aux": "comfyui-env-variable-reader [UNSAFE]"}], "https://github.com/tanmoy-it/comfyuiCustomNode": [["DownloadImageDataUrl"], {"title_aux": "comfyuiCustomNode"}], "https://github.com/tc888/ComfyUI_Save_Flux_Image": [["Cfg Literal", "Int Literal", "Sampler Select", "Save Flux Image with Metadata", "Scheduler Select", "Seed Gen", "String Literal", "Unet Select", "Width/Height Literal"], {"title_aux": "ComfyUI_Save_Flux_Image"}], "https://github.com/techidsk/comfyui_molook_nodes": [["ImageOutpaintPadding(Molook)", "MaskExpand(Molook)", "OpenAIProvider(Molook)"], {"title_aux": "comfyui_molook_nodes [WIP]"}], "https://github.com/techtruth/ComfyUI-Dreambooth": [["DreamboothNode"], {"title_aux": "ComfyUI-Dreambooth"}], "https://github.com/techzuhaib/ComfyUI-CacheImageNode": [["CacheImageNode"], {"title_aux": "ComfyUI-CacheImageNode"}], "https://github.com/thderoo/ComfyUI-_topfun_s_nodes": [["ConditioningPerturbation", "TextGenerator"], {"title_aux": "_topfun_s_nodes"}], "https://github.com/threadedblue/MLXnodes": [["MLXImg2Img", "MLXText2Image"], {"title_aux": "MLXnodes [WIP]"}], "https://github.com/tjorbogarden/my-useful-comfyui-custom-nodes": [["ImageSizer", "KSamplerSDXLAdvanced"], {"title_aux": "my-useful-comfyui-custom-nodes"}], "https://github.com/tom-doerr/dspy_nodes": [["Accepted Examples Viewer", "Dataset Reader", "DynamicOptionsNode", "Few Shot CoT", "Few Shot Control", "Few Shot Review", "FewShotReview", "FewShotReviewServer", "Model", "Predict", "Print Hello World", "Show Text", "ShowText|pysssss", "String List Viewer", "String Splitter", "StringReverser", "StringSplitter", "Text Field", "Text Output"], {"title_aux": "<PERSON><PERSON><PERSON> [WIP]"}], "https://github.com/tracerstar/comfyui-p5js-node": [["HYPE_P5JSImage"], {"title_aux": "comfyui-p5js-node"}], "https://github.com/trampolin/comfy-ui-scryfall": [["ScryfallCardInfoNode", "ScryfallDecklistParserNode", "ScryfallImageExtractorNode", "ScryfallSearchNode"], {"title_aux": "comfy-ui-scryfall"}], "https://github.com/trashgraphicard/Albedo-Sampler-for-ComfyUI": [["Make Seamless Tile", "Sample Image"], {"title_aux": "Albedo-Sampler-for-ComfyUI"}], "https://github.com/truebillyblue/lC.ComfyUI_epistemic_nodes": [["AddApplicationNode", "AddInterpretationNode", "AddObservationNode", "CreatePbiNode", "CreateRDSOTMComponentNode", "GetMadaObjectNode", "InitiateOiaNode", "InitiateRDSOTMCycleNode", "LcADKConfigNode", "LcADKGuiInteractionNode", "LcAddCommentToPbiNode", "LcAnchorClickNode", "LcApiLlmAgentNode", "LcApplyDoneNode", "LcEpistemicPipelineNode", "LcFieldClickNode", "LcFrameClickNode", "LcGetPbiDetailsNode", "LcKeymapClickNode", "LcLinkPbiNode", "LcMemWriteNode", "LcReflectBoomNode", "LcStartleNode", "LcWebLlmAgentNode", "QueryPbisNode", "ShowTextNode", "StoreMadaObjectNode", "UpdatePbiNode", "ViewOiaCycleNode", "ViewRDSOTMCycleDetailsNode"], {"title_aux": "lC.ComfyUI_epistemic_nodes [WIP]"}], "https://github.com/tuckerdarby/ComfyUI-TDNodes": [["HandTrackerNode", "InstanceDiffusionLoader", "InstanceTrackerPrompt", "KSamplerBatchedNode", "KSamplerRAVE", "KSamplerTF", "TemporalNetPreprocessor", "TrackerNode", "VideoTrackerPromptNode"], {"title_aux": "ComfyUI-TDNodes [WIP]"}], "https://github.com/turskeli/comfyui-SetWallpaper": [["SetWallpaper"], {"title_aux": "comfyui-SetWallpaper"}], "https://github.com/tzsoulcap/ComfyUI-SaveImg-W-MetaData": [["CAP Cfg Literal", "CAP Checkpoint Selector", "CAP Int Literal", "CAP Load Image with Metadata", "CAP Sampler Selector", "CAP Save Image w/Metadata", "CAP Scheduler Selector", "CAP Seed Generator", "CAP String Literal", "CAP Tag Image", "CAP Width/Height Literal"], {"title_aux": "ComfyUI-SaveImg-W-MetaData"}], "https://github.com/umisetokikaze/comfyui_mergekit": [["DefineSaveName", "LoadLR", "LoadTarget", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "SetModels", "SetTokenizer", "get_skip"], {"title_aux": "comfyui_mergekit [WIP]"}], "https://github.com/unanan/ComfyUI-Dist": [["LoadCheckpointFromLAN", "LoadCheckpointFromURL", "LoadImageFromLAN", "LoadImageFromURL", "LoadWorkflowFromLAN", "LoadWorkflowFromURL"], {"title_aux": "ComfyUI-Dist [WIP]"}], "https://github.com/usman2003/ComfyUI-Classifiers": [["GenderClassificationNode"], {"title_aux": "ComfyUI-Classifiers"}], "https://github.com/usman2003/ComfyUI-RaceDetect": [["RaceDetectionNodeV2"], {"title_aux": "ComfyUI-RaceDetect"}], "https://github.com/usrname0/ComfyUI-AllergicPack": [["FolderFileCounter_Allergic", "IncrementorPlus"], {"title_aux": "ComfyUI-AllergicPack [WIP]"}], "https://github.com/var1ableX/ComfyUI_Accessories": [["ACC_AnyCast", "AccMakeListNode", "GetMaskDimensions", "GetRandomDimensions", "isImageEmpty", "isMaskEmpty"], {"title_aux": "ComfyUI_Accessories"}], "https://github.com/vchopine/ComfyUI_Toolbox": [["ModelAspectRatioSelector"], {"title_aux": "ComfyUI_Toolbox"}], "https://github.com/virallover/comfyui-virallover": [["BrightnessCorrectionNode", "ConcatHorizontalWithMask", "DeHaloAlphaWithMaskTorch", "<PERSON><PERSON><PERSON>F<PERSON>r", "DownloadAndLoadLoraModelOnly", "<PERSON><PERSON><PERSON>", "FeatheredSharpen", "IterativeDeHaloAlphaWithMaskTorch"], {"title_aux": "comfyui-virallover"}], "https://github.com/visualbruno/ComfyUI-Hunyuan3d-2-1": [["Hy3D21CameraConfig", "Hy3D21ExportMesh", "Hy3D21LoadImageWithTransparency", "Hy3D21LoadMesh", "Hy3D21MeshUVWrap", "Hy3D21PostprocessMesh", "Hy3D21ResizeImages", "Hy3D21VAEConfig", "Hy3D21VAEDecode", "Hy3D21VAELoader", "Hy3DBakeMultiViews", "Hy3DInPaint", "Hy3DMeshGenerator", "Hy3DMultiViewsGenerator"], {"title_aux": "ComfyUI-Hunyuan3d-2-1"}], "https://github.com/vladp0727/Comfyui-with-Furniture": [["GetMaskFromAlpha", "GetQuadrilateralOutfit"], {"title_aux": "ComfyUI Simple Image Tools [WIP]"}], "https://github.com/vovler/ComfyUI-vovlerTools": [["WD14BlackListLoader", "WD14TaggerAndImageFilterer", "WD14TensorRTModelLoader", "WDTaggerONNXtoTENSORRT"], {"title_aux": "comfyui-vovlertools"}], "https://github.com/wTechArtist/ComfyUI_VVL_Segmentation": [["Mask2FormerPanoptic", "OneFormerPanoptic"], {"title_aux": "ComfyUI_VVL_Segmentation [WIP]"}], "https://github.com/wTechArtist/ComfyUI_VVL_VideoCamera": [["ImageSequenceCameraEstimator", "VVLColmapMVSDepthNode"], {"title_aux": "ComfyUI_VVL_VideoCamera"}], "https://github.com/wTechArtist/ComfyUI_vvl_BBOX": [["vvl_BBoxInput"], {"title_aux": "ComfyUI_vvl_BBOX"}], "https://github.com/walterFeng/ComfyUI-Image-Utils": [["Calculate Image Brightness", "Calculate Image Contrast", "Calculate Image Saturation", "Color Similarity Checker", "Crop <PERSON> Util", "Displace Filter", "Image Fix (tensor shape convert)", "Load Image (By Url)", "Mask Refine (Aliyun)"], {"title_aux": "ComfyUI-Image-Utils"}], "https://github.com/warshanks/Shank-Tools": [["HeightWidth", "ResolutionDivider", "TileCalculator"], {"title_aux": "Shank-Tools"}], "https://github.com/watarika/ComfyUI-Text-Utility": [["LoadTextFile", "PromptsFromTextbox", "RemoveComments", "ReplaceVariables", "SaveTextFile", "StringsFromTextbox"], {"title_aux": "ComfyUI-Text-Utility [UNSAFE]"}], "https://github.com/watarika/ComfyUI-exit": [["ExitComfyUI", "FetchApi"], {"title_aux": "ComfyUI-exit [UNSAFE]"}], "https://github.com/waynepimpzhang/comfyui-opencv-brightestspot": [["FindBrightestSpot"], {"title_aux": "FindBrightestSpot [WIP]"}], "https://github.com/whmc76/ComfyUI-LongTextTTSSuite": [["AudioConcatenateFree", "CombineAudioFromList", "IndexSelectFromList", "ListLength", "LongTextSplitter", "MakeAudioBatch", "SubtitleFileLoader"], {"title_aux": "ComfyUI-LongTextTTSSuite [WIP]"}], "https://github.com/wildminder/ComfyUI-MagCache": [["<PERSON><PERSON><PERSON><PERSON>"], {"title_aux": "ComfyUI-MagCache [NAME CONFLICT|WIP]"}], "https://github.com/willblaschko/ComfyUI-Unload-Models": [["DeleteAnyObject", "UnloadAllModels", "UnloadOneModel"], {"title_aux": "ComfyUI-Unload-Models"}], "https://github.com/wilzamguerrero/Comfyui-zZzZz": [["CaptureZNode", "CompressFolderNode", "CreateZNode", "DeleteZNode", "DownloadFileNode", "InfiniteZNode", "MoveZNode", "RenameZNode", "VideoZNode", "ZFShareScreen"], {"title_aux": "Comfyui-zZzZz [UNSAFE]"}], "https://github.com/wordbrew/comfyui-wan-control-nodes": [["WanWeightedControlToVideo"], {"title_aux": "WAN Control Nodes for ComfyUI [WIP]"}], "https://github.com/wormley/comfyui-wormley-nodes": [["CheckpointVAELoaderSimpleText", "CheckpointVAESelectorText", "LoRA_Tag_To_Stack"], {"title_aux": "comfyui-wormley-nodes"}], "https://github.com/xiaoyumu/ComfyUI-XYNodes": [["AdjustImageColor", "AppyColorToImage", "PrimitiveBBOX", "StringToBBOX"], {"title_aux": "ComfyUI-XYNodes"}], "https://github.com/xinyiSS/CombineMasksNode": [["CombineMasksNode"], {"title_aux": "CombineMasksNode"}], "https://github.com/xl0/q_tools": [["PreviewModelMetadata", "QBlendLatent", "QConcatLatentBatch", "QGaussianLatent", "QImageSizeSetter", "QKSampler", "QLatentOp", "QLatentToShape", "QLinearScheduler", "QLoadLatent", "QLoadLatentTimeline", "QParamRandomizerRange", "QParamaRandomizerList", "QPreviewLatent", "QReshapeLatent", "QSamplerCustom", "QSamplerEulerAncestral", "QUniformLatent"], {"title_aux": "q_tools"}], "https://github.com/xmarked-ai/ComfyUI_misc": [["BLIPMatcherX", "BlendLatentsX", "ColorCorrectionX", "ColorSpaceConversionX", "ColorTransferNodeX", "CommonSourcesX", "ConstantColorX", "ConvexHullByMaskX", "CropBorderX", "DepthDisplaceX", "EmptyLatentX", "ExpressionsX", "FourCornerPinMaskX", "GaussianBlurX", "GaussianMaskBlurX", "IfConditionX", "ImageCompositionX", "ImageResizeX", "ImageTileSquare", "ImageUntileSquare", "KSamplerComboX", "LoopCloseX", "LoopOpenX", "LoraBatchSamplerX", "RegionTesterNodeX", "RegionalPromptSamplerX", "RelightX", "RemoveBackgroundX", "SamplersTestX", "SaveImageX", "SelectiveDepthLoraBlocksX", "SimpleBlockerX", "SplineImageMask", "TextConcatX", "TextX", "WhiteBalanceX"], {"title_aux": "ComfyUI_misc"}], "https://github.com/xqqe/honey_nodes": [["ExtractLoRAName", "<PERSON> Lora Loader", "HoneyBatchAspectRatio", "HoneyLoraStackTags", "HoneyTextConcat", "Honey_LoRAStackRandom", "Honey_LoRATags", "Small Lora Loader", "TagAdder"], {"title_aux": "honey_nodes [WIP]"}], "https://github.com/xzuyn/ComfyUI-xzuynodes": [["CLIPLoaderXZ", "CLIPTextEncodeXZ", "DualCLIPLoaderXZ", "FirstLastFrameXZ", "ImageResizeKJ", "ImageResizeXZ", "TripleCLIPLoaderXZ", "WanImageToVideoXZ"], {"title_aux": "xzuynodes-ComfyUI"}], "https://github.com/y4my4my4m/ComfyUI_Direct3DS2": [["Direct3DS2ModelDownloader", "Direct3DS2Node"], {"title_aux": "ComfyUI-Direct3DS2 [WIP]"}], "https://github.com/yamanacn/comfyui_qwen_object": [["BBoxToSAM", "DetectObject", "LoadQwenModel", "SortBBox"], {"title_aux": "comfyui_qwen_object [WIP]"}], "https://github.com/yamanacn/comfyui_qwenbbox": [["BBoxToSAM_v2", "LoadQwenModel_v2", "QwenBbox"], {"title_aux": "comfyui_qwen<PERSON>ox"}], "https://github.com/yanhuifair/ComfyUI-FairLab": [["AppendTagsNode", "BlacklistTagsNode", "CLIPTranslatedNode", "DownloadImageNode", "FillAlphaNode", "FixUTF8StringNode", "FloatNode", "ImageResizeNode", "ImageToVideoNode", "IntNode", "LoadImageFromDirectoryNode", "LoadImageFromURLNode", "PrependTagsNode", "PrintAnyNode", "PrintImageNode", "SaveImageToDirectoryNode", "SaveStringToDirectoryNode", "SequenceStringListNode", "StringCombineNode", "StringNode", "TranslateStringNode", "VideoToImageNode"], {"title_aux": "ComfyUI-FairLab"}], "https://github.com/yanhuifair/comfyui-deepseek": [["DeepSeekChatNode", "DeepSeekChatProNode", "DeepSeekReasonerNode"], {"title_aux": "comfyui-deepseek [WIP]"}], "https://github.com/yanlang0123/ComfyUI_Lam": [["AppParams", "AspectRatio", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "DoWhileEnd", "DoWhileStart", "EasyPromptSelecto", "FaceFusion", "ForEnd", "ForInnerEnd", "ForInnerStart", "ForStart", "GLM3Prompt", "IdentifyingQR", "IfInnerExecute", "Image2Video", "ImageAddMask", "ImageBlank", "ImageClone", "ImageCropFaces", "ImageLama", "ImageToMasks", "LAM.OpenPoseEditorPlus", "LamCommonHidden", "LamCommonNames", "LamCommonPrint", "LamCommonPrintNoOutput", "LamFaceAnalysisModels", "LamGetPngInfo", "LamLoadImageBase64", "LamLoadPathImage", "LamLoadVideo", "LamSamplerName", "LamSaveOnly", "LamScheduler", "LamSwitcherCase", "LoadDirImgPaths", "LoadReplaceImage", "LongTextToList", "MultiControlNetApply", "MultiGLIGENTextBoxApply", "MultiIPAdapterRegional", "MultiIntFormula", "MultiParamFormula", "MultiTextConcatenate", "MultiTextEncode", "MultiTextEncodeAdvanced", "MultiTextSelelct", "MultiTextSetArea", "MultiTextSetGligen", "MultiTextSetMask", "OutDoWhileEnd", "OutDoWhileStart", "PreviewImageLam", "PromptTranslator", "QRCode", "SaveImageLam", "SaveImgOutputLam", "SectionEnd", "SectionStart", "StyleSelecto", "Text2AutioEdgeTts", "TextListSelelct", "VideoAddAudio", "VideoFaceFusion", "VideoPath", "WaitImagSelector", "ZhPromptTranslator"], {"title_aux": "ComfyUI_Lam"}], "https://github.com/yichengup/ComfyUI-Transition": [["CircularSequenceTransition", "CircularTransition", "DualLineTransition", "GradientTransition", "LinearTransition", "SequenceTransition"], {"title_aux": "ComfyUI-Transition"}], "https://github.com/yichengup/ComfyUI-YCNodes_Advance": [["FaceDetectorSelector", "HumanPartsUltra", "YC Color Match", "YCFaceAlignToCanvas", "YCFaceAlignToCanvasV2", "YCFaceAnalysisModels"], {"title_aux": "ComfyUI-YCNodes_Advance"}], "https://github.com/yichengup/Comfyui-NodeSpark": [["ImageCircleWarp", "ImageStretch", "ImageWaveWarp", "LiquifyNode"], {"title_aux": "Comfyui-NodeSpark"}], "https://github.com/yincangshiwei/ComfyUI-SEQLToolNode": [["CanvasFusionNode", "ImageCropByAlpha"], {"title_aux": "ComfyUI-SEQLToolNode"}], "https://github.com/yojimbodayne/ComfyUI-Dropbox-API": [["FetchTokenFromDropbox", "PostImagesToDropboxAPI", "PostPromptsToDropboxAPI", "PullImagesFromDropboxAPI", "PullTextFromDropboxAPI", "PullVideosFromDropboxAPI", "VideoCombineAndExportToDropboxAPI"], {"title_aux": "ComfyUI-Dropbox-API [WIP]"}], "https://github.com/zackabrams/ComfyUI-KeySyncWrapper": [["KeySyncAdvanced", "KeySyncWrapper"], {"title_aux": "ComfyUI-KeySyncWrapper [WIP]"}], "https://github.com/zhaorishuai/ComfyUI-StoryboardDistributor": [["StoryboardDistributor"], {"title_aux": "ComfyUI-StoryboardDistributor"}], "https://github.com/zhengxyz123/ComfyUI-CLIPSeg": [["CLIPSegImage", "CLIPSegText"], {"title_aux": "zhengxyz123/ComfyUI-CLIPSeg [NAME CONFLICT]"}], "https://github.com/zhongpei/Comfyui_image2prompt": [["CLIP AdvancedTextEncode|fofo", "CLIP PromptConditioning|fofo", "Image2Text", "Image2TextWithTags", "ImageBatchToList|fofo", "ImageRewardScore|fofo", "LoadImage2TextModel", "LoadImageRewardScoreModel|fofo", "LoadT5Model|fofo", "LoadText2PromptModel", "ShowText|fofo", "T5QuantizationConfig|fofo", "T5Text2Prompt|fofo", "Text2GPTPrompt", "Text2Prompt", "TextBox|fofo", "Translate2Chinese|fofo"], {"title_aux": "Comfyui_image2prompt"}], "https://github.com/zjkhurry/comfyui_MetalFX": [["metalFXImg"], {"title_aux": "comfyui_MetalFX [WIP]"}], "https://github.com/zyd232/ComfyUI-zyd232-Nodes": [["zyd232 ImagesPixelsCompare", "zyd232_SavePreviewImages"], {"title_aux": "ComfyUI-zyd232-Nodes"}], "https://raw.githubusercontent.com/NeuralNotW0rk/ComfyUI-Waveform-Extensions/main/EXT_VariationUtils.py": [["BatchToList", "ConcatAudioList", "SequenceVariation", "SliceAudio"], {"title_aux": "ComfyUI-Waveform-Extensions"}], "https://raw.githubusercontent.com/jp0215/comfyUI_padding-resize_node/main/PaddingNode.py": [["function"], {"title_aux": "comfyUI_padding-resize_node"}], "https://raw.githubusercontent.com/komojini/ComfyUI_Prompt_Template_CustomNodes/main/prompt_with_template.py": [["ObjectPromptWithTemplate", "PromptWithTemplate"], {"title_aux": "ComfyUI_Prompt_Template_CustomNodes"}], "https://raw.githubusercontent.com/okg21/VLLMVisionChatNode/refs/heads/main/VLLMVisionChatNode.py": [["VLLMVisionChatNode"], {"title_aux": "VLLMVisionChatNode"}], "https://raw.githubusercontent.com/olyyarm/ComfyUI-VLMStudio/refs/heads/master/vlm_visionary_node_v3_.py": [["GemmaMultimodalAnalyzer"], {"title_aux": "ComfyUI-VLMStudio"}]}