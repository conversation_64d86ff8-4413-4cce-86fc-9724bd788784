{"custom_nodes": [{"author": "sdtana", "title": "ComfyUI-FDG", "reference": "https://github.com/sdtana/ComfyUI-FDG", "files": ["https://github.com/sdtana/ComfyUI-FDG"], "install_type": "git-clone", "description": "Implementation of [a/Guidance in the Frequency Domain Enables High-Fidelity Sampling at Low CFG Scales](https://arxiv.org/abs/2506.19713) for ComfyUI."}, {"author": "quasiblob", "title": "ComfyUI-EsesImageLensEffects", "reference": "https://github.com/quasiblob/ComfyUI-EsesImageLensEffects", "files": ["https://github.com/quasiblob/ComfyUI-EsesImageLensEffects"], "install_type": "git-clone", "description": "The 'Eses Image Lens Effects' node is a multipurpose node for ComfyUI designed to simulate a variety of lens characteristics. It combines several typical effects into a single, convenient node, allowing to add realistic or stylistic lens distortion, chromatic aberration, post-process scaling, and a highly configurable vignette."}, {"author": "aassas2", "title": "ComfyUI-EsesImageEffectBloom", "reference": "https://github.com/aassas2/ComfyUI-EsesImageEffectBloom", "files": ["https://github.com/aassas2/ComfyUI-EsesImageEffectBloom"], "install_type": "git-clone", "description": "ComfyUI-EsesImageEffectBloom provides a powerful bloom image post-processing effect for Comfy. This tool leverages GPU capabilities to deliver optimized blur effect calculations, enhancing your images with a stunning glow. Whether you’re a developer or a digital artist, this effect can elevate your visuals effortlessly."}, {"author": "1038lab", "title": "ComfyUI-MiniMax-Remover", "reference": "https://github.com/1038lab/ComfyUI-MiniMax-Remover", "files": ["https://github.com/1038lab/ComfyUI-MiniMax-Remover"], "install_type": "git-clone", "description": "ComfyUI-MiniMax-Remover is a custom node for ComfyUI that enables fast and efficient object removal using minimax optimization. It works in two stages: first, it trains a remover with a simplified DiT model; then it distills a robust version using CFG guidance and fewer inference steps."}, {"author": "alchemine", "title": "ComfyUI-Alchemine-Pack", "reference": "https://github.com/alchemine/comfyui-alchemine-pack", "files": ["https://github.com/alchemine/comfyui-alchemine-pack"], "install_type": "git-clone", "description": "Custom nodes pack for ComfyUI"}, {"author": "AbstractEyes", "title": "comfyui-lycoris", "reference": "https://github.com/AbstractEyes/comfyui-lycoris", "files": ["https://github.com/AbstractEyes/comfyui-lycoris"], "install_type": "git-clone", "description": "A properly implemented lycoris loader for comfyui."}, {"author": "bobsblazed", "title": "<PERSON><PERSON>_<PERSON><PERSON>_<PERSON>", "reference": "https://github.com/BobsBlazed/<PERSON><PERSON>-<PERSON><PERSON>-<PERSON>", "files": ["https://github.com/BobsBlazed/<PERSON><PERSON>-<PERSON><PERSON>-<PERSON>"], "install_type": "git-clone", "description": "A custom LoRA loader node for ComfyUI with advanced block-weighting controls for both SDXL and FLUX models. Features presets for common use-cases like 'Character' and 'Style', and a 'Custom' mode for fine-grained control over individual model blocks."}, {"author": "Yuan-ManX", "title": "ComfyUI-PosterCraft", "reference": "https://github.com/Yuan-ManX/ComfyUI-PosterCraft", "files": ["https://github.com/Yuan-ManX/ComfyUI-PosterCraft"], "install_type": "git-clone", "description": "ComfyUI-PosterCraft is now available in ComfyUI, PosterCraft is a unified framework for high-quality aesthetic poster generation that excels in precise text rendering, seamless integration of abstract art, striking layouts, and stylistic harmony."}, {"author": "uinodes", "title": "ComfyUI-uinodesDOC", "reference": "https://github.com/uinodes/ComfyUI-uinodesDOC", "files": ["https://github.com/uinodes/ComfyUI-uinodesDOC"], "install_type": "git-clone", "description": "This custom node is designed to provide graphical documentation for ComfyUI custom nodes ."}, {"author": "puke3615", "title": "ComfyUI-OneAPI", "reference": "https://github.com/puke3615/ComfyUI-OneAPI", "files": ["https://github.com/puke3615/ComfyUI-OneAPI"], "install_type": "git-clone", "description": "Simple REST API interfaces for ComfyUI with dynamic parameter replacement and output management"}, {"author": "<PERSON>-yeh", "title": "ComfyUI-WebPrompter", "reference": "https://github.com/<PERSON>-yeh/ComfyUI-WebPrompter", "files": ["https://github.com/<PERSON>-yeh/ComfyUI-WebPrompter"], "install_type": "git-clone", "description": "This is a custom node suite for ComfyUI that automates the conversion of web content into an AI-refined news script. This simplified version focuses on the core 'fetch-and-process' workflow for a fast and direct automation experience."}, {"author": "quasiblob", "title": "ComfyUI-EsesImageOffset", "reference": "https://github.com/quasiblob/ComfyUI-EsesImageOffset", "files": ["https://github.com/quasiblob/ComfyUI-EsesImageOffset"], "install_type": "git-clone", "description": "The 'Eses Image Offset' node offers basic image offsetting capabilities within ComfyUI. It allows shifting image and mask content horizontally and/or vertically, with an option to wrap content around the canvas edges for a tiling effect."}, {"author": "Ambrosinus", "title": "ComfyUI-ATk-Nodes", "reference": "https://github.com/lucianoambrosini/ComfyUI-ATk-Nodes", "files": ["https://github.com/lucianoambrosini/ComfyUI-ATk-Nodes"], "install_type": "git-clone", "description": "Ambrosinus ToolKit - Streamlined workflow export with transparent backgrounds, professional themes, and smart scaling. Perfect for creating clean, high-resolution workflow documentation and sharing.", "category": "Utils"}, {"author": "wasilone11", "title": "ComfyUI Sync Lipsync Node", "reference": "https://github.com/wasilone11/comfyui-sync-lipsync-node", "files": ["https://github.com/wasilone11/comfyui-sync-lipsync-node"], "install_type": "git-clone", "description": "This custom node allows you to perform audio-video lip synchronization inside ComfyUI using a simple interface."}, {"author": "AgencyMind", "title": "ComfyUI-Satori", "reference": "https://github.com/AgencyMind/ComfyUI-Satori", "files": ["https://github.com/AgencyMind/ComfyUI-Satori"], "install_type": "git-clone", "description": "When your workflow starts acting weird and you need to know what's actually happening to your data - not just guess from looking at the output."}, {"author": "fotobudka-team", "title": "ComfyUI AI Faces - Photo Verification Node", "reference": "https://github.com/fotobudka-team/comfyui-ai-faces", "files": ["https://github.com/fotobudka-team/comfyui-ai-faces"], "install_type": "git-clone", "description": "A ComfyUI custom node for automated face verification, designed to check if a person is clearly visible and suitable for passport-style photos. This node performs comprehensive facial analysis to ensure photo quality meets identification document standards."}, {"author": "A043-studios", "title": "ComfyUI ASCII Generator Node", "reference": "https://github.com/A043-studios/Comfyui-ascii-generator", "files": ["https://github.com/A043-studios/Comfyui-ascii-generator"], "install_type": "git-clone", "description": "ASCII art generator for ComfyUI with multi-language character set support"}, {"author": "<PERSON>-yeh", "title": "comfyui-super-captioner", "reference": "https://github.com/<PERSON>-yeh/comfyui-super-captioner", "files": ["https://github.com/<PERSON>-yeh/comfyui-super-captioner"], "install_type": "git-clone", "description": "A powerful multi-model image captioning node that supports both local BLIP models and the cloud-based Google Gemini API, specifically designed for ComfyUI."}, {"author": "kpsss34", "title": "ComfyUI Sana Custom Node", "reference": "https://github.com/kpsss34/ComfyUI-kpsss34-Sana", "files": ["https://github.com/kpsss34/ComfyUI-kpsss34-Sana"], "install_type": "git-clone", "description": "A custom node for ComfyUI that supports Sana text-to-image models (600M/1.6B parameters) with advanced features including LoRA support, PAG (Perturbed-Attention Guidance), and optimized VRAM usage."}, {"author": "itsjustregi", "title": "Easy Color Correction", "reference": "https://github.com/regiellis/ComfyUI-EasyColorCorrector", "files": ["https://github.com/regiellis/ComfyUI-EasyColorCorrector"], "install_type": "git-clone", "description": "ComfyUI custom node for flexible and efficient image color correction and post-processing."}, {"author": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "title": "Flux Context ComfyUI Node", "reference": "https://github.com/leonardomiramondi/flux-context-comfyui", "files": ["https://github.com/leonardomiramondi/flux-context-comfyui"], "install_type": "git-clone", "description": "ComfyUI node for Flux Context (Kontext) image editing"}, {"author": "Shiba-2-s<PERSON><PERSON>", "title": "ComfyUI-Magcache-for-SDXL", "reference": "https://github.com/Shiba-2-shiba/ComfyUI-Magcache-for-SDXL", "files": ["https://github.com/Shiba-2-shiba/ComfyUI-Magcache-for-SDXL"], "install_type": "git-clone", "description": "An experimental implementation of MagCache for SDXL"}, {"author": "synthetai", "title": "ComfyUI-JM-Volcengine-API", "reference": "https://github.com/synthetai/ComfyUI-JM-Volcengine-API", "files": ["https://github.com/synthetai/ComfyUI-JM-Volcengine-API"], "install_type": "git-clone", "description": "volcengine comfyui api"}, {"author": "fredconex", "title": "SongBloom", "reference": "https://github.com/fredconex/ComfyUI-SongBloom", "files": ["https://github.com/fredconex/ComfyUI-SongBloom"], "install_type": "git-clone", "description": "ComfyUI Nodes for SongBloom"}, {"author": "AKharytonchyk", "title": "ComfyUI-telegram-bot-node", "reference": "https://github.com/AKharytonchyk/ComfyUI-telegram-bot-node", "files": ["https://github.com/AKharytonchyk/ComfyUI-telegram-bot-node"], "install_type": "git-clone", "description": "ComfyUI custom nodes for Telegram bot integration"}, {"author": "cmdicely", "title": "GrsAI api in ComfyUI", "reference": "https://github.com/31702160136/ComfyUI-GrsAI", "files": ["https://github.com/31702160136/ComfyUI-GrsAI"], "install_type": "git-clone", "description": "GrsAI API node supports models: Flux-Pro-1.1 (¥ 0.03), Flux-Ultra-1.1 (¥ 0.04), Flux Kontext Pro (¥ 0.035), Flux Kontext Max (¥ 0.07), GPT Image (¥ 0.02). Support text generated images, image generated images, and multi image fusion."}, {"author": "Limbicnation", "title": "ComfyUI Face Detection Node", "id": "comfyui-face-detection-node", "reference": "https://github.com/Limbicnation/ComfyUI_FaceDetectionNode", "files": ["https://github.com/Limbicnation/ComfyUI_FaceDetectionNode"], "install_type": "git-clone", "description": "A ComfyUI custom node for face detection and cropping using OpenCV Haar cascades, with full ComfyUI v3 schema support and backward compatibility. Features adjustable detection threshold, minimum face size, padding, and multiple classifier options.", "nodename_pattern": "FaceDetectionNode"}, {"author": "jurdnf", "title": "ComfyUI-JurdnsIterativeNoiseKsampler", "reference": "https://github.com/jurdnf/ComfyUI-JurdnsIterativeNoiseKSampler", "files": ["https://github.com/jurdnf/ComfyUI-JurdnsIterativeNoiseKSampler"], "install_type": "git-clone", "description": "A ComfyUI custom node that adds controlled noise injection during the sampling process for enhanced image generation quality and detail."}, {"author": "o-l-l-i", "title": "<PERSON><PERSON> Editor for ComfyUI", "reference": "https://github.com/o-l-l-i/ComfyUI-Olm-CurveEditor", "files": ["https://github.com/o-l-l-i/ComfyUI-Olm-CurveEditor"], "install_type": "git-clone", "description": "A single-purpose, multi-channel curve editor for ComfyUI, providing precise color control over R, G, B, and Luma channels directly within the node graph. It’s a focused, lightweight, and standalone solution built specifically for one task: applying color curves cleanly and efficiently."}, {"author": "TashaSkyUp", "title": "EternalKernel PyTorch Nodes", "reference": "https://github.com/TashaSkyUp/EternalKernelPytorchNodes", "files": ["https://github.com/TashaSkyUp/EternalKernelPytorchNodes"], "install_type": "git-clone", "description": "Comprehensive PyTorch nodes for ComfyUI - Neural network training, inference, and ML workflows"}, {"author": "quasiblob", "title": "ComfyUI-EsesCompositionGuides", "reference": "https://github.com/quasiblob/ComfyUI-EsesCompositionGuides", "files": ["https://github.com/quasiblob/ComfyUI-EsesCompositionGuides"], "install_type": "git-clone", "description": "Non-destructive visual image composition helper tool node for ComfyUI with minimal requirements, works with larger images too."}, {"author": "ChenDarYen", "title": "ComfyUI-NAG", "reference": "https://github.com/ChenDarYen/ComfyUI-NAG", "files": ["https://github.com/ChenDarYen/ComfyUI-NAG"], "install_type": "git-clone", "description": "ComfyUI implemtation for NAG"}, {"author": "cmdicely", "title": "Simple Image To Palette", "reference": "https://github.com/cmdicely/simple_image_to_palette", "files": ["https://github.com/cmdicely/simple_image_to_palette"], "install_type": "git-clone", "description": "Custom node to extract the colors in an image as a palette for use with ComfyUI-PixelArt-Detector"}, {"author": "orion4d", "title": "ComfyUI Illusion & Pattern Nodes", "reference": "https://github.com/orion4d/illusion_node", "files": ["https://github.com/orion4d/illusion_node"], "install_type": "git-clone", "description": "This repository contains a collection of custom nodes for ComfyUI, designed for generating various patterns, optical illusions, and performing related image manipulations. All nodes are categorized under 'illusion' in the ComfyUI menu."}, {"author": "lucak5s", "title": "ComfyUI GFPGAN", "reference": "https://github.com/lucak5s/comfyui_gfpgan", "files": ["https://github.com/lucak5s/comfyui_gfpgan"], "install_type": "git-clone", "description": "Face restoration with GFPGAN."}, {"author": "kiko9", "title": "ComfyUI-KikoStats", "reference": "https://github.com/ComfyAssets/ComfyUI-KikoStats", "files": ["https://github.com/ComfyAssets/ComfyUI-KikoStats"], "install_type": "git-clone", "description": "Real-time monitoring and statistics for ComfyUI"}, {"author": "NumZ", "title": "ComfyUI-SeedVR2_VideoUpscaler", "id": "SeedVR2_VideoUpscaler", "reference": "https://github.com/numz/ComfyUI-SeedVR2_VideoUpscaler", "files": ["https://github.com/numz/ComfyUI-SeedVR2_VideoUpscaler"], "install_type": "git-clone", "description": "Upscale your videos with this SeedVR2"}, {"author": "Yuan-ManX", "title": "ComfyUI-OmniGen2", "reference": "https://github.com/Yuan-ManX/ComfyUI-OmniGen2", "files": ["https://github.com/Yuan-ManX/ComfyUI-OmniGen2"], "install_type": "git-clone", "description": "ComfyUI-OmniGen2 is now available in ComfyUI, OmniGen2 is a powerful and efficient unified multimodal model. Its architecture is composed of two key components: a 3B Vision-Language Model (VLM) and a 4B diffusion model."}, {"author": "aleolidev", "title": "Kaizen Package", "id": "kaizen_package", "reference": "https://github.com/aleolidev/comfy_kaizen_package", "files": ["https://github.com/aleolidev/comfy_kaizen_package"], "install_type": "git-clone", "description": "A collection of custom image processing nodes for ComfyUI"}, {"author": "j<PERSON><PERSON><PERSON>", "title": "AI4ArtsEd Nodes", "reference": "https://github.com/joeriben/ai4artsed_comfyui_nodes", "files": ["https://github.com/joeriben/ai4artsed_comfyui_nodes"], "install_type": "git-clone", "description": "ComfyUI nodes for the project AI for Arts Education"}, {"author": "DebugPadawan", "title": "DebugPadawan's ComfyUI Essentials", "reference": "https://github.com/DebugPadawan/DebugPadawans-ComfyUI-Essentials", "files": ["https://github.com/DebugPadawan/DebugPadawans-ComfyUI-Essentials"], "install_type": "git-clone", "description": "Essential custom nodes for ComfyUI workflows"}, {"author": "<PERSON><PERSON><PERSON>", "title": "Blur Mask", "reference": "https://github.com/rookiepsi/comfypsi_blur_mask", "files": ["https://github.com/rookiepsi/comfypsi_blur_mask"], "install_type": "git-clone", "description": "A custom node for ComfyUI that applies a Gaussian blur to a mask."}, {"author": "dseditor", "title": "ComfyUI-ListHelper", "reference": "https://github.com/dseditor/ComfyUI-ListHelper", "files": ["https://github.com/dseditor/ComfyUI-ListHelper"], "install_type": "git-clone", "description": "The ListHelper collection is a comprehensive set of custom nodes for ComfyUI that provides powerful list manipulation capabilities. This collection includes audio processing, text splitting, and number generation tools for enhanced workflow automation."}, {"author": "Maxed-Out-99", "title": "ComfyUI-MaxedOut", "reference": "https://github.com/Maxed-Out-99/ComfyUI-MaxedOut", "files": ["https://github.com/Maxed-Out-99/ComfyUI-MaxedOut"], "install_type": "git-clone", "description": "Custom ComfyUI nodes used in Maxed Out workflows (SDXL, Flux, etc.)"}, {"author": "smthemex", "title": "ComfyUI_SongGeneration", "reference": "https://github.com/smthemex/ComfyUI_SongGeneration", "files": ["https://github.com/smthemex/ComfyUI_SongGeneration"], "install_type": "git-clone", "description": "[a/SongGeneration](https://github.com/tencent-ailab/SongGeneration):High-Quality Song Generation with Multi-Preference Alignment (SOTA),you can try VRAM>12G"}, {"author": "834t", "title": "Scene Composer for ComfyUI", "reference": "https://github.com/834t/ComfyUI_834t_scene_composer", "files": ["https://github.com/834t/ComfyUI_834t_scene_composer"], "install_type": "git-clone", "description": "An intuitive, all-in-one node for ComfyUI that brings a powerful, layer-based regional prompting workflow directly into your graph. Say goodbye to managing countless Conditioning (Set Area) nodes and hello to drawing your creative vision."}, {"author": "quasiblob", "title": "ComfyUI-EsesImageAdjustments", "reference": "https://github.com/quasiblob/ComfyUI-EsesImageAdjustments", "files": ["https://github.com/quasiblob/ComfyUI-EsesImageAdjustments"], "install_type": "git-clone", "description": "Image Adjustments node for ComfyUI with minimal requirements, uses PyTorch for image manipulation operations."}, {"author": "coiichan", "title": "ComfyUI-FuncAsTexture-CoiiNode", "reference": "https://github.com/CoiiChan/ComfyUI-FuncAsTexture-CoiiNode", "files": ["https://github.com/CoiiChan/ComfyUI-FuncAsTexture-CoiiNode"], "install_type": "git-clone", "description": "This allows for mathematical operations on input images and precise manipulation of channels through NumPy formulas, making it suitable for ComfyUI users with programming experience."}, {"author": "impactframes", "title": "ComfyUI-WanResolutionSelector", "reference": "https://github.com/if-ai/ComfyUI-WanResolutionSelector", "files": ["https://github.com/if-ai/ComfyUI-WanResolutionSelector"], "install_type": "git-clone", "description": "A ComfyUI custom node that automatically selects appropriate video resolution dimensions based on generation mode, aspect ratio, and quality settings. Designed to work seamlessly with video generation models and KJNodes image resize nodes."}, {"author": "TheLustriVA", "title": "ComfyUI Image Size Tool", "reference": "https://github.com/TheLustriVA/ComfyUI-Image-Size-Tool", "files": ["https://github.com/TheLustriVA/ComfyUI-Image-Size-Tool"], "install_type": "git-clone", "description": "Resolution calculator nodes for ComfyUI with model-specific constraints and optimal bucket resolutions"}, {"author": "highdoping", "title": "ComfyUI-ASSSSA", "reference": "https://github.com/HighDoping/ComfyUI_ASSSSA", "files": ["https://github.com/HighDoping/ComfyUI_ASSSSA"], "install_type": "git-clone", "description": "Add ASS/SSA subtitle to video using ffmpeg."}, {"author": "highdoping", "title": "lama_with_refiner", "reference": "https://github.com/fplu/comfyui_lama_with_refiner", "files": ["https://github.com/fplu/comfyui_lama_with_refiner"], "install_type": "git-clone", "description": "Nodes for lama+refiner inpainting with ComfyUI."}, {"author": "robin-collins", "title": "ComfyUI-TechsToolz", "reference": "https://github.com/robin-collins/ComfyUI-TechsToolz", "files": ["https://github.com/robin-collins/ComfyUI-TechsToolz"], "install_type": "git-clone", "description": "A modular collection of ComfyUI custom nodes with advanced dependency management and ComfyUI Manager integration."}, {"author": "lum3on", "title": "ComfyUI-AudioX", "reference": "https://github.com/lum3on/ComfyUI-StableAudioX", "files": ["https://github.com/lum3on/ComfyUI-StableAudioX"], "install_type": "git-clone", "description": "A powerful audio generation extension for ComfyUI that integrates AudioX models for high-quality audio synthesis from text and video inputs."}, {"author": "smthemex", "title": "ComfyUI_PartPacker", "reference": "https://github.com/smthemex/ComfyUI_PartPacker", "files": ["https://github.com/smthemex/ComfyUI_PartPacker"], "install_type": "git-clone", "description": "This is the comfyui implementation of [a/PartPacker](https://github.com/NVlabs/PartPacker): Efficient Part-level 3D Object Generation via Dual Volume Packing.Max varm12G"}, {"author": "azazeal04", "title": "anime_character_selector", "reference": "https://github.com/azazeal04/Azazeal_Anime_Characters_ComfyUI", "files": ["https://github.com/azazeal04/Azazeal_Anime_Characters_ComfyUI"], "install_type": "git-clone", "description": "character nodes for characters from various anime shows and comics"}, {"author": "drphero", "title": "ComfyUI-PromptTester", "reference": "https://github.com/drphero/comfyui_prompttester", "files": ["https://github.com/drphero/comfyui_prompttester"], "install_type": "git-clone", "description": "Automatically tests the impact of each phrase in a prompt by generating images with one phrase omitted at a time."}, {"author": "dseditor", "title": "ComfyUI-ScheduledTask", "reference": "https://github.com/dseditor/ComfyUI-ScheduledTask", "files": ["https://github.com/dseditor/ComfyUI-ScheduledTask"], "install_type": "git-clone", "description": "A powerful workflow scheduling extension for ComfyUI that enables automated daily execution of workflows with an intuitive web interface."}, {"author": "😈 <PERSON><PERSON><PERSON><PERSON><PERSON>", "title": "MiniMax Video Object Remover Suite", "reference": "https://github.com/casterpollux/MiniMax-bmo", "files": ["https://github.com/casterpollux/MiniMax-bmo"], "nodename_pattern": "MiniMax.*BMO|BMO.*MiniMax", "pip": ["segment-anything"], "tags": ["video", "inpainting", "object-removal", "suite", "professional", "BMO"], "install_type": "git-clone", "description": "Professional video object removal suite using MiniMax optimization. Includes BMO-enhanced nodes with VAE normalization, temporal preservation, and 6-step inference. Complete video inpainting solution for ComfyUI."}, {"author": "<PERSON><PERSON><PERSON><PERSON>", "title": "OcclusionMask", "reference": "https://github.com/ialhabbal/OcclusionMask", "files": ["https://github.com/ialhabbal/OcclusionMask"], "install_type": "git-clone", "description": "A powerful ComfyUI custom node for advanced face occlusion, segmentation, and masking, leveraging state-of-the-art face detection (insightface buffalo models) for robust and accurate results."}, {"author": "Charonartist", "title": "ComfyUI Auto LoRA", "reference": "https://github.com/Charonartist/comfyui-auto-lora-v2", "files": ["https://github.com/Charonartist/comfyui-auto-lora-v2"], "install_type": "git-clone", "description": "This is a ComfyUI custom node that automatically detects trigger words from text prompts and applies the corresponding LoRA models."}, {"author": "akatz-ai", "title": "ComfyUI-Basic-Math", "reference": "https://github.com/akatz-ai/ComfyUI-Basic-Math", "files": ["https://github.com/akatz-ai/ComfyUI-Basic-Math"], "install_type": "git-clone", "description": "Custom nodes for performing basic math operations"}, {"author": "kael558", "title": "ComfyUI-GGUF-FantasyTalking", "reference": "https://github.com/kael558/ComfyUI-GGUF-FantasyTalking", "files": ["https://github.com/kael558/ComfyUI-GGUF-FantasyTalking"], "install_type": "git-clone", "description": "GGUF Quantization support for native ComfyUI models with FantasyTalking."}, {"author": "Lord <PERSON><PERSON><PERSON>", "title": "ComfyUI-RPG-Characters", "id": "rpg-characters", "reference": "https://github.com/lord-lethris/ComfyUI-RPG-Characters", "files": ["https://github.com/lord-lethris/ComfyUI-RPG-Characters"], "install_type": "git-clone", "description": "Stylized RPG character prompt generator for ComfyUI. Supports standard and Ollama-based prompts, works with SD, SDXL, Flux, and more."}, {"author": "Phospholipids", "title": "PPWildCard", "reference": "https://github.com/kohs100/comfyui-ppwc", "files": ["https://github.com/kohs100/comfyui-ppwc"], "install_type": "git-clone", "description": "This extension offers wildcard prompting works solely in workflow."}, {"author": "linjian-ufo", "title": "DeepSeek Chat Node for ComfyUI", "reference": "https://github.com/linjian-ufo/comfyui_deepseek_lj257_update", "files": ["https://github.com/linjian-ufo/comfyui_deepseek_lj257_update"], "install_type": "git-clone", "description": "This is a custom node for ComfyUI that calls the DeepSeek Chat API to process text input and return text output."}, {"author": "<PERSON><PERSON><PERSON><PERSON>", "title": "ImageLoadFromLocalOrUrl Node for ComfyUI", "id": "JkhaImageLoaderPathOrUrl", "reference": "https://gitee.com/yyh915/jkha-load-img", "files": ["https://gitee.com/yyh915/jkha-load-img"], "install_type": "git-clone", "description": "This is a node to load an image from local path or url."}, {"author": "jurdnf", "title": "ComfyUI-JurdnsModelSculptor", "reference": "https://github.com/jurdnf/ComfyUI-JurdnsModelSculptor", "files": ["https://github.com/jurdnf/ComfyUI-JurdnsModelSculptor"], "install_type": "git-clone", "description": "A ComfyUI custom node package for seamless integration with Threads (Meta's social platform). This package allows you to publish posts, manage images, and retrieve post history directly from your ComfyUI workflows."}]}