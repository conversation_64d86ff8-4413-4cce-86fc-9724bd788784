## ComfyUI-Manager: installing dependencies done.
[2025-06-28 17:34:50.512] ** ComfyUI startup time: 2025-06-28 17:34:50.512
[2025-06-28 17:34:50.512] ** Platform: Windows
[2025-06-28 17:34:50.512] ** Python version: 3.13.5 (tags/v3.13.5:6cb20a2, Jun 11 2025, 16:15:46) [MSC v.1943 64 bit (AMD64)]
[2025-06-28 17:34:50.512] ** Python executable: C:\Users\<USER>\ai\ComfiGit\comfyui_env\Scripts\python.exe
[2025-06-28 17:34:50.513] ** ComfyUI Path: C:\Users\<USER>\ai\ComfiGit\ComfyUI-master
[2025-06-28 17:34:50.513] ** ComfyUI Base Folder Path: C:\Users\<USER>\ai\ComfiGit\ComfyUI-master
[2025-06-28 17:34:50.513] ** User directory: C:\Users\<USER>\ai\ComfiGit\ComfyUI-master\user
[2025-06-28 17:34:50.513] ** ComfyUI-Manager config path: C:\Users\<USER>\ai\ComfiGit\ComfyUI-master\user\default\ComfyUI-Manager\config.ini
[2025-06-28 17:34:50.513] ** Log path: C:\Users\<USER>\ai\ComfiGit\ComfyUI-master\user\comfyui.log
[ComfyUI-Manager] In Python 3.13 and above, PIP Fixer does not downgrade `numpy` below version 2.0. If you need to force a downgrade of `numpy`, please use `pip_auto_fix.list`.
[2025-06-28 17:34:51.269] 
Prestartup times for custom nodes:
[2025-06-28 17:34:51.269]    2.1 seconds: C:\Users\<USER>\ai\ComfiGit\ComfyUI-master\custom_nodes\ComfyUI-Manager
[2025-06-28 17:34:51.269] 
