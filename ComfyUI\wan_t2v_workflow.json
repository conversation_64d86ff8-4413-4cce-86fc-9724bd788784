{"1": {"inputs": {"model": "wan2.1_t2v_1.3B_fp16.safetensors", "base_precision": "bf16", "quantization": "disabled", "load_device": "offload_device"}, "class_type": "WanVideoModelLoader", "_meta": {"title": "<PERSON>ad <PERSON>"}}, "2": {"inputs": {"model_name": "umt5_xxl_fp8_e4m3fn_scaled.pth", "precision": "bf16"}, "class_type": "LoadWanVideoT5TextEncoder", "_meta": {"title": "Load T5 Text Encoder"}}, "3": {"inputs": {"model_name": "wan_2.1_vae.pth", "precision": "bf16"}, "class_type": "WanVideoVAELoader", "_meta": {"title": "Load WanVideo VAE"}}, "4": {"inputs": {"positive_prompt": "A beautiful cherry blossom tree in full bloom, petals gently falling in the wind, soft sunlight filtering through the branches, peaceful spring scene, cinematic quality, 4k", "negative_prompt": "blurry, low quality, distorted, ugly, bad anatomy", "t5": ["2", 0]}, "class_type": "WanVideoTextEncode", "_meta": {"title": "Text Encode"}}, "5": {"inputs": {"model": ["1", 0], "positive": ["4", 0], "width": 512, "height": 320, "num_frames": 16, "steps": 25, "cfg": 7.5, "seed": 42, "shift": 1.0, "image_embeds": null, "force_offload": true, "scheduler": "unipc", "riflex_freq_index": 0}, "class_type": "WanVideoSampler", "_meta": {"title": "WanVide<PERSON>"}}, "6": {"inputs": {"samples": ["5", 0], "vae": ["3", 0], "enable_vae_tiling": false, "tile_x": 512, "tile_y": 320, "tile_stride_x": 256, "tile_stride_y": 160}, "class_type": "WanVideoDecode", "_meta": {"title": "WanVideo Decode"}}, "7": {"inputs": {"images": ["6", 0], "frame_rate": 8.0, "loop_count": 0, "filename_prefix": "wan_t2v_cherry_blossom", "format": "video/h264-mp4", "pingpong": false, "save_output": true, "pix_fmt": "yuv420p", "crf": 19, "save_metadata": true}, "class_type": "VHS_VideoCombine", "_meta": {"title": "Video Combine"}}}