{"1": {"inputs": {"image": "example.png", "upload": "image"}, "class_type": "LoadImage", "_meta": {"title": "Load Image"}}, "2": {"inputs": {"ckpt_name": "svd_xt.safetensors"}, "class_type": "ImageOnlyCheckpointLoader", "_meta": {"title": "ImageOnlyCheckpointLoader"}}, "3": {"inputs": {"width": 1024, "height": 576, "video_frames": 25, "motion_bucket_id": 127, "fps": 6, "augmentation_level": 0, "clip_vision": ["2", 1], "init_image": ["1", 0], "vae": ["2", 2]}, "class_type": "SVD_img2vid_Conditioning", "_meta": {"title": "SVD img2vid Conditioning"}}, "4": {"inputs": {"seed": 42, "steps": 20, "cfg": 2.5, "sampler_name": "euler", "scheduler": "karras", "denoise": 1, "model": ["2", 0], "positive": ["3", 0], "negative": ["3", 1], "latent_image": ["3", 2]}, "class_type": "K<PERSON><PERSON><PERSON>", "_meta": {"title": "K<PERSON><PERSON><PERSON>"}}, "5": {"inputs": {"samples": ["4", 0], "vae": ["2", 2]}, "class_type": "VAEDecode", "_meta": {"title": "VAE Decode"}}, "6": {"inputs": {"frame_rate": 6, "loop_count": 0, "filename_prefix": "SVD_output", "format": "video/h264-mp4", "pix_fmt": "yuv420p", "crf": 19, "save_metadata": true, "pingpong": false, "save_output": true, "images": ["5", 0]}, "class_type": "VHS_VideoCombine", "_meta": {"title": "Video Combine 🎥🅥🅗🅢"}}}