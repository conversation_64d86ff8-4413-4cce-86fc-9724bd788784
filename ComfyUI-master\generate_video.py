#!/usr/bin/env python3
"""
ComfyUI SVD Video Generation Script
"""

import json
import requests
import time
import uuid
import os

# ComfyUI server settings
SERVER_ADDRESS = "127.0.0.1:8188"
CLIENT_ID = str(uuid.uuid4())

def queue_prompt(prompt):
    """Queue a prompt for execution"""
    p = {"prompt": prompt, "client_id": CLIENT_ID}
    data = json.dumps(p).encode('utf-8')
    req = requests.post(f"http://{SERVER_ADDRESS}/prompt", data=data)
    return json.loads(req.text)

def get_image(filename, subfolder, folder_type):
    """Get an image from ComfyUI"""
    data = {"filename": filename, "subfolder": subfolder, "type": folder_type}
    url_values = "&".join([f"{k}={v}" for k, v in data.items()])
    response = requests.get(f"http://{SERVER_ADDRESS}/view?{url_values}")
    return response.content

def get_history(prompt_id):
    """Get the history of a prompt"""
    response = requests.get(f"http://{SERVER_ADDRESS}/history/{prompt_id}")
    return json.loads(response.text)

def get_images(ws, prompt):
    """Get images from a prompt execution"""
    response = queue_prompt(prompt)
    print(f"Queue response: {response}")

    if 'prompt_id' not in response:
        print(f"Error: No prompt_id in response: {response}")
        return None

    prompt_id = response['prompt_id']
    output_images = {}

    print(f"Queued prompt with ID: {prompt_id}")
    print("Waiting for execution to complete...")

    while True:
        history = get_history(prompt_id)
        if prompt_id in history:
            history = history[prompt_id]
            if 'outputs' in history:
                break
        time.sleep(1)

    print("Execution completed!")
    return history

def main():
    """Main function to generate SVD video"""
    print("🎬 Starting SVD Video Generation...")
    
    # Load the workflow
    with open('svd_workflow.json', 'r', encoding='utf-8') as f:
        workflow = json.load(f)
    
    print("📋 Workflow loaded successfully")
    print("🚀 Executing SVD video generation...")
    
    try:
        # Execute the workflow
        history = get_images(None, workflow)
        
        print("✅ Video generation completed!")
        print("📁 Output saved to ComfyUI output directory")
        
        # Print output information
        if 'outputs' in history:
            for node_id, node_output in history['outputs'].items():
                if 'gifs' in node_output:
                    for gif in node_output['gifs']:
                        print(f"🎥 Generated video: {gif['filename']}")
                if 'videos' in node_output:
                    for video in node_output['videos']:
                        print(f"🎥 Generated video: {video['filename']}")
        
    except Exception as e:
        print(f"❌ Error during video generation: {e}")
        return False
    
    return True

if __name__ == "__main__":
    main()
