## ComfyUI-Manager: installing dependencies done.
[2025-06-28 17:34:50.512] ** ComfyUI startup time: 2025-06-28 17:34:50.512
[2025-06-28 17:34:50.512] ** Platform: Windows
[2025-06-28 17:34:50.512] ** Python version: 3.13.5 (tags/v3.13.5:6cb20a2, Jun 11 2025, 16:15:46) [MSC v.1943 64 bit (AMD64)]
[2025-06-28 17:34:50.512] ** Python executable: C:\Users\<USER>\ai\ComfiGit\comfyui_env\Scripts\python.exe
[2025-06-28 17:34:50.513] ** ComfyUI Path: C:\Users\<USER>\ai\ComfiGit\ComfyUI-master
[2025-06-28 17:34:50.513] ** ComfyUI Base Folder Path: C:\Users\<USER>\ai\ComfiGit\ComfyUI-master
[2025-06-28 17:34:50.513] ** User directory: C:\Users\<USER>\ai\ComfiGit\ComfyUI-master\user
[2025-06-28 17:34:50.513] ** ComfyUI-Manager config path: C:\Users\<USER>\ai\ComfiGit\ComfyUI-master\user\default\ComfyUI-Manager\config.ini
[2025-06-28 17:34:50.513] ** Log path: C:\Users\<USER>\ai\ComfiGit\ComfyUI-master\user\comfyui.log
[ComfyUI-Manager] In Python 3.13 and above, PIP Fixer does not downgrade `numpy` below version 2.0. If you need to force a downgrade of `numpy`, please use `pip_auto_fix.list`.
[2025-06-28 17:34:51.269] 
Prestartup times for custom nodes:
[2025-06-28 17:34:51.269]    2.1 seconds: C:\Users\<USER>\ai\ComfiGit\ComfyUI-master\custom_nodes\ComfyUI-Manager
[2025-06-28 17:34:51.269] 
[2025-06-28 17:34:53.589] Checkpoint files will always be loaded safely.
[2025-06-28 17:34:56.809] Total VRAM 24463 MB, total RAM 64957 MB
[2025-06-28 17:34:56.809] pytorch version: 2.7.1+cu128
[2025-06-28 17:34:56.810] Set vram state to: NORMAL_VRAM
[2025-06-28 17:34:56.810] Device: cuda:0 NVIDIA GeForce RTX 5090 Laptop GPU : cudaMallocAsync
[2025-06-28 17:34:58.314] Using pytorch attention
[2025-06-28 17:35:00.299] Python version: 3.13.5 (tags/v3.13.5:6cb20a2, Jun 11 2025, 16:15:46) [MSC v.1943 64 bit (AMD64)]
[2025-06-28 17:35:00.299] ComfyUI version: 0.3.43
[2025-06-28 17:35:00.345] ComfyUI frontend version: 1.23.4
[2025-06-28 17:35:00.346] [Prompt Server] web root: C:\Users\<USER>\ai\ComfiGit\comfyui_env\Lib\site-packages\comfyui_frontend_package\static
[2025-06-28 17:35:01.079] ### Loading: ComfyUI-Impact-Pack (V8.17.1)
[2025-06-28 17:35:01.207] [Impact Pack] Wildcards loading done.
[2025-06-28 17:35:01.244] ### Loading: ComfyUI-Manager (V3.33.3)
[2025-06-28 17:35:01.244] [ComfyUI-Manager] network_mode: public
[2025-06-28 17:35:01.245] ### ComfyUI Revision: UNKNOWN (The currently installed ComfyUI is not a Git repository)
[2025-06-28 17:35:01.471] 
Import times for custom nodes:
[2025-06-28 17:35:01.471]    0.0 seconds: C:\Users\<USER>\ai\ComfiGit\ComfyUI-master\custom_nodes\websocket_image_save.py
[2025-06-28 17:35:01.471]    0.0 seconds: C:\Users\<USER>\ai\ComfiGit\ComfyUI-master\custom_nodes\ComfyUI-Custom-Scripts
[2025-06-28 17:35:01.471]    0.0 seconds: C:\Users\<USER>\ai\ComfiGit\ComfyUI-master\custom_nodes\efficiency-nodes-comfyui
[2025-06-28 17:35:01.471]    0.0 seconds: C:\Users\<USER>\ai\ComfiGit\ComfyUI-master\custom_nodes\ComfyUI-Advanced-ControlNet
[2025-06-28 17:35:01.471]    0.0 seconds: C:\Users\<USER>\ai\ComfiGit\ComfyUI-master\custom_nodes\ComfyUI-Manager
[2025-06-28 17:35:01.472]    0.1 seconds: C:\Users\<USER>\ai\ComfiGit\ComfyUI-master\custom_nodes\ComfyUI-Impact-Pack
[2025-06-28 17:35:01.472]    0.2 seconds: C:\Users\<USER>\ai\ComfiGit\ComfyUI-master\custom_nodes\ComfyUI-VideoHelperSuite
[2025-06-28 17:35:01.472] 
[2025-06-28 17:35:01.810] [ComfyUI-Manager] default cache updated: https://raw.githubusercontent.com/ltdrdata/ComfyUI-Manager/main/extension-node-map.json
[2025-06-28 17:35:01.833] [ComfyUI-Manager] default cache updated: https://raw.githubusercontent.com/ltdrdata/ComfyUI-Manager/main/github-stats.json
[2025-06-28 17:35:01.834] [ComfyUI-Manager] default cache updated: https://raw.githubusercontent.com/ltdrdata/ComfyUI-Manager/main/alter-list.json
[2025-06-28 17:35:01.841] [ComfyUI-Manager] default cache updated: https://raw.githubusercontent.com/ltdrdata/ComfyUI-Manager/main/model-list.json
[2025-06-28 17:35:01.870] [ComfyUI-Manager] default cache updated: https://raw.githubusercontent.com/ltdrdata/ComfyUI-Manager/main/custom-node-list.json
[2025-06-28 17:35:02.003] Context impl SQLiteImpl.
[2025-06-28 17:35:02.004] Will assume non-transactional DDL.
[2025-06-28 17:35:02.005] No target revision found.
[2025-06-28 17:35:02.018] Starting server

[2025-06-28 17:35:02.018] To see the GUI go to: http://127.0.0.1:8188
[2025-06-28 17:35:05.601] FETCH ComfyRegistry Data: 5/90
[2025-06-28 17:35:09.351] FETCH ComfyRegistry Data: 10/90
[2025-06-28 17:35:12.875] FETCH ComfyRegistry Data: 15/90
[2025-06-28 17:35:16.498] FETCH ComfyRegistry Data: 20/90
[2025-06-28 17:35:20.390] FETCH ComfyRegistry Data: 25/90
[2025-06-28 17:35:24.571] FETCH ComfyRegistry Data: 30/90
[2025-06-28 17:35:28.700] FETCH ComfyRegistry Data: 35/90
[2025-06-28 17:35:32.521] FETCH ComfyRegistry Data: 40/90
[2025-06-28 17:35:36.696] FETCH ComfyRegistry Data: 45/90
[2025-06-28 17:35:40.435] FETCH ComfyRegistry Data: 50/90
[2025-06-28 17:35:44.057] FETCH ComfyRegistry Data: 55/90
[2025-06-28 17:35:47.685] FETCH ComfyRegistry Data: 60/90
[2025-06-28 17:35:51.859] FETCH ComfyRegistry Data: 65/90
[2025-06-28 17:35:55.431] FETCH ComfyRegistry Data: 70/90
[2025-06-28 17:35:59.202] FETCH ComfyRegistry Data: 75/90
