## ComfyUI-Manager: installing dependencies done.
[2025-06-28 19:52:20.188] ** ComfyUI startup time: 2025-06-28 19:52:20.188
[2025-06-28 19:52:20.188] ** Platform: Windows
[2025-06-28 19:52:20.189] ** Python version: 3.10.11 (tags/v3.10.11:7d4cc5a, Apr  5 2023, 00:38:17) [MSC v.1929 64 bit (AMD64)]
[2025-06-28 19:52:20.189] ** Python executable: C:\Users\<USER>\.pyenv\pyenv-win\versions\3.10.11\python.exe
[2025-06-28 19:52:20.190] ** ComfyUI Path: C:\Users\<USER>\ai\ComfiGit\ComfyUI
[2025-06-28 19:52:20.190] ** ComfyUI Base Folder Path: C:\Users\<USER>\ai\ComfiGit\ComfyUI
[2025-06-28 19:52:20.190] ** User directory: C:\Users\<USER>\ai\ComfiGit\ComfyUI\user
[2025-06-28 19:52:20.190] ** ComfyUI-Manager config path: C:\Users\<USER>\ai\ComfiGit\ComfyUI\user\default\ComfyUI-Manager\config.ini
[2025-06-28 19:52:20.191] ** Log path: C:\Users\<USER>\ai\ComfiGit\ComfyUI\user\comfyui.log

Prestartup times for custom nodes:
[2025-06-28 19:52:21.112]    6.6 seconds: C:\Users\<USER>\ai\ComfiGit\ComfyUI\custom_nodes\ComfyUI-Manager
[2025-06-28 19:52:21.112] 
[2025-06-28 19:53:09.527] Checkpoint files will always be loaded safely.
[2025-06-28 19:53:09.557] C:\Users\<USER>\.pyenv\pyenv-win\versions\3.10.11\lib\site-packages\torch\cuda\__init__.py:235: UserWarning: 
NVIDIA GeForce RTX 5090 Laptop GPU with CUDA capability sm_120 is not compatible with the current PyTorch installation.
The current PyTorch install supports CUDA capabilities sm_50 sm_60 sm_61 sm_70 sm_75 sm_80 sm_86 sm_90.
If you want to use the NVIDIA GeForce RTX 5090 Laptop GPU GPU with PyTorch, please check the instructions at https://pytorch.org/get-started/locally/

  warnings.warn(
[2025-06-28 19:53:09.635] Total VRAM 24463 MB, total RAM 64957 MB
[2025-06-28 19:53:09.635] pytorch version: 2.5.1+cu121
[2025-06-28 19:53:09.635] Set vram state to: NORMAL_VRAM
[2025-06-28 19:53:09.635] Device: cuda:0 NVIDIA GeForce RTX 5090 Laptop GPU : cudaMallocAsync
[2025-06-28 19:53:28.954] Using pytorch attention
[2025-06-28 19:53:37.411] Python version: 3.10.11 (tags/v3.10.11:7d4cc5a, Apr  5 2023, 00:38:17) [MSC v.1929 64 bit (AMD64)]
[2025-06-28 19:53:37.411] ComfyUI version: 0.3.43
[2025-06-28 19:53:37.442] ComfyUI frontend version: 1.23.4
[2025-06-28 19:53:37.443] [Prompt Server] web root: C:\Users\<USER>\.pyenv\pyenv-win\versions\3.10.11\lib\site-packages\comfyui_frontend_package\static
[2025-06-28 19:53:39.008] ### Loading: ComfyUI-Impact-Pack (V8.17.1)
[2025-06-28 19:53:39.009] [Impact Pack] Failed to import due to several dependencies are missing!!!!
[2025-06-28 19:53:39.011] Traceback (most recent call last):
  File "C:\Users\<USER>\ai\ComfiGit\ComfyUI\nodes.py", line 2124, in load_custom_node
    module_spec.loader.exec_module(module)
  File "<frozen importlib._bootstrap_external>", line 883, in exec_module
  File "<frozen importlib._bootstrap>", line 241, in _call_with_frames_removed
  File "C:\Users\<USER>\ai\ComfiGit\ComfyUI\custom_nodes\ComfyUI-Impact-Pack\__init__.py", line 46, in <module>
    raise e
  File "C:\Users\<USER>\ai\ComfiGit\ComfyUI\custom_nodes\ComfyUI-Impact-Pack\__init__.py", line 28, in <module>
    import cv2
ModuleNotFoundError: No module named 'cv2'

[2025-06-28 19:53:39.011] Cannot import C:\Users\<USER>\ai\ComfiGit\ComfyUI\custom_nodes\ComfyUI-Impact-Pack module for custom nodes: No module named 'cv2'
[2025-06-28 19:53:39.052] ### Loading: ComfyUI-Manager (V3.33.3)
[2025-06-28 19:53:39.052] [ComfyUI-Manager] network_mode: public
[2025-06-28 19:53:39.281] ### ComfyUI Version: v0.3.43 | Released on '2025-06-27'
[2025-06-28 19:53:39.594] [VideoHelperSuite] - [0;33mWARNING[0m - Failed to import imageio_ffmpeg
[2025-06-28 19:53:39.600] [VideoHelperSuite] - [0;31mERROR[0m - No valid ffmpeg found.
[2025-06-28 19:53:39.631] Traceback (most recent call last):
  File "C:\Users\<USER>\ai\ComfiGit\ComfyUI\nodes.py", line 2124, in load_custom_node
    module_spec.loader.exec_module(module)
  File "<frozen importlib._bootstrap_external>", line 883, in exec_module
  File "<frozen importlib._bootstrap>", line 241, in _call_with_frames_removed
  File "C:\Users\<USER>\ai\ComfiGit\ComfyUI\custom_nodes\ComfyUI-VideoHelperSuite\__init__.py", line 1, in <module>
    from .videohelpersuite.nodes import NODE_CLASS_MAPPINGS, NODE_DISPLAY_NAME_MAPPINGS
  File "C:\Users\<USER>\ai\ComfiGit\ComfyUI\custom_nodes\ComfyUI-VideoHelperSuite\videohelpersuite\nodes.py", line 20, in <module>
    from .load_video_nodes import LoadVideoUpload, LoadVideoPath, LoadVideoFFmpegUpload, LoadVideoFFmpegPath, LoadImagePath
  File "C:\Users\<USER>\ai\ComfiGit\ComfyUI\custom_nodes\ComfyUI-VideoHelperSuite\videohelpersuite\load_video_nodes.py", line 6, in <module>
    import cv2
ModuleNotFoundError: No module named 'cv2'

[2025-06-28 19:53:39.631] Cannot import C:\Users\<USER>\ai\ComfiGit\ComfyUI\custom_nodes\ComfyUI-VideoHelperSuite module for custom nodes: No module named 'cv2'
[2025-06-28 19:53:39.671] [ComfyUI-Manager] default cache updated: https://raw.githubusercontent.com/ltdrdata/ComfyUI-Manager/main/alter-list.json
[2025-06-28 19:53:39.673] Traceback (most recent call last):
  File "C:\Users\<USER>\ai\ComfiGit\ComfyUI\nodes.py", line 2124, in load_custom_node
    module_spec.loader.exec_module(module)
  File "<frozen importlib._bootstrap_external>", line 883, in exec_module
  File "<frozen importlib._bootstrap>", line 241, in _call_with_frames_removed
  File "C:\Users\<USER>\ai\ComfiGit\ComfyUI\custom_nodes\ComfyUI-WanVideoWrapper\__init__.py", line 1, in <module>
    from .nodes import NODE_CLASS_MAPPINGS, NODE_DISPLAY_NAME_MAPPINGS
  File "C:\Users\<USER>\ai\ComfiGit\ComfyUI\custom_nodes\ComfyUI-WanVideoWrapper\nodes.py", line 5, in <module>
    from .utils import log, print_memory, apply_lora, clip_encode_image_tiled, fourier_filter
  File "C:\Users\<USER>\ai\ComfiGit\ComfyUI\custom_nodes\ComfyUI-WanVideoWrapper\utils.py", line 8, in <module>
    from accelerate.utils import set_module_tensor_to_device
ModuleNotFoundError: No module named 'accelerate'

[2025-06-28 19:53:39.673] Cannot import C:\Users\<USER>\ai\ComfiGit\ComfyUI\custom_nodes\ComfyUI-WanVideoWrapper module for custom nodes: No module named 'accelerate'
[2025-06-28 19:53:39.679] [ComfyUI-Manager] default cache updated: https://raw.githubusercontent.com/ltdrdata/ComfyUI-Manager/main/model-list.json
[2025-06-28 19:53:39.773] [ComfyUI-Manager] default cache updated: https://raw.githubusercontent.com/ltdrdata/ComfyUI-Manager/main/extension-node-map.json
[2025-06-28 19:53:39.777] [93mEfficiency Nodes Warning:[0m Failed to import python package 'simpleeval'; related nodes disabled.
[2025-06-28 19:53:39.777] 
[2025-06-28 19:53:39.801] 
Import times for custom nodes:
[2025-06-28 19:53:39.802]    0.0 seconds: C:\Users\<USER>\ai\ComfiGit\ComfyUI\custom_nodes\websocket_image_save.py
[2025-06-28 19:53:39.803]    0.0 seconds: C:\Users\<USER>\ai\ComfiGit\ComfyUI\custom_nodes\ComfyUI-WanVideoKsampler
[2025-06-28 19:53:39.803]    0.0 seconds (IMPORT FAILED): C:\Users\<USER>\ai\ComfiGit\ComfyUI\custom_nodes\ComfyUI-Impact-Pack
[2025-06-28 19:53:39.804]    0.0 seconds (IMPORT FAILED): C:\Users\<USER>\ai\ComfiGit\ComfyUI\custom_nodes\ComfyUI-WanVideoWrapper
[2025-06-28 19:53:39.804]    0.0 seconds: C:\Users\<USER>\ai\ComfiGit\ComfyUI\custom_nodes\ComfyUI-Custom-Scripts
[2025-06-28 19:53:39.805]    0.1 seconds (IMPORT FAILED): C:\Users\<USER>\ai\ComfiGit\ComfyUI\custom_nodes\ComfyUI-VideoHelperSuite
[2025-06-28 19:53:39.805]    0.1 seconds: C:\Users\<USER>\ai\ComfiGit\ComfyUI\custom_nodes\ComfyUI-Advanced-ControlNet
[2025-06-28 19:53:39.805]    0.1 seconds: C:\Users\<USER>\ai\ComfiGit\ComfyUI\custom_nodes\efficiency-nodes-comfyui
[2025-06-28 19:53:39.806]    0.2 seconds: C:\Users\<USER>\ai\ComfiGit\ComfyUI\custom_nodes\ComfyUI-AnimateDiff-Evolved
[2025-06-28 19:53:39.806]    0.6 seconds: C:\Users\<USER>\ai\ComfiGit\ComfyUI\custom_nodes\ComfyUI-Manager
[2025-06-28 19:53:39.806] 
[2025-06-28 19:53:39.822] [ComfyUI-Manager] default cache updated: https://raw.githubusercontent.com/ltdrdata/ComfyUI-Manager/main/custom-node-list.json
[2025-06-28 19:53:40.010] [ComfyUI-Manager] default cache updated: https://raw.githubusercontent.com/ltdrdata/ComfyUI-Manager/main/github-stats.json
[2025-06-28 19:53:41.370] Context impl SQLiteImpl.
[2025-06-28 19:53:41.370] Will assume non-transactional DDL.
[2025-06-28 19:53:41.372] No target revision found.
[2025-06-28 19:53:41.386] Starting server

[2025-06-28 19:53:41.386] To see the GUI go to: http://127.0.0.1:8188
[2025-06-28 19:53:43.560] FETCH ComfyRegistry Data: 5/90
[2025-06-28 19:53:47.311] FETCH ComfyRegistry Data: 10/90
[2025-06-28 19:53:51.041] FETCH ComfyRegistry Data: 15/90
[2025-06-28 19:53:54.888] FETCH ComfyRegistry Data: 20/90
[2025-06-28 19:54:12.246] FETCH ComfyRegistry Data: 25/90
[2025-06-28 19:54:22.196] FETCH ComfyRegistry Data: 30/90
[2025-06-28 19:54:25.909] FETCH ComfyRegistry Data: 35/90
[2025-06-28 19:54:29.591] FETCH ComfyRegistry Data: 40/90
[2025-06-28 19:54:33.264] FETCH ComfyRegistry Data: 45/90
[2025-06-28 19:54:37.337] FETCH ComfyRegistry Data: 50/90
[2025-06-28 19:54:40.997] FETCH ComfyRegistry Data: 55/90
[2025-06-28 19:54:44.699] FETCH ComfyRegistry Data: 60/90
[2025-06-28 19:54:48.369] FETCH ComfyRegistry Data: 65/90
[2025-06-28 19:54:52.618] FETCH ComfyRegistry Data: 70/90
