## ComfyUI-Manager: installing dependencies done.
[2025-06-28 17:47:16.340] ** ComfyUI startup time: 2025-06-28 17:47:16.340
[2025-06-28 17:47:16.340] ** Platform: Windows
[2025-06-28 17:47:16.340] ** Python version: 3.13.5 (tags/v3.13.5:6cb20a2, Jun 11 2025, 16:15:46) [MSC v.1943 64 bit (AMD64)]
[2025-06-28 17:47:16.340] ** Python executable: C:\Users\<USER>\ai\ComfiGit\comfyui_env\Scripts\python.exe
[2025-06-28 17:47:16.341] ** ComfyUI Path: C:\Users\<USER>\ai\ComfiGit\ComfyUI
[2025-06-28 17:47:16.341] ** ComfyUI Base Folder Path: C:\Users\<USER>\ai\ComfiGit\ComfyUI
[2025-06-28 17:47:16.341] ** User directory: C:\Users\<USER>\ai\ComfiGit\ComfyUI\user
[2025-06-28 17:47:16.342] ** ComfyUI-Manager config path: C:\Users\<USER>\ai\ComfiGit\ComfyUI\user\default\ComfyUI-Manager\config.ini
[2025-06-28 17:47:16.342] ** Log path: C:\Users\<USER>\ai\ComfiGit\ComfyUI\user\comfyui.log
[ComfyUI-Manager] In Python 3.13 and above, PIP Fixer does not downgrade `numpy` below version 2.0. If you need to force a downgrade of `numpy`, please use `pip_auto_fix.list`.
[2025-06-28 17:47:17.053] 
Prestartup times for custom nodes:
[2025-06-28 17:47:17.053]    1.9 seconds: C:\Users\<USER>\ai\ComfiGit\ComfyUI\custom_nodes\ComfyUI-Manager
[2025-06-28 17:47:17.053] 
[2025-06-28 17:47:18.304] Checkpoint files will always be loaded safely.
[2025-06-28 17:47:18.432] Total VRAM 24463 MB, total RAM 64957 MB
[2025-06-28 17:47:18.432] pytorch version: 2.7.1+cu128
[2025-06-28 17:47:18.433] Set vram state to: NORMAL_VRAM
[2025-06-28 17:47:18.433] Device: cuda:0 NVIDIA GeForce RTX 5090 Laptop GPU : cudaMallocAsync
[2025-06-28 17:47:19.368] Using pytorch attention
[2025-06-28 17:47:20.602] Python version: 3.13.5 (tags/v3.13.5:6cb20a2, Jun 11 2025, 16:15:46) [MSC v.1943 64 bit (AMD64)]
[2025-06-28 17:47:20.603] ComfyUI version: 0.3.43
[2025-06-28 17:47:20.648] ComfyUI frontend version: 1.23.4
[2025-06-28 17:47:20.650] [Prompt Server] web root: C:\Users\<USER>\ai\ComfiGit\comfyui_env\Lib\site-packages\comfyui_frontend_package\static
[2025-06-28 17:47:21.144] ### Loading: ComfyUI-Impact-Pack (V8.17.1)
[2025-06-28 17:47:21.201] [Impact Pack] Wildcards loading done.
[2025-06-28 17:47:21.206] ### Loading: ComfyUI-Manager (V3.33.3)
[2025-06-28 17:47:21.207] [ComfyUI-Manager] network_mode: public
[2025-06-28 17:47:21.415] ### ComfyUI Version: v0.3.43 | Released on '2025-06-27'
[2025-06-28 17:47:21.772] [ComfyUI-Manager] default cache updated: https://raw.githubusercontent.com/ltdrdata/ComfyUI-Manager/main/alter-list.json
[2025-06-28 17:47:21.780] [ComfyUI-Manager] default cache updated: https://raw.githubusercontent.com/ltdrdata/ComfyUI-Manager/main/model-list.json
[2025-06-28 17:47:21.798] [ComfyUI-Manager] default cache updated: https://raw.githubusercontent.com/ltdrdata/ComfyUI-Manager/main/github-stats.json
[2025-06-28 17:47:21.829] [ComfyUI-Manager] default cache updated: https://raw.githubusercontent.com/ltdrdata/ComfyUI-Manager/main/extension-node-map.json
[2025-06-28 17:47:21.854] [ComfyUI-Manager] default cache updated: https://raw.githubusercontent.com/ltdrdata/ComfyUI-Manager/main/custom-node-list.json
[2025-06-28 17:47:21.920] 
Import times for custom nodes:
[2025-06-28 17:47:21.920]    0.0 seconds: C:\Users\<USER>\ai\ComfiGit\ComfyUI\custom_nodes\websocket_image_save.py
[2025-06-28 17:47:21.920]    0.0 seconds: C:\Users\<USER>\ai\ComfiGit\ComfyUI\custom_nodes\ComfyUI-Custom-Scripts
[2025-06-28 17:47:21.921]    0.0 seconds: C:\Users\<USER>\ai\ComfiGit\ComfyUI\custom_nodes\efficiency-nodes-comfyui
[2025-06-28 17:47:21.921]    0.0 seconds: C:\Users\<USER>\ai\ComfiGit\ComfyUI\custom_nodes\ComfyUI-Advanced-ControlNet
[2025-06-28 17:47:21.921]    0.1 seconds: C:\Users\<USER>\ai\ComfiGit\ComfyUI\custom_nodes\ComfyUI-Impact-Pack
[2025-06-28 17:47:21.921]    0.2 seconds: C:\Users\<USER>\ai\ComfiGit\ComfyUI\custom_nodes\ComfyUI-VideoHelperSuite
[2025-06-28 17:47:21.921]    0.5 seconds: C:\Users\<USER>\ai\ComfiGit\ComfyUI\custom_nodes\ComfyUI-Manager
[2025-06-28 17:47:21.921] 
[2025-06-28 17:47:22.123] Context impl SQLiteImpl.
[2025-06-28 17:47:22.123] Will assume non-transactional DDL.
[2025-06-28 17:47:22.124] No target revision found.
[2025-06-28 17:47:22.135] Starting server

[2025-06-28 17:47:22.136] To see the GUI go to: http://127.0.0.1:8188
[2025-06-28 17:47:25.072] FETCH ComfyRegistry Data: 5/90
[2025-06-28 17:47:28.961] FETCH ComfyRegistry Data: 10/90
[2025-06-28 17:47:32.595] FETCH ComfyRegistry Data: 15/90
[2025-06-28 17:47:36.469] FETCH ComfyRegistry Data: 20/90
[2025-06-28 17:47:40.105] FETCH ComfyRegistry Data: 25/90
[2025-06-28 17:47:43.953] FETCH ComfyRegistry Data: 30/90
[2025-06-28 17:47:47.608] FETCH ComfyRegistry Data: 35/90
[2025-06-28 17:47:51.329] FETCH ComfyRegistry Data: 40/90
[2025-06-28 17:47:54.981] FETCH ComfyRegistry Data: 45/90
[2025-06-28 17:47:58.594] FETCH ComfyRegistry Data: 50/90
[2025-06-28 17:48:02.342] FETCH ComfyRegistry Data: 55/90
[2025-06-28 17:48:05.914] FETCH ComfyRegistry Data: 60/90
[2025-06-28 17:48:09.605] FETCH ComfyRegistry Data: 65/90
[2025-06-28 17:48:13.398] FETCH ComfyRegistry Data: 70/90
[2025-06-28 17:48:14.727] got prompt
[2025-06-28 17:48:14.791] model weight dtype torch.float16, manual cast: None
[2025-06-28 17:48:14.796] model_type EPS
[2025-06-28 17:48:15.251] Using pytorch attention in VAE
[2025-06-28 17:48:15.252] Using pytorch attention in VAE
[2025-06-28 17:48:15.333] VAE load device: cuda:0, offload device: cpu, dtype: torch.bfloat16
[2025-06-28 17:48:15.413] CLIP/text encoder model load device: cuda:0, offload device: cpu, current: cpu, dtype: torch.float16
[2025-06-28 17:48:15.895] Requested to load SD1ClipModel
[2025-06-28 17:48:16.126] loaded completely 21615.8 235.84423828125 True
[2025-06-28 17:48:16.553] Requested to load BaseModel
[2025-06-28 17:48:16.923] loaded completely 21301.83067779541 1639.406135559082 True
[2025-06-28 17:48:16.934] FETCH ComfyRegistry Data: 75/90
[2025-06-28 17:48:18.722] 
100%|██████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████| 20/20 [00:01<00:00, 14.56it/s]
100%|██████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████| 20/20 [00:01<00:00, 11.90it/s]
[2025-06-28 17:48:18.761] Requested to load AutoencoderKL
[2025-06-28 17:48:18.884] loaded completely 19390.61699295044 159.55708122253418 True
[2025-06-28 17:48:19.126] Prompt executed in 4.40 seconds
[2025-06-28 17:48:21.344] FETCH ComfyRegistry Data: 80/90
[2025-06-28 17:48:24.847] FETCH ComfyRegistry Data: 85/90
[2025-06-28 17:48:28.388] FETCH ComfyRegistry Data: 90/90
[2025-06-28 17:48:28.889] FETCH ComfyRegistry Data [DONE]
[2025-06-28 17:48:28.965] [ComfyUI-Manager] default cache updated: https://api.comfy.org/nodes
[2025-06-28 17:48:28.974] FETCH DATA from: https://raw.githubusercontent.com/ltdrdata/ComfyUI-Manager/main/custom-node-list.json [DONE]
[2025-06-28 17:48:29.089] [ComfyUI-Manager] All startup tasks have been completed.
