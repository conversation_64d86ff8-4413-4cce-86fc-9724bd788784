## ComfyUI-Manager: installing dependencies done.
[2025-06-28 18:43:00.516] ** ComfyUI startup time: 2025-06-28 18:43:00.516
[2025-06-28 18:43:00.516] ** Platform: Windows
[2025-06-28 18:43:00.516] ** Python version: 3.13.5 (tags/v3.13.5:6cb20a2, Jun 11 2025, 16:15:46) [MSC v.1943 64 bit (AMD64)]
[2025-06-28 18:43:00.516] ** Python executable: C:\Users\<USER>\ai\ComfiGit\comfyui_env\Scripts\python.exe
[2025-06-28 18:43:00.516] ** ComfyUI Path: C:\Users\<USER>\ai\ComfiGit\ComfyUI
[2025-06-28 18:43:00.516] ** ComfyUI Base Folder Path: C:\Users\<USER>\ai\ComfiGit\ComfyUI
[2025-06-28 18:43:00.516] ** User directory: C:\Users\<USER>\ai\ComfiGit\ComfyUI\user
[2025-06-28 18:43:00.518] ** ComfyUI-Manager config path: C:\Users\<USER>\ai\ComfiGit\ComfyUI\user\default\ComfyUI-Manager\config.ini
[2025-06-28 18:43:00.518] ** Log path: C:\Users\<USER>\ai\ComfiGit\ComfyUI\user\comfyui.log
[ComfyUI-Manager] In Python 3.13 and above, PIP Fixer does not downgrade `numpy` below version 2.0. If you need to force a downgrade of `numpy`, please use `pip_auto_fix.list`.
[2025-06-28 18:43:01.251] 
Prestartup times for custom nodes:
[2025-06-28 18:43:01.251]    2.0 seconds: C:\Users\<USER>\ai\ComfiGit\ComfyUI\custom_nodes\ComfyUI-Manager
[2025-06-28 18:43:01.251] 
[2025-06-28 18:43:02.543] Checkpoint files will always be loaded safely.
[2025-06-28 18:43:02.678] Total VRAM 24463 MB, total RAM 64957 MB
[2025-06-28 18:43:02.678] pytorch version: 2.7.1+cu128
[2025-06-28 18:43:02.679] Set vram state to: NORMAL_VRAM
[2025-06-28 18:43:02.679] Device: cuda:0 NVIDIA GeForce RTX 5090 Laptop GPU : cudaMallocAsync
[2025-06-28 18:43:03.626] Using pytorch attention
[2025-06-28 18:43:04.872] Python version: 3.13.5 (tags/v3.13.5:6cb20a2, Jun 11 2025, 16:15:46) [MSC v.1943 64 bit (AMD64)]
[2025-06-28 18:43:04.872] ComfyUI version: 0.3.43
[2025-06-28 18:43:04.915] ComfyUI frontend version: 1.23.4
[2025-06-28 18:43:04.916] [Prompt Server] web root: C:\Users\<USER>\ai\ComfiGit\comfyui_env\Lib\site-packages\comfyui_frontend_package\static
[2025-06-28 18:43:05.402] Traceback (most recent call last):
  File "C:\Users\<USER>\ai\ComfiGit\ComfyUI\nodes.py", line 2124, in load_custom_node
    module_spec.loader.exec_module(module)
    ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~^^^^^^^^
  File "<frozen importlib._bootstrap_external>", line 1022, in exec_module
  File "<frozen importlib._bootstrap_external>", line 1159, in get_code
  File "<frozen importlib._bootstrap_external>", line 1217, in get_data
FileNotFoundError: [Errno 2] No such file or directory: 'C:\\Users\\<USER>\\ai\\ComfiGit\\ComfyUI\\custom_nodes\\ComfyUI\\__init__.py'

[2025-06-28 18:43:05.403] Cannot import C:\Users\<USER>\ai\ComfiGit\ComfyUI\custom_nodes\ComfyUI module for custom nodes: [Errno 2] No such file or directory: 'C:\\Users\\<USER>\\ai\\ComfiGit\\ComfyUI\\custom_nodes\\ComfyUI\\__init__.py'
[2025-06-28 18:43:05.547] ### Loading: ComfyUI-Impact-Pack (V8.17.1)
[2025-06-28 18:43:05.603] [Impact Pack] Wildcards loading done.
[2025-06-28 18:43:05.608] ### Loading: ComfyUI-Manager (V3.33.3)
[2025-06-28 18:43:05.609] [ComfyUI-Manager] network_mode: public
[2025-06-28 18:43:05.843] ### ComfyUI Version: v0.3.43 | Released on '2025-06-27'
[2025-06-28 18:43:06.205] [ComfyUI-Manager] default cache updated: https://raw.githubusercontent.com/ltdrdata/ComfyUI-Manager/main/alter-list.json
[2025-06-28 18:43:06.213] [ComfyUI-Manager] default cache updated: https://raw.githubusercontent.com/ltdrdata/ComfyUI-Manager/main/model-list.json
[2025-06-28 18:43:06.233] [ComfyUI-Manager] default cache updated: https://raw.githubusercontent.com/ltdrdata/ComfyUI-Manager/main/github-stats.json
[2025-06-28 18:43:06.267] [ComfyUI-Manager] default cache updated: https://raw.githubusercontent.com/ltdrdata/ComfyUI-Manager/main/extension-node-map.json
[2025-06-28 18:43:06.289] [ComfyUI-Manager] default cache updated: https://raw.githubusercontent.com/ltdrdata/ComfyUI-Manager/main/custom-node-list.json
[2025-06-28 18:43:06.355] 
Import times for custom nodes:
[2025-06-28 18:43:06.355]    0.0 seconds: C:\Users\<USER>\ai\ComfiGit\ComfyUI\custom_nodes\websocket_image_save.py
[2025-06-28 18:43:06.355]    0.0 seconds (IMPORT FAILED): C:\Users\<USER>\ai\ComfiGit\ComfyUI\custom_nodes\ComfyUI
[2025-06-28 18:43:06.355]    0.0 seconds: C:\Users\<USER>\ai\ComfiGit\ComfyUI\custom_nodes\ComfyUI-Custom-Scripts
[2025-06-28 18:43:06.355]    0.0 seconds: C:\Users\<USER>\ai\ComfiGit\ComfyUI\custom_nodes\efficiency-nodes-comfyui
[2025-06-28 18:43:06.356]    0.0 seconds: C:\Users\<USER>\ai\ComfiGit\ComfyUI\custom_nodes\ComfyUI-Advanced-ControlNet
[2025-06-28 18:43:06.356]    0.1 seconds: C:\Users\<USER>\ai\ComfiGit\ComfyUI\custom_nodes\ComfyUI-Impact-Pack
[2025-06-28 18:43:06.356]    0.1 seconds: C:\Users\<USER>\ai\ComfiGit\ComfyUI\custom_nodes\ComfyUI-AnimateDiff-Evolved
[2025-06-28 18:43:06.356]    0.2 seconds: C:\Users\<USER>\ai\ComfiGit\ComfyUI\custom_nodes\ComfyUI-VideoHelperSuite
[2025-06-28 18:43:06.356]    0.5 seconds: C:\Users\<USER>\ai\ComfiGit\ComfyUI\custom_nodes\ComfyUI-Manager
[2025-06-28 18:43:06.356] 
[2025-06-28 18:43:06.560] Context impl SQLiteImpl.
[2025-06-28 18:43:06.560] Will assume non-transactional DDL.
[2025-06-28 18:43:06.561] No target revision found.
[2025-06-28 18:43:06.573] Starting server

[2025-06-28 18:43:06.574] To see the GUI go to: http://127.0.0.1:8188
[2025-06-28 18:43:09.418] FETCH ComfyRegistry Data: 5/90
[2025-06-28 18:43:13.232] FETCH ComfyRegistry Data: 10/90
[2025-06-28 18:43:16.805] FETCH ComfyRegistry Data: 15/90
[2025-06-28 18:43:21.455] FETCH ComfyRegistry Data: 20/90
[2025-06-28 18:43:24.993] FETCH ComfyRegistry Data: 25/90
[2025-06-28 18:43:28.526] FETCH ComfyRegistry Data: 30/90
[2025-06-28 18:43:31.983] FETCH ComfyRegistry Data: 35/90
[2025-06-28 18:43:35.692] FETCH ComfyRegistry Data: 40/90
[2025-06-28 18:43:39.961] FETCH ComfyRegistry Data: 45/90
[2025-06-28 18:43:43.864] FETCH ComfyRegistry Data: 50/90
[2025-06-28 18:43:47.510] FETCH ComfyRegistry Data: 55/90
[2025-06-28 18:43:51.220] FETCH ComfyRegistry Data: 60/90
[2025-06-28 18:43:54.788] FETCH ComfyRegistry Data: 65/90
[2025-06-28 18:43:58.564] FETCH ComfyRegistry Data: 70/90
[2025-06-28 18:44:02.075] FETCH ComfyRegistry Data: 75/90
[2025-06-28 18:44:05.708] FETCH ComfyRegistry Data: 80/90
[2025-06-28 18:44:09.241] FETCH ComfyRegistry Data: 85/90
[2025-06-28 18:44:12.737] FETCH ComfyRegistry Data: 90/90
[2025-06-28 18:44:13.238] FETCH ComfyRegistry Data [DONE]
[2025-06-28 18:44:13.330] [ComfyUI-Manager] default cache updated: https://api.comfy.org/nodes
[2025-06-28 18:44:13.342] FETCH DATA from: https://raw.githubusercontent.com/ltdrdata/ComfyUI-Manager/main/custom-node-list.json [DONE]
[2025-06-28 18:44:13.468] [ComfyUI-Manager] All startup tasks have been completed.
