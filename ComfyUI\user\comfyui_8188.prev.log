## ComfyUI-Manager: installing dependencies done.
[2025-06-28 19:06:15.384] ** ComfyUI startup time: 2025-06-28 19:06:15.384
[2025-06-28 19:06:15.385] ** Platform: Windows
[2025-06-28 19:06:15.385] ** Python version: 3.13.5 (tags/v3.13.5:6cb20a2, Jun 11 2025, 16:15:46) [MSC v.1943 64 bit (AMD64)]
[2025-06-28 19:06:15.385] ** Python executable: C:\Users\<USER>\ai\ComfiGit\comfyui_env\Scripts\python.exe
[2025-06-28 19:06:15.385] ** ComfyUI Path: C:\Users\<USER>\ai\ComfiGit\ComfyUI
[2025-06-28 19:06:15.385] ** ComfyUI Base Folder Path: C:\Users\<USER>\ai\ComfiGit\ComfyUI
[2025-06-28 19:06:15.385] ** User directory: C:\Users\<USER>\ai\ComfiGit\ComfyUI\user
[2025-06-28 19:06:15.386] ** ComfyUI-Manager config path: C:\Users\<USER>\ai\ComfiGit\ComfyUI\user\default\ComfyUI-Manager\config.ini
[2025-06-28 19:06:15.386] ** Log path: C:\Users\<USER>\ai\ComfiGit\ComfyUI\user\comfyui.log
[ComfyUI-Manager] In Python 3.13 and above, PIP Fixer does not downgrade `numpy` below version 2.0. If you need to force a downgrade of `numpy`, please use `pip_auto_fix.list`.
[2025-06-28 19:06:16.177] 
Prestartup times for custom nodes:
[2025-06-28 19:06:16.177]    2.1 seconds: C:\Users\<USER>\ai\ComfiGit\ComfyUI\custom_nodes\ComfyUI-Manager
[2025-06-28 19:06:16.177] 
[2025-06-28 19:06:17.467] Checkpoint files will always be loaded safely.
[2025-06-28 19:06:17.603] Total VRAM 24463 MB, total RAM 64957 MB
[2025-06-28 19:06:17.603] pytorch version: 2.7.1+cu128
[2025-06-28 19:06:17.604] Set vram state to: NORMAL_VRAM
[2025-06-28 19:06:17.604] Device: cuda:0 NVIDIA GeForce RTX 5090 Laptop GPU : cudaMallocAsync
[2025-06-28 19:06:18.543] Using pytorch attention
[2025-06-28 19:06:19.926] Python version: 3.13.5 (tags/v3.13.5:6cb20a2, Jun 11 2025, 16:15:46) [MSC v.1943 64 bit (AMD64)]
[2025-06-28 19:06:19.926] ComfyUI version: 0.3.43
[2025-06-28 19:06:19.983] ComfyUI frontend version: 1.23.4
[2025-06-28 19:06:19.985] [Prompt Server] web root: C:\Users\<USER>\ai\ComfiGit\comfyui_env\Lib\site-packages\comfyui_frontend_package\static
[2025-06-28 19:06:20.572] ### Loading: ComfyUI-Impact-Pack (V8.17.1)
[2025-06-28 19:06:20.634] [Impact Pack] Wildcards loading done.
[2025-06-28 19:06:20.642] ### Loading: ComfyUI-Manager (V3.33.3)
[2025-06-28 19:06:20.643] [ComfyUI-Manager] network_mode: public
[2025-06-28 19:06:20.852] ### ComfyUI Version: v0.3.43 | Released on '2025-06-27'
[2025-06-28 19:06:21.268] [ComfyUI-Manager] default cache updated: https://raw.githubusercontent.com/ltdrdata/ComfyUI-Manager/main/model-list.json
[2025-06-28 19:06:21.272] [ComfyUI-Manager] default cache updated: https://raw.githubusercontent.com/ltdrdata/ComfyUI-Manager/main/alter-list.json
[2025-06-28 19:06:21.373] [ComfyUI-Manager] default cache updated: https://raw.githubusercontent.com/ltdrdata/ComfyUI-Manager/main/extension-node-map.json
[2025-06-28 19:06:21.385] [ComfyUI-Manager] default cache updated: https://raw.githubusercontent.com/ltdrdata/ComfyUI-Manager/main/github-stats.json
[2025-06-28 19:06:21.413] [ComfyUI-Manager] default cache updated: https://raw.githubusercontent.com/ltdrdata/ComfyUI-Manager/main/custom-node-list.json
[2025-06-28 19:06:21.446] 
Import times for custom nodes:
[2025-06-28 19:06:21.446]    0.0 seconds: C:\Users\<USER>\ai\ComfiGit\ComfyUI\custom_nodes\websocket_image_save.py
[2025-06-28 19:06:21.446]    0.0 seconds: C:\Users\<USER>\ai\ComfiGit\ComfyUI\custom_nodes\ComfyUI-WanVideoKsampler
[2025-06-28 19:06:21.446]    0.0 seconds: C:\Users\<USER>\ai\ComfiGit\ComfyUI\custom_nodes\ComfyUI-Custom-Scripts
[2025-06-28 19:06:21.446]    0.0 seconds: C:\Users\<USER>\ai\ComfiGit\ComfyUI\custom_nodes\efficiency-nodes-comfyui
[2025-06-28 19:06:21.446]    0.0 seconds: C:\Users\<USER>\ai\ComfiGit\ComfyUI\custom_nodes\ComfyUI-Advanced-ControlNet
[2025-06-28 19:06:21.446]    0.0 seconds: C:\Users\<USER>\ai\ComfiGit\ComfyUI\custom_nodes\ComfyUI-AnimateDiff-Evolved
[2025-06-28 19:06:21.447]    0.1 seconds: C:\Users\<USER>\ai\ComfiGit\ComfyUI\custom_nodes\ComfyUI-Impact-Pack
[2025-06-28 19:06:21.447]    0.3 seconds: C:\Users\<USER>\ai\ComfiGit\ComfyUI\custom_nodes\ComfyUI-VideoHelperSuite
[2025-06-28 19:06:21.447]    0.5 seconds: C:\Users\<USER>\ai\ComfiGit\ComfyUI\custom_nodes\ComfyUI-Manager
[2025-06-28 19:06:21.447] 
[2025-06-28 19:06:21.986] Context impl SQLiteImpl.
[2025-06-28 19:06:21.986] Will assume non-transactional DDL.
[2025-06-28 19:06:21.987] No target revision found.
[2025-06-28 19:06:22.002] Starting server

[2025-06-28 19:06:22.003] To see the GUI go to: http://127.0.0.1:8188
[2025-06-28 19:06:25.374] FETCH ComfyRegistry Data: 5/90
[2025-06-28 19:06:29.627] FETCH ComfyRegistry Data: 10/90
[2025-06-28 19:06:34.465] FETCH ComfyRegistry Data: 15/90
[2025-06-28 19:06:38.535] FETCH ComfyRegistry Data: 20/90
[2025-06-28 19:06:42.660] FETCH ComfyRegistry Data: 25/90
[2025-06-28 19:06:47.290] FETCH ComfyRegistry Data: 30/90
[2025-06-28 19:06:51.471] FETCH ComfyRegistry Data: 35/90
[2025-06-28 19:06:55.417] FETCH ComfyRegistry Data: 40/90
[2025-06-28 19:06:59.943] FETCH ComfyRegistry Data: 45/90
[2025-06-28 19:07:03.955] FETCH ComfyRegistry Data: 50/90
[2025-06-28 19:07:07.899] FETCH ComfyRegistry Data: 55/90
[2025-06-28 19:07:12.942] FETCH ComfyRegistry Data: 60/90
[2025-06-28 19:07:18.088] FETCH ComfyRegistry Data: 65/90
[2025-06-28 19:07:22.299] FETCH ComfyRegistry Data: 70/90
[2025-06-28 19:07:26.189] FETCH ComfyRegistry Data: 75/90
[2025-06-28 19:07:30.144] FETCH ComfyRegistry Data: 80/90
[2025-06-28 19:07:34.212] FETCH ComfyRegistry Data: 85/90
[2025-06-28 19:07:48.971] FETCH ComfyRegistry Data: 90/90
[2025-06-28 19:07:49.472] FETCH ComfyRegistry Data [DONE]
[2025-06-28 19:07:49.548] [ComfyUI-Manager] default cache updated: https://api.comfy.org/nodes
[2025-06-28 19:07:49.560] FETCH DATA from: https://raw.githubusercontent.com/ltdrdata/ComfyUI-Manager/main/custom-node-list.json [DONE]
[2025-06-28 19:07:49.882] [ComfyUI-Manager] All startup tasks have been completed.
