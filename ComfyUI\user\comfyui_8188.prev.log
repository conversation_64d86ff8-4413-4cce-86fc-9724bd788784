## ComfyUI-Manager: installing dependencies done.
[2025-06-28 19:56:35.050] ** ComfyUI startup time: 2025-06-28 19:56:35.050
[2025-06-28 19:56:35.050] ** Platform: Windows
[2025-06-28 19:56:35.050] ** Python version: 3.10.11 (tags/v3.10.11:7d4cc5a, Apr  5 2023, 00:38:17) [MSC v.1929 64 bit (AMD64)]
[2025-06-28 19:56:35.050] ** Python executable: C:\Users\<USER>\.pyenv\pyenv-win\versions\3.10.11\python.exe
[2025-06-28 19:56:35.051] ** ComfyUI Path: C:\Users\<USER>\ai\ComfiGit\ComfyUI
[2025-06-28 19:56:35.051] ** ComfyUI Base Folder Path: C:\Users\<USER>\ai\ComfiGit\ComfyUI
[2025-06-28 19:56:35.051] ** User directory: C:\Users\<USER>\ai\ComfiGit\ComfyUI\user
[2025-06-28 19:56:35.052] ** ComfyUI-Manager config path: C:\Users\<USER>\ai\ComfiGit\ComfyUI\user\default\ComfyUI-Manager\config.ini
[2025-06-28 19:56:35.052] ** Log path: C:\Users\<USER>\ai\ComfiGit\ComfyUI\user\comfyui.log

Prestartup times for custom nodes:
[2025-06-28 19:56:36.009]    2.0 seconds: C:\Users\<USER>\ai\ComfiGit\ComfyUI\custom_nodes\ComfyUI-Manager
[2025-06-28 19:56:36.009] 
[2025-06-28 19:56:37.240] Checkpoint files will always be loaded safely.
[2025-06-28 19:56:37.270] C:\Users\<USER>\.pyenv\pyenv-win\versions\3.10.11\lib\site-packages\torch\cuda\__init__.py:235: UserWarning: 
NVIDIA GeForce RTX 5090 Laptop GPU with CUDA capability sm_120 is not compatible with the current PyTorch installation.
The current PyTorch install supports CUDA capabilities sm_50 sm_60 sm_61 sm_70 sm_75 sm_80 sm_86 sm_90.
If you want to use the NVIDIA GeForce RTX 5090 Laptop GPU GPU with PyTorch, please check the instructions at https://pytorch.org/get-started/locally/

  warnings.warn(
[2025-06-28 19:56:37.349] Total VRAM 24463 MB, total RAM 64957 MB
[2025-06-28 19:56:37.349] pytorch version: 2.5.1+cu121
[2025-06-28 19:56:37.350] Set vram state to: NORMAL_VRAM
[2025-06-28 19:56:37.350] Device: cuda:0 NVIDIA GeForce RTX 5090 Laptop GPU : cudaMallocAsync
[2025-06-28 19:56:38.253] Using pytorch attention
[2025-06-28 19:56:39.490] Python version: 3.10.11 (tags/v3.10.11:7d4cc5a, Apr  5 2023, 00:38:17) [MSC v.1929 64 bit (AMD64)]
[2025-06-28 19:56:39.490] ComfyUI version: 0.3.43
[2025-06-28 19:56:39.514] ComfyUI frontend version: 1.23.4
[2025-06-28 19:56:39.516] [Prompt Server] web root: C:\Users\<USER>\.pyenv\pyenv-win\versions\3.10.11\lib\site-packages\comfyui_frontend_package\static
[2025-06-28 19:56:40.197] ### Loading: ComfyUI-Impact-Pack (V8.17.1)
[2025-06-28 19:56:40.256] Traceback (most recent call last):
  File "C:\Users\<USER>\ai\ComfiGit\ComfyUI\nodes.py", line 2124, in load_custom_node
    module_spec.loader.exec_module(module)
  File "<frozen importlib._bootstrap_external>", line 883, in exec_module
  File "<frozen importlib._bootstrap>", line 241, in _call_with_frames_removed
  File "C:\Users\<USER>\ai\ComfiGit\ComfyUI\custom_nodes\ComfyUI-Impact-Pack\__init__.py", line 49, in <module>
    import impact.impact_server  # to load server api
  File "C:\Users\<USER>\ai\ComfiGit\ComfyUI\custom_nodes\ComfyUI-Impact-Pack\modules\impact\impact_server.py", line 12, in <module>
    import impact.core as core
  File "C:\Users\<USER>\ai\ComfiGit\ComfyUI\custom_nodes\ComfyUI-Impact-Pack\modules\impact\core.py", line 7, in <module>
    from segment_anything import SamPredictor
ModuleNotFoundError: No module named 'segment_anything'

[2025-06-28 19:56:40.256] Cannot import C:\Users\<USER>\ai\ComfiGit\ComfyUI\custom_nodes\ComfyUI-Impact-Pack module for custom nodes: No module named 'segment_anything'
[2025-06-28 19:56:40.260] ### Loading: ComfyUI-Manager (V3.33.3)
[2025-06-28 19:56:40.261] [ComfyUI-Manager] network_mode: public
[2025-06-28 19:56:40.478] ### ComfyUI Version: v0.3.43 | Released on '2025-06-27'
[2025-06-28 19:56:40.786] [ComfyUI-Manager] default cache updated: https://raw.githubusercontent.com/ltdrdata/ComfyUI-Manager/main/model-list.json
[2025-06-28 19:56:40.797] [ComfyUI-Manager] default cache updated: https://raw.githubusercontent.com/ltdrdata/ComfyUI-Manager/main/alter-list.json
[2025-06-28 19:56:40.812] [ComfyUI-Manager] default cache updated: https://raw.githubusercontent.com/ltdrdata/ComfyUI-Manager/main/github-stats.json
[2025-06-28 19:56:40.860] [ComfyUI-Manager] default cache updated: https://raw.githubusercontent.com/ltdrdata/ComfyUI-Manager/main/extension-node-map.json
[2025-06-28 19:56:40.891] [ComfyUI-Manager] default cache updated: https://raw.githubusercontent.com/ltdrdata/ComfyUI-Manager/main/custom-node-list.json
[2025-06-28 19:56:41.805] Warning: Could not load sageattention: No module named 'sageattention'
[2025-06-28 19:56:41.806] sageattention package is not installed
[2025-06-28 19:56:41.962] 
Import times for custom nodes:
[2025-06-28 19:56:41.962]    0.0 seconds: C:\Users\<USER>\ai\ComfiGit\ComfyUI\custom_nodes\websocket_image_save.py
[2025-06-28 19:56:41.962]    0.0 seconds: C:\Users\<USER>\ai\ComfiGit\ComfyUI\custom_nodes\ComfyUI-WanVideoKsampler
[2025-06-28 19:56:41.962]    0.0 seconds: C:\Users\<USER>\ai\ComfiGit\ComfyUI\custom_nodes\ComfyUI-Custom-Scripts
[2025-06-28 19:56:41.962]    0.0 seconds: C:\Users\<USER>\ai\ComfiGit\ComfyUI\custom_nodes\efficiency-nodes-comfyui
[2025-06-28 19:56:41.962]    0.0 seconds: C:\Users\<USER>\ai\ComfiGit\ComfyUI\custom_nodes\ComfyUI-Advanced-ControlNet
[2025-06-28 19:56:41.962]    0.1 seconds (IMPORT FAILED): C:\Users\<USER>\ai\ComfiGit\ComfyUI\custom_nodes\ComfyUI-Impact-Pack
[2025-06-28 19:56:41.962]    0.1 seconds: C:\Users\<USER>\ai\ComfiGit\ComfyUI\custom_nodes\ComfyUI-AnimateDiff-Evolved
[2025-06-28 19:56:41.962]    0.2 seconds: C:\Users\<USER>\ai\ComfiGit\ComfyUI\custom_nodes\ComfyUI-VideoHelperSuite
[2025-06-28 19:56:41.963]    0.5 seconds: C:\Users\<USER>\ai\ComfiGit\ComfyUI\custom_nodes\ComfyUI-Manager
[2025-06-28 19:56:41.963]    1.0 seconds: C:\Users\<USER>\ai\ComfiGit\ComfyUI\custom_nodes\ComfyUI-WanVideoWrapper
[2025-06-28 19:56:41.963] 
[2025-06-28 19:56:42.157] Context impl SQLiteImpl.
[2025-06-28 19:56:42.157] Will assume non-transactional DDL.
[2025-06-28 19:56:42.158] No target revision found.
[2025-06-28 19:56:42.171] Starting server

[2025-06-28 19:56:42.172] To see the GUI go to: http://127.0.0.1:8188
[2025-06-28 19:56:44.504] FETCH ComfyRegistry Data: 5/90
[2025-06-28 19:56:48.095] FETCH ComfyRegistry Data: 10/90
[2025-06-28 19:56:49.638] 
Stopped server
[2025-06-28 19:56:50.199] Cannot connect to comfyregistry.
[2025-06-28 19:56:50.203] FETCH DATA from: https://raw.githubusercontent.com/ltdrdata/ComfyUI-Manager/main/custom-node-list.json[ComfyUI-Manager] Due to a network error, switching to local mode.
=> custom-node-list.json
=> cannot schedule new futures after shutdown
[2025-06-28 19:56:50.231] FETCH DATA from: C:\Users\<USER>\ai\ComfiGit\ComfyUI\custom_nodes\ComfyUI-Manager\custom-node-list.json [DONE]
[2025-06-28 19:56:50.262] [ComfyUI-Manager] All startup tasks have been completed.
