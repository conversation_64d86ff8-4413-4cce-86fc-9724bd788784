## ComfyUI-Manager: installing dependencies done.
[2025-06-28 20:33:08.187] ** ComfyUI startup time: 2025-06-28 20:33:08.187
[2025-06-28 20:33:08.188] ** Platform: Windows
[2025-06-28 20:33:08.188] ** Python version: 3.12.10 (tags/v3.12.10:0cc8128, Apr  8 2025, 12:21:36) [MSC v.1943 64 bit (AMD64)]
[2025-06-28 20:33:08.188] ** Python executable: C:\Users\<USER>\.pyenv\pyenv-win\versions\3.12.10\python.exe
[2025-06-28 20:33:08.188] ** ComfyUI Path: C:\Users\<USER>\ai\ComfiGit\ComfyUI
[2025-06-28 20:33:08.188] ** ComfyUI Base Folder Path: C:\Users\<USER>\ai\ComfiGit\ComfyUI
[2025-06-28 20:33:08.189] ** User directory: C:\Users\<USER>\ai\ComfiGit\ComfyUI\user
[2025-06-28 20:33:08.189] ** ComfyUI-Manager config path: C:\Users\<USER>\ai\ComfiGit\ComfyUI\user\default\ComfyUI-Manager\config.ini
[2025-06-28 20:33:08.189] ** Log path: C:\Users\<USER>\ai\ComfiGit\ComfyUI\user\comfyui.log
[ComfyUI-Manager] PyTorch is not installed
[2025-06-28 20:33:08.756] 
Prestartup times for custom nodes:
[2025-06-28 20:33:08.756]    1.6 seconds: C:\Users\<USER>\ai\ComfiGit\ComfyUI\custom_nodes\ComfyUI-Manager
[2025-06-28 20:33:08.756] 
[2025-06-28 20:33:09.724] Checkpoint files will always be loaded safely.
[2025-06-28 20:33:09.740] Total VRAM 64957 MB, total RAM 64957 MB
[2025-06-28 20:33:09.740] pytorch version: 2.7.1+cpu
[2025-06-28 20:33:09.741] Set vram state to: DISABLED
[2025-06-28 20:33:09.741] Device: cpu
[2025-06-28 20:33:10.654] Using sub quadratic optimization for attention, if you have memory or speed issues try using: --use-split-cross-attention
[2025-06-28 20:33:10.661] torchaudio missing, ACE model will be broken
[2025-06-28 20:33:10.661] torchaudio missing, ACE model will be broken
[2025-06-28 20:33:11.700] Python version: 3.12.10 (tags/v3.12.10:0cc8128, Apr  8 2025, 12:21:36) [MSC v.1943 64 bit (AMD64)]
[2025-06-28 20:33:11.701] ComfyUI version: 0.3.43
[2025-06-28 20:33:11.725] ComfyUI frontend version: 1.23.4
[2025-06-28 20:33:11.726] [Prompt Server] web root: C:\Users\<USER>\.pyenv\pyenv-win\versions\3.12.10\Lib\site-packages\comfyui_frontend_package\static
[2025-06-28 20:33:12.090] Traceback (most recent call last):
  File "C:\Users\<USER>\ai\ComfiGit\ComfyUI\nodes.py", line 2124, in load_custom_node
    module_spec.loader.exec_module(module)
  File "<frozen importlib._bootstrap_external>", line 999, in exec_module
  File "<frozen importlib._bootstrap>", line 488, in _call_with_frames_removed
  File "C:\Users\<USER>\ai\ComfiGit\ComfyUI\comfy_extras\nodes_audio.py", line 4, in <module>
    import torchaudio
ModuleNotFoundError: No module named 'torchaudio'

[2025-06-28 20:33:12.091] Cannot import C:\Users\<USER>\ai\ComfiGit\ComfyUI\comfy_extras\nodes_audio.py module for custom nodes: No module named 'torchaudio'
[2025-06-28 20:33:12.325] ### Loading: ComfyUI-Impact-Pack (V8.17.1)
[2025-06-28 20:33:12.381] [Impact Pack] Wildcards loading done.
[2025-06-28 20:33:12.386] ### Loading: ComfyUI-Manager (V3.33.3)
[2025-06-28 20:33:12.387] [ComfyUI-Manager] network_mode: public
[2025-06-28 20:33:12.590] ### ComfyUI Version: v0.3.43 | Released on '2025-06-27'
[2025-06-28 20:33:12.923] [ComfyUI-Manager] default cache updated: https://raw.githubusercontent.com/ltdrdata/ComfyUI-Manager/main/model-list.json
[2025-06-28 20:33:12.925] [ComfyUI-Manager] default cache updated: https://raw.githubusercontent.com/ltdrdata/ComfyUI-Manager/main/alter-list.json
[2025-06-28 20:33:12.952] [ComfyUI-Manager] default cache updated: https://raw.githubusercontent.com/ltdrdata/ComfyUI-Manager/main/github-stats.json
[2025-06-28 20:33:13.018] [ComfyUI-Manager] default cache updated: https://raw.githubusercontent.com/ltdrdata/ComfyUI-Manager/main/extension-node-map.json
[2025-06-28 20:33:13.045] [ComfyUI-Manager] default cache updated: https://raw.githubusercontent.com/ltdrdata/ComfyUI-Manager/main/custom-node-list.json
[2025-06-28 20:33:14.006] Warning: Could not load sageattention: No module named 'sageattention'
[2025-06-28 20:33:14.006] sageattention package is not installed
[2025-06-28 20:33:14.087] 
Import times for custom nodes:
[2025-06-28 20:33:14.088]    0.0 seconds: C:\Users\<USER>\ai\ComfiGit\ComfyUI\custom_nodes\websocket_image_save.py
[2025-06-28 20:33:14.088]    0.0 seconds: C:\Users\<USER>\ai\ComfiGit\ComfyUI\custom_nodes\ComfyUI-WanVideoKsampler
[2025-06-28 20:33:14.088]    0.0 seconds: C:\Users\<USER>\ai\ComfiGit\ComfyUI\custom_nodes\ComfyUI-Custom-Scripts
[2025-06-28 20:33:14.088]    0.0 seconds: C:\Users\<USER>\ai\ComfiGit\ComfyUI\custom_nodes\efficiency-nodes-comfyui
[2025-06-28 20:33:14.088]    0.0 seconds: C:\Users\<USER>\ai\ComfiGit\ComfyUI\custom_nodes\ComfyUI-Advanced-ControlNet
[2025-06-28 20:33:14.088]    0.0 seconds: C:\Users\<USER>\ai\ComfiGit\ComfyUI\custom_nodes\ComfyUI-AnimateDiff-Evolved
[2025-06-28 20:33:14.088]    0.1 seconds: C:\Users\<USER>\ai\ComfiGit\ComfyUI\custom_nodes\ComfyUI-Impact-Pack
[2025-06-28 20:33:14.088]    0.2 seconds: C:\Users\<USER>\ai\ComfiGit\ComfyUI\custom_nodes\ComfyUI-VideoHelperSuite
[2025-06-28 20:33:14.088]    0.5 seconds: C:\Users\<USER>\ai\ComfiGit\ComfyUI\custom_nodes\ComfyUI-Manager
[2025-06-28 20:33:14.088]    1.0 seconds: C:\Users\<USER>\ai\ComfiGit\ComfyUI\custom_nodes\ComfyUI-WanVideoWrapper
[2025-06-28 20:33:14.088] 
[2025-06-28 20:33:14.089] WARNING: some comfy_extras/ nodes did not import correctly. This may be because they are missing some dependencies.

[2025-06-28 20:33:14.089] IMPORT FAILED: nodes_audio.py
[2025-06-28 20:33:14.089] 
This issue might be caused by new missing dependencies added the last time you updated ComfyUI.
[2025-06-28 20:33:14.089] Please do a: pip install -r requirements.txt
[2025-06-28 20:33:14.089] 
[2025-06-28 20:33:14.429] Context impl SQLiteImpl.
[2025-06-28 20:33:14.429] Will assume non-transactional DDL.
[2025-06-28 20:33:14.430] No target revision found.
[2025-06-28 20:33:14.439] Starting server

[2025-06-28 20:33:14.439] To see the GUI go to: http://127.0.0.1:8189
[2025-06-28 20:33:16.558] FETCH ComfyRegistry Data: 5/90
[2025-06-28 20:33:20.262] FETCH ComfyRegistry Data: 10/90
[2025-06-28 20:33:24.041] FETCH ComfyRegistry Data: 15/90
[2025-06-28 20:33:27.698] FETCH ComfyRegistry Data: 20/90
[2025-06-28 20:33:31.178] FETCH ComfyRegistry Data: 25/90
[2025-06-28 20:33:34.966] FETCH ComfyRegistry Data: 30/90
[2025-06-28 20:33:38.590] FETCH ComfyRegistry Data: 35/90
[2025-06-28 20:33:42.163] FETCH ComfyRegistry Data: 40/90
[2025-06-28 20:33:46.535] FETCH ComfyRegistry Data: 45/90
[2025-06-28 20:33:50.246] FETCH ComfyRegistry Data: 50/90
[2025-06-28 20:33:53.815] FETCH ComfyRegistry Data: 55/90
[2025-06-28 20:33:57.488] FETCH ComfyRegistry Data: 60/90
[2025-06-28 20:34:01.122] FETCH ComfyRegistry Data: 65/90
[2025-06-28 20:34:05.367] FETCH ComfyRegistry Data: 70/90
[2025-06-28 20:34:09.981] FETCH ComfyRegistry Data: 75/90
[2025-06-28 20:34:13.665] FETCH ComfyRegistry Data: 80/90
[2025-06-28 20:34:17.282] FETCH ComfyRegistry Data: 85/90
[2025-06-28 20:34:21.057] FETCH ComfyRegistry Data: 90/90
[2025-06-28 20:34:21.558] FETCH ComfyRegistry Data [DONE]
[2025-06-28 20:34:21.626] [ComfyUI-Manager] default cache updated: https://api.comfy.org/nodes
[2025-06-28 20:34:21.637] FETCH DATA from: https://raw.githubusercontent.com/ltdrdata/ComfyUI-Manager/main/custom-node-list.json [DONE]
[2025-06-28 20:34:21.764] [ComfyUI-Manager] All startup tasks have been completed.
[2025-06-28 20:35:07.376] got prompt
[2025-06-28 20:35:07.378] Failed to validate prompt for output 9:
[2025-06-28 20:35:07.378] * VAEDecode 8:
[2025-06-28 20:35:07.378]   - Return type mismatch between linked nodes: samples, received_type(MODEL) mismatch input_type(LATENT)
[2025-06-28 20:35:07.378] Output will be ignored
[2025-06-28 20:35:07.378] invalid prompt: {'type': 'prompt_outputs_failed_validation', 'message': 'Prompt outputs failed validation', 'details': '', 'extra_info': {}}
[2025-06-28 20:35:55.432] got prompt
[2025-06-28 20:35:55.558] model weight dtype torch.float32, manual cast: None
[2025-06-28 20:35:55.559] model_type EPS
[2025-06-28 20:35:56.675] Using split attention in VAE
[2025-06-28 20:35:56.676] Using split attention in VAE
[2025-06-28 20:35:56.813] VAE load device: cpu, offload device: cpu, dtype: torch.float32
[2025-06-28 20:35:56.866] Requested to load SD1ClipModel
[2025-06-28 20:35:56.869] loaded completely 9.5367431640625e+25 235.84423828125 True
[2025-06-28 20:35:56.870] CLIP/text encoder model load device: cpu, offload device: cpu, current: cpu, dtype: torch.float16
[2025-06-28 20:35:57.693] Requested to load BaseModel
[2025-06-28 20:35:57.702] loaded completely 9.5367431640625e+25 3278.812271118164 True
