{"last_node_id": 20, "last_link_id": 23, "nodes": [{"id": 18, "type": "VHS_VideoInfoLoaded", "pos": [424, 427], "size": {"0": 304.79998779296875, "1": 106}, "flags": {}, "order": 2, "mode": 0, "inputs": [{"name": "video_info", "type": "VHS_VIDEOINFO", "link": 19}], "outputs": [{"name": "fps🟦", "type": "FLOAT", "links": [20], "slot_index": 0, "shape": 3}, {"name": "frame_count🟦", "type": "INT", "links": null, "shape": 3}, {"name": "duration🟦", "type": "FLOAT", "links": null, "shape": 3}, {"name": "width🟦", "type": "INT", "links": null, "shape": 3}, {"name": "height🟦", "type": "INT", "links": null, "shape": 3}], "properties": {"Node name for S&R": "VHS_VideoInfoLoaded"}, "widgets_values": {}}, {"id": 17, "type": "VHS_VideoCombine", "pos": [783, 223], "size": [315, 286], "flags": {}, "order": 3, "mode": 0, "inputs": [{"name": "images", "type": "IMAGE", "link": 21}, {"name": "audio", "type": "VHS_AUDIO", "link": null}, {"name": "meta_batch", "type": "VHS_BatchManager", "link": null}, {"name": "frame_rate", "type": "FLOAT", "link": 20, "widget": {"name": "frame_rate"}}], "outputs": [{"name": "Filenames", "type": "VHS_FILENAMES", "links": null, "shape": 3}], "properties": {"Node name for S&R": "VHS_VideoCombine"}, "widgets_values": {"frame_rate": 8, "loop_count": 0, "filename_prefix": "AnimateDiff", "format": "video/h264-mp4", "pix_fmt": "yuv420p", "crf": 19, "save_metadata": true, "pingpong": false, "save_output": false, "videopreview": {"hidden": false, "paused": false, "params": {"filename": "AnimateDiff_00001.mp4", "subfolder": "", "type": "temp", "format": "video/h264-mp4"}}}}, {"id": 16, "type": "VHS_LoadVideoPath", "pos": [96, 230], "size": [315, 449.5], "flags": {}, "order": 1, "mode": 0, "inputs": [{"name": "meta_batch", "type": "VHS_BatchManager", "link": null}, {"name": "frame_load_cap", "type": "INT", "link": 23, "slot_index": 1, "widget": {"name": "frame_load_cap"}}], "outputs": [{"name": "IMAGE", "type": "IMAGE", "links": [21], "slot_index": 0, "shape": 3}, {"name": "frame_count", "type": "INT", "links": null, "shape": 3}, {"name": "audio", "type": "VHS_AUDIO", "links": null, "shape": 3}, {"name": "video_info", "type": "VHS_VIDEOINFO", "links": [19], "slot_index": 3, "shape": 3}], "properties": {"Node name for S&R": "VHS_LoadVideoPath"}, "widgets_values": {"video": "input/leader.webm", "force_rate": 0, "force_size": "Disabled", "custom_width": 512, "custom_height": 512, "frame_load_cap": 64, "skip_first_frames": 0, "select_every_nth": 1, "videopreview": {"hidden": false, "paused": false, "params": {"frame_load_cap": 64, "skip_first_frames": 0, "force_rate": 0, "filename": "input/leader.webm", "type": "path", "format": "video/webm", "select_every_nth": 1}}}}, {"id": 20, "type": "PrimitiveNode", "pos": [-240, 170], "size": {"0": 210, "1": 80}, "flags": {}, "order": 0, "mode": 0, "outputs": [{"name": "INT", "type": "INT", "links": [23], "widget": {"name": "frame_load_cap"}}], "properties": {"Run widget replace on values": false}, "widgets_values": [64, "fixed"]}], "links": [[19, 16, 3, 18, 0, "VHS_VIDEOINFO"], [20, 18, 0, 17, 3, "FLOAT"], [21, 16, 0, 17, 0, "IMAGE"], [23, 20, 0, 16, 1, "INT"]], "groups": [], "config": {}, "extra": {}, "version": 0.4, "tests": {"17": [{"type": "video", "key": "width", "value": 1440}, {"type": "video", "key": "height", "value": 1080}, {"type": "video", "key": "nb_read_packets", "value": "64"}], "length": 1}}