{"custom_nodes": [{"author": "Comfy-Org", "title": "ComfyUI React Extension Template", "reference": "https://github.com/Comfy-Org/ComfyUI-React-Extension-Template", "files": ["https://github.com/Comfy-Org/ComfyUI-React-Extension-Template"], "install_type": "git-clone", "description": "A minimal template for creating React/TypeScript frontend extensions for ComfyUI, with complete boilerplate setup including internationalization and unit testing."}, {"author": "Suzie1", "title": "Guide To Making Custom Nodes in ComfyUI", "reference": "https://github.com/Suzie1/ComfyUI_Guide_To_Making_Custom_Nodes", "files": ["https://github.com/Suzie1/ComfyUI_Guide_To_Making_Custom_Nodes"], "install_type": "git-clone", "description": "There is a small node pack attached to this guide. This includes the init file and 3 nodes associated with the tutorials."}, {"author": "bamboodia", "title": "BAM Nodes", "reference": "https://github.com/bamboodia/BAM_Nodes", "files": ["https://github.com/bamboodia/BAM_Nodes"], "install_type": "git-clone", "description": "A collection of comfyui nodes that I have made for nothing more than educational purposes."}, {"author": "BadCafeCode", "title": "execution-inversion-demo-comfyui", "reference": "https://github.com/BadCafeCode/execution-inversion-demo-comfyui", "files": ["https://github.com/BadCafeCode/execution-inversion-demo-comfyui"], "install_type": "git-clone", "description": "These are demo nodes for [a/PR2666](https://github.com/comfyanonymous/ComfyUI/pull/2666)"}, {"author": "ec<PERSON><PERSON>", "title": "ecjojo_example_nodes", "reference": "https://github.com/ecjojo/ecjojo-example-nodes", "files": ["https://github.com/ecjojo/ecjojo-example-nodes"], "install_type": "git-clone", "description": "Welcome to ecjojo_example_nodes! This example is specifically designed for beginners who want to learn how to write a simple custom node.\nFeel free to modify this example and make it your own. Experiment with different features and functionalities to enhance your understanding of ComfyUI custom nodes. Don't be afraid to explore and customize the code to suit your needs.\nBy diving into this example and making it your own, you'll gain valuable hands-on experience in creating custom nodes in ComfyUI. Enjoy the process of learning and have fun with your custom node development journey!"}, {"author": "dynamixar", "title": "<PERSON><PERSON><PERSON>", "reference": "https://github.com/dynamixar/Atluris", "files": ["https://github.com/dynamixar/Atluris"], "install_type": "git-clone", "description": "Nodes:Random Line"}, {"author": "et118", "title": "ComfyUI-ElGogh-Nodes", "reference": "https://github.com/et118/ComfyUI-ElGogh-Nodes", "files": ["https://github.com/et118/ComfyUI-ElGogh-Nodes"], "install_type": "git-clone", "description": "Nodes:ElGogh Positive Prompt, ElGogh NEGATIVE Prompt, ElGogh Empty Latent Image, ElGogh Checkpoint Loader Simple"}, {"author": "LarryJane491", "title": "Custom-Node-Base", "reference": "https://github.com/LarryJane491/Custom-Node-Base", "files": ["https://github.com/LarryJane491/Custom-Node-Base"], "install_type": "git-clone", "description": "This project is an `empty` custom node that is already in its own folder. It serves as a base to build any custom node. Whenever you want to create a custom node, you can download that, put it in custom_nodes, then you just have to change the names and fill it with code!"}, {"author": "foxtrot-roger", "title": "comfyui-custom-nodes", "reference": "https://github.com/foxtrot-roger/comfyui-custom-nodes", "files": ["https://github.com/foxtrot-roger/comfyui-custom-nodes"], "install_type": "git-clone", "description": "Tutorial nodes"}, {"author": "wailovet", "title": "ComfyUI-WW", "reference": "https://github.com/wailovet/ComfyUI-WW", "files": ["https://github.com/wailovet/ComfyUI-WW"], "install_type": "git-clone", "description": "Nodes:WW_ImageResize"}, {"author": "azure-dragon-ai", "title": "ComfyUI-HPSv2-Nodes", "reference": "https://github.com/azure-dragon-ai/ComfyUI-HPSv2-Nodes", "files": ["https://github.com/azure-dragon-ai/ComfyUI-HPSv2-Nodes"], "install_type": "git-clone", "description": "Nodes:Loader, Image Processor, Text Processor, ImageScore"}, {"author": "kappa54m", "title": "ComfyUI-HPSv2-Nodes", "reference": "https://github.com/kappa54m/ComfyUI_Usability", "files": ["https://github.com/kappa54m/ComfyUI_Usability"], "install_type": "git-clone", "description": "Nodes:Load Image Dedup"}, {"author": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "title": "comfyui-node-int-to-string-convertor", "reference": "https://github.com/<PERSON><PERSON><PERSON>/comfyui-node-int-to-string-convertor", "files": ["https://github.com/<PERSON><PERSON><PERSON>/comfyui-node-int-to-string-convertor"], "install_type": "git-clone", "description": "Nodes:Int To String Convertor"}, {"author": "yowipr", "title": "ComfyUI-Manual", "reference": "https://github.com/yowipr/ComfyUI-Manual", "files": ["https://github.com/yowipr/ComfyUI-Manual"], "install_type": "git-clone", "description": "Nodes:<PERSON><PERSON><PERSON><PERSON>, M_Output"}, {"author": "and<PERSON><PERSON><PERSON>", "title": "ComfyUI Function Annotator", "reference": "https://github.com/andrewharp/ComfyUI-Annotations", "files": ["https://github.com/andrewharp/ComfyUI-Annotations"], "install_type": "git-clone", "description": "This module provides an annotation @ComfyFunc to streamline adding custom node types in ComfyUI. It processes your function's signature to create a wrapped function and custom node definition required for ComfyUI, eliminating all the boilerplate code. In most cases you can just add a @ComfyFunc(\"category\") annotation to your existing function."}, {"author": "OuticNZ", "title": "ComfyUI-Simple-Of-Complex", "reference": "https://github.com/OuticNZ/ComfyUI-Simple-Of-Complex", "files": ["https://github.com/OuticNZ/ComfyUI-Simple-Of-Complex"], "install_type": "git-clone", "description": "Keeping it simple for starting. Single branch for now and will add development branch later."}, {"author": "jtong", "title": "comfyui-jtong-workflow", "reference": "https://github.com/jtong/comfyui-jtong-workflow", "files": ["https://github.com/jtong/comfyui-jtong-workflow"], "install_type": "git-clone", "description": "Nodes:jtong.Highway, Example"}, {"author": "thinkthinking", "title": "ComfyUI-Ye", "reference": "https://github.com/thinkthinking/ComfyUI-Ye", "files": ["https://github.com/thinkthinking/ComfyUI-Ye"], "install_type": "git-clone", "description": "Nodes:Signature|Ye, CheckpointLoader|Ye, PrintHelloWorld|Ye."}, {"author": "BoosterCore", "title": "ComfyUI-BC-Experimental", "reference": "https://github.com/BoosterCore/ComfyUI-BC-Experimental", "files": ["https://github.com/BoosterCore/ComfyUI-BC-Experimental"], "install_type": "git-clone", "description": "Nodes:ClipTextEncodeBC, SaveAnyText, SimpleText"}, {"author": "sonyeon-sj", "title": "ComfyUI-easy_ImageSize_Selecter", "reference": "https://github.com/sonyeon-sj/ComfyUI-easy_ImageSize_Selecter", "files": ["https://github.com/sonyeon-sj/ComfyUI-easy_ImageSize_Selecter"], "install_type": "git-clone", "description": "Custom node for ComfyUI Select the image size from the preset and select Vertical and Horizontal to output Width and Height."}, {"author": "boricuapab", "title": "ComfyUI_BoricuapabWFNodePack", "reference": "https://github.com/boricuapab/ComfyUI_BoricuapabWFNodePack", "files": ["https://github.com/boricuapab/ComfyUI_BoricuapabWFNodePack"], "install_type": "git-clone", "description": "Learning how to make my own comfy ui custom nodes"}, {"author": "mira-6", "title": "mira-wildcard-node", "reference": "https://github.com/mira-6/mira-wildcard-node", "files": ["https://github.com/mira-6/mira-wildcard-node"], "install_type": "git-clone", "description": "Mira's Simple Wildcard Node"}, {"author": "BetaDoggo", "title": "ComfyUI Tetris", "id": "tetris", "reference": "https://github.com/BetaDoggo/ComfyUI-Tetris", "files": ["https://github.com/BetaDoggo/ComfyUI-Tetris"], "install_type": "git-clone", "description": "The primitive node and dummy input are required because comfy doesn't accept requests with identical graphs. You'll also need a show text node. I like the one from ComfyUI-Custom-Scripts. I got the generic tetris remake from claude so it may or may not be ripped from somewhere else."}, {"author": "FlyMyAI", "title": "ComfyUI-ExampleNode", "reference": "https://github.com/FlyMyAI/ComfyUI-ExampleNode", "files": ["https://github.com/FlyMyAI/ComfyUI-ExampleNode"], "install_type": "git-clone", "description": "Node to provide convenient ComfyUI standard, supported by flymy_comfyui."}, {"author": "<PERSON><PERSON><PERSON>", "title": "ComfyUI_RAGDemo", "reference": "https://github.com/Wanghanying/ComfyUI_RAGDemo", "files": ["https://github.com/Wanghanying/ComfyUI_RAGDemo"], "install_type": "git-clone", "description": "RAG Demo for LLM"}, {"author": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "title": "BachelorThesis", "reference": "https://github.com/FelixTeutsch/BachelorThesis", "files": ["https://github.com/FelixTeutsch/BachelorThesis"], "install_type": "git-clone", "description": "This is a ComfyUi custom node, that build a new UI on top of the already existing AI, to enable the use of custom controllers"}, {"author": "jhj0517", "title": "ComfyUI-CustomNodes-Template", "reference": "https://github.com/jhj0517/ComfyUI-CustomNodes-Template", "files": ["https://github.com/jhj0517/ComfyUI-CustomNodes-Template"], "install_type": "git-clone", "description": "This is the ComfyUI custom node template repository that anyone can use to create their own custom nodes."}, {"author": "laogou666", "title": "Comfyui_LG_Advertisement", "reference": "https://github.com/LAOGOU-666/Comfyui_LG_Advertisement", "files": ["https://github.com/LAOGOU-666/Comfyui_LG_Advertisement"], "install_type": "git-clone", "description": "A node for demonstration."}, {"author": "amorano", "title": "cozy_spoke", "reference": "https://github.com/cozy-comfyui/cozy_spoke", "files": ["https://github.com/cozy-comfyui/cozy_spoke"], "install_type": "git-clone", "description": "Example node communicating between ComfyUI Javascript and Python."}, {"author": "amorano", "title": "Cozy <PERSON> Toggle", "id": "cozyLinkToggle", "reference": "https://github.com/cozy-comfyui/cozy_link_toggle", "files": ["https://github.com/cozy-comfyui/cozy_link_toggle"], "install_type": "git-clone", "description": "Example of using ComfyUI Toolbar to Toggle ComfyUI links on/off"}, {"author": "xhiroga", "title": "ComfyUI-TypeScript-CustomNode", "reference": "https://github.com/xhiroga/ComfyUI-TypeScript-CustomNode", "files": ["https://github.com/xhiroga/ComfyUI-TypeScript-CustomNode"], "install_type": "git-clone", "description": "This project is generated from xhiroga/ComfyUI-TypeScript-CustomNode"}, {"author": "zentrocdot", "title": "ComfyUI-Turtle_Graphics_Demos", "reference": "https://github.com/zentrocdot/ComfyUI-Turtle_Graphics_Demo", "files": ["https://github.com/zentrocdot/ComfyUI-Turtle_Graphics_Demo"], "description": "ComfyUI node for creating some Turtle Graphic demos.", "install_type": "git-clone"}, {"author": "cozy-comfyui", "title": "cozy_ex_dynamic", "reference": "https://github.com/cozy-comfyui/cozy_ex_dynamic", "files": ["https://github.com/cozy-comfyui/cozy_ex_dynamic"], "description": "Dynamic Node examples for ComfyUI", "install_type": "git-clone"}]}