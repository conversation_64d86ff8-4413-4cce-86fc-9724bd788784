Failed to execute startup-script: C:\Users\<USER>\ai\ComfiGit\ComfyUI-master\custom_nodes\ComfyUI-Manager\prestartup_script.py / Failed to initialize: Bad git executable.
The git executable must be specified in one of the following ways:
    - be included in your $PATH
    - be set via $GIT_PYTHON_GIT_EXECUTABLE
    - explicitly set via git.refresh(<full-path-to-git-executable>)

All git commands will error until this is rectified.

This initial message can be silenced or aggravated in the future by setting the
$GIT_PYTHON_REFRESH environment variable. Use one of the following values:
    - quiet|q|silence|s|silent|none|n|0: for no message or exception
    - warn|w|warning|log|l|1: for a warning message (logging level CRITICAL, displayed by default)
    - error|e|exception|raise|r|2: for a raised exception

Example:
    export GIT_PYTHON_REFRESH=quiet

[2025-06-28 17:03:02.277] 
Prestartup times for custom nodes:
[2025-06-28 17:03:02.277]    1.2 seconds (PRESTARTUP FAILED): C:\Users\<USER>\ai\ComfiGit\ComfyUI-master\custom_nodes\ComfyUI-Manager
[2025-06-28 17:03:02.277] 
[2025-06-28 17:03:03.550] Checkpoint files will always be loaded safely.
[2025-06-28 17:03:03.684] Total VRAM 24463 MB, total RAM 64957 MB
[2025-06-28 17:03:03.685] pytorch version: 2.7.1+cu128
[2025-06-28 17:03:03.685] Set vram state to: NORMAL_VRAM
[2025-06-28 17:03:03.686] Device: cuda:0 NVIDIA GeForce RTX 5090 Laptop GPU : cudaMallocAsync
[2025-06-28 17:03:04.633] Using pytorch attention
[2025-06-28 17:03:05.866] Python version: 3.13.5 (tags/v3.13.5:6cb20a2, Jun 11 2025, 16:15:46) [MSC v.1943 64 bit (AMD64)]
[2025-06-28 17:03:05.866] ComfyUI version: 0.3.43
[2025-06-28 17:03:05.911] ComfyUI frontend version: 1.23.4
[2025-06-28 17:03:05.913] [Prompt Server] web root: C:\Users\<USER>\ai\ComfiGit\comfyui_env\Lib\site-packages\comfyui_frontend_package\static
### Loading: ComfyUI-Impact-Pack (V8.17.1)
[2025-06-28 17:03:06.465] [Impact Pack] Wildcards loading done.
[2025-06-28 17:03:06.472] Traceback (most recent call last):
  File "C:\Users\<USER>\ai\ComfiGit\comfyui_env\Lib\site-packages\git\__init__.py", line 296, in <module>
    refresh()
    ~~~~~~~^^
  File "C:\Users\<USER>\ai\ComfiGit\comfyui_env\Lib\site-packages\git\__init__.py", line 287, in refresh
    if not Git.refresh(path=path):
           ~~~~~~~~~~~^^^^^^^^^^^
  File "C:\Users\<USER>\ai\ComfiGit\comfyui_env\Lib\site-packages\git\cmd.py", line 631, in refresh
    raise ImportError(err)
ImportError: Bad git executable.
The git executable must be specified in one of the following ways:
    - be included in your $PATH
    - be set via $GIT_PYTHON_GIT_EXECUTABLE
    - explicitly set via git.refresh(<full-path-to-git-executable>)

All git commands will error until this is rectified.

This initial message can be silenced or aggravated in the future by setting the
$GIT_PYTHON_REFRESH environment variable. Use one of the following values:
    - quiet|q|silence|s|silent|none|n|0: for no message or exception
    - warn|w|warning|log|l|1: for a warning message (logging level CRITICAL, displayed by default)
    - error|e|exception|raise|r|2: for a raised exception

Example:
    export GIT_PYTHON_REFRESH=quiet


The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "C:\Users\<USER>\ai\ComfiGit\ComfyUI-master\nodes.py", line 2124, in load_custom_node
    module_spec.loader.exec_module(module)
    ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~^^^^^^^^
  File "<frozen importlib._bootstrap_external>", line 1026, in exec_module
  File "<frozen importlib._bootstrap>", line 488, in _call_with_frames_removed
  File "C:\Users\<USER>\ai\ComfiGit\ComfyUI-master\custom_nodes\ComfyUI-Manager\__init__.py", line 12, in <module>
    import manager_server  # noqa: F401
    ^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\ai\ComfiGit\ComfyUI-master\custom_nodes\ComfyUI-Manager\glob\manager_server.py", line 13, in <module>
    import git
  File "C:\Users\<USER>\ai\ComfiGit\comfyui_env\Lib\site-packages\git\__init__.py", line 298, in <module>
    raise ImportError("Failed to initialize: {0}".format(_exc)) from _exc
ImportError: Failed to initialize: Bad git executable.
The git executable must be specified in one of the following ways:
    - be included in your $PATH
    - be set via $GIT_PYTHON_GIT_EXECUTABLE
    - explicitly set via git.refresh(<full-path-to-git-executable>)

All git commands will error until this is rectified.

This initial message can be silenced or aggravated in the future by setting the
$GIT_PYTHON_REFRESH environment variable. Use one of the following values:
    - quiet|q|silence|s|silent|none|n|0: for no message or exception
    - warn|w|warning|log|l|1: for a warning message (logging level CRITICAL, displayed by default)
    - error|e|exception|raise|r|2: for a raised exception

Example:
    export GIT_PYTHON_REFRESH=quiet


[2025-06-28 17:03:06.473] Cannot import C:\Users\<USER>\ai\ComfiGit\ComfyUI-master\custom_nodes\ComfyUI-Manager module for custom nodes: Failed to initialize: Bad git executable.
The git executable must be specified in one of the following ways:
    - be included in your $PATH
    - be set via $GIT_PYTHON_GIT_EXECUTABLE
    - explicitly set via git.refresh(<full-path-to-git-executable>)

All git commands will error until this is rectified.

This initial message can be silenced or aggravated in the future by setting the
$GIT_PYTHON_REFRESH environment variable. Use one of the following values:
    - quiet|q|silence|s|silent|none|n|0: for no message or exception
    - warn|w|warning|log|l|1: for a warning message (logging level CRITICAL, displayed by default)
    - error|e|exception|raise|r|2: for a raised exception

Example:
    export GIT_PYTHON_REFRESH=quiet

[2025-06-28 17:03:06.696] 
Import times for custom nodes:
[2025-06-28 17:03:06.696]    0.0 seconds: C:\Users\<USER>\ai\ComfiGit\ComfyUI-master\custom_nodes\websocket_image_save.py
[2025-06-28 17:03:06.696]    0.0 seconds: C:\Users\<USER>\ai\ComfiGit\ComfyUI-master\custom_nodes\ComfyUI-Custom-Scripts
[2025-06-28 17:03:06.696]    0.0 seconds (IMPORT FAILED): C:\Users\<USER>\ai\ComfiGit\ComfyUI-master\custom_nodes\ComfyUI-Manager
[2025-06-28 17:03:06.696]    0.0 seconds: C:\Users\<USER>\ai\ComfiGit\ComfyUI-master\custom_nodes\efficiency-nodes-comfyui
[2025-06-28 17:03:06.696]    0.0 seconds: C:\Users\<USER>\ai\ComfiGit\ComfyUI-master\custom_nodes\ComfyUI-Advanced-ControlNet
[2025-06-28 17:03:06.696]    0.1 seconds: C:\Users\<USER>\ai\ComfiGit\ComfyUI-master\custom_nodes\ComfyUI-Impact-Pack
[2025-06-28 17:03:06.697]    0.2 seconds: C:\Users\<USER>\ai\ComfiGit\ComfyUI-master\custom_nodes\ComfyUI-VideoHelperSuite
[2025-06-28 17:03:06.697] 
[2025-06-28 17:03:07.065] Context impl SQLiteImpl.
[2025-06-28 17:03:07.065] Will assume non-transactional DDL.
[2025-06-28 17:03:07.066] No target revision found.
[2025-06-28 17:03:07.077] Starting server

[2025-06-28 17:03:07.077] To see the GUI go to: http://0.0.0.0:8188
[2025-06-28 17:06:52.410] got prompt
[2025-06-28 17:06:52.412] Failed to validate prompt for output 6:
[2025-06-28 17:06:52.412] * SVD_img2vid_Conditioning 3:
[2025-06-28 17:06:52.412]   - Return type mismatch between linked nodes: clip_vision, received_type(CLIP) mismatch input_type(CLIP_VISION)
[2025-06-28 17:06:52.412] Output will be ignored
[2025-06-28 17:06:52.413] invalid prompt: {'type': 'prompt_outputs_failed_validation', 'message': 'Prompt outputs failed validation', 'details': '', 'extra_info': {}}
[2025-06-28 17:07:13.494] got prompt
[2025-06-28 17:07:13.496] Failed to validate prompt for output 6:
[2025-06-28 17:07:13.496] * SVD_img2vid_Conditioning 3:
[2025-06-28 17:07:13.496]   - Return type mismatch between linked nodes: clip_vision, received_type(CLIP) mismatch input_type(CLIP_VISION)
[2025-06-28 17:07:13.497] Output will be ignored
[2025-06-28 17:07:13.497] invalid prompt: {'type': 'prompt_outputs_failed_validation', 'message': 'Prompt outputs failed validation', 'details': '', 'extra_info': {}}
[2025-06-28 17:07:33.880] got prompt
[2025-06-28 17:07:34.471] model weight dtype torch.float16, manual cast: None
[2025-06-28 17:07:34.479] model_type V_PREDICTION_EDM
[2025-06-28 17:07:35.434] Using pytorch attention in VAE
[2025-06-28 17:07:35.441] Using pytorch attention in VAE
[2025-06-28 17:07:35.578] VAE load device: cuda:0, offload device: cpu, dtype: torch.bfloat16
[2025-06-28 17:07:36.835] Requested to load CLIPVisionModelProjection
[2025-06-28 17:07:37.310] loaded completely 21615.8 1208.09814453125 True
[2025-06-28 17:07:37.951] Requested to load AutoencodingEngine
[2025-06-28 17:07:38.054] loaded completely 19160.8994140625 186.42292404174805 True
[2025-06-28 17:07:38.587] Requested to load SVD_img2vid
[2025-06-28 17:07:39.230] loaded completely 12425.998943328857 2907.987823486328 True
[2025-06-28 17:09:26.257] 
100%|██████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████| 20/20 [01:46<00:00,  5.10s/it]
100%|██████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████| 20/20 [01:46<00:00,  5.34s/it]
[2025-06-28 17:09:27.587] Requested to load AutoencodingEngine
[2025-06-28 17:09:27.897] loaded completely 16283.75357055664 186.42292404174805 True
[2025-06-28 17:09:34.902] [VideoHelperSuite] - [0;33mWARNING[0m - Missing input for t has been set to False
[2025-06-28 17:09:35.463] Prompt executed in 121.58 seconds
