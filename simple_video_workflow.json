{"1": {"inputs": {"ckpt_name": "v1-5-pruned-em<PERSON><PERSON><PERSON>.safetensors"}, "class_type": "CheckpointLoaderSimple", "_meta": {"title": "Load Checkpoint"}}, "2": {"inputs": {"text": "A beautiful cherry blossom tree in full bloom, petals gently falling in the wind, soft sunlight filtering through the branches, peaceful spring scene, cinematic quality, 4k", "clip": ["1", 1]}, "class_type": "CLIPTextEncode", "_meta": {"title": "CLIP Text Encode (Prompt)"}}, "3": {"inputs": {"text": "blurry, low quality, distorted, ugly, bad anatomy", "clip": ["1", 1]}, "class_type": "CLIPTextEncode", "_meta": {"title": "CLIP Text Encode (Negative)"}}, "4": {"inputs": {"width": 512, "height": 512, "batch_size": 16}, "class_type": "EmptyLatentImage", "_meta": {"title": "Empty Latent Image"}}, "5": {"inputs": {"seed": 42, "steps": 20, "cfg": 7.5, "sampler_name": "euler", "scheduler": "normal", "denoise": 1.0, "model": ["1", 0], "positive": ["2", 0], "negative": ["3", 0], "latent_image": ["4", 0]}, "class_type": "K<PERSON><PERSON><PERSON>", "_meta": {"title": "K<PERSON><PERSON><PERSON>"}}, "6": {"inputs": {"samples": ["5", 0], "vae": ["1", 2]}, "class_type": "VAEDecode", "_meta": {"title": "VAE Decode"}}, "7": {"inputs": {"images": ["6", 0], "frame_rate": 8.0, "loop_count": 0, "filename_prefix": "simple_video_cherry_blossom", "format": "video/h264-mp4", "pingpong": false, "save_output": true, "pix_fmt": "yuv420p", "crf": 19, "save_metadata": true}, "class_type": "VHS_VideoCombine", "_meta": {"title": "Video Combine"}}}