[project]
name = "comfyui-manager"
description = "ComfyUI-Manager provides features to install and manage custom nodes for ComfyUI, as well as various functionalities to assist with ComfyUI."
version = "3.33.3"
license = { file = "LICENSE.txt" }
dependencies = ["GitPython", "PyGithub", "matrix-client==0.4.0", "transformers", "huggingface-hub>0.20", "typer", "rich", "typing-extensions", "toml", "uv", "chardet"]

[project.urls]
Repository = "https://github.com/ltdrdata/ComfyUI-Manager"
#  Used by Comfy Registry https://comfyregistry.org

[tool.comfy]
PublisherId = "drltdata"
DisplayName = "ComfyUI-Manager"
Icon = ""
