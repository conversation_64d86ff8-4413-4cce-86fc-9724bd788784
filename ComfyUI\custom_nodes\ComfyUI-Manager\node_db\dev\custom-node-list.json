{"custom_nodes": [{"author": "<PERSON><PERSON><PERSON><PERSON>", "title": "DoomFLUX Nodes [WIP]", "reference": "https://github.com/PeterMikhai/Doom_Flux_NodePack", "files": ["https://github.com/PeterMikhai/Doom_Flux_NodePack"], "install_type": "git-clone", "description": "Custom nodes for FLUX models, including a loader and specialized samplers for standard and inpaint generation.\nNOTE: The files in the repo are not organized."}, {"author": "maque", "title": "comfyui_video_BC [WIP]", "reference": "https://github.com/JioJe/comfyui_video_BC", "files": ["https://github.com/JioJe/comfyui_video_BC"], "install_type": "git-clone", "description": "Batch load video nodes and save videos in custom paths\nNOTE: The files in the repo are not organized."}, {"author": "RedmondAI", "title": "comfyui-tools [UNSAFE/NAME CONFLICT]", "reference": "https://github.com/RedmondAI/comfyui-tools", "files": ["https://github.com/RedmondAI/comfyui-tools"], "install_type": "git-clone", "description": "Custom extensions for ComfyUI used by the Redmond3D VFX team.[w/This node pack has a vulnerability that allows it to create files at arbitrary paths.]"}, {"author": "coVISIONSld", "title": "ComfyUI-OmniGen2 [NAME CONFLICT]", "reference": "https://github.com/coVISIONSld/ComfyUI-OmniGen2", "files": ["https://github.com/coVISIONSld/ComfyUI-OmniGen2"], "install_type": "git-clone", "description": "ComfyUI-OmniGen2 is a custom node package for the OmniGen2 model, enabling advanced text-to-image generation and visual understanding."}, {"author": "ZHO-ZHO-ZHO", "title": "ComfyUI-Gemini [NAME CONFLICT]", "id": "gemini", "reference": "https://github.com/ZHO-ZHO-ZHO/ComfyUI-Gemini", "files": ["https://github.com/ZHO-ZHO-ZHO/ComfyUI-Gemini"], "install_type": "git-clone", "description": "Using Gemini-pro & Gemini-pro-vision in ComfyUI."}, {"author": "No-22-<PERSON><PERSON><PERSON>", "title": "ComfyUI_SaveImageCustom", "reference": "https://github.com/No-22-Github/ComfyUI_SaveImageCustom", "files": ["https://github.com/No-22-Github/ComfyUI_SaveImageCustom"], "install_type": "git-clone", "description": "NODES: Fish-Speech Loader, Fish-Speech TTS, Fish-Speech Audio Preview"}, {"author": "jiafuzeng", "title": "comfyui-fishSpeech", "reference": "https://github.com/jiafuzeng/comfyui-fishSpeech", "files": ["https://github.com/jiafuzeng/comfyui-fishSpeech"], "install_type": "git-clone", "description": "NODES: Save Image (Dir + Name)"}, {"author": "bleash-dev", "title": "ComfyUI-Auth-Manager", "reference": "https://github.com/bleash-dev/ComfyUI-Auth-Manager", "files": ["https://github.com/bleash-dev/ComfyUI-Auth-Manager"], "install_type": "git-clone", "description": "A custom node that provides email/password authentication for ComfyUI pods with a beautiful modal interface."}, {"author": "<PERSON><PERSON><PERSON>", "title": "ComfyUI-Distributed [WIP]", "reference": "https://github.com/robertvoy/ComfyUI-Distributed", "files": ["https://github.com/robertvoy/ComfyUI-Distributed"], "install_type": "git-clone", "description": "A custom node extension for ComfyUI that enables distributed image generation across multiple GPUs through a master-worker architecture."}, {"author": "filliptm", "title": "ComfyUI_Fill-Node-Loader [WIP]", "reference": "https://github.com/filliptm/ComfyUI_Fill-Node-Loader", "files": ["https://github.com/filliptm/ComfyUI_Fill-Node-Loader"], "install_type": "git-clone", "description": "A ComfyUI plugin to simplify loading and managing custom nodes with a sidebar interface."}, {"author": "<PERSON><PERSON><PERSON>", "title": "Comfy Inpainting Works [WIP]", "reference": "https://github.com/diodiogod/Comfy-Inpainting-Works", "files": ["https://github.com/diodiogod/Comfy-Inpainting-Works"], "install_type": "git-clone", "description": "Go to the top menu>Workflow>Browse Templates. This is a collection of my Inpainting workflows for Flux (expanded and COMPACT) + others. Previously called: 'Proper Flux Control-Net inpainting and/or outpainting with batch size - Alimama or Flux Fill'. By installing this 'node' you can always keep them up to date by updating on the manager. This is not a new custom node. You will still need to install all other custom nodes used on the workflows. You will also find my 'Flux LoRA Block Weights Preset Tester' here as well.\nNOTE: The files in the repo are not organized."}, {"author": "Malloc-pix", "title": "comfyui-QwenVL", "reference": "https://github.com/Malloc-pix/comfyui-QwenVL", "files": ["https://github.com/Malloc-pix/comfyui-QwenVL"], "install_type": "git-clone", "description": "NODES: Qwen2.5VL, Qwen2.5"}, {"author": "artifyfun", "title": "ComfyUI-JS [UNSAFE]", "reference": "https://github.com/artifyfun/ComfyUI-JS", "files": ["https://github.com/artifyfun/ComfyUI-JS"], "install_type": "git-clone", "description": "A ComfyUI custom node capable of executing JavaScript code: it takes JavaScript code as input and outputs the execution result.[w/This extension has an XSS vulnerability that can be triggered through workflow execution.]"}, {"author": "OgreLemonSoup", "title": "ComfyUI-Notes-manager", "reference": "https://github.com/OgreLemonSoup/ComfyUI-Notes-manager", "files": ["https://github.com/OgreLemonSoup/ComfyUI-Notes-manager"], "install_type": "git-clone", "description": "This extension provides the note feature."}, {"author": "WozStudios", "title": "ComfyUI-WozNodes", "reference": "https://github.com/WozStudios/ComfyUI-WozNodes", "files": ["https://github.com/WozStudios/ComfyUI-WozNodes"], "install_type": "git-clone", "description": "NODES: Trim Image Batch, Create Image Batch, Select Image Batch by Mask, Advanced Batch Creator"}, {"author": "DDDDEEP", "title": "ComfyUI-DDDDEEP", "reference": "https://github.com/DDDDEEP/ComfyUI-DDDDEEP", "files": ["https://github.com/DDDDEEP/ComfyUI-DDDDEEP"], "install_type": "git-clone", "description": "NODES: AutoWidthHeight, ReturnIntSeed, OppositeBool, PromptItemCollection"}, {"author": "stalkervr", "title": "comfyui-custom-path-nodes [UNSAFE]", "reference": "https://github.com/stalkervr/comfyui-custom-path-nodes", "files": ["https://github.com/stalkervr/comfyui-custom-path-nodes"], "install_type": "git-clone", "description": "Nodes for path handling and image cropping.[w/This nodepack has a vulnerability that allows remote access to arbitrary file paths.]"}, {"author": "vovler", "title": "comfyui-vovlertools", "reference": "https://github.com/vovler/ComfyUI-vovlerTools", "files": ["https://github.com/vovler/ComfyUI-vovlerTools"], "install_type": "git-clone", "description": "Advanced ComfyUI nodes for WD14 tagging, image filtering, and CLIP to TensorRT conversion"}, {"author": "ELiZswe", "title": "ComfyUI-ELiZTools", "reference": "https://github.com/ELiZswe/ComfyUI-ELiZTools", "files": ["https://github.com/ELiZswe/ComfyUI-ELiZTools"], "install_type": "git-clone", "description": "ELIZ Tools"}, {"author": "yamanacn", "title": "comfyui_qwen<PERSON>ox", "reference": "https://github.com/yamanacn/comfyui_qwenbbox", "files": ["https://github.com/yamanacn/comfyui_qwenbbox"], "install_type": "git-clone", "description": "NODES: <PERSON><PERSON> (v2), Qwen Bbox Detection, Prepare BBox for SAM (v2)"}, {"author": "mikheys", "title": "ComfyUI-mikheys", "reference": "https://github.com/mikheys/ComfyUI-mikheys", "files": ["https://github.com/mikheys/ComfyUI-mikheys"], "install_type": "git-clone", "description": "NODES: WAN Optimal Resolution Selector, WAN Show Image Dimensions"}, {"author": "iacoposk8", "title": "ComfyUI XOR Pickle Nodes", "reference": "https://github.com/iacoposk8/xor_pickle_nodes", "files": ["https://github.com/iacoposk8/xor_pickle_nodes"], "install_type": "git-clone", "description": "Two custom nodes for ComfyUI that allow you to encrypt and decrypt Python objects using simple XOR encryption with pickle."}, {"author": "Aryan185", "title": "ComfyUI-ReplicateFluxKontext", "reference": "https://github.com/Aryan185/ComfyUI-ReplicateFluxKontext", "files": ["https://github.com/Aryan185/ComfyUI-ReplicateFluxKontext"], "install_type": "git-clone", "description": "ComfyUI node for Flux Kontext Pro and Max models from Replicate"}, {"author": "yamanacn", "title": "comfyui_qwen_object [WIP]", "reference": "https://github.com/yamanacn/comfyui_qwen_object", "files": ["https://github.com/yamanacn/comfyui_qwen_object"], "install_type": "git-clone", "description": "This is a custom node for ComfyUI that integrates the Qwen vision model for tasks such as object detection.\nNOTE: The files in the repo are not organized."}, {"author": "<PERSON><PERSON><PERSON>", "title": "ComfyUI-Show-o [WIP]", "reference": "https://github.com/neverbiasu/ComfyUI-Show-o", "files": ["https://github.com/neverbiasu/ComfyUI-Show-o"], "install_type": "git-clone", "description": "NODES: Show-o <PERSON> Loader, Show-o Text to Image, Show-o Image Captioning, Show-o Image Inpainting"}, {"author": "<PERSON><PERSON><PERSON><PERSON>", "title": "ComfyUI-Hunyuan3d-2-1", "reference": "https://github.com/visualbruno/ComfyUI-Hunyuan3d-2-1", "files": ["https://github.com/visualbruno/ComfyUI-Hunyuan3d-2-1"], "install_type": "git-clone", "description": "NODES: Hunyuan 3D 2.1 Mesh Generator, Hunyuan 3D 2.1 MultiViews Generator, Hunyuan 3D 2.1 Bake MultiViews, Hunyuan 3D 2.1 InPaint, Hunyuan 3D 2.1 Camera Config"}, {"author": "zyquon", "title": "ComfyUI Stash", "reference": "https://github.com/zyquon/ComfyUI-Stash", "files": ["https://github.com/zyquon/ComfyUI-Stash"], "install_type": "git-clone", "description": "Nodes to use Stash within Comfy workflows"}, {"author": "tanken<PERSON><PERSON>-ola", "title": "comfyui-env-variable-reader [UNSAFE]", "reference": "https://github.com/tankenyuen-ola/comfyui-env-variable-reader", "files": ["https://github.com/tankenyuen-ola/comfyui-env-variable-reader"], "install_type": "git-clone", "description": "NODES: Environment Variable Reader [w/Installing this node may expose environment variables that contain sensitive information such as API keys.]"}, {"author": "ftf001-tech", "title": "ComfyUI-Lucian [WIP]", "reference": "https://github.com/ftf001-tech/ComfyUI-ExternalLLMDetector", "files": ["https://github.com/ftf001-tech/ComfyUI-ExternalLLMDetector"], "install_type": "git-clone", "description": "These nodes allow you to configure LLM API connections, send images with custom prompts, and convert the LLM's JSON bounding box responses into a format compatible with segmentation nodes like SAM2\nNOTE: The files in the repo are not organized."}, {"author": "LucianGnn", "title": "ComfyUI-Lucian [WIP]", "reference": "https://github.com/LucianGnn/ComfyUI-Lucian", "files": ["https://github.com/LucianGnn/ComfyUI-Lucian"], "install_type": "git-clone", "description": "NODES: Audio Duration Calculator\nNOTE: The files in the repo are not organized."}, {"author": "akatz-ai", "title": "ComfyUI-Execution-Inversion", "reference": "https://github.com/akatz-ai/ComfyUI-Execution-Inversion", "files": ["https://github.com/akatz-ai/ComfyUI-Execution-Inversion"], "install_type": "git-clone", "description": "Contains nodes related to the new execution inversion engine in ComfyUI. Node pack originally from [a/https://github.com/BadCafeCode/execution-inversion-demo-comfyui](https://github.com/BadCafeCode/execution-inversion-demo-comfyui)"}, {"author": "<PERSON><PERSON><PERSON>", "title": "comfyui_minicpm_vision", "reference": "https://github.com/mamorett/comfyui_minicpm_vision", "files": ["https://github.com/mamorett/comfyui_minicpm_vision"], "install_type": "git-clone", "description": "NODES: MiniCPM Vision GGUF"}, {"author": "BigStationW", "title": "flowmatch_scheduler-comfyui", "reference": "https://github.com/BigStationW/flowmatch_scheduler-comfyui", "files": ["https://github.com/BigStationW/flowmatch_scheduler-comfyui"], "install_type": "git-clone", "description": "NODES: FlowMatchSigmas"}, {"author": "<PERSON><PERSON><PERSON><PERSON>", "title": "MiniMax-bmo", "reference": "https://github.com/casterpollux/MiniMax-bmo", "files": ["https://github.com/casterpollux/MiniMax-bmo"], "install_type": "git-clone", "description": "ComfyUI MiniMax Remover Node"}, {"author": "franky519", "title": "ComfyUI Face Four Image Matcher [WIP]", "reference": "https://github.com/franky519/comfyui_fnckc_Face_analysis", "files": ["https://github.com/franky519/comfyui_fnckc_Face_analysis"], "install_type": "git-clone", "description": "ComfyUI custom node for four face image matching and face swap control\nNOTE: Invalid pyproject.toml"}, {"author": "bleash-dev", "title": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>-Checker", "reference": "https://github.com/bleash-dev/Comfyui-Idle-Checker", "files": ["https://github.com/bleash-dev/Comfyui-Idle-Checker"], "install_type": "git-clone", "description": "front extension for idle checker"}, {"author": "fangg2000", "title": "ComfyUI-StableAudioFG [WIP]", "reference": "https://github.com/fangg2000/ComfyUI-StableAudioFG", "files": ["https://github.com/fangg2000/ComfyUI-StableAudioFG"], "install_type": "git-clone", "description": "The ComfyUI plugin for stable-audio (supports offline use)\nNOTE: The files in the repo are not organized."}, {"author": "hdfhssg", "title": "comfyui_EvoSearch [WIP]", "reference": "https://github.com/hdfhssg/comfyui_EvoSearch", "files": ["https://github.com/hdfhssg/comfyui_EvoSearch"], "install_type": "git-clone", "description": "NODES: EvoSearch_FLUX, EvoSearch_SD21, EvoSearch_WAN, EvolutionScheduleGenerator, GuidanceRewardsGenerator"}, {"author": "simon<PERSON><PERSON>", "title": "ComfyUI-sjnodes", "reference": "https://github.com/simonjaq/ComfyUI-sjnodes", "files": ["https://github.com/simonjaq/ComfyUI-sjnodes"], "install_type": "git-clone", "description": "Some modified ComfyUI custom nodes"}, {"author": "A4P7J1N7M05OT", "title": "ComfyUI-VAELoaderSDXLmod", "reference": "https://github.com/A4P7J1N7M05OT/ComfyUI-VAELoaderSDXLmod", "files": ["https://github.com/A4P7J1N7M05OT/ComfyUI-VAELoaderSDXLmod"], "install_type": "git-clone", "description": "NODES: Modified SDXL VAE Loader, Empty Latent Image Variable"}, {"author": "<PERSON><PERSON><PERSON>", "title": "xzuynodes-ComfyUI", "reference": "https://github.com/xzuyn/ComfyUI-xzuynodes", "files": ["https://github.com/xzuyn/ComfyUI-xzuynodes"], "install_type": "git-clone", "description": "NODES: First/Last Frame (XZ), Resize Image (Original KJ), Resize Image (XZ), CLIP Text Encode (XZ), Load CLIP (XZ), TripleCLIPLoader (XZ), WanImageToVideo (XZ)"}, {"author": "gilons", "title": "ComfyUI-GoogleDrive-Downloader [UNSAFE]", "reference": "https://github.com/gilons/ComfyUI-GoogleDrive-Downloader", "files": ["https://github.com/gilons/ComfyUI-GoogleDrive-Downloader"], "install_type": "git-clone", "description": "ComfyUI custom node for downloading files from Google Drive.[w/There is a vulnerability that allows saving a remote file to an arbitrary local path.]"}, {"author": "<PERSON>w<PERSON><PERSON>", "title": "ComfyUI-FileBrowserAPI [UNSAFE]", "reference": "https://github.com/GalactusX31/ComfyUI-FileBrowserAPI", "files": ["https://github.com/GalactusX31/ComfyUI-FileBrowserAPI"], "install_type": "git-clone", "description": "A general-purpose, dependency-free File and Folder Browser API for ComfyUI custom nodes.[w/path traversal vulnerability]"}, {"author": "<PERSON>w<PERSON><PERSON>", "title": "comfyui-moonpack", "reference": "https://github.com/moonwhaler/comfyui-moonpack", "files": ["https://github.com/moonwhaler/comfyui-moonpack"], "install_type": "git-clone", "description": "NODES: Proportional Dimension, Simple String Replace, Regex String Replace, VACE Looper Frame Scheduler"}, {"author": "DreamsInAutumn", "title": "ComfyUI-Autumn-LLM-Nodes", "reference": "https://github.com/DreamsInAutumn/ComfyUI-Autumn-LLM-Nodes", "files": ["https://github.com/DreamsInAutumn/ComfyUI-Autumn-LLM-Nodes"], "install_type": "git-clone", "description": "NODES: Gemini-Image-To-Prompt, Gemini-Prompt-Builder, LLM-Prompt-Builder"}, {"author": "alexgenovese", "title": "ComfyUI-Reica", "reference": "https://github.com/alexgenovese/ComfyUI-Reica", "files": ["https://github.com/alexgenovese/ComfyUI-Reica"], "install_type": "git-clone", "description": "NODES: 'Reica GCP: Read Image', 'Reica GCP: Write Image & Get URL', 'Reica Text Image Display', 'Reica Read Image URL', 'Reica URL Image Loader Filename', 'Reica API: Send HTTP Notification', 'Insert Anything'"}, {"author": "y<PERSON><PERSON><PERSON>", "title": "ComfyUI-Transition", "reference": "https://github.com/yichengup/ComfyUI-Transition", "files": ["https://github.com/yichengup/ComfyUI-Transition"], "install_type": "git-clone", "description": "NODES: Linear Transition, Gradient Transition, Dual Line Transition, Sequence Transition, Circular Transition, Circular Sequence Transition"}, {"author": "wildminder", "title": "ComfyUI-MagCache [NAME CONFLICT|WIP]", "reference": "https://github.com/wildminder/ComfyUI-MagCache", "files": ["https://github.com/wildminder/ComfyUI-MagCache"], "install_type": "git-clone", "description": "official implementation of [zehong-ma/MagCache](https://github.com/zehong-ma/MagCache) for ComfyUI"}, {"author": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "title": "ComfyUI Storyboard [WIP]", "reference": "https://github.com/laubsauger/comfyui-storyboard", "files": ["https://github.com/laubsauger/comfyui-storyboard"], "install_type": "git-clone", "description": "This custom node for ComfyUI provides a markdown renderer to display formatted text and notes within your workflow."}, {"author": "IsItDanOrAi", "title": "ComfyUI-exLoadout [WIP]", "reference": "https://github.com/IsItDanOrAi/ComfyUI-exLoadout", "files": ["https://github.com/IsItDanOrAi/ComfyUI-exLoadout"], "install_type": "git-clone", "description": "exLoadout is a suite of lightweight ComfyUI custom nodes that let you define and switch between full 'loadouts' stored in an Excel sheet. A loadout could include any node inputs that expect string values—models (checkpoints, CLIP, VAE, ControlNets, LoRAs, UNets), numeric or text variables (CFG, sampler names, scheduler types, etc.)—all pulled from a row in your sheet. By selecting a row, you instantly apply all of its settings in your workflow, with built‑in support for editing and reading those cells right inside the UI.\nNOTE: The files in the repo are not organized."}, {"author": "grokuku", "title": "ComfyUI-Holaf-Terminal [UNSAFE]", "reference": "https://github.com/grokuku/ComfyUI-Holaf-Utilities", "files": ["https://github.com/grokuku/ComfyUI-Holaf-Utilities"], "install_type": "git-clone", "description": "Interactive Terminal in a node for ComfyUI[w/This custom extension provides a remote web-based shell (terminal) interface to the machine running the ComfyUI server. By installing and using this extension, you are opening a direct, powerful, and potentially dangerous access point to your system.]"}, {"author": "whmc76", "title": "ComfyUI-LongTextTTSSuite [WIP]", "reference": "https://github.com/whmc76/ComfyUI-LongTextTTSSuite", "files": ["https://github.com/whmc76/ComfyUI-LongTextTTSSuite"], "install_type": "git-clone", "description": "This plugin can cut txt or srt files, hand them over to TTS for speech slicing generation, and synthesize long speech\nNOTE: The files in the repo are not organized."}, {"author": "usrname0", "title": "ComfyUI-AllergicPack [WIP]", "reference": "https://github.com/usrname0/ComfyUI-AllergicPack", "files": ["https://github.com/usrname0/ComfyUI-AllergicPack"], "install_type": "git-clone", "description": "This package is not ready for primetime but I'm making it public anyway. If I'm using the node then I'm putting it here. Might make it more official later. Use at your own risk."}, {"author": "spawner", "title": "comfyui-spawner-nodes", "reference": "https://github.com/spawner1145/comfyui-spawner-nodes", "files": ["https://github.com/spawner1145/comfyui-spawner-nodes"], "install_type": "git-clone", "description": "NODES: Read Image Metadata, JSON process, Text Encoder/Decoder"}, {"author": "cesilk10", "title": "cesilk-comfyui-nodes", "reference": "https://github.com/cesilk10/cesilk-comfyui-nodes", "files": ["https://github.com/cesilk10/cesilk-comfyui-nodes"], "install_type": "git-clone", "description": "NODES: Save and Upload to S3, SDXL Image Sizes"}, {"author": "COcisuts", "title": "CObot-ComfyUI-WhisperToTranscription [WIP]", "reference": "https://github.com/COcisuts/CObot-ComfyUI-WhisperToTranscription", "files": ["https://github.com/COcisuts/CObot-ComfyUI-WhisperToTranscription"], "install_type": "git-clone", "description": "CObot-ComfyUI-WhisperToTranscription\nNOTE: missing requirements.txt"}, {"author": "xuhuan2048", "title": "ExtractStoryboards [WIP]", "reference": "https://github.com/gitadmini/comfyui_extractstoryboards", "files": ["https://github.com/gitadmini/comfyui_extractstoryboards"], "install_type": "git-clone", "description": "A tool for decomposing video storyboards, which can obtain storyboards and keyframes"}, {"author": "jinchanz", "title": "ComfyUI-AliCloud-Bailian [WIP]", "reference": "https://github.com/jinchanz/ComfyUI-AliCloud-Bailian", "files": ["https://github.com/jinchanz/ComfyUI-AliCloud-Bailian"], "install_type": "git-clone", "description": "This is a collection of custom nodes for invoking Alibaba Cloud's DashScope API within ComfyUI.\nNOTE: The files in the repo are not organized."}, {"author": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "title": "ComfyUI-KontextOfficialNode", "reference": "https://github.com/<PERSON><PERSON><PERSON>ta-Yukinoe/ComfyUI-KontextOfficialNode", "files": ["https://github.com/<PERSON><PERSON><PERSON>ta-Yukinoe/ComfyUI-KontextOfficialNode"], "install_type": "git-clone", "description": "NODES: Kontext Text-to-Image (Official Max), Kontext Image Editing (Official Max)"}, {"author": "takoyaki1118", "title": "ComfyUI_PromptExtractor", "reference": "https://github.com/takoyaki1118/ComfyUI_PromptExtractor", "files": ["https://github.com/takoyaki1118/ComfyUI_PromptExtractor"], "install_type": "git-clone", "description": "NODES: Custom Load Image With Path, Prompt Extractor Node"}, {"author": "littleowl", "title": "ComfyUI-MV-HECV", "reference": "https://github.com/littleowl/ComfyUI-MV-HECV", "files": ["https://github.com/littleowl/ComfyUI-MV-HECV"], "install_type": "git-clone", "description": "ComfyUI export of 3D Videos and Images Compatible with VR / XR, including the AVP."}, {"author": "BinglongLi", "title": "ComfyUI_ToolsForAutomask", "reference": "https://github.com/BinglongLi/ComfyUI_ToolsForAutomask", "files": ["https://github.com/BinglongLi/ComfyUI_ToolsForAutomask"], "install_type": "git-clone", "description": "NODES: Directional Mask Expansion, Remove Small Regions Mask, Precise Subtract Mask, Precise Add Mask, Closing Mask, Opening Mask, Conditional Mask Selector, Prune Thin Branches Mask, Mask Fill Gaps Convex Hull"}, {"author": "strhwste", "title": "CSV Utils [WIP]", "reference": "https://github.com/strhwste/comfyui_csv_utils", "files": ["https://github.com/strhwste/comfyui_csv_utils"], "install_type": "git-clone", "description": "Custom CSV handling nodes for ComfyUI\nNOTE: invalid pyproject.toml"}, {"author": "retech995", "title": "ComfyUI_SaveImageBulk [UNSAFE]", "reference": "https://github.com/retech995/Save_Florence2_Bulk_Prompts", "files": ["https://github.com/retech995/Save_Florence2_Bulk_Prompts"], "install_type": "git-clone", "description": "This comfyui node helps save image[w/This node can write files to an arbitrary path.]"}, {"author": "Oct7", "title": "ComfyUI-LaplaMask", "reference": "https://github.com/Oct7/ComfyUI-LaplaMask", "files": ["https://github.com/Oct7/ComfyUI-LaplaMask"], "install_type": "git-clone", "description": "NODES: Blur→Mask"}, {"author": "etng", "title": "ComfyUI-Heartbeat [UNSAFE]", "reference": "https://github.com/etng/ComfyUI-Heartbeat", "files": ["https://github.com/etng/ComfyUI-Heartbeat"], "install_type": "git-clone", "description": "A plugin for ComfyUI that sends periodic heartbeat requests to a configured gateway, including system information and node status."}, {"author": "Novavision0313", "title": "ComfyUI-NVVS [WIP]", "reference": "https://github.com/Novavision0313/ComfyUI-NVVS", "files": ["https://github.com/Novavision0313/ComfyUI-NVVS"], "install_type": "git-clone", "description": "A ComfyUI plugin customized by NOVEVISION\nNOTE: The files in the repo are not organized."}, {"author": "zackabrams", "title": "ComfyUI-KeySyncWrapper [WIP]", "reference": "https://github.com/zackabrams/ComfyUI-KeySyncWrapper", "files": ["https://github.com/zackabrams/ComfyUI-KeySyncWrapper"], "install_type": "git-clone", "description": "implementation of KeySync in ComfyUI"}, {"author": "godric8", "title": "ComfyUI_Step1X-Edit [NAME CONFLICT]", "reference": "https://github.com/godric8/ComfyUI_Step1X-Edit", "files": ["https://github.com/godric8/ComfyUI_Step1X-Edit"], "install_type": "git-clone", "description": "ComfyUI nodes for Step1X-Edit"}, {"author": "violet0927", "title": "ComfyUI-Direct3DS2 [WIP]", "reference": "https://github.com/y4my4my4m/ComfyUI_Direct3DS2", "files": ["https://github.com/y4my4my4m/ComfyUI_Direct3DS2"], "install_type": "git-clone", "description": "Direct3D-S2 plugin for ComfyUI. [w/Doesn't work yet]"}, {"author": "gam<PERSON><PERSON><PERSON>", "title": "ComfyUI-N_SwapInput [UNSAFE]", "reference": "https://github.com/gamtruliar/ComfyUI-N_SwapInput", "files": ["https://github.com/gamtruliar/ComfyUI-N_SwapInput"], "install_type": "git-clone", "description": "This is a simple tool for swapping input folders with custom suffix in comfy-UI[w/]This node pack performs deletion operations on local files and contains a vulnerability that allows arbitrary paths to be deleted."}, {"author": "bulldog68", "title": "ComfyUI_FMJ [WIP]", "reference": "https://github.com/bulldog68/ComfyUI_FMJ", "files": ["https://github.com/bulldog68/ComfyUI_FMJ"], "install_type": "git-clone", "description": "Generate random prompts easily for FMJ.\nNOTE: The files in the repo are not organized."}, {"author": "amami<PERSON><PERSON><PERSON>", "title": "MixvtonComfyui [WIP]", "reference": "https://github.com/amamisonlyuser/MixvtonComfyui", "files": ["https://github.com/amamisonlyuser/MixvtonComfyui"], "install_type": "git-clone", "description": "NODES: C<PERSON>H_Leffa_Viton_Load, CXH_Leffa_Viton_Run\nNOTE: The files in the repo are not organized."}, {"author": "pictorialink", "title": "comfyui-static-resource[UNSAFE]", "reference": "https://github.com/pictorialink/ComfyUI-static-resource", "files": ["https://github.com/pictorialink/ComfyUI-static-resource"], "install_type": "git-clone", "description": "Use model bending to push your model beyond its visuals' limits. These nodes allow you to apply transformations to the intemediate densoising steps during sampling, e.g. add, multiplty, scale, rotate, dilate, erode ..etc.[w/This node pack includes a feature that allows downloading remote files to arbitrary local paths. This is a vulnerability that can lead to Remote Code Execution.]"}, {"author": "brace-great", "title": "comfyui-mc [WIP]", "reference": "https://github.com/brace-great/comfyui-mc", "files": ["https://github.com/brace-great/comfyui-mc"], "install_type": "git-clone", "description": "NODES: IncrementCounterOnMatch\nNOTE: The files in the repo are not organized."}, {"author": "blueraincoatli", "title": "ComfyModelCleaner [WIP]", "reference": "https://github.com/blueraincoatli/ComfyUI-Model-Cleaner", "files": ["https://github.com/blueraincoatli/ComfyUI-Model-Cleaner"], "install_type": "git-clone", "description": "This plugin helps identify and clean up unused model files in ComfyUI installations. It analyzes workflows, custom nodes, and model usage to safely identify redundant files."}, {"author": "avocadori", "title": "ComfyUI Audio Amplitude Converter [WIP]", "reference": "https://github.com/avocadori/ComfyUI-AudioAmplitudeConverter", "files": ["https://github.com/avocadori/ComfyUI-AudioAmplitudeConverter"], "install_type": "git-clone", "description": "This is a high-performance custom node for ComfyUI that performs audio amplitude conversion.\nNOTE: The files in the repo are not organized."}, {"author": "wTechArtist", "title": "ComfyUI_VVL_VideoCamera", "reference": "https://github.com/wTechArtist/ComfyUI_VVL_VideoCamera", "files": ["https://github.com/wTechArtist/ComfyUI_VVL_VideoCamera"], "install_type": "git-clone", "description": "NODES: VVL Video Camera Estimator, VVL Video Frame Extractor"}, {"author": "wTechArtist", "title": "ComfyUI_VVL_Segmentation [WIP]", "reference": "https://github.com/wTechArtist/ComfyUI_VVL_Segmentation", "files": ["https://github.com/wTechArtist/ComfyUI_VVL_Segmentation"], "install_type": "git-clone", "description": "NODES: VVL Mask2Former Panoptic (Enhanced), VVL OneFormer Universal Segmentation\nNOTE: The files in the repo are not organized."}, {"author": "lum3on", "title": "comfyui_RollingDepth [WIP]", "reference": "https://github.com/lum3on/comfyui_RollingDepth", "files": ["https://github.com/lum3on/comfyui_RollingDepth"], "install_type": "git-clone", "description": "ComfyuI Needs longer to start the first time, because the mode gets downloaded.\nNOTE: The files in the repo are not organized."}, {"author": "abuzreq", "title": "ComfyUI Model Bending [UNSAFE]", "reference": "https://github.com/abuzreq/ComfyUI-Model-Bending", "files": ["https://github.com/abuzreq/ComfyUI-Model-Bending"], "install_type": "git-clone", "description": "Use model bending to push your model beyond its visuals' limits. These nodes allow you to apply transformations to the intemediate densoising steps during sampling, e.g. add, multiplty, scale, rotate, dilate, erode ..etc.[w/This node pack contains a vulnerability that allows remote code execution.]"}, {"author": "Stable Diffusion VN", "title": "SDVN Comfy node [UNSAFE]", "id": "SDVN", "reference": "https://github.com/StableDiffusionVN/SDVN_Comfy_node", "files": ["https://github.com/StableDiffusionVN/SDVN_Comfy_node"], "install_type": "git-clone", "description": "Update IC Lora Layout Support Node[w/This node pack contains a vulnerability that allows remote code execution.]"}, {"author": "<PERSON><PERSON><PERSON>", "title": "comfyui_caption-around-image", "reference": "https://github.com/Sephrael/comfyui_caption-around-image", "files": ["https://github.com/Sephrael/comfyui_caption-around-image"], "install_type": "git-clone", "description": "NODES: A comfyUI node to create captions around a generated image with the ability to dynamically include generation parameters"}, {"author": "EQXai", "title": "ComfyUI_EQX", "reference": "https://github.com/EQXai/ComfyUI_EQX", "files": ["https://github.com/EQXai/ComfyUI_EQX"], "install_type": "git-clone", "description": "NODES: SaveImage_EQX, File Image Selector, Load Prompt From File - EQX, LoraStackEQX_random, Extract Filename - EQX, Extract LORA name - EQX, NSFW Detector EQX, NSFW Detector Advanced EQX"}, {"author": "yincangshiwei", "title": "ComfyUI-SEQLToolNode", "reference": "https://github.com/yincangshiwei/ComfyUI-SEQLToolNode", "files": ["https://github.com/yincangshiwei/ComfyUI-SEQLToolNode"], "install_type": "git-clone", "description": "NODES: ImageCropAlphaNode (Image), CanvasFusionNode (Image)"}, {"author": "gabe-init", "title": "comfyui_ui_render [UNSAFE]", "reference": "https://github.com/gabe-init/comfyui_ui_render", "files": ["https://github.com/gabe-init/comfyui_ui_render"], "install_type": "git-clone", "description": "ComfyUI HTML Renderer Node - Display rich HTML content within ComfyUI nodes[w/This nodepack contains nodes that potentially have XSS vulnerabilities.]"}, {"author": "gabe-init", "title": "ComfyUI LM Studio Node [WIP]", "reference": "https://github.com/gabe-init/ComfyUI-LM-Studio", "files": ["https://github.com/gabe-init/ComfyUI-LM-Studio"], "install_type": "git-clone", "description": "A powerful ComfyUI custom node that seamlessly integrates LM Studio's local language models into your ComfyUI workflows. This node supports both text-only and multimodal (text + image) inputs, making it perfect for complex AI-driven creative workflows.\nNOTE: The files in the repo are not organized."}, {"author": "LyazS", "title": "ComfyUI-aznodes", "reference": "https://github.com/LyazS/ComfyUI-aznodes", "files": ["https://github.com/LyazS/ComfyUI-aznodes"], "install_type": "git-clone", "description": "NODES: CrossFadeImageSequence, SaveImageAZ"}, {"author": "truebillyblue", "title": "lC.ComfyUI_epistemic_nodes [WIP]", "reference": "https://github.com/truebillyblue/lC.ComfyUI_epistemic_nodes", "files": ["https://github.com/truebillyblue/lC.ComfyUI_epistemic_nodes"], "install_type": "git-clone", "description": "NODES: lC L1 Startle, lC L2 FrameClick, lC L3 KeymapClick, lC L4 AnchorClick, lC L5 FieldClick, lC L6 ReflectBoom, lC Epistemic Pipeline (L1-L7), Create PBI (lC), Query PBIs (lC), Update PBI (lC), lC API LLM Agent, lC Web LLM Agent, ...\nNOTE: The files in the repo are not organized."}, {"author": "aklevecz", "title": "ComfyUI-AutoPrompt [WIP]", "reference": "https://github.com/aklevecz/ComfyUI-AutoPrompt", "files": ["https://github.com/aklevecz/ComfyUI-AutoPrompt"], "install_type": "git-clone", "description": "NODES: Ollama Prompt Generator, Ollama Model Lister, <PERSON><PERSON><PERSON>, Text Display"}, {"author": "<PERSON><PERSON><PERSON>", "title": "ComfyUI Timesaver Nodes", "reference": "https://github.com/AlexYez/comfyui-timesaver", "files": ["https://github.com/AlexYez/comfyui-timesaver"], "install_type": "git-clone", "description": "ComfyUI nodes from [Timesaver](https://github.com/AlexYez/comfyui-timesaver)."}, {"author": "aa-parky", "title": "pipemind-comfyui", "reference": "https://github.com/aa-parky/pipemind-comfyui", "files": ["https://github.com/aa-parky/pipemind-comfyui"], "install_type": "git-clone", "description": "NODES: Random Line from File (Seeded), Keyword Prompt Composer, Simple Prompt Combiner (5x), Boolean Switch (Any), Select Line from TxT (Any), Multiline Text Input, Flux 2M Aspect Ratios, SDXL Aspect Ratios, Room Mapper"}, {"author": "pacchikAI", "title": "ImagePromptBatch [UNSAFE]", "reference": "https://github.com/pacchikAI/ImagePromptBatch", "files": ["https://github.com/pacchikAI/ImagePromptBatch"], "install_type": "git-clone", "description": "NODES: Load Image and Prompt[w/This includes a node that can read the contents of a `.csv` file from an arbitrary path.]"}, {"author": "papcorns", "title": "ComfyUI-Papcorns-Node-UploadToGCS", "reference": "https://github.com/papcorns/ComfyUI-Papcorns-Node-UploadToGCS", "files": ["https://github.com/papcorns/ComfyUI-Papcorns-Node-UploadToGCS"], "install_type": "git-clone", "description": "NODES: Upload Image To GCS"}, {"author": "ZHO-ZHO-ZHO", "title": "Qwen-2.5 in ComfyUI [NAME CONFLICT]", "reference": "https://github.com/ZHO-ZHO-ZHO/ComfyUI-Qwen", "files": ["https://github.com/ZHO-ZHO-ZHO/ComfyUI-Qwen"], "install_type": "git-clone", "description": "Using Qwen-2.5 in ComfyUI"}, {"author": "Charonartist", "title": "gabe-init [WIP]", "reference": "https://github.com/gabe-init/ComfyUI-Repo-Eater", "files": ["https://github.com/gabe-init/ComfyUI-Repo-Eater"], "install_type": "git-clone", "description": "A ComfyUI custom node that ingests GitHub repositories and outputs their content as text along with token count.\nNOTE: The files in the repo are not organized."}, {"author": "Charonartist", "title": "ComfyUI-send-eagle-pro", "reference": "https://github.com/Charonartist/ComfyUI-send-eagle-pro_2", "files": ["https://github.com/Charonartist/ComfyUI-send-eagle-pro_2"], "install_type": "git-clone", "description": "Eagle integration extension node for ComfyUI (Pro version)"}, {"author": "<PERSON><PERSON><PERSON>", "title": "comfyui-yaml-prompt", "reference": "https://github.com/Mervent/comfyui-yaml-prompt", "files": ["https://github.com/Mervent/comfyui-yaml-prompt"], "install_type": "git-clone", "description": "NODES: YAMLPromptParser"}, {"author": "dhpdong", "title": "ComfyUI-IPAdapter-Flux-Repair", "reference": "https://github.com/dhpdong/ComfyUI-IPAdapter-Flux-Repair", "files": ["https://github.com/dhpdong/ComfyUI-IPAdapter-Flux-Repair"], "install_type": "git-clone", "description": "The IPAdapter-Flux node may cause some GPU memory to not be properly released during multiple inferences or when alternating between two nodes, eventually leading to a memory overflow. This project addresses and fixes that issue."}, {"author": "usman2003", "title": "ComfyUI-RaceDetect", "reference": "https://github.com/usman2003/ComfyUI-RaceDetect", "files": ["https://github.com/usman2003/ComfyUI-RaceDetect"], "install_type": "git-clone", "description": "NODES: Race Detection V2"}, {"author": "<PERSON><PERSON><PERSON>", "title": "comfyui-telegram-send", "reference": "https://github.com/Mervent/comfyui-telegram-send", "files": ["https://github.com/Mervent/comfyui-telegram-send"], "install_type": "git-clone", "description": "NODES: TelegramSend, TelegramReply"}, {"author": "qlikpetersen", "title": "ComfyUI-AI_Tools [UNSAFE]", "reference": "https://github.com/qlikpetersen/ComfyUI-AI_Tools", "files": ["https://github.com/qlikpetersen/ComfyUI-AI_Tools"], "install_type": "git-clone", "description": "NODES: Do<PERSON><PERSON><PERSON>, HttpRequest, Json2String, String2Json, CreateListString, CreateListJSON, Query_OpenAI, Image_Attachment, JSON_Attachment, String_Attachment, RunPython\n[w/This node pack contains a node with a vulnerability that allows arbitrary code execution.]"}, {"author": "MuAIGC", "title": "DMXAPI Nodes [WIP]", "reference": "https://github.com/MuAIGC/ComfyUI-DMXAPI_mmx", "files": ["https://github.com/MuAIGC/ComfyUI-DMXAPI_mmx"], "install_type": "git-clone", "description": "DMXAPI integration for ComfyUI with Seedream-3.0 text-to-image model\nNOTE: invalid pyproject.toml"}, {"author": "Ha<PERSON>leg", "title": "This n that (<PERSON><PERSON>)", "reference": "https://github.com/Hapseleg/ComfyUI-This-n-That", "files": ["https://github.com/Hapseleg/ComfyUI-This-n-That"], "install_type": "git-clone", "description": "Comfyui custom nodes I use for... This n That..."}, {"author": "matDobek", "title": "ComfyUI_duck", "reference": "https://github.com/matDobek/ComfyUI_duck", "files": ["https://github.com/matDobek/ComfyUI_duck"], "install_type": "git-clone", "description": "NODES: Combine Images (duck)"}, {"author": "usman2003", "title": "ComfyUI-Classifiers", "reference": "https://github.com/usman2003/ComfyUI-Classifiers", "files": ["https://github.com/usman2003/ComfyUI-Classifiers"], "install_type": "git-clone", "description": "NODES: Gender Classification"}, {"author": "wTechArtist", "title": "ComfyUI_vvl_BBOX", "reference": "https://github.com/wTechArtist/ComfyUI_vvl_BBOX", "files": ["https://github.com/wTechArtist/ComfyUI_vvl_BBOX"], "install_type": "git-clone", "description": "NODES: vvl BBox Input"}, {"author": "zhengxyz123", "title": "zhengxyz123/ComfyUI-CLIPSeg [NAME CONFLICT]", "reference": "https://github.com/zhengxyz123/ComfyUI-CLIPSeg", "files": ["https://github.com/zhengxyz123/ComfyUI-CLIPSeg"], "install_type": "git-clone", "description": "Using CLIPSeg model to generate masks for image inpainting tasks based on text or image prompts."}, {"author": "Alazuaka", "title": "ComfyUI Image Analysis Toolkit [WIP]", "reference": "https://github.com/ThatGlennD/ComfyUI-Image-Analysis-Tools", "files": ["https://github.com/ThatGlennD/ComfyUI-Image-Analysis-Tools"], "install_type": "git-clone", "description": "A suite of custom ComfyUI nodes built to evaluate and diagnose the technical qualities of images—especially those generated by AI models. Rather than creating visuals, these tools measure them, offering precise insights into sharpness, noise, exposure, color balance, and more.\nNOTE: The files in the repo are not organized."}, {"author": "trampolin", "title": "comfy-ui-scryfall", "reference": "https://github.com/trampolin/comfy-ui-scryfall", "files": ["https://github.com/trampolin/comfy-ui-scryfall"], "install_type": "git-clone", "description": "Some ComfyUI nodes to fetch cards from scryfall"}, {"author": "pomelyu", "title": "cy-prompt-tools", "reference": "https://github.com/pomelyu/cy-prompt-tools", "files": ["https://github.com/pomelyu/cy-prompt-tools"], "install_type": "git-clone", "description": "prompt tools for comfyui"}, {"author": "Alazuaka", "title": "ES_nodes for ComfyUI by Alazuka [WIP]", "reference": "https://github.com/Alazuaka/comfyui-lora-stack-node", "files": ["https://github.com/Alazuaka/comfyui-lora-stack-node"], "install_type": "git-clone", "description": "Node for LoRA stack management in ComfyUI\nNOTE: The files in the repo are not organized."}, {"author": "Good-Dream-Studio", "title": "ComfyUI-Connect [WIP]", "reference": "https://github.com/Good-Dream-Studio/ComfyUI-Connect", "files": ["https://github.com/Good-Dream-Studio/ComfyUI-Connect"], "install_type": "git-clone", "description": "Transform your ComfyUI into a powerful API, exposing all your saved workflows as ready-to-use HTTP endpoints."}, {"author": "fuzr0dah", "title": "comfyui-sceneassembly", "reference": "https://github.com/fuzr0dah/comfyui-sceneassembly", "files": ["https://github.com/fuzr0dah/comfyui-sceneassembly"], "install_type": "git-clone", "description": "A bunch of nodes I created that I also find useful."}, {"author": "PabloGrant", "title": "comfyui-giraffe-test-panel", "reference": "https://github.com/PabloGrant/comfyui-giraffe-test-panel", "files": ["https://github.com/PabloGrant/comfyui-giraffe-test-panel"], "install_type": "git-clone", "description": "General-purpose test node. [w/Use at your own risk. No warranties. No guaranteed support or future updates. Feel free to fork, but remember to share in case anyone else can benefit.]"}, {"author": "<PERSON><PERSON><PERSON><PERSON>", "title": "Comfyui-Condition-Utils [WIP]", "reference": "https://github.com/lrzjason/Comfyui-Condition-Utils", "files": ["https://github.com/lrzjason/Comfyui-Condition-Utils"], "install_type": "git-clone", "description": "A collection of utility nodes for handling condition tensors in ComfyUI."}, {"author": "gordon123", "title": "ComfyUI_DreamBoard [WIP]", "reference": "https://github.com/gordon123/ComfyUI_DreamBoard", "files": ["https://github.com/gordon123/ComfyUI_DreamBoard"], "install_type": "git-clone", "description": "for making storyboard UNDERCONSTRUCTION!"}, {"author": "erosDiffusion", "title": "Select key from JSON (Alpha) [UNSAFE]", "reference": "https://github.com/erosDiffusion/ComfyUI-enricos-json-file-load-and-value-selector", "files": ["https://github.com/erosDiffusion/ComfyUI-enricos-json-file-load-and-value-selector"], "install_type": "git-clone", "description": "this node lists json files in the ComfyUI input folder[w/If this node pack is installed and the server is running with remote access enabled, it can read the contents of JSON files located in arbitrary paths.]"}, {"author": "y<PERSON><PERSON><PERSON>", "title": "ComfyUI-YCNodes_Advance", "reference": "https://github.com/yichengup/ComfyUI-YCNodes_Advance", "files": ["https://github.com/yichengup/ComfyUI-YCNodes_Advance"], "install_type": "git-clone", "description": "NODES: Face Detector Selector, YC Human Parts Ultra(Advance), Color Match (YC)"}, {"author": "rakki194", "title": "ComfyUI_WolfSigmas [UNSAFE]", "reference": "https://github.com/rakki194/ComfyUI_WolfSigmas", "files": ["https://github.com/rakki194/ComfyUI_WolfSigmas"], "install_type": "git-clone", "description": "This custom node pack for ComfyUI provides a suite of tools for generating and manipulating sigma schedules for diffusion models. These nodes are particularly useful for fine-tuning the sampling process, experimenting with different step counts, and adapting schedules for specific models.[w/Security Warning: Remote Code Execution]"}, {"author": "xl0", "title": "q_tools", "reference": "https://github.com/xl0/q_tools", "files": ["https://github.com/xl0/q_tools"], "install_type": "git-clone", "description": "NODES: QLoadLatent, QLinearScheduler, QPreviewLatent, QGaussianLatent, QUniformLatent, QKSampler"}, {"author": "virallover", "reference": "https://github.com/maizerrr/comfyui-code-nodes", "files": ["https://github.com/maizerrr/comfyui-code-nodes"], "install_type": "git-clone", "description": "NODES: <PERSON><PERSON><PERSON> Drawer, <PERSON><PERSON><PERSON> Parser, <PERSON><PERSON>through Node, Batch Images (up to 5), Mask Editor, OpenAI GPT-Image-1 Node, GhatGPT Node"}, {"author": "virallover", "title": "comfyui-virallover", "reference": "https://github.com/virallover/comfyui-virallover", "files": ["https://github.com/virallover/comfyui-virallover"], "install_type": "git-clone", "description": "NODES: Download and Load Lora Model Only, <PERSON><PERSON>h Fitter, Brightness Correction, Edge Noise, Feathered Sharpen, Con<PERSON> Horizontal With Mask"}, {"author": "noband<PERSON><PERSON>", "title": "Ino Custom Nodes", "reference": "https://github.com/nobandegani/comfyui_ino_nodes", "files": ["https://github.com/nobandegani/comfyui_ino_nodes"], "install_type": "git-clone", "description": "NODES: BeDrive Save Image, BeDrive Save File, BeDrive Get Parent ID, Ino Parse File Path, In<PERSON> Not Boolean, Ino Count Files"}, {"author": "jax-explorer", "title": "ComfyUI-DreamO", "reference": "https://github.com/jax-explorer/ComfyUI-DreamO", "files": ["https://github.com/jax-explorer/ComfyUI-DreamO"], "install_type": "git-clone", "description": "[a/https://github.com/bytedance/DreamO](https://github.com/bytedance/DreamO]) ComfyUI Warpper"}, {"author": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "title": "ComfyUI-MakkiTools", "reference": "https://github.com/MakkiShizu/ComfyUI-MakkiTools", "files": ["https://github.com/MakkiShizu/ComfyUI-MakkiTools"], "install_type": "git-clone", "description": "NODES: GetImageNthCount, ImageChannelSeparate, ImageCountConcatenate, MergeImageChannels, ImageWidthStitch, ImageHeigthStitch"}, {"author": "SKBv0", "title": "Retro Engine Node for ComfyUI", "reference": "https://github.com/SKBv0/ComfyUI-RetroEngine", "files": ["https://github.com/SKBv0/ComfyUI-RetroEngine"], "install_type": "git-clone", "description": "This custom node integrates [a/EmulatorJS](https://github.com/EmulatorJS/EmulatorJS) into ComfyUI, allowing you to run retro games and capture their screens for your image generation workflows."}, {"author": "brace-great", "title": "comfy<PERSON>-eim", "reference": "https://github.com/brace-great/comfyui-eim", "files": ["https://github.com/brace-great/comfyui-eim"], "install_type": "git-clone", "description": "NODES: EncryptImage"}, {"author": "p1atdev", "title": "comfyui-aesthetic-predictor", "reference": "https://github.com/p1atdev/comfyui-aesthetic-predictor", "files": ["https://github.com/p1atdev/comfyui-aesthetic-predictor"], "install_type": "git-clone", "description": "NODES: Load Aesthetic Predictor, Predict Aesthetic Score"}, {"author": "<PERSON><PERSON><PERSON>", "title": "barakapa-nodes", "reference": "https://github.com/barakapa/barakapa-nodes", "files": ["https://github.com/barakapa/barakapa-nodes"], "install_type": "git-clone", "description": "Compare and save unique workflows, count tokens in prompt, and other utility."}, {"author": "VictorLopes643", "title": "ComfyUI-Video-Dataset-Tools [WIP]", "reference": "https://github.com/VictorLopes643/ComfyUI-Video-Dataset-Tools", "files": ["https://github.com/VictorLopes643/ComfyUI-Video-Dataset-Tools"], "install_type": "git-clone", "description": "NODES: Video Frame Extractor, Image Frame Saver\nNOTE: The files in the repo are not organized."}, {"author": "George0726", "title": "ComfyUI-video-accessory [WIP]", "reference": "https://github.com/George0726/ComfyUI-video-accessory", "files": ["https://github.com/George0726/ComfyUI-video-accessory"], "install_type": "git-clone", "description": "accessory nodes for video generation"}, {"author": "b<PERSON>s", "title": "ComfyUI-glb-to-stl [WIP]", "reference": "https://github.com/maurorilla/ComfyUI-MisterMR-Nodes", "files": ["https://github.com/maurorilla/ComfyUI-MisterMR-Nodes"], "install_type": "git-clone", "description": "A collection of custom nodes for ComfyUI that add drawing capabilities to your workflow.\nNOTE: The files in the repo are not organized."}, {"author": "TheJorseman", "title": "IntrinsicCompositingClean-ComfyUI", "reference": "https://github.com/TheJorseman/IntrinsicCompositingClean-ComfyUI", "files": ["https://github.com/TheJorseman/IntrinsicCompositingClean-ComfyUI"], "install_type": "git-clone", "description": "NODES: <PERSON>pth<PERSON>odel<PERSON>oader, NormalsModelLoader, IntrinsicModelLoader, AlbedoModelLoader, ReshadingModelLoader, ReshadingProcessor, ...\nNOTE: The files in the repo are not organized."}, {"author": "b<PERSON>s", "title": "ComfyUI-glb-to-stl [WIP]", "reference": "https://github.com/bheins/ComfyUI-glb-to-stl", "files": ["https://github.com/bheins/ComfyUI-glb-to-stl"], "install_type": "git-clone", "description": "GLB conversion to STL node for ComfyUI\nNOTE: The files in the repo are not organized."}, {"author": "cyberhirsch", "title": "seb_nodes [WIP]", "reference": "https://github.com/cyberhirsch/seb_nodes", "files": ["https://github.com/cyberhirsch/seb_nodes"], "install_type": "git-clone", "description": "A custom node for ComfyUI providing more control over image saving, including dynamic subfolder creation and a convenient button to open the last used output folder directly from the UI.\nNOTE: The files in the repo are not organized."}, {"author": "Anonymzx", "title": "ComfyUI-Indonesia-TTS [WIP]", "reference": "https://github.com/Anonymzx/ComfyUI-Indonesia-TTS", "files": ["https://github.com/Anonymzx/ComfyUI-Indonesia-TTS"], "description": "Repositori ini menyediakan integrasi model Text-to-Speech (TTS) Bahasa Indonesia dari Facebook (MMS-TTS-IND) ke dalam ComfyUI, sehingga Anda dapat langsung menyintesis suara berbahasa Indonesia dengan kontrol penuh via antarmuka node-based.\nNOTE: The files in the repo are not organized.", "install_type": "git-clone"}, {"author": "3dmindscapper", "title": "ComfyUI-<PERSON> [WIP]", "reference": "https://github.com/3dmindscapper/ComfyUI-Sam-<PERSON>sh", "files": ["https://github.com/3dmindscapper/ComfyUI-Sam-<PERSON>sh"], "install_type": "git-clone", "description": "comfyui implementation of SaMesh segmentation of 3d meshes\nNOTE: The files in the repo are not organized."}, {"author": "shinich39", "title": "comfyui-run-js [UNSAFE]", "reference": "https://github.com/shinich39/comfyui-run-js", "files": ["https://github.com/shinich39/comfyui-run-js"], "description": "Manipulate workflow via javascript on node.", "install_type": "git-clone"}, {"author": "fangg2000", "title": "ComfyUI-SenseVoice [WIP]", "reference": "https://github.com/fangg2000/ComfyUI-SenseVoice", "files": ["https://github.com/fangg2000/ComfyUI-SenseVoice"], "description": "A comfyui node plug-in developed based on the SenseVoise project, and a simple recording node.\nNOTE: The files in the repo are not organized.", "install_type": "git-clone"}, {"author": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "title": "ComfyUI_FaceMesh_Eyewear_Mask", "reference": "https://github.com/risunobushi/ComfyUI_FaceMesh_Eyewear_Mask", "files": ["https://github.com/risunobushi/ComfyUI_FaceMesh_Eyewear_Mask"], "description": "NODES: Face Mesh Eyewear Mask, OpenPose Eyewear Mask (DWPose), Mask From Facial Keypoints", "install_type": "git-clone"}, {"author": "machinesarenotpeople", "title": "comfyui-energycost", "reference": "https://github.com/machinesarenotpeople/comfyui-energycost", "files": ["https://github.com/machinesarenotpeople/comfyui-energycost"], "description": "NODES: Energy Cost Timer, Energy Cost Calculator", "install_type": "git-clone"}, {"author": "xqqe", "title": "honey_nodes [WIP]", "reference": "https://github.com/xqqe/honey_nodes", "files": ["https://github.com/xqqe/honey_nodes"], "description": "honey nodes for comfyui\nNOTE: The files in the repo are not organized.", "install_type": "git-clone"}, {"author": "<PERSON><PERSON>", "title": "Kuniklo Collection", "reference": "https://github.com/Raidez/comfyui-kuniklo-collection", "files": ["https://github.com/Raidez/comfyui-kuniklo-collection"], "description": "NODES: Properties, Apply SVG to Image", "install_type": "git-clone"}, {"author": "AhBumm", "title": "ComfyUI_MangaLineExtraction", "reference": "https://github.com/AhBumm/ComfyUI_MangaLineExtraction-hf", "files": ["https://github.com/AhBumm/ComfyUI_MangaLineExtraction-hf"], "description": "p1atdev/MangaLineExtraction-hf as a node in comfyui", "install_type": "git-clone"}, {"author": "Kur0butiMegane", "title": "Comfyui-StringUtils", "reference": "https://github.com/Kur0butiMegane/Comfyui-StringUtils2", "files": ["https://github.com/Kur0butiMegane/Comfyui-StringUtils2"], "install_type": "git-clone", "description": "NODES: Normalizer, Splitter, Selector, XML Parser, XML Parser, Make Property, Add XML Tag, Is String Empty, Cond Passthrough, CLIP Passthrough, ClipRegion Passthrough, Scheduler Selector (Impact), Scheduler Selector (Inspire), Save Text, XML to Cutoff"}, {"author": "ronaldstg", "title": "comfyui-plus-integrations [WIP]", "reference": "https://github.com/ronalds-eu/comfyui-plus-integrations", "files": ["https://github.com/ronalds-eu/comfyui-plus-integrations"], "install_type": "git-clone", "description": "NODES: Image Pass Through, Upload Image to S3\nNOTE: The files in the repo are not organized."}, {"author": "kevin314", "title": "ComfyUI-FastVideo", "reference": "https://github.com/kevin314/ComfyUI-FastVideo", "files": ["https://github.com/kevin314/ComfyUI-FastVideo"], "description": "NODES: Video Generator, Inference Args, VAE Config, Text Encoder Config, DIT Config", "install_type": "git-clone"}, {"author": "benda1989", "title": "Comfyui lama remover [WIP]", "reference": "https://github.com/benda1989/WaterMarkRemover_ComfyUI", "files": ["https://github.com/benda1989/WaterMarkRemover_ComfyUI"], "install_type": "git-clone", "description": "A very simple ComfyUI node to remove item like image/video with mask watermark\nNOTE: The files in the repo are not organized."}, {"author": "3dmindscapper", "title": "ComfyUI-PartField [WIP]", "reference": "https://github.com/3dmindscapper/ComfyUI-PartField", "files": ["https://github.com/3dmindscapper/ComfyUI-PartField"], "install_type": "git-clone", "description": "ComfyUI implementation of the partfield nvidea segmentation models\nNOTE: The files in the repo are not organized."}, {"author": "shinich39", "title": "comfyui-textarea-is-shit", "reference": "https://github.com/shinich39/comfyui-textarea-is-shit", "files": ["https://github.com/shinich39/comfyui-textarea-is-shit"], "description": "HTML gives me a textarea like piece of shit.", "install_type": "git-clone"}, {"author": "shinich39", "title": "comfyui-nothing-happened", "reference": "httphttps://github.com/shinich39/comfyui-nothing-happened", "files": ["https://github.com/shinich39/comfyui-nothing-happened"], "description": "Save image and keep metadata.", "install_type": "git-clone"}, {"author": "CY-CHENYUE", "title": "ComfyUI-FramePack-HY", "reference": "https://github.com/CY-CHENYUE/ComfyUI-FramePack-HY", "files": ["https://github.com/CY-CHENYUE/ComfyUI-FramePack-HY"], "description": "FramePack in ComfyUI", "install_type": "git-clone"}, {"author": "silveroxides", "title": "ComfyUI_ReduxEmbedToolkit", "reference": "https://github.com/silveroxides/ComfyUI_ReduxEmbedToolkit", "files": ["https://github.com/silveroxides/ComfyUI_ReduxEmbedToolkit"], "install_type": "git-clone", "description": "Custom nodes for managing, saving and loading of Redux/Style based embeddings."}, {"author": "StaffsGull", "title": "comfyui_scene_builder [WIP]", "reference": "https://github.com/StaffsGull/comfyui_scene_builder", "files": ["https://github.com/StaffsGull/comfyui_scene_builder"], "install_type": "git-clone", "description": "NODES: CharacterBuilderNode, ClothingItemNode, ClothingMergerNode, EnvironmentBuilderNode, MergeCharactersNode, PhotoStyleBuilderNode, SceneCombinerNode\nNOTE: The files in the repo are not organized."}, {"author": "<PERSON><PERSON><PERSON>ce", "title": "ComfyUI_gaga_utils", "reference": "https://github.com/gagaprince/ComfyUI_gaga_utils", "files": ["https://github.com/gagaprince/ComfyUI_gaga_utils"], "install_type": "git-clone", "description": "NODES: GagaGetFileList, GagaGetStringListSize, GagaSplitStringToList, GagaTest, GagaBatchStringReplace"}, {"author": "ftechmax", "title": "ComfyUI-NovaKit-Pack", "reference": "https://github.com/ftechmax/ComfyUI-NovaKit-Pack", "files": ["https://github.com/ftechmax/ComfyUI-NovaKit-Pack"], "install_type": "git-clone", "description": "NODES: <PERSON>"}, {"author": "BobRandomNumber", "title": "ComfyUI DiaTest TTS Node [WIP]", "reference": "https://github.com/BobRandomNumber/ComfyUI-DiaTTS", "files": ["https://github.com/BobRandomNumber/ComfyUI-DiaTTS"], "install_type": "git-clone", "description": "Partial ComfyUI Dia implementation"}, {"author": "jtydhr88", "title": "ComfyUI-1hewNodes [WIP]", "reference": "https://github.com/1hew/ComfyUI-1hewNodes", "files": ["https://github.com/1hew/ComfyUI-1hewNodes"], "install_type": "git-clone", "description": "NODES: Solid, <PERSON><PERSON>, Image Concatenate, Image Crop With BBox, Image Paste\nNOTE: The files in the repo are not organized."}, {"author": "jtydhr88", "title": "ComfyUI Frontend Vue Basic [WIP]", "reference": "https://github.com/jtydhr88/ComfyUI_frontend_vue_basic", "files": ["https://github.com/jtydhr88/ComfyUI_frontend_vue_basic"], "install_type": "git-clone", "description": "A demonstration custom node that showcases how to integrate Vue as a frontend framework within ComfyUI, complete with PrimeVue components and vue-i18n support."}, {"author": "silent-rain", "title": "ComfyUI-SilentRain", "reference": "https://github.com/silent-rain/ComfyUI-SilentRain", "files": ["https://github.com/silent-rain/ComfyUI-SilentRain"], "install_type": "git-clone", "description": "An attempt to implement ComfyUI custom nodes using the Rust programming language."}, {"author": "<PERSON><PERSON><PERSON>", "title": "ComfyUI-Linsoo-Custom-Nodes", "reference": "https://github.com/Linsoo/ComfyUI-Linsoo-Custom-Nodes", "files": ["https://github.com/Linsoo/ComfyUI-Linsoo-Custom-Nodes"], "install_type": "git-clone", "description": "NODES: Linsoo Save Image, Linsoo Load Image (In development.. not working), Linsoo Empty Latent Image, Linsoo Multi Inputs, Linsoo Multi Outputs"}, {"author": "facok", "title": "ComfyUI-FokToolset", "reference": "https://github.com/facok/ComfyUI-FokToolset", "files": ["https://github.com/facok/ComfyUI-FokToolset"], "install_type": "git-clone", "description": "NODES: Fok Preprocess Ref Image (Phantom)"}, {"author": "<PERSON><PERSON><PERSON><PERSON>", "title": "Comfy-Metadata-System [WIP]", "reference": "https://github.com/EricRollei/Comfy-Metadata-System", "files": ["https://github.com/EricRollei/Comfy-Metadata-System"], "install_type": "git-clone", "description": "Series of custom Comfyui Nodes that collects and saves metadata to embedded (png, jpg) as well as optional xmp and txt sidecars and database"}, {"author": "<PERSON><PERSON><PERSON><PERSON>", "title": "comfyui-SetWallpaper", "reference": "https://github.com/turskeli/comfyui-SetWallpaper", "files": ["https://github.com/turskeli/comfyui-SetWallpaper"], "install_type": "git-clone", "description": "Simple wallpaper node for ComfyUI. Curently only supports Windows OS"}, {"author": "Sophylax", "title": "ComfyUI-ReferenceMerge", "reference": "https://github.com/Sophylax/ComfyUI-ReferenceMerge", "files": ["https://github.com/Sophylax/ComfyUI-ReferenceMerge"], "install_type": "git-clone", "description": "NODES: Combine Images and Mask, Restitch Combined Crop"}, {"author": "bandido37", "title": "Kaggle ComfyUI Local Save Node [WIP]", "reference": "https://github.com/bandido37/comfyui-kaggle-local-save", "files": ["https://github.com/bandido37/comfyui-kaggle-local-save"], "install_type": "git-clone", "description": "This custom node for ComfyUI allows you to save generated images directly to your local PC instead of <PERSON><PERSON>'s cloud output folder.\nNOTE: The files in the repo are not organized."}, {"author": "springjk", "title": "Psutil Container Memory Patch", "reference": "https://github.com/springjk/ComfyUI-Psutil-Container-Memory-Patch", "files": ["https://github.com/springjk/ComfyUI-Psutil-Container-Memory-Patch"], "install_type": "git-clone", "description": "Make ComfyUI get correct memory information in the container (psutil monkey path)"}, {"author": "<PERSON><PERSON><PERSON>", "title": "ComfyUI-DMM [WIP]", "reference": "https://github.com/songtianhui/ComfyUI-DMM", "files": ["https://github.com/songtianhui/ComfyUI-DMM"], "install_type": "git-clone", "description": "NODES: <PERSON><PERSON><PERSON><PERSON><PERSON>, DMMApply"}, {"author": "leon-etienne", "title": "ComfyUI_Scoring-Nodes", "reference": "https://github.com/leon-etienne/ComfyUI_Scoring-Nodes", "files": ["https://github.com/leon-etienne/ComfyUI_Scoring-Nodes"], "install_type": "git-clone", "description": "NODES: Text Similarity (CLIP), Image Similarity (CLIP), Multi Text→Image Similarity, Multi Image→Text Similarity, Aesthetic Score, Multi Aesthetic Comparison"}, {"author": "tanmoy-it", "title": "comfyuiCustomNode", "reference": "https://github.com/tanmoy-it/comfyuiCustomNode", "files": ["https://github.com/tanmoy-it/comfyuiCustomNode"], "install_type": "git-clone", "description": "NODES: Download Image (Direct/No Save)"}, {"author": "<PERSON><PERSON>-genies", "title": "comfyui-genies-nodes", "reference": "https://github.com/Jingwen-genies/comfyui-genies-nodes", "files": ["https://github.com/Jingwen-genies/comfyui-genies-nodes"], "install_type": "git-clone", "description": "NODES: Genies Pose Estimation, Genies Scale Face by Keypoints, Get V Channel from HSV, Select RGB by Mask"}, {"author": "Tawbaware", "title": "ComfyUI-Tawbaware [WIP]", "reference": "https://github.com/Tawbaware/ComfyUI-Tawbaware", "files": ["https://github.com/Tawbaware/ComfyUI-Tawbaware"], "install_type": "git-clone", "description": "A collection of custom nodes for ComfyUI\nNOTE: The files in the repo are not organized."}, {"author": "l<PERSON><PERSON><PERSON><PERSON><PERSON>", "title": "LF Nodes [UNSAFE]", "reference": "https://github.com/lucafoscili/lf-nodes", "files": ["https://github.com/lucafoscili/lf-nodes"], "install_type": "git-clone", "description": "Custom nodes with a touch of extra UX, including: history for primitives, JSON manipulation, logic switches with visual feedback, LLM chat... and more!\n[w/This node pack contains a node with a vulnerability that allows arbitrary code execution.]"}, {"author": "jerryname2022", "title": "ComfyUI-Real-ESRGAN [WIP]", "reference": "https://github.com/jerryname2022/ComfyUI-Real-ESRGAN", "files": ["https://github.com/jerryname2022/ComfyUI-Real-ESRGAN"], "install_type": "git-clone", "description": "NODES: Real-ESRGAN Model Loader, GFPGAN Model Loader, Real-ESRGAN Image Generator, GFPGAN Image Generator"}, {"author": "mm-akhtar", "title": "comfyui-mask-selector-node", "reference": "https://github.com/mm-akhtar/comfyui-mask-selector-node", "files": ["https://github.com/mm-akhtar/comfyui-mask-selector-node"], "install_type": "git-clone", "description": "NODES: Mask Selector"}, {"author": "ryanontheinside", "title": "ComfyUI-Livepeer [WIP]", "reference": "https://github.com/ryanontheinside/ComfyUI-Livepeer", "files": ["https://github.com/ryanontheinside/ComfyUI-Livepeer"], "install_type": "git-clone", "description": "A ComfyUI extension that provides integration with [a/Livepeer](https://livepeer.org/)'s AI services allowing for both sync and async generation."}, {"author": "<PERSON><PERSON>a", "title": "ComfyUI-Remote-Save-Image [UNSAFE]", "reference": "https://github.com/newraina/ComfyUI-Remote-Save-Image", "files": ["https://github.com/newraina/ComfyUI-Remote-Save-Image"], "install_type": "git-clone", "description": "A custom node for ComfyUI that allows uploading generated images to any HTTP endpoint.[w/This node allows any users to send any locally stored image to a specified URL.]"}, {"author": "SXQBW", "title": "ComfyUI-Qwen-VLM [WIP]", "reference": "https://github.com/SXQBW/ComfyUI-Qwen3", "files": ["https://github.com/SXQBW/ComfyUI-Qwen3"], "install_type": "git-clone", "description": "NODES: QwenVLM"}, {"author": "kijai", "title": "ComfyUI-FramePackWrapper [WIP]", "reference": "https://github.com/kijai/ComfyUI-FramePackWrapper", "files": ["https://github.com/kijai/ComfyUI-FramePackWrapper"], "install_type": "git-clone", "description": "ComfyUI Wrapper for FramePack by lllyasviel"}, {"author": "WaiyanLing", "title": "ComfyUI-Tracking [WIP]", "reference": "https://github.com/WaiyanLing/ComfyUI-Tracking", "files": ["https://github.com/WaiyanLing/ComfyUI-Tracking"], "install_type": "git-clone", "description": "ComfyUI-Tracking This node pack helps to conveniently collect invocation data from workflows for further study.\nNOTE: The files in the repo are not organized."}, {"author": "vladp0727", "title": "ComfyUI Simple Image Tools [WIP]", "reference": "https://github.com/vladp0727/Comfyui-with-Furniture", "files": ["https://github.com/vladp0727/Comfyui-with-Furniture"], "install_type": "git-clone", "description": "NODES: Get Mask From Alpha, Get Quadrilateral Outfit\nNOTE: The files in the repo are not organized."}, {"author": "<PERSON><PERSON><PERSON><PERSON>", "title": "Simlym/comfyui-prompt-helper [WIP]", "reference": "https://github.com/Simlym/comfyui-prompt-helper", "files": ["https://github.com/Simlym/comfyui-prompt-helper"], "install_type": "git-clone", "description": "A ComfyUI custom node for processing Chinese prompts and generating English prompts with LLM\nNOTE: The files in the repo are not organized."}, {"author": "ryanontheinside", "title": "ComfyUI MineWorld Nodes [WIP]", "reference": "https://github.com/ryanontheinside/ComfyUI-MineWorld", "files": ["https://github.com/ryanontheinside/ComfyUI-MineWorld"], "install_type": "git-clone", "description": "This extension integrates Microsoft's MineWorld - an interactive world model for Minecraft - into ComfyUI.\nMineWorld allows you to generate interactive Minecraft gameplay based on actions you provide, creating realistic Minecraft gameplay videos."}, {"author": "SanDiegoDude", "title": "HiDreamSampler for ComfyUI [WIP]", "reference": "https://github.com/SanDiegoDude/ComfyUI-HiDream-Sampler", "files": ["https://github.com/SanDiegoDude/ComfyUI-HiDream-Sampler"], "install_type": "git-clone", "description": "A custom ComfyUI node for generating images using the HiDream AI model.\nNOTE: The files in the repo are not organized."}, {"author": "ZenAI-Vietnam", "title": "ComfyUI_InfiniteYou [NAME CONFLICT]", "reference": "https://github.com/ZenAI-Vietnam/ComfyUI_InfiniteYou", "files": ["https://github.com/ZenAI-Vietnam/ComfyUI_InfiniteYou"], "install_type": "git-clone", "description": "An implementation of InfiniteYou for ComfyUI. Native support for [a/InfiniteYou](https://github.com/bytedance/InfiniteYou) in ComfyUI, designed by the ZenAI team."}, {"author": "filipemeneses", "title": "ComfyUI_html [UNSAFE]", "reference": "https://github.com/filipemeneses/ComfyUI_html", "files": ["https://github.com/filipemeneses/ComfyUI_html"], "install_type": "git-clone", "description": "Nodes to manipulate HTML.[w/This extension poses a risk of XSS vulnerability.]"}, {"author": "LLMCoder2023", "title": "ComfyUI-LLMCoderNodes", "reference": "https://github.com/LLMCoder2023/ComfyUI-LLMCoder2023Nodes", "files": ["https://github.com/LLMCoder2023/ComfyUI-LLMCoder2023Nodes"], "install_type": "git-clone", "description": "NODES: String Template Interpolation, Variable Definition, Weighted Attributes Formatter"}, {"author": "FaberVS", "title": "MultiModel", "reference": "https://github.com/FaberVS/MultiModel", "files": ["https://github.com/FaberVS/MultiModel"], "install_type": "git-clone", "description": "A collection of ComfyUI nodes enabling seamless integration of multiple models into workflows without requiring constant configuration."}, {"author": "m-ai-studio", "title": "mai-prompt-progress", "reference": "https://github.com/m-ai-studio/mai-prompt-progress", "files": ["https://github.com/m-ai-studio/mai-prompt-progress"], "install_type": "git-clone", "description": "ComfyUI extensions for sending prompt progress to webhook"}, {"author": "<PERSON><PERSON><PERSON>", "title": "ComfyUI_MoreComfy", "reference": "https://github.com/ashllay/ComfyUI_MoreComfy", "files": ["https://github.com/ashllay/ComfyUI_MoreComfy"], "install_type": "git-clone", "description": "NODES: MC Switch Seed/Image/Latent/Model/String, MC <PERSON><PERSON> Seed, MC <PERSON> <PERSON>ile Size, MC Get Image Size, MC <PERSON> Concat"}, {"author": "gordon1chuge2623", "title": "ComfyUI_seal_migration [WIP]", "reference": "https://github.com/chuge26/ComfyUI_seal_migration", "files": ["https://github.com/chuge26/ComfyUI_seal_migration"], "install_type": "git-clone", "description": "This project implements stamp migration in PDF files based on ComfyUI, allowing stamps from specified pages of a source PDF to be transferred to specified pages of a target PDF.\nNOTE: The files in the repo are not organized."}, {"author": "gordon123", "title": "ComfyUI_srt2speech [WIP]", "reference": "https://github.com/gordon123/ComfyUI_srt2speech", "files": ["https://github.com/gordon123/ComfyUI_srt2speech"], "install_type": "git-clone", "description": "ComfyUI_srt2speech"}, {"author": "hnmr293", "title": "ComfyUI-SamOne - one-step sampling", "reference": "https://github.com/hnmr293/ComfyUI-SamOne", "files": ["https://github.com/hnmr293/ComfyUI-SamOne"], "install_type": "git-clone", "description": "This is a node that advances sampling by just one step in ComfyUI."}, {"author": "<PERSON><PERSON><PERSON><PERSON>", "title": "comfyui-videodepthanything", "reference": "https://github.com/rphmeier/comfyui-videodepthanything", "files": ["https://github.com/rphmeier/comfyui-videodepthanything"], "install_type": "git-clone", "description": "VideoDepthAnything nodes for ComfyUI"}, {"author": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "title": "ComfyGCS [WIP]", "reference": "https://github.com/benmizrahi/ComfyGCS", "files": ["https://github.com/benmizrahi/ComfyGCS"], "install_type": "git-clone", "description": "ComfyGCS is a robust read/write plugin for Google Cloud Storage, designed to simplify interaction with GCS buckets in your projects.\nNOTE: The files in the repo are not organized."}, {"author": "dogcomplex", "title": "ComfyUI-LOKI [WIP]", "reference": "https://github.com/dogcomplex/ComfyUI-LOKI", "files": ["https://github.com/dogcomplex/ComfyUI-LOKI"], "install_type": "git-clone", "description": "NODES: Glamour\nNOTE: This node pack installs pip dependencies outside the control of ComfyUI-Manager."}, {"author": "hunzmusic", "title": "Comfyui-CraftsMan3DWrapper [WIP]", "reference": "https://github.com/hunzmusic/Comfyui-CraftsMan3DWrapper", "files": ["https://github.com/hunzmusic/Comfyui-CraftsMan3DWrapper"], "install_type": "git-clone", "description": "A wrapper for CraftsMan\nNOTE: The files in the repo are not organized."}, {"author": "Slix-M-<PERSON><PERSON><PERSON>", "title": "comfyui-enhanced [WIP]", "reference": "https://github.com/Slix-<PERSON>-Les<PERSON>gg/comfyui-enhanced", "files": ["https://github.com/Slix-<PERSON>-Les<PERSON>gg/comfyui-enhanced"], "install_type": "git-clone", "description": "A collection of enhanced nodes for ComfyUI that provide powerful additional functionality to your workflows.\nNOTE: The files in the repo are not organized."}, {"author": "tzsoulcap", "title": "ComfyUI-SaveImg-W-MetaData", "reference": "https://github.com/tzsoulcap/ComfyUI-SaveImg-W-MetaData", "files": ["https://github.com/tzsoulcap/ComfyUI-SaveImg-W-MetaData"], "install_type": "git-clone", "description": "NODES: CAP Checkpoint Selector, CAP Save Image w/Metadata, CAP Load Image with Metadata, CAP Tag Image, CAP Sampler Selector, CAP Scheduler Selector, CAP Seed Generator, CAP String Literal, CAP Width/Height Literal, CAP Cfg Literal, CAP Int Literal"}, {"author": "hylarucoder", "title": "comfyui-copilot", "reference": "https://github.com/hylarucoder/comfyui-copilot", "files": ["https://github.com/hylarucoder/comfyui-copilot"], "install_type": "git-clone", "description": "NODES: Eagle Image Node for PNGInfo, SDXL Resolution Presets (ws), SDXL Prompt Styler, SDXL Prompt Styler Advanced"}, {"author": "SS-snap", "title": "Com<PERSON>ui_SSsnap_pose-Remapping", "reference": "https://github.com/SS-snap/Comfyui_SSsnap_pose-Remapping", "files": ["https://github.com/SS-snap/Comfyui_SSsnap_pose-Remapping"], "install_type": "git-clone", "description": "NODES: SSsnap Apply Pose Diff ✂️, SSsnap Pose Diff Calculator 🛠️"}, {"author": "<PERSON>", "title": "TUZZI-ByPass [WIP]", "reference": "https://github.com/AlejandroTuzzi/TUZZI-ByPass", "files": ["https://github.com/AlejandroTuzzi/TUZZI-ByPass"], "install_type": "git-clone", "description": "Custom nodes for automated AI pipelines\nNOTE: The files in the repo are not organized."}, {"author": "oxysoft", "title": "Comfy-Compel", "reference": "https://github.com/oxysoft/Comfy-Compel", "files": ["https://github.com/oxysoft/Comfy-Compel"], "install_type": "git-clone", "description": "NODES: CLIP Embed (Compel)"}, {"author": "QingLuanWithoutHeart", "title": "ComfyUI File/Image Utils Nodes [UNSAFE]", "reference": "https://github.com/QingLuanWithoutHeart/comfyui-file-image-utils", "files": ["https://github.com/QingLuanWithoutHeart/comfyui-file-image-utils"], "install_type": "git-clone", "description": "This custom node set provides useful utilities for file operations and image loading in ComfyUI."}, {"author": "pmarmotte2", "title": "VibeVoiceSelector [WIP]", "reference": "https://github.com/pmarmotte2/Comfyui-VibeVoiceSelector", "files": ["https://github.com/pmarmotte2/Comfyui-VibeVoiceSelector"], "install_type": "git-clone", "description": "NODES: Vibe Voice Selector"}, {"author": "<PERSON><PERSON><PERSON>", "title": "TWanVideoSigmaSampler: EXPERIMENTAL [WIP]", "reference": "https://github.com/Temult/TWanSigmaSampler", "files": ["https://github.com/Temult/TWanSigmaSampler"], "install_type": "git-clone", "description": "A ComfyUI custom node that modifies the WanVideoSampler to accept an external sigma schedule. Allows for customized and non-standard noise schedules in Wan 2.1 video generation workflow.\nNOTE: The files in the repo are not organized."}, {"author": "wordbrew", "title": "WAN Control Nodes for ComfyUI [WIP]", "reference": "https://github.com/wordbrew/comfyui-wan-control-nodes", "files": ["https://github.com/wordbrew/comfyui-wan-control-nodes"], "install_type": "git-clone", "description": "This pack provides enhanced control nodes for working with Wan video models in ComfyUI. It is under active development and may change regularly, or may not. Depends entirely on my free time and waning interest. Please don't come to rely on it for anything, but you are welcome to improve on it.\nNOTE: The files in the repo are not organized."}, {"author": "techtruth", "title": "ComfyUI-Dreambooth", "reference": "https://github.com/techtruth/ComfyUI-Dreambooth", "files": ["https://github.com/techtruth/ComfyUI-Dreambooth"], "install_type": "git-clone", "description": "NODES: Dreambooth Trainer"}, {"author": "438443467", "title": "ComfyUI-SanMian-Nodes", "reference": "https://github.com/438443467/ComfyUI-SanMian-Nodes", "files": ["https://github.com/438443467/ComfyUI-SanMian-Nodes"], "install_type": "git-clone", "description": "NODES: Add Text To Image, Adjust Hex Brightness, Adjust Transparency By Mask, Align Images with Mask, Align Restore Json, Bin<PERSON>ze Mask, Blend ICLight, ..."}, {"author": "alexgenovese", "title": "ComfyUI-Reica", "reference": "https://github.com/alexgenovese/ComfyUI-Reica", "files": ["https://github.com/alexgenovese/ComfyUI-Reica"], "install_type": "git-clone", "description": "NODES: Reica Text Image Display, Flux Image Generator, Reica GCP: Read Image, Reica GCP: Write Image & Get URL, Reica API: Send HTTP Notification"}, {"author": "yanlang0123", "title": "ComfyUI_Lam", "reference": "https://github.com/yanlang0123/ComfyUI_Lam", "files": ["https://github.com/yanlang0123/ComfyUI_Lam"], "install_type": "git-clone", "description": "This extension has some useful nodes, loops, wechat public number +AI chat drawing, distributed cluster."}, {"author": "Stable-X", "title": "ComfyUI-Hi3DGen", "reference": "https://github.com/Stable-X/ComfyUI-Hi3DGen", "files": ["https://github.com/Stable-X/ComfyUI-Hi3DGen"], "install_type": "git-clone", "description": "This extension integrates [a/Hi3DGen](https://github.com/Stable-X/Hi3DGen) into ComfyUI, allowing user to generate high-fidelity 3D geometry generation from Images.[w/If the *sageattention* package is installed, this node pack causes problems.]"}, {"author": "stiffy-committee", "title": "comfyui-stiffy-nodes", "reference": "https://github.com/V-woodpecker-V/comfyui-stiffy-nodes", "files": ["https://github.com/V-woodpecker-V/comfyui-stiffy-nodes"], "install_type": "git-clone", "description": "NODES: <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>Debug<PERSON>, ..."}, {"author": "ch<PERSON>ang<PERSON><PERSON>", "title": "Comfyui-supabase", "reference": "https://github.com/chetusangolgi/Comfyui-supabase", "files": ["https://github.com/chetusangolgi/Comfyui-supabase"], "install_type": "git-clone", "description": "NODES: Watch Supabase Bucket, Upload Image to Supabase"}, {"author": "rickyars", "title": "sd-cn-animation", "reference": "https://github.com/rickyars/sd-cn-animation", "files": ["https://github.com/rickyars/sd-cn-animation"], "install_type": "git-clone", "description": "SD-CN animation for Comfyui"}, {"author": "daracazamea", "title": "DCNodess [WIP]", "reference": "https://github.com/daracazamea/comfyUI-DCNodes", "files": ["https://github.com/daracazamea/comfyUI-DCNodes"], "install_type": "git-clone", "description": "NODES: Start Timer (Pass-Through), Get Generation Time, Manual Trigger, Flux: Resolution Picker, SDXL: Resolution Picker\nNOTE: The files in the repo are not organized."}, {"author": "hunzmusic", "title": "ComfyUI-Hunyuan3DTools [WIP]", "reference": "https://github.com/hunzmusic/ComfyUI-Hunyuan3DTools", "files": ["https://github.com/hunzmusic/ComfyUI-Hunyuan3DTools"], "install_type": "git-clone", "description": "NODES: Hy3DTools Render Specific View, Hy3DTools Back-Project Inpaint\nNOTE: The files in the repo are not organized."}, {"author": "grokuku", "title": "Holaf Custom Nodes for ComfyUI", "reference": "https://github.com/grokuku/ComfyUI-Holaf", "files": ["https://github.com/grokuku/ComfyUI-Holaf"], "install_type": "git-clone", "description": "NODES: Neurogrid Overload, Tile Calculator, Slice Calculator, Save Image, Tiled KSampler, KSampler, Image Comparer, Upscale, Overlay, Resolution Preset, Benchmark Runner, Benchmark Plotter, Benchmark Loader"}, {"author": "Burgstall-labs", "title": "ComfyUI-BS_FalAi-API-Video [WIP]", "reference": "https://github.com/Burgstall-labs/ComfyUI-BS_FalAi-API-Video", "files": ["https://github.com/Burgstall-labs/ComfyUI-BS_FalAi-API-Video"], "install_type": "git-clone", "description": "Experimental ComfyUI Custom Node for generating videos using various FAL AI API endpoints.\nNOTE: The files in the repo are not organized."}, {"author": "uau<PERSON><PERSON><PERSON>", "title": "Mycraft [WIP]", "reference": "https://github.com/sorption-dev/mycraft-comfyui", "files": ["https://github.com/sorption-dev/mycraft-comfyui"], "install_type": "git-clone", "description": "Mycraft provides a limitless storyboard experience for image generation, powered by the ComfyUI API.\nEach container functions as an independent ComfyUI workflow, Supports workflows (text-to-text) and fine-tuning (image-to-image), Supports workflow customization."}, {"author": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "title": "ComfyUI-StoryboardDistributor", "reference": "https://github.com/zhaorishuai/ComfyUI-StoryboardDistributor", "files": ["https://github.com/zhaorishuai/ComfyUI-StoryboardDistributor"], "install_type": "git-clone", "description": "A ComfyUI plugin that automatically assigns storyboard content to 9 storyboard nodes."}, {"author": "alexgenovese", "title": "ComfyUI-Diffusion-4k [WIP]", "reference": "https://github.com/alexgenovese/ComfyUI-Diffusion-4k", "files": ["https://github.com/alexgenovese/ComfyUI-Diffusion-4k"], "install_type": "git-clone", "description": "A ComfyUI custom node implementation of the Diffusion 4K research paper.\nNOTE: The files in the repo are not organized."}, {"author": "KERRY-YUAN", "title": "Python_Executor [UNSAFE]", "id": "PythonExecutor", "reference": "https://github.com/KERRY-YUAN/ComfyUI_Python_Executor", "files": ["https://github.com/KERRY-YUAN/ComfyUI_Python_Executor"], "install_type": "git-clone", "description": "Nodes: Provides nodes to execute arbitrary Python code snippets and Resize images directly within ComfyUI workflows. [w/This node allows you to execute arbitrary code via the workflow.]"}, {"author": "<PERSON><PERSON><PERSON>", "title": "ComfyUI_MoreComfy", "reference": "https://github.com/ashllay/ComfyUI_MoreComfy", "files": ["https://github.com/ashllay/ComfyUI_MoreComfy"], "install_type": "git-clone", "description": "NODES: <PERSON> Seed, <PERSON> <PERSON><PERSON>, MC <PERSON>, MC <PERSON> Concat"}, {"author": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "title": "ComfyUI-KLUT-DeepSeek-API [WIP]", "reference": "https://github.com/ayaoayaoayaoaya/ComfyUI-KLUT-DeepSeek-API", "files": ["https://github.com/ayaoayaoayaoaya/ComfyUI-KLUT-DeepSeek-API"], "install_type": "git-clone", "description": "A collection of utility / quality-of-life nodes for ComfyUI. Probably only useful to me.\nNOTE: The files in the repo are not organized."}, {"author": "olyyarm", "title": "ComfyUI-VLMStudio", "reference": "https://github.com/KurtHokke/ComfyUI_KurtHokke_Nodes", "files": ["https://github.com/KurtHokke/ComfyUI_KurtHokke_Nodes"], "install_type": "git-clone", "description": "NODES: Node_BOOL/INT/Float, BooleanToPipe, BooleanFromPipe, ExpMath, ExpMathDual/Quad, ...."}, {"author": "olyyarm", "title": "ComfyUI-VLMStudio", "reference": "https://github.com/olyyarm/ComfyUI-VLMStudio", "files": ["https://raw.githubusercontent.com/olyyarm/ComfyUI-VLMStudio/refs/heads/master/vlm_visionary_node_v3_.py"], "install_type": "copy", "description": "NODES: GemmaMultimodalAnalyzer"}, {"author": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "title": "nova_utils", "reference": "https://github.com/apetitbois/nova_utils", "files": ["https://github.com/apetitbois/nova_utils"], "install_type": "git-clone", "description": "Nova utils for ComfyUI"}, {"author": "sugarkwork", "title": "comfyui_my_img_util", "reference": "https://github.com/sugarkwork/comfyui_my_img_util", "files": ["https://github.com/sugarkwork/comfyui_my_img_util"], "install_type": "git-clone", "description": "NODES: Simple Image Rotate"}, {"author": "DonutsDelivery", "title": "ComfyUI-DonutDetailer", "reference": "https://github.com/DonutsDelivery/ComfyUI-DonutNodes", "files": ["https://github.com/DonutsDelivery/ComfyUI-DonutNodes"], "install_type": "git-clone", "description": "This is an experimental node I made to mimick the 'adjust' in A1111 Supermerger [a/https://github.com/hako-mikan/sd-webui-supermerger?tab=readme-ov-file#adjust](https://github.com/hako-mikan/sd-webui-supermerger?tab=readme-ov-file#adjust)."}, {"author": "ZenAI-Vietnam", "title": "ComfyUI-gemini-IG", "reference": "https://github.com/ZenAI-Vietnam/ComfyUI-gemini-IG", "files": ["https://github.com/ZenAI-Vietnam/ComfyUI-gemini-IG"], "install_type": "git-clone", "description": "NODES: Gemini Image Generation, Gemini Text Generation"}, {"author": "hunzmusic", "title": "comfyui-h<PERSON>nodes", "reference": "https://github.com/hunzmusic/comfyui-hnznodes", "files": ["https://github.com/hunzmusic/comfyui-hnznodes"], "install_type": "git-clone", "description": "NODES: Combine Channels Grayscale, Reorder <PERSON> Batch, Male Character Prompt"}, {"author": "cidiro", "title": "cid-node-pack", "reference": "https://github.com/cidiro/cid-node-pack", "files": ["https://github.com/cidiro/cid-node-pack"], "install_type": "git-clone", "description": "A lightweight node pack for ComfyUI that adds a few handy nodes that I use in my workflows"}, {"author": "CeeVeeR", "title": "ComfyUi-Text-Tiler", "reference": "https://github.com/CeeVeeR/ComfyUi-Text-Tiler", "files": ["https://github.com/CeeVeeR/ComfyUi-Text-Tiler"], "install_type": "git-clone", "description": "NODES: Text Tiler"}, {"author": "Dreamshot-io", "title": "ComfyUI-Extend-Resolution", "reference": "https://github.com/Dreamshot-io/ComfyUI-Extend-Resolution", "files": ["https://github.com/Dreamshot-io/ComfyUI-Extend-Resolution"], "install_type": "git-clone", "description": "NODES: Resolution Padding"}, {"author": "l1yongch1", "title": "ComfyUI-YcNodes", "reference": "https://github.com/l1yongch1/ComfyUI-YcNodes", "files": ["https://github.com/l1yongch1/ComfyUI-YcNodes"], "install_type": "git-clone", "description": "NODES: RemoveHighlightAndBlur, RoundedCorners, PaddingAccordingToBackground\npersonal custom nodes for learning"}, {"author": "vchopine", "title": "ComfyUI_Toolbox", "reference": "https://github.com/vchopine/ComfyUI_Toolbox", "files": ["https://github.com/vchopine/ComfyUI_Toolbox"], "install_type": "git-clone", "description": "Model & Aspect Ratio Selector Node for ComfyUI\nNOTE: The files in the repo are not organized."}, {"author": "Solan<PERSON><PERSON><PERSON><PERSON><PERSON>", "title": "PMSnodes [WIP]", "reference": "https://github.com/Solankimayursinh/PMSnodes", "files": ["https://github.com/Solankimayursinh/PMSnodes"], "install_type": "git-clone", "description": "A custom nodes for ComfyUI to Load audio in Base64 format and Send Audio to Websocket in Base64 Format for creating API of Audio related AI\nNOTE: The files in the repo are not organized."}, {"author": "rhinoflavored", "title": "comfyui_QT", "reference": "https://github.com/rhinoflavored/comfyui_QT", "files": ["https://github.com/rhinoflavored/comfyui_QT"], "install_type": "git-clone", "description": "bunch of image manipulation nodes....\nNOTE: The files in the repo are not organized."}, {"author": "ricklove", "title": "ComfyUI-AutoSeg-SAM2", "reference": "https://github.com/ricklove/ComfyUI-AutoSeg-SAM2", "files": ["https://github.com/ricklove/ComfyUI-AutoSeg-SAM2"], "install_type": "git-clone", "description": "NODES: AutoSeg-SAM2 Batch Segmentation"}, {"author": "JoeAu", "title": "ComfyUI-PythonNode [UNSAFE]", "reference": "https://github.com/JoeAu/ComfyUI-PythonNode", "files": ["https://github.com/JoeAu/ComfyUI-PythonNode"], "install_type": "git-clone", "description": "A custom ComfyUI node that allows users to execute arbitrary Python code with a single input (value) and output (result), enabling flexible processing of the input value using any Python code before assigning the final result to result. It also captures print() output and exceptions for debugging.[w/This node is an unsafe node that includes the capability to execute arbitrary python script.]"}, {"author": "smthemex", "title": "ComfyUI_GPT_SoVITS_Lite", "reference": "https://github.com/smthemex/ComfyUI_GPT_SoVITS_Lite", "files": ["https://github.com/smthemex/ComfyUI_GPT_SoVITS_Lite"], "install_type": "git-clone", "description": "[a/GPT_SoVITS](https://github.com/RVC-Boss/GPT-SoVITS) infer only for ComfyUI users\nNOTE: The files in the repo are not organized."}, {"author": "Nambi24", "title": "ComfyUI-Save_Image", "reference": "https://github.com/Nambi24/ComfyUI-Save_Image", "files": ["https://github.com/Nambi24/ComfyUI-Save_Image"], "description": "NODES: Save Image With Subfolder, Extract Last Path Component\nNOTE: The files in the repo are not organized.", "install_type": "git-clone"}, {"author": "sugarkwork", "title": "comfyui_image_crop", "reference": "https://github.com/sugarkwork/comfyui_image_crop", "files": ["https://github.com/sugarkwork/comfyui_image_crop"], "description": "NODES: CropTransparent, RestoreCrop, ExpandMultiple, CropReapply", "install_type": "git-clone"}, {"author": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "title": "ComfyUI-Tools", "reference": "https://github.com/AkiEvansDev/ComfyUI-Tools", "files": ["https://github.com/AkiEvansDev/ComfyUI-Tools"], "install_type": "git-clone", "description": "Custom nodes for basic actions."}, {"author": "<PERSON><PERSON><PERSON>", "title": "ComfyUI-Qdrant-Saver", "reference": "https://github.com/longzoho/ComfyUI-Qdrant-Saver", "files": ["https://github.com/longzoho/ComfyUI-Qdrant-Saver"], "install_type": "git-clone", "description": "NODES: <PERSON><PERSON><PERSON> Saver Node"}, {"author": "RUFFY-369", "title": "ComfyUI-FeatureBank", "reference": "https://github.com/RUFFY-369/ComfyUI-FeatureBank", "files": ["https://github.com/RUFFY-369/ComfyUI-FeatureBank"], "install_type": "git-clone", "description": "NODES: FeatureBankAttentionProcessor"}, {"author": "<PERSON><PERSON><PERSON>", "title": "ComfyUI-Sa2VAWrapper [WIP]", "reference": "https://github.com/Pablerdo/ComfyUI-Sa2VAWrapper", "files": ["https://github.com/Pablerdo/ComfyUI-Sa2VAWrapper"], "install_type": "git-clone", "description": "Wrapper for the Sa2VA model"}, {"author": "aria1th", "title": "ComfyUI-camietagger-onnx", "reference": "https://github.com/aria1th/ComfyUI-camietagger-onnx", "files": ["https://github.com/aria1th/ComfyUI-camietagger-onnx"], "install_type": "git-clone", "description": "NODES: <PERSON><PERSON>"}, {"author": "zjkhurry", "title": "comfyui_MetalFX [WIP]", "reference": "https://github.com/zjkhurry/comfyui_MetalFX", "files": ["https://github.com/zjkhurry/comfyui_MetalFX"], "install_type": "git-clone", "description": "A custom node for ComfyUI that enables high-quality image and video upscaling using Apple MetalFX technology.\nNOTE: The files in the repo are not organized."}, {"author": "<PERSON><PERSON><PERSON>", "title": "Miscomfy Nodes [WIP]", "reference": "https://github.com/RoyKillington/miscomfy-nodes", "files": ["https://github.com/RoyKillington/miscomfy-nodes"], "install_type": "git-clone", "description": "A repo of custom nodes for ComfyUI, from interacting with certain APIs to whatever other miscellanea I end up making"}, {"author": "xmarked-ai", "title": "ComfyUI_misc", "reference": "https://github.com/xmarked-ai/ComfyUI_misc", "files": ["https://github.com/xmarked-ai/ComfyUI_misc"], "install_type": "git-clone", "description": "NODES: Ace IntegerX, Ace FloatX, Ace Color FixX, White Balance X, Depth Displace X, Empty Latent X, KSampler Combo X, ..."}, {"author": "<PERSON><PERSON>", "title": "ComfyUI-Prompt-Helper [WIP]", "reference": "https://github.com/Elypha/ComfyUI-Prompt-Helper", "files": ["https://github.com/Elypha/ComfyUI-Prompt-Helper"], "install_type": "git-clone", "description": "Concat conditions and prompts for ComfyUI"}, {"author": "<PERSON><PERSON><PERSON><PERSON>", "title": "comfyui_flux_collection_advanced [WIP]", "reference": "https://github.com/StoryWalker/comfyui_flux_collection_advanced", "files": ["https://github.com/StoryWalker/comfyui_flux_collection_advanced"], "install_type": "git-clone", "description": "This is a collection focused in give a little more flexibility in the use of Flux models."}, {"author": "OSAnimate", "title": "ComfyUI-SpriteSheetMaker [WIP]", "reference": "https://github.com/OSAnimate/ComfyUI-SpriteSheetMaker", "files": ["https://github.com/OSAnimate/ComfyUI-SpriteSheetMaker"], "install_type": "git-clone", "description": "The sprite sheet maker node is a simple way to create sprite sheets and image grids.\nNOTE: The files in the repo are not organized."}, {"author": "BuffMcBigHuge", "title": "ComfyUI-Buff-<PERSON>des [WIP]", "reference": "https://github.com/BuffMcBigHuge/ComfyUI-Buff-Nodes", "files": ["https://github.com/BuffMcBigHuge/ComfyUI-Buff-Nodes"], "install_type": "git-clone", "description": "Several quality-of-life batch operation and string manipulation nodes."}, {"author": "ritikvirus", "title": "ComfyUI Terminal Command Node [UNSAFE]", "reference": "https://github.com/ritikvirus/comfyui-terminal-modal-node", "files": ["https://github.com/ritikvirus/comfyui-terminal-modal-node"], "install_type": "git-clone", "description": "This repository provides a custom ComfyUI node that lets you execute arbitrary terminal commands directly from the ComfyUI interface. [w/This extension allows remote command execution.]"}, {"author": "pixuai", "title": "ComfyUI-PixuAI", "reference": "https://github.com/pixuai/ComfyUI-PixuAI", "files": ["https://github.com/pixuai/ComfyUI-PixuAI"], "install_type": "git-clone", "description": "A collection of ComfyUI nodes designed to streamline prompt creation, organization, and discovery - making your workflows faster and more intuitive."}, {"author": "techidsk", "title": "comfyui_molook_nodes [WIP]", "reference": "https://github.com/techidsk/comfyui_molook_nodes", "files": ["https://github.com/techidsk/comfyui_molook_nodes"], "install_type": "git-clone", "description": "Some extra nodes"}, {"author": "Northerner1", "title": "ComfyUI_North_Noise [WIP]", "reference": "https://github.com/Northerner1/ComfyUI_North_Noise", "files": ["https://github.com/Northerner1/ComfyUI_North_Noise"], "install_type": "git-clone", "description": "NODES: North Noise"}, {"author": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "title": "ComfyUI_BodyEstimation_Nodes", "reference": "https://github.com/ManuShamil/ComfyUI_BodyEstimation_Nodes", "files": ["https://github.com/ManuShamil/ComfyUI_BodyEstimation_Nodes"], "install_type": "git-clone", "description": "NODES: CogitareLabsPoseIDExtractor"}, {"author": "MockbaTheBorg", "title": "ComfyUI-Mockba", "reference": "https://github.com/MockbaTheBorg/ComfyUI-Mockba", "files": ["https://github.com/MockbaTheBorg/ComfyUI-Mockba"], "install_type": "git-clone", "description": "NODES: Image Batch/Flip/Rotate/Subtract/Dither, Barcode, Select, ..."}, {"author": "j<PERSON>me", "title": "AsunaroTools", "reference": "https://github.com/jcomeme/ComfyUI-AsunaroTools", "files": ["https://github.com/jcomeme/ComfyUI-AsunaroTools"], "install_type": "git-clone", "description": "A collection of custom nodes for ComfyUI"}, {"author": "ZHO-ZHO-ZHO", "title": "ComfyUI Wan2.1 [WIP]", "reference": "https://github.com/ZHO-ZHO-ZHO/ComfyUI-Wan-ZHO", "files": ["https://github.com/ZHO-ZHO-ZHO/ComfyUI-Wan-ZHO"], "install_type": "git-clone", "description": "It’s estimated that ComfyUI itself will support it soon, so go ahead and give it a try!"}, {"author": "kijai", "title": "ComfyUI-WanVideoWrapper [WIP]", "reference": "https://github.com/kijai/ComfyUI-WanVideoWrapper", "files": ["https://github.com/kijai/ComfyUI-WanVideoWrapper"], "install_type": "git-clone", "description": "ComfyUI diffusers wrapper nodes for WanVideo"}, {"author": "ltdrdata", "title": "comfyui-unsafe-torch [UNSAFE]", "reference": "https://github.com/ltdrdata/comfyui-unsafe-torch", "files": ["https://github.com/ltdrdata/comfyui-unsafe-torch"], "install_type": "git-clone", "description": "disable torch.load's `weigths_only`"}, {"author": "muvich3n", "title": "ComfyUI-Crop-Border", "reference": "https://github.com/muvich3n/ComfyUI-Crop-Border", "files": ["https://github.com/muvich3n/ComfyUI-Crop-Border"], "install_type": "git-clone", "description": "NODES: Crop Image Borders"}, {"author": "masmullin2000", "title": "ComfyUI-MMYolo", "reference": "https://github.com/masmullin2000/ComfyUI-MMYolo", "files": ["https://github.com/masmullin2000/ComfyUI-MMYolo"], "install_type": "git-clone", "description": "A comfy node to find faces and output a mask"}, {"author": "<PERSON><PERSON><PERSON>", "title": "ComfyUI_LLM_Are_You_Listening [WIP]", "reference": "https://github.com/Yeonri/ComfyUI_LLM_Are_You_Listening", "files": ["https://github.com/Yeonri/ComfyUI_LLM_Are_You_Listening"], "install_type": "git-clone", "description": "NODES: AY<PERSON>_Node, AYL_GGUF_Node, AYL_API_Node\nNOTE: The files in the repo are not organized."}, {"author": "altkeyproject", "title": "<PERSON> Painter [WIP]", "reference": "https://github.com/alt-key-project/comfyui-dream-painter", "files": ["https://github.com/alt-key-project/comfyui-dream-painter"], "install_type": "git-clone", "description": "Provide utilities for 2D image generation and processing."}, {"author": "kimara-ai", "title": "ComfyUI-Kimara-AI-Image-From-URL [WIP]", "reference": "https://github.com/kimara-ai/ComfyUI-Kimara-AI-Image-From-URL", "files": ["https://github.com/kimara-ai/ComfyUI-Kimara-AI-Image-From-URL"], "install_type": "git-clone", "description": "Load image from URL and downscale to desired megapixels. Set megapixels to 0 for no downscaling."}, {"author": "krisshen2021", "title": "comfyui_OpenRouterNodes [WIP]", "reference": "https://github.com/krisshen2021/comfyui_OpenRouterNodes", "files": ["https://github.com/krisshen2021/comfyui_OpenRouterNodes"], "install_type": "git-clone", "description": "LLM custom nodes for comfyui\nNOTE: The files in the repo are not organized."}, {"author": "Velour-Fog", "title": "comfy-latent-nodes [UNSAFE]", "reference": "https://github.com/Velour-Fog/comfy-latent-nodes", "files": ["https://github.com/Velour-Fog/comfy-latent-nodes"], "install_type": "git-clone", "description": "ComfyUI nodes to save and load a latent to a specified directory. Saves time for doing operations on a latent such as upscaling without having to re-trigger the creation of the original latent.[w/This node can write files to an arbitrary path.]"}, {"author": "jgbyte", "title": "ComfyUI-RandomCube [WIP]", "reference": "https://github.com/jgbyte/ComfyUI-RandomCube", "files": ["https://github.com/jgbyte/ComfyUI-RandomCube"], "install_type": "git-clone", "description": "NODES: RandomCubeGrid"}, {"author": "thot-experiment", "title": "comfy-live-preview [WIP]", "reference": "https://github.com/thot-experiment/comfy-live-preview", "files": ["https://github.com/thot-experiment/comfy-live-preview"], "install_type": "git-clone", "description": "external live preview plugin for ComfyUI"}, {"author": "AhBumm", "title": "ComfyUI-Upscayl", "reference": "https://github.com/AhBumm/ComfyUI-Upscayl", "files": ["https://github.com/AhBumm/ComfyUI-Upscayl"], "nodename_pattern": "\\(BillBum\\)$", "install_type": "git-clone", "description": "NODES: Upscayl Upscaler"}, {"author": "NEZHA625", "title": "ComfyUI-tools-by-dong [UNSAFE]", "reference": "https://github.com/NEZHA625/ComfyUI-tools-by-dong", "files": ["https://github.com/NEZHA625/ComfyUI-tools-by-dong"], "install_type": "git-clone", "description": "NODES: HuggingFaceUploadNode, ImageDownloader, LoraIterator, FileMoveNode, InputDetectionNode, ...\nNOTE: The files in the repo are not organized.[w/This node pack includes nodes that can modify arbitrary files.]"}, {"author": "if-ai", "title": "ComfyUI-IF_Zonos [WIP]", "reference": "https://github.com/if-ai/ComfyUI-IF_Zonos", "files": ["https://github.com/if-ai/ComfyUI-IF_Zonos"], "install_type": "git-clone", "description": "Zonos for ComfyUI"}, {"author": "grinlau18", "title": "Xiser_Nodes [WIP]", "reference": "https://github.com/grinlau18/ComfyUI_XISER_Nodes", "files": ["https://github.com/grinlau18/ComfyUI_XISER_Nodes"], "install_type": "git-clone", "description": "Custom nodes for customizing workflows\nNOTE: The files in the repo are not organized."}, {"author": "LAOGOU-666", "title": "Comfyui_StartPatch [UNSAFE]", "reference": "https://github.com/LAOGOU-666/Comfyui_StartPatch", "files": ["https://github.com/LAOGOU-666/Comfyui_StartPatch"], "install_type": "git-clone", "description": "This patch plugin optimizes the node information processing mechanism of the ComfyUI server, significantly improving server performance and response speed. It greatly reduces the browser page initialization waiting time. [w/Since this patch modifies key functions of ComfyUI, it is highly likely to cause compatibility issues.]"}, {"author": "badmike", "title": "Prompt Factory [CONFLICT]", "reference": "https://github.com/badmike/comfyui-prompt-factory", "files": ["https://github.com/badmike/comfyui-prompt-factory"], "install_type": "git-clone", "description": "A modular system that adds randomness to prompt generation [w/This node pack is causing a name conflict with https://github.com/satche/comfyui-prompt-factory]"}, {"author": "<PERSON><PERSON><PERSON><PERSON>", "title": "ComfyUI-tilefusion", "reference": "https://github.com/owengillett/ComfyUI-tilefusion", "files": ["https://github.com/owengillett/ComfyUI-tilefusion"], "install_type": "git-clone", "description": "Helper nodes for generating seamless tiles."}, {"author": "Scaryplasmon", "title": "<PERSON>mf<PERSON><PERSON><PERSON> [WIP]", "reference": "https://github.com/Scaryplasmon/ComfTrellis", "files": ["https://github.com/Scaryplasmon/ComfTrellis"], "install_type": "git-clone", "description": "1 click install to run Trellis in ComfyUI\nNOTE: The files in the repo are not organized."}, {"author": "fangziheng2321", "title": "comfyuinode_chopmask [WIP]", "reference": "https://github.com/fangziheng2321/comfyuinode_chopmask", "files": ["https://github.com/fangziheng2321/comfyuinode_chopmask"], "install_type": "git-clone", "description": "a custom comfyui node for '/fooocusinpaint_upload'\nNOTE: The files in the repo are not organized."}, {"author": "D1-3105", "title": "ComfyUI-VideoStream", "reference": "https://github.com/D1-3105/ComfyUI-VideoStream", "files": ["https://github.com/D1-3105/ComfyUI-VideoStream"], "install_type": "git-clone", "description": "NODES: FloWWeaverExportSingleFrameGRPC"}, {"author": "gmorks", "title": "ComfyUI Animagine prompt [WIP]", "reference": "https://github.com/gmorks/ComfyUI-Animagine-Prompt", "files": ["https://github.com/gmorks/ComfyUI-Animagine-Prompt"], "install_type": "git-clone", "description": "Comfy UI node to prompt build for [a/https://huggingface.co/cagliostrolab/animagine-xl-4.0](https://huggingface.co/cagliostrolab/animagine-xl-4.0) model\nNOTE: The files in the repo are not organized."}, {"author": "wirytiox", "title": "ComfyUI-Qwen [CONFLICT]", "reference": "https://github.com/mr-krak3n/ComfyUI-Qwen", "files": ["https://github.com/mr-krak3n/ComfyUI-Qwen"], "install_type": "git-clone", "description": "This repository contains custom nodes for ComfyUI, designed to facilitate working with language models such as Qwen2.5 and DeepSeek. [w/This node pack is causing a name conflict with https://github.com/ZHO-ZHO-ZHO/ComfyUI-Qwen]"}, {"author": "hiusdev", "title": "ComfyUI_Lah_Toffee", "reference": "https://github.com/hiusdev/ComfyUI_Lah_Toffee", "files": ["https://github.com/hiusdev/ComfyUI_Lah_Toffee"], "install_type": "git-clone", "description": "NODES: Lah LoadVideoRandom"}, {"author": "hdfhssg", "title": "ComfyUI_pxtool [WIP]", "reference": "https://github.com/hdfhssg/ComfyUI_pxtool", "files": ["https://github.com/hdfhssg/ComfyUI_pxtool"], "install_type": "git-clone", "description": "This is a custom plugin node for ComfyUI that modifies and extends some features from existing projects. The main implementations include:\n* Reproducing some features of the [a/Stable-Diffusion-Webui-Civitai-Helper](https://github.com/zixaphir/Stable-Diffusion-Webui-Civitai-Helper) project within ComfyUI\n* Implementing a feature to randomly generate related prompt words by referencing the [a/noob-wiki dataset](https://huggingface.co/datasets/Laxhar/noob-wiki/tree/main)\nNOTE: The files in the repo are not organized."}, {"author": "franky519", "title": "comfyui-redux-style", "reference": "https://github.com/franky519/comfyui-redux-style", "files": ["https://github.com/franky519/comfyui-redux-style"], "install_type": "git-clone", "description": "NODES: Style Model Grid, Style Model Apply, Style Model Advanced"}, {"author": "rishipandey125", "title": "ComfyUI-FramePacking [WIP]", "reference": "https://github.com/rishipandey125/ComfyUI-FramePacking", "files": ["https://github.com/rishipandey125/ComfyUI-FramePacking"], "install_type": "git-clone", "description": "NODES: <PERSON>d Grid Boundaries, <PERSON>ames, <PERSON><PERSON> Frames, <PERSON><PERSON><PERSON>ame"}, {"author": "Northerner1", "title": "ComfyUI_North_Noise [WIP]", "reference": "https://github.com/Northerner1/ComfyUI_North_Noise", "files": ["https://github.com/Northerner1/ComfyUI_North_Noise"], "install_type": "git-clone", "description": "NODES: Unsampler"}, {"author": "kimara-ai", "title": "ComfyUI-Kimara-AI-Image-From-URL [WIP]", "reference": "https://github.com/kimara-ai/ComfyUI-Kimara-AI-Image-From-URL", "files": ["https://github.com/kimara-ai/ComfyUI-Kimara-AI-Image-From-URL"], "install_type": "git-clone", "description": "Load image from URL and downscale to desired megapixels. Set megapixels to 0 for no downscaling."}, {"author": "tc8M4lF3s88", "title": "comfy-tif-support", "reference": "https://github.com/M4lF3s/comfy-tif-support", "files": ["https://github.com/M4lF3s/comfy-tif-support"], "install_type": "git-clone", "description": "NODES: Load TIFF"}, {"author": "tc888", "title": "ComfyUI_Save_Flux_Image", "reference": "https://github.com/tc888/ComfyUI_Save_Flux_Image", "files": ["https://github.com/tc888/ComfyUI_Save_Flux_Image"], "install_type": "git-clone", "description": "Customized version of comfyui-image-save tailored for saving Flux images"}, {"author": "var1ableX", "title": "ComfyUI_Accessories", "reference": "https://github.com/var1ableX/ComfyUI_Accessories", "files": ["https://github.com/var1ableX/ComfyUI_Accessories"], "install_type": "git-clone", "description": "NODES: Get Mask Dimensions, Get Random Dimensions, Is Mask Empty/Image, Any Cast, Make List From Text"}, {"author": "xinyiSS", "title": "CombineMasksNode", "reference": "https://github.com/xinyiSS/CombineMasksNode", "files": ["https://github.com/xinyiSS/CombineMasksNode"], "install_type": "git-clone", "description": "NODES: Combine Masks Node"}, {"author": "osuiso-depot", "title": "comfyui-keshigom_custom", "reference": "https://github.com/osuiso-depot/comfyui-keshigom_custom", "files": ["https://github.com/osuiso-depot/comfyui-keshigom_custom"], "install_type": "git-clone", "description": "NODES: <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, FLIP-W/H Selector, FLIP-W/H SelectorConst, TextFind, ckpt_Loader_Simple, True-or-False, myStringNode"}, {"author": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "title": "ComfyUI-Sentinel [WIP]", "reference": "https://github.com/LucipherDev/ComfyUI-Sentinel", "files": ["https://github.com/LucipherDev/ComfyUI-Sentinel"], "install_type": "git-clone", "description": "ComfyUI Extension for Advanced Security. Implements login, multi-user registration, IP filtering, and user-specific input/output directories.[w/WARN:While ComfyUI Sentinel enhances security for ComfyUI, it does not guarantee absolute protection. Security is about risk mitigation, not elimination. Users are responsible for implementing their own security measures.]"}, {"author": "threadedblue", "title": "MLXnodes [WIP]", "reference": "https://github.com/threadedblue/MLXnodes", "files": ["https://github.com/threadedblue/MLXnodes"], "install_type": "git-clone", "description": "A port of MLX Examples to ComfyUI custom_nodes. These are intended to run on a macOS M1.\nNOTE: The files in the repo are not organized."}, {"author": "<PERSON><PERSON><PERSON><PERSON>", "title": "Comfy-InterestingPixels [WIP]", "reference": "https://github.com/jschoormans/Comfy-InterestingPixels", "files": ["https://github.com/jschoormans/Comfy-InterestingPixels"], "install_type": "git-clone", "description": "NODES: Shareable Image Slider, Random Palette\nNOTE: The files in the repo are not organized."}, {"author": "<PERSON><PERSON><PERSON><PERSON>", "title": "ComfyUI-TexturePacker [WIP]", "reference": "https://github.com/kijai/ComfyUI-TexturePacker", "files": ["https://github.com/jschoormans/Comfy-InterestingPixels"], "install_type": "git-clone", "description": "ComfyUI node to use PyTexturePacker\nNOTE: The files in the repo are not organized."}, {"author": "lum3on", "title": "comfyui_<PERSON><PERSON>_Polymath [WIP]", "reference": "https://github.com/lum3on/comfyui_LLM_Polymath", "files": ["https://github.com/lum3on/comfyui_LLM_Polymath"], "install_type": "git-clone", "description": "An advanced chat node, that integrates large language models to automate data processes and enhance prompt responses through real-time web search and image handling. It supports both OpenAI's GPT-like models and a local Ollama API. Custom node finder and smart assistant tools provide tailored workflow recommendations for efficient integration. Additionally, the node dynamically augments prompts and offers flexible output compression options.\nNOTE: The files in the repo are not organized."}, {"author": "<PERSON><PERSON>", "title": "ComfyUI_mickster_nodes [WIP]", "reference": "https://github.com/MickeyJ/ComfyUI_mickster_nodes", "files": ["https://github.com/MickeyJ/ComfyUI_mickster_nodes"], "install_type": "git-clone", "description": "A collection of custom nodes for ComfyUI, focusing on image handling and LoRA training."}, {"author": "gold24park", "title": "loki-comfyui-node", "reference": "https://github.com/gold24park/loki-comfyui-node", "files": ["https://github.com/gold24park/loki-comfyui-node"], "install_type": "git-clone", "description": "NODES: Get Image Luminance, Get Dominant Color, Overlay Text"}, {"author": "hayden-fr", "title": "ComfyUI-Image-Browsing [USAFE]", "id": "image-browsing", "reference": "https://github.com/hayden-fr/ComfyUI-Image-Browsing", "files": ["https://github.com/hayden-fr/ComfyUI-Image-Browsing"], "install_type": "git-clone", "description": "Image Browsing: browsing, download and delete."}, {"author": "molbal", "title": "comfy-url-fetcher [WIP]", "reference": "https://github.com/molbal/comfy-url-fetcher", "files": ["https://github.com/molbal/comfy-url-fetcher"], "install_type": "git-clone", "description": "Fetches URLs"}, {"author": "<PERSON><PERSON><PERSON>", "title": "ComfyUI_Output_as_Input", "reference": "https://github.com/a-und-b/ComfyUI_Output_as_Input", "files": ["https://github.com/a-und-b/ComfyUI_Output_as_Input"], "install_type": "git-clone", "description": "This is a simple custom ComfyUI node that allows you to easily use recent output images as input in your workflows. It does not allow image uploads on purpose and does not require any additional dependencies.\nNOTE: The files in the repo are not organized."}, {"author": "<PERSON><PERSON><PERSON>", "title": "ComfyUI-DeepSeek", "reference": "https://github.com/neverbiasu/ComfyUI-DeepSeek", "files": ["https://github.com/neverbiasu/ComfyUI-DeepSeek"], "install_type": "git-clone", "description": "NODES: DeepSeek Caller"}, {"author": "Krish-701", "title": "R<PERSON><PERSON>m<PERSON>ui", "reference": "https://github.com/Krish-701/RK_Comfyui", "files": ["https://github.com/Krish-701/RK_Comfyui"], "install_type": "git-clone", "description": "NODES: RK Excel File State Looper, RK Accumulate Text, RK Advanced Script Finder, RK CSV File State Looper, RK Read Excel Row, RK Sequential Image Viewer, RK Concatenate Text, RK Write Text, RK Save Image, RK Seed Loop"}, {"author": "mikebilly", "title": "transparent-background-comfyui", "reference": "https://github.com/mikebilly/Transparent-background-comfyUI", "files": ["https://github.com/mikebilly/Transparent-background-comfyUI"], "install_type": "git-clone", "description": "NODES: Transparentbackground RemBg"}, {"author": "<PERSON><PERSON>", "title": "Time Series Nodes for ComfyUI [Experimental]", "reference": "https://github.com/Kayarte/Time-Series-Nodes-for-ComfyUI", "files": ["https://github.com/Kayarte/Time-Series-Nodes-for-ComfyUI"], "install_type": "git-clone", "description": "Basic nodes for time series analysis in ComfyUI. Currently in early development."}, {"author": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "title": "ComfyUI-LLMs-Toolkit [WIP]", "reference": "https://github.com/HuangYuChuh/ComfyUI-LLMs-Toolkit", "files": ["https://github.com/HuangYuChuh/ComfyUI-LLMs-Toolkit"], "install_type": "git-clone", "description": "Enhance your ComfyUI workflows with powerful LLMs! This custom node suite integrates DeepSeek, Qwen, and other leading Chinese LLMs directly into your ComfyUI environment. Create innovative AI-powered applications with a range of useful nodes designed to leverage the advanced capabilities of these LLMs for image generation, understanding, and more.\nNOTE: The files in the repo are not organized."}, {"author": "comfyuiblog", "title": "deepseek_prompt_generator_comfyui [WIP]", "reference": "https://github.com/comfyuiblog/deepseek_prompt_generator_comfyui", "files": ["https://github.com/comfyuiblog/deepseek_prompt_generator_comfyui"], "install_type": "git-clone", "description": "Prompt Expansion for Stable Diffusion, using Deepseek API.\nNOTE: The files in the repo are not organized."}, {"author": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "title": "ComfyUI_HEXtoRGB", "reference": "https://github.com/risunobushi/ComfyUI_HEXtoRGB", "files": ["https://github.com/risunobushi/ComfyUI_HEXtoRGB"], "install_type": "git-clone", "description": "NODES: Hex to RGB Converter"}, {"author": "EmanueleUniroma2", "title": "ComfyUI-FLAC-to-WAV [WIP]", "reference": "https://github.com/EmanueleUniroma2/ComfyUI-FLAC-to-WAV", "files": ["https://github.com/EmanueleUniroma2/ComfyUI-FLAC-to-WAV"], "install_type": "git-clone", "description": "A custom node to convert flac files to wav inside comfy UI\nComfyUI Custom Node: FLAC to WAV Converter Welcome to the ComfyUI Custom Node: FLAC to WAV Converter repository! This project provides a custom node for ComfyUI that allows you to easily convert .flac audio files to .wav format, making it simpler to work with a variety of audio tools and applications.\nNOTE: The files in the repo are not organized."}, {"author": "<PERSON><PERSON><PERSON><PERSON>", "title": "comfyui_runway_gen3", "reference": "https://github.com/eyekayem/comfyui_runway_gen3", "files": ["https://github.com/eyekayem/comfyui_runway_gen3"], "install_type": "git-clone", "description": "NODES: Runway Video Gen, Runway Video Preview"}, {"author": "StartHua", "title": "Comfyui_CXH_joy_caption [SECURITY SCREENING]", "reference": "https://github.com/StartHua/Comfyui_CXH_joy_caption", "files": ["https://github.com/StartHua/Comfyui_CXH_joy_caption"], "install_type": "git-clone", "description": "Nodes:Joy_caption_load, Joy_caption\nNOTE:This node pack has been transitioned to a security screening status due to policy."}, {"author": "kijai", "title": "ComfyUI-ComfyUI-Hunyuan3DWrapper [WIP]", "reference": "https://github.com/kijai/ComfyUI-Hunyuan3DWrapper", "files": ["https://github.com/kijai/ComfyUI-Hunyuan3DWrapper"], "install_type": "git-clone", "description": "Wrapper nodes for https://github.com/Tencent/Hunyuan3D-2, additional installation steps needed, please check the github repository"}, {"author": "7BEII", "title": "comfyui-promptbymood [WIP]", "reference": "https://github.com/7BEII/Comfyui_PDuse", "files": ["https://github.com/7BEII/Comfyui_PDuse"], "install_type": "git-clone", "description": "NODES: PD_json_group_fontsize, PD_Incremental_JSON, PD_removeword, PD_Image Crop Location, PD_ImageConcanate, PD_FileName_refixer\nNOTE: The files in the repo are not organized."}, {"author": "RLW-Chars", "title": "comfyui-promptbymood [WIP]", "reference": "https://github.com/RLW-Chars/comfyui-promptbymood", "files": ["https://github.com/RLW-Chars/comfyui-promptbymood"], "install_type": "git-clone", "description": "A plugin for ComfyUI to create random prompts.\nNOTE: The files in the repo are not organized."}, {"author": "mohamed<PERSON>bhi777", "title": "ComfyUI-FramerComfy [WIP]", "reference": "https://github.com/mohamedsobhi777/ComfyUI-FramerComfy", "files": ["https://github.com/mohamedsobhi777/ComfyUI-FramerComfy"], "install_type": "git-clone", "description": "NODES: FramerComfy Input String/Number/Image/Float/Boolean/Image, ...\nNOTE: The files in the repo are not organized."}, {"author": "<PERSON><PERSON><PERSON><PERSON>", "title": "comfyui-inodes", "reference": "https://github.com/naderzare/comfyui-inodes", "files": ["https://github.com/naderzare/comfyui-inodes"], "install_type": "git-clone", "description": "NODES: If-Else, Multiline Split, Azure AI API"}, {"author": "sizzlebop", "title": "ComfyUI LLM Prompt Enhancer [WIP]", "reference": "https://github.com/pinkpixel-dev/comfyui-llm-prompt-enhancer", "files": ["https://github.com/pinkpixel-dev/comfyui-llm-prompt-enhancer"], "install_type": "git-clone", "description": "A ComfyUI node for enhancing prompts using various LLM providers\nNOTE: The files in the repo are not organized."}, {"author": "a-One-Fan", "title": "ComfyUI-Blenderesque-Nodes [WIP]", "reference": "https://github.com/a-One-Fan/ComfyUI-Blenderesque-Nodes", "files": ["https://github.com/a-One-Fan/ComfyUI-Blenderesque-Nodes"], "install_type": "git-clone", "description": "Blender-like nodes for ComfyUI."}, {"author": "<PERSON><PERSON><PERSON><PERSON>", "title": "comfyui-deepseek [WIP]", "reference": "https://github.com/yanhuifair/comfyui-deepseek", "files": ["https://github.com/yanhuifair/comfyui-deepseek"], "install_type": "git-clone", "description": "nodes for deepseek api\nNOTE: The files in the repo are not organized."}, {"author": "IfnotFr", "title": "ComfyUI-Ifnot-Pack", "reference": "https://github.com/IfnotFr/ComfyUI-Ifnot-Pack", "files": ["https://github.com/IfnotFr/ComfyUI-Ifnot-Pack"], "install_type": "git-clone", "description": "NODES: Face Crop, [w/A pack of custom nodes used in my projects. Not intended to be used by other persons as the usage is not documented. But if something interests you in this repository, go for it !]"}, {"author": "KihongK", "title": "ComfyUI-RoysNodes [WIP]", "reference": "https://github.com/KihongK/comfyui-roysnodes", "files": ["https://github.com/KihongK/comfyui-roysnodes"], "install_type": "git-clone", "description": "WIP custom nodes for Creation of AI images & videos"}, {"author": "catboxanon", "title": "ComfyUI-Pixelsmith [WIP]", "reference": "https://github.com/catboxanon/ComfyUI-Pixelsmith", "files": ["https://github.com/catboxanon/ComfyUI-Pixelsmith"], "install_type": "git-clone", "description": "NODES: <PERSON><PERSON><PERSON><PERSON>"}, {"author": "smthemex", "title": "ComfyUI_MangaNinjia [WIP]", "reference": "https://github.com/smthemex/ComfyUI_MangaNinjia", "files": ["https://github.com/smthemex/ComfyUI_MangaNinjia"], "install_type": "git-clone", "description": "ComfyUI_MangaNinjia is a ComfyUI node of MangaNinja which  is a Line Art Colorization with Precise Reference Following method.\nNOTE: invalid pyproject.toml file."}, {"author": "hunterssl", "title": "ComfyUI_SSLNodes", "reference": "https://github.com/hunterssl/ComfyUI_SSLNodes", "files": ["https://github.com/hunterssl/ComfyUI_SSLNodes"], "install_type": "git-clone", "description": "NODES: <PERSON><PERSON> Load <PERSON>, SSL Get Json Keys Count, SSL Load Checkpoint By Name, SSL Random Num In Loop, SSL Save Image Outside"}, {"author": "<PERSON><PERSON><PERSON><PERSON>", "title": "ComfyUI-Legendary-Nodes", "reference": "https://github.com/ammahmoudi/ComfyUI-Legendary-Nodes", "files": ["https://github.com/ammahmoudi/ComfyUI-Legendary-Nodes"], "install_type": "git-clone", "description": "NODES: <PERSON><PERSON> URL Loader, Legendary Lora URL Loader"}, {"author": "y<PERSON><PERSON><PERSON>", "title": "Comfyui-NodeSpark", "reference": "https://github.com/yichengup/Comfyui-NodeSpark", "files": ["https://github.com/yichengup/Comfyui-NodeSpark"], "install_type": "git-clone", "description": "NODES: Image Circle Warp, Image Stretch, Image Wave Warp, Liquify Effect"}, {"author": "kijai", "title": "ComfyUI-VideoNoiseWarp [WIP]", "reference": "https://github.com/kijai/ComfyUI-VideoNoiseWarp", "files": ["https://github.com/kijai/ComfyUI-VideoNoiseWarp"], "install_type": "git-clone", "description": "Nodes to generate noise from video for [a/https://github.com/Eyeline-Research/Go-with-the-Flow](https://github.com/Eyeline-Research/Go-with-the-Flow)"}, {"author": "muvich3n", "title": "ComfyUI-Claude-I2T", "reference": "https://github.com/muvich3n/ComfyUI-Claude-I2T", "files": ["https://github.com/muvich3n/ComfyUI-Claude-I2T"], "install_type": "git-clone", "description": "NODES: <PERSON> to Prompt Generator"}, {"author": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "title": "ComfyUI-ALB-Login", "reference": "https://github.com/maekawataiki/ComfyUI-ALB-Login", "files": ["https://github.com/maekawataiki/ComfyUI-ALB-Login"], "install_type": "git-clone", "description": "Auth library to inspect token provided by ALB to protect ComfyUI."}, {"author": "<PERSON><PERSON><PERSON>", "title": "Kwai_font_comfyui", "reference": "https://github.com/<PERSON><PERSON><PERSON>/<PERSON><PERSON>_font_comfyui", "files": ["https://github.com/<PERSON><PERSON><PERSON>/<PERSON><PERSON>_font_comfyui"], "install_type": "git-clone", "description": "NODES: <PERSON><PERSON><PERSON><PERSON>_Resnet50_Runner, <PERSON><PERSON><PERSON><PERSON>_Resnet50_Loader, <PERSON><PERSON><PERSON>nt_Resnet101_Runner, <PERSON><PERSON><PERSON>nt_Resnet101_<PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>_Image_Cropper"}, {"author": "PATATAJEC", "title": "Patatajec-Nodes [WIP]", "reference": "https://github.com/PATATAJEC/Patatajec-Nodes", "files": ["https://github.com/PATATAJEC/Patatajec-Nodes"], "install_type": "git-clone", "description": "NODES: HyVid Switcher\nNOTE: The files in the repo are not organized."}, {"author": "sourceful-official", "title": "comfyui-sourceful-official", "reference": "https://github.com/sourceful-official/comfyui-sourceful-official", "files": ["https://github.com/sourceful-official/comfyui-sourceful-official"], "description": "NODES: SourcefulOfficialComfyuiIncontextThreePanels, FalFluxLoraSourcefulOfficial, FalIcLightV2SourcefulOfficial", "install_type": "git-clone"}, {"author": "<PERSON><PERSON><PERSON><PERSON>", "title": "ComfyUI-SunoAI-Mds", "reference": "https://github.com/Alvaroeai/ComfyUI-SunoAI-Mds", "files": ["https://github.com/Alvaroeai/ComfyUI-SunoAI-Mds"], "install_type": "git-clone", "description": "NODES: Suno Generate, Suno Download, Suno Proxy Generate, Suno Proxy Download"}, {"author": "<PERSON><PERSON><PERSON><PERSON>", "title": "ComfyUI-MochiWrapper-I2V [WIP]", "reference": "https://github.com/parmarjh/ComfyUI-MochiWrapper-I2V", "files": ["https://github.com/parmarjh/ComfyUI-MochiWrapper-I2V"], "install_type": "git-clone", "description": "ComfyUI wrapper nodes for [a/<PERSON><PERSON>](https://github.com/genmoai/models) video generator"}, {"author": "Symbiomatrix", "title": "Comfyui-Sort-Files", "reference": "https://github.com/Symbiomatrix/Comfyui-Sort-Files", "files": ["https://github.com/Symbiomatrix/Comfyui-Sort-Files"], "install_type": "git-clone", "description": "Monkeypatch file sort to date modified or custom instead of lexicographic."}, {"author": "x3bits", "title": "ComfyUI-Power-Flow [UNSAFE]", "reference": "https://github.com/x3bits/ComfyUI-Power-Flow", "files": ["https://github.com/x3bits/ComfyUI-Power-Flow"], "install_type": "git-clone", "description": "A ComfyUI node package that introduces common programming logic to enhance the flexibility of ComfyUI workflows. It supports features such as function definition and execution, 'for' loops, 'while' loops, and Python code execution.\n[w/This extension allows the execution of arbitrary Python code from a workflow.]"}, {"author": "<PERSON><PERSON><PERSON><PERSON>", "title": "ComfyUI-Math [WIP]", "reference": "https://github.com/EmilioPlumed/ComfyUI-Math", "files": ["https://github.com/EmilioPlumed/ComfyUI-Math"], "install_type": "git-clone", "description": "Custom nodes that take 2 float inputs and calculates greatest common denominator and least common multiple, returning them as ints.\nNOTE: The files in the repo are not organized."}, {"author": "mliand", "title": "ComfyUI-Calendar-Node [WIP]", "reference": "https://github.com/mliand/ComfyUI-Calendar-Node", "files": ["https://github.com/mliand/ComfyUI-Calendar-Node"], "install_type": "git-clone", "description": "A custom node for Comfyui to create a Calendar like grid\nNOTE: The files in the repo are not organized."}, {"author": "phamngoctukts", "title": "ComyUI-Tupham", "reference": "https://github.com/phamngoctukts/ComyUI-Tupham", "files": ["https://github.com/phamngoctukts/ComyUI-Tupham"], "install_type": "git-clone", "description": "NODES: Ghép Ảnh, Multi Prompt v2.0, Condition Upscale, Multi sampler, Run node selected"}, {"author": "5x00", "title": "ComfyUI-Prompt-Plus [WIP]", "reference": "https://github.com/5x00/ComfyUI-Prompt-Plus", "files": ["https://github.com/5x00/ComfyUI-Prompt-Plus"], "install_type": "git-clone", "description": "Prompt Plus is a collection of LLM and VLM nodes that make prompting easier for image and video generation.\nNOTE: The files in the repo are not organized."}, {"author": "aria1th", "title": "ComfyUI-CairoSVG", "reference": "https://github.com/aria1th/ComfyUI-CairoSVG", "files": ["https://github.com/aria1th/ComfyUI-CairoSVG"], "install_type": "git-clone", "description": "NODES: VectorizedUpscaleScaling, VectorizedUpscaleSize"}, {"author": "g<PERSON><PERSON><PERSON>", "title": "FlowNodes [WIP]", "reference": "https://github.com/gitmylo/FlowNodes", "files": ["https://github.com/gitmylo/FlowNodes"], "install_type": "git-clone", "description": "A ComfyUI node pack containing nodes for basic programming logic."}, {"author": "<PERSON><PERSON><PERSON>yi", "title": "Comfy-WaveSpeed [WIP]", "reference": "https://github.com/chengzeyi/Comfy-WaveSpeed", "files": ["https://github.com/chengzeyi/Comfy-WaveSpeed"], "install_type": "git-clone", "description": "The all in one inference optimization solution for ComfyUI, universal, flexible, and fast."}, {"author": "zyd232", "title": "ComfyUI-zyd232-Nodes", "reference": "https://github.com/zyd232/ComfyUI-zyd232-Nodes", "files": ["https://github.com/zyd232/ComfyUI-zyd232-Nodes"], "install_type": "git-clone", "description": "NODES: Image Pixels Compare, Save Preview Images"}, {"author": "<PERSON><PERSON><PERSON><PERSON>", "title": "ComfyUI-FairLab", "reference": "https://github.com/yanhuifair/ComfyUI-FairLab", "files": ["https://github.com/yanhuifair/ComfyUI-FairLab"], "install_type": "git-clone", "description": "NODES: CLIP Text Encode Translated, Translate String, Load Image From Folder, Save String To Folder, Fix UTF-8 String, String Combine, String Field, Download Image, Save Images To Folder, Save Image To Folder, Image Resize, ..."}, {"author": "nomcycle", "title": "ComfyUI_Cluster [WIP]", "reference": "https://github.com/nomcycle/ComfyUI_Cluster", "files": ["https://github.com/nomcycle/ComfyUI_Cluster"], "install_type": "git-clone", "description": "Very early W<PERSON>I.P of clustered ComfyUI inference."}, {"author": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "title": "FindBrightestSpot [WIP]", "reference": "https://github.com/waynepimpzhang/comfyui-opencv-brightestspot", "files": ["https://github.com/waynepimpzhang/comfyui-opencv-brightestspot"], "install_type": "git-clone", "description": "Analyze the image to find the x and y coordinates of the brightest point.\nNOTE: The files in the repo are not organized."}, {"author": "power88", "title": "ComfyUI-PDiD-Nodes [WIP]", "reference": "https://github.com/power88/ComfyUI-PDiD-Nodes", "files": ["https://github.com/power88/ComfyUI-PDiD-Nodes"], "install_type": "git-clone", "description": "NODES: Get Image Size, Check Character Tag, Nearest SDXL Resolution divided by 64, Get Image Main Color, Blend Images, List Operations, Make Image Gray.\nNOTE: not working"}, {"author": "FinetunersAI", "title": "ComfyUI Finetuners [WIP]", "reference": "https://github.com/FinetunersAI/finetuners", "files": ["https://github.com/FinetunersAI/finetuners"], "install_type": "git-clone", "description": "A collection of utility nodes for ComfyUI to enhance your workflow.\nNOTE: The files in the repo are not organized."}, {"author": "sourceful-official", "title": "ComfyUI_InstructPixToPixConditioningLatent [WIP]", "reference": "https://github.com/sourceful-official/ComfyUI_InstructPixToPixConditioningLatent", "files": ["https://github.com/sourceful-official/ComfyUI_InstructPixToPixConditioningLatent"], "description": "ComfyUI-ComfyUI_InstructPixToPixConditioningLatent\nNOTE:invalid pyproject.toml", "install_type": "git-clone"}, {"author": "fritzp<PERSON>", "title": "ComfyUI-LLM-Utils [WIP]", "reference": "https://github.com/fritzprix/ComfyUI-LLM-Utils", "files": ["https://github.com/fritzprix/ComfyUI-LLM-Utils"], "install_type": "git-clone", "description": "A collection of utility nodes for ComfyUI focused on text and LLM-related operations\nNOTE: The files in the repo are not organized."}, {"author": "ciga2011", "title": "ComfyUI-AppGen [UNSAFE]", "reference": "https://github.com/ciga2011/ComfyUI-AppGen", "files": ["https://github.com/ciga2011/ComfyUI-AppGen"], "install_type": "git-clone", "description": "A ComfyUI node pack designed to generate and edit Single Page Applications (SPAs) using natural language.[w/This extension allows arbitrary JavaScript code to be executed through the execution of workflows.]"}, {"author": "Dracon<PERSON><PERSON><PERSON><PERSON>", "title": "ComfyUI e621 booru Toolkit", "reference": "https://github.com/DraconicDragon/ComfyUI_e621_booru_toolkit", "files": ["https://github.com/DraconicDragon/ComfyUI_e621_booru_toolkit"], "install_type": "git-clone", "description": "WIP. Nodes: Fetch e621/danbooru image and/or tags etc from a given URL; Get the Wiki entry for a tag through a button press."}, {"author": "Grey3016", "title": "Save2Icon", "reference": "https://github.com/Grey3016/Save2Icon", "files": ["https://github.com/Grey3016/Save2Icon"], "install_type": "git-clone", "description": "NODES: Save2Icon"}, {"author": "<PERSON><PERSON>", "title": "ComfyUI-vts-nodes [WIP]", "reference": "https://github.com/Chargeuk/ComfyUI-vts-nodes", "files": ["https://github.com/Chargeuk/ComfyUI-vts-nodes"], "install_type": "git-clone", "description": "NODES: Clean Text, Color Mask To Mask, Conditioning Set Batch Mask, <PERSON>rge Delimited Text, Reduce Batch <PERSON>, Text To Batch Prompt, To Text, "}, {"author": "ryanontheinside", "title": "ComfyUI_YoloNasObjectDetection_Tensorrt [WIP]", "reference": "https://github.com/ryanontheinside/ComfyUI_YoloNasObjectDetection_Tensorrt", "files": ["https://github.com/ryanontheinside/ComfyUI_YoloNasObjectDetection_Tensorrt"], "install_type": "git-clone", "description": "ComfyUI YOLO NAS Object Detection with TensorRT"}, {"author": "steelan9199", "title": "ComfyUI-Teeth [UNSAFE]", "reference": "https://github.com/steelan9199/ComfyUI-Teeth", "files": ["https://github.com/steelan9199/ComfyUI-Teeth"], "install_type": "git-clone", "description": "Run Python code, Outline, List, Four-quadrant grid, Nine-square grid[w/This extension poses a risk of executing arbitrary commands through workflow execution. Please be cautious.]"}, {"author": "aiden1020", "title": "ComfyUI_Artcoder [WIP]", "reference": "https://github.com/aiden1020/ComfyUI_Artcoder", "files": ["https://github.com/aiden1020/ComfyUI_Artcoder"], "install_type": "git-clone", "description": "This project is a custom node for ComfyUI that uses [a/ArtCoder](https://arxiv.org/abs/2011.07815) [CVPR 2021] to refine videos generated by [a/AnimateDiff](https://arxiv.org/abs/2307.04725) [ICLR2024 Spotlight] or the other video. The node is to transform these videos into functional QR code videos that can be scanned.\nNOTE: The files in the repo are not organized."}, {"author": "A4P7J1N7M05OT", "title": "ComfyUI-ManualSigma", "reference": "https://github.com/A4P7J1N7M05OT/ComfyUI-ManualSigma", "files": ["https://github.com/A4P7J1N7M05OT/ComfyUI-ManualSigma"], "install_type": "git-clone", "description": "NODES: Manual Sigma"}, {"author": "<PERSON><PERSON><PERSON>", "title": "ComfyUI-StereoCrafter [WIP]", "reference": "https://github.com/neverbiasu/ComfyUI-StereoCrafter", "files": ["https://github.com/neverbiasu/ComfyUI-StereoCrafter"], "install_type": "git-clone", "description": "NODES: Depth Splatting Model Loader, Depth Splatting Node, Inpainting Inference Node"}, {"author": "<PERSON><PERSON><PERSON>", "title": "ComfyUI-exit [UNSAFE]", "reference": "https://github.com/watarika/ComfyUI-exit", "files": ["https://github.com/watarika/ComfyUI-exit"], "install_type": "git-clone", "description": "Custom node to handle text.[w/This custom node includes a custom node that can terminate ComfyUI.]"}, {"author": "<PERSON><PERSON><PERSON>", "title": "ComfyUI-Text-Utility [UNSAFE]", "reference": "https://github.com/watarika/ComfyUI-Text-Utility", "files": ["https://github.com/watarika/ComfyUI-Text-Utility"], "install_type": "git-clone", "description": "Custom node to handle text.[w/This node pack contains a custom node that poses a security risk by providing the ability to read text from arbitrary paths.]"}, {"author": "mehbebe", "title": "ComfyLoraGallery [WIP]", "reference": "https://github.com/mehbebe/ComfyLoraGallery", "files": ["https://github.com/mehbebe/ComfyLoraGallery"], "install_type": "git-clone", "description": "A custom node for ComfyUI that will provide a gallery style lora selector similar to the 'lora' tab in Automatic1111."}, {"author": "karthikg-09", "title": "ComfyUI-KG09 [WIP]", "reference": "https://github.com/karthikg-09/ComfyUI-3ncrypt", "files": ["https://github.com/karthikg-09/ComfyUI-3ncrypt"], "install_type": "git-clone", "description": "NODES: Save Image+[w/The web extension of this node pack modifies part of ComfyUI's asset files.]"}, {"author": "AustinMroz", "title": "ComfyUI-MinCache", "id": "comfyui-mincache", "reference": "https://github.com/AustinMroz/ComfyUI-MinCache", "files": ["https://github.com/AustinMroz/ComfyUI-MinCache"], "install_type": "git-clone", "description": "Modifies execution to minimize RAM at the cost of performance"}, {"author": "glamorfleet0i", "title": "ComfyUI Firewall", "reference": "https://github.com/glamorfleet0i/ComfyUI-Firewall", "files": ["https://github.com/glamorfleet0i/ComfyUI-Firewall"], "install_type": "git-clone", "description": "A very basic firewall-like middleware that restricts access to your ComfyUI server based on a list of specified IP addresses. As this is configured as middleware, the firewall will restrict both the web UI and any API endpoints."}, {"author": "<PERSON>hanks", "title": "Shank-Tools", "reference": "https://github.com/warshanks/Shank-Tools", "files": ["https://github.com/warshanks/Shank-Tools"], "install_type": "git-clone", "description": "NODES: Tile Calculator, Resolution Divider, Height & Width"}, {"author": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "title": "ComfyUI-FileOps [UNSAFE]", "reference": "https://github.com/BaronVonBoolean/ComfyUI-FileOps", "files": ["https://github.com/BaronVonBoolean/ComfyUI-FileOps"], "install_type": "git-clone", "description": "NODES: File Mv, File Path, File Dir.\n[w/This is dangerous as it provides the ability to manipulate arbitrary user files.]"}, {"author": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "title": "ComfyUI-Jissi-List [WIP]", "reference": "https://github.com/JissiChoi/ComfyUI-Jissi-List", "files": ["https://github.com/JissiChoi/ComfyUI-Jissi-List"], "install_type": "git-clone", "description": "Data List Management for ComfyUI\nNOTE: The files in the repo are not organized."}, {"author": "Maxim-Dey", "title": "ComfyUI-MS_Tools [WIP]", "reference": "https://github.com/Maxim-Dey/ComfyUI-MaksiTools", "files": ["https://github.com/Maxim-Dey/ComfyUI-MaksiTools"], "install_type": "git-clone", "description": "NODES: MS Time Measure NodeMaksiTools"}, {"author": "jammyfu", "title": "ComfyUI PaintingCoderUtils Nodes [WIP]", "reference": "https://github.com/jammyfu/ComfyUI_PaintingCoderUtils", "files": ["https://github.com/jammyfu/ComfyUI_PaintingCoderUtils"], "install_type": "git-clone", "description": "A collection of utility nodes designed for ComfyUI, offering convenient image processing tools.\nNOTE: The files in the repo are not organized.\nNOTE: The files in the repo are not organized."}, {"author": "krich-cto", "title": "ComfyUI Flow Control [UNSTABLE]", "reference": "https://github.com/krich-cto/ComfyUI-Flow-Control", "files": ["https://github.com/krich-cto/ComfyUI-Flow-Control"], "install_type": "git-clone", "description": "This is an Extension for ComfyUI. This project will help you control the flow logic via many controls.[w/Installing this custom node currently causes a conflict with the UnetLoaderGGUF of ComfyUI-GGUF.]"}, {"author": "<PERSON><PERSON>", "title": "ComfyUI Random Keypoints for InstantID [WIP]", "reference": "https://github.com/dihan/comfyui-random-kps", "files": ["https://github.com/dihan/comfyui-random-kps"], "install_type": "git-clone", "description": "A custom node for ComfyUI that generates random facial keypoints compatible with InstantID.\nNOTE: The files in the repo are not organized."}, {"author": "<PERSON><PERSON><PERSON><PERSON>", "title": "ComfyUI-FasterLivePortrait", "reference": "https://github.com/emranemran/ComfyUI-FasterLivePortrait", "files": ["https://github.com/emranemran/ComfyUI-FasterLivePortrait"], "install_type": "git-clone", "description": "Improve mouth tracking with live AI Video"}, {"author": "kandy", "title": "ComfyUI-KAndy", "reference": "https://github.com/kandy/ComfyUI-KAndy", "files": ["https://github.com/kandy/ComfyUI-KAndy"], "install_type": "git-clone", "description": "NODES: Civit Prompt API, Load Image From Url, Civit Images API, KAndyNoiseCondition, KAndyImagesByCss"}, {"author": "StartHua", "title": "Comfyui_leffa", "reference": "https://github.com/StartHua/Comfyui_leffa", "files": ["https://github.com/StartHua/Comfyui_leffa"], "install_type": "git-clone", "description": "NODES: <PERSON><PERSON><PERSON>_Leffa_Viton_Load, C<PERSON><PERSON>_Leffa_Viton_Run"}, {"author": "logtd", "title": "ComfyUI-HunyuanLoom [WIP]", "id": "comfyui-42lux", "reference": "https://github.com/logtd/ComfyUI-HunyuanLoom", "files": ["https://github.com/logtd/ComfyUI-HunyuanLoom"], "install_type": "git-clone", "description": "A set of nodes to edit videos using the Hunyuan Video model"}, {"author": "<PERSON><PERSON><PERSON>", "title": "ComfyUI-exit [UNSAFE]", "reference": "https://github.com/watarika/ComfyUI-exit", "files": ["https://github.com/watarika/ComfyUI-exit"], "install_type": "git-clone", "description": "A custom node that terminates ComfyUI after a specified number of seconds. Use this node if you want Google Colab to automatically terminate after mass generation. It is necessary to disconnect and delete the Google Colab runtime on the Notebook side."}, {"author": "Eagle-CN", "title": "ComfyUI-Addoor [UNSAFE]", "reference": "https://github.com/Eagle-CN/ComfyUI-Addoor", "files": ["https://github.com/Eagle-CN/ComfyUI-Addoor"], "install_type": "git-clone", "description": "NODES: AD_BatchImageLoadFromDir, AD_DeleteLocalAny, AD_TextListToString, AD_AnyFileList, AD_ZipSave, AD_ImageSaver, AD_FluxTrainStepMath, AD_TextSaver, AD_PromptReplace.\nNOTE: This node pack includes nodes that can delete arbitrary files."}, {"author": "backearth1", "title": "Comfyui-MiniMax-Video [WIP]", "reference": "https://github.com/backearth1/Comfyui-MiniMax-Video", "files": ["https://github.com/backearth1/Comfyui-MiniMax-Video"], "install_type": "git-clone", "description": "A ComfyUI extension that integrates MiniMax AI's image-to-video and text-to-video generation capabilities, allowing users to easily convert static images into dynamic videos.\nNOTE: The files in the repo are not organized."}, {"author": "FinetunersAI", "title": "Fast Group Link [WIP]", "id": "fast-group-link", "reference": "https://github.com/FinetunersAI/comfyui-fast-group-link", "files": ["https://github.com/FinetunersAI/comfyui-fast-group-link"], "install_type": "git-clone", "description": "Link and control ComfyUI groups with a simple ON/OFF toggle. Control multiple groups at once with an easy-to-use interface.\nNOTE: The files in the repo are not organized."}, {"author": "kijai", "title": "ComfyUI-MMAudio", "reference": "https://github.com/kijai/ComfyUI-MMAudio", "files": ["https://github.com/kijai/ComfyUI-MMAudio"], "install_type": "git-clone", "description": "ComfyUI nodes to use [a/MMAudio](https://github.com/hkchengrex/MMAudio)"}, {"author": "<PERSON><PERSON><PERSON><PERSON>", "title": "ComfyUI-SD-Slicer", "reference": "https://github.com/kuschanow/ComfyUI-SD-Slicer", "files": ["https://github.com/kuschanow/ComfyUI-SD-Slicer"], "install_type": "git-clone", "description": "NODES: Slicer"}, {"author": "ralonso<PERSON><PERSON>", "title": "ComfyUI-HDRConversion [WIP]", "reference": "https://github.com/ralonsobeas/ComfyUI-HDRConversion", "files": ["https://github.com/ralonsobeas/ComfyUI-HDRConversion"], "install_type": "git-clone", "description": "NODES: Generate HDR image"}, {"author": "Matrix-King-Studio", "title": "ComfyUI-MoviePy", "reference": "https://github.com/Matrix-King-Studio/ComfyUI-MoviePy", "files": ["https://github.com/Matrix-King-Studio/ComfyUI-MoviePy"], "install_type": "git-clone", "description": "NODES: Image Clip Node, Audio Duration Node, Save Video Node"}, {"author": "oxysoft", "title": "ComfyUI-uiapi", "reference": "https://github.com/oxysoft/ComfyUI-uiapi", "files": ["https://github.com/oxysoft/ComfyUI-uiapi"], "install_type": "git-clone", "description": "UIAPI is an intermediate and frontend plugin which allow communicating with the Comfy webui through server connection. This saves the need to export a workflow.json and instead directly sending a queue command to the frontend. This way, the user can experiment in realtime as they are running some professional industry or rendering software which uses UIAPI / ComfyUI as a backend. There is no way to switch seamlessly between UIAPI and regular server connection - though as of late summer 2023 it was inferior to use the server connection because the server would constantly unload models and start from scratch, and the schema of the workfow json was completely different and much less convenient, losing crucial information for efficient querying of nodes and assigning data dynamically."}, {"author": "esciron", "title": "ComfyUI-HunyuanVideoWrapper-Extended [WIP]", "reference": "https://github.com/esciron/ComfyUI-HunyuanVideoWrapper-Extended", "files": ["https://github.com/esciron/ComfyUI-HunyuanVideoWrapper-Extended"], "install_type": "git-clone", "description": "Extended ComfyUI wrapper nodes for [a/HunyuanVideo](https://github.com/Tencent/HunyuanVideo)"}, {"author": "hotpot-killer", "title": "ComfyUI_AlexNodes", "reference": "https://github.com/hotpot-killer/ComfyUI_AlexNodes", "files": ["https://github.com/hotpot-killer/ComfyUI_AlexNodes"], "install_type": "git-clone", "description": "NODES: InstructPG - editing images with text prompt, ...\nNOTE: The files in the repo are not organized."}, {"author": "ps<PERSON><PERSON><PERSON>l", "title": "ComfyUI-StreamDiffusion", "reference": "https://github.com/pschroedl/ComfyUI-StreamDiffusion", "files": ["https://github.com/pschroedl/ComfyUI-StreamDiffusion"], "install_type": "git-clone", "description": "NODES: StreamDiffusionConfig, StreamDiffusionAccelerationSampler, StreamDiffusionLoraLoader, StreamDiffusionAccelerationConfig, StreamDiffusionSimilarityFilterConfig, StreamDiffusionModelLoader, ..."}, {"author": "c0ffymachyne", "title": "ComfyUI Signal Processing [WIP]", "reference": "https://github.com/c0ffymachyne/ComfyUI_SignalProcessing", "files": ["https://github.com/c0ffymachyne/ComfyUI_SignalProcessing"], "install_type": "git-clone", "description": "This repo contains signal processing nodes for ComfyUI allowing for audio manipulation."}, {"author": "Junst", "title": "ComfyUI-PNG2SVG2PNG", "reference": "https://github.com/Junst/ComfyUI-PNG2SVG2PNG", "files": ["https://github.com/Junst/ComfyUI-PNG2SVG2PNG"], "description": "NODES:PNG2SVG2PNG", "install_type": "git-clone"}, {"author": "animEEEmpire", "title": "ComfyUI-Animemory-Loader", "reference": "https://github.com/animEEEmpire/ComfyUI-Animemory-Loader", "files": ["https://github.com/animEEEmpire/ComfyUI-Animemory-Loader"], "install_type": "git-clone", "description": "AniMemory-alpha Custom Node for ComfyUI"}, {"author": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "title": "ComfyUI-Mojen-Nodeset", "reference": "https://github.com/ShahFaisalWani/ComfyUI-Mojen-Nodeset", "files": ["https://github.com/ShahFaisalWani/ComfyUI-Mojen-Nodeset"], "install_type": "git-clone", "description": "A collection of powerful, versatile, and community-driven custom nodes for ComfyUI, designed to elevate AI workflows!"}, {"author": "kijai", "title": "ComfyUI-HunyuanVideoWrapper [WIP]", "reference": "https://github.com/kijai/ComfyUI-HunyuanVideoWrapper", "files": ["https://github.com/kijai/ComfyUI-HunyuanVideoWrapper"], "install_type": "git-clone", "description": "ComfyUI wrapper nodes for [a/HunyuanVideo](https://github.com/Tencent/HunyuanVideo)"}, {"author": "grimli333", "title": "ComfyUI_Grim", "reference": "https://github.com/grimli333/ComfyUI_Grim", "files": ["https://github.com/grimli333/ComfyUI_Grim"], "install_type": "git-clone", "description": "NODES: Generate a unique filename and folder name, Format Strings with Two Inputs"}, {"author": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "title": "ComfyUI_FocusMask", "reference": "https://github.com/risunobushi/ComfyUI_FocusMask", "files": ["https://github.com/risunobushi/ComfyUI_FocusMask"], "install_type": "git-clone", "description": "NODES: Extract Focus Mask"}, {"author": "<PERSON><PERSON><PERSON><PERSON>", "title": "comfyui-oss-image-save [WIP]", "reference": "https://github.com/RicherdLee/comfyui-oss-image-save", "files": ["https://github.com/RicherdLee/comfyui-oss-image-save"], "install_type": "git-clone", "description": "NODES: SaveImageOSS."}, {"author": "Matrix-King-Studio", "title": "ComfyUI-MoviePy", "reference": "https://github.com/Matrix-King-Studio/ComfyUI-MoviePy", "files": ["https://github.com/Matrix-King-Studio/ComfyUI-MoviePy"], "install_type": "git-clone", "description": "NODES: Image Clip Node, Audio Duration Node, Save Video Node,..."}, {"author": "Big Idea Technology", "title": "ComfyUI-Movie-Tools [WIP]", "reference": "https://github.com/Big-Idea-Technology/ComfyUI-Movie-Tools", "files": ["https://github.com/Big-Idea-Technology/ComfyUI-Movie-Tools"], "install_type": "git-clone", "description": "Movie Tools is a set of custom nodes, designed to simplify saving and loading batches of images with enhanced functionality like subfolder management and batch image handling."}, {"author": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "title": "comfyui-face-remap [WIP]", "reference": "https://github.com/ArthusLiang/comfyui-face-remap", "files": ["https://github.com/ArthusLiang/comfyui-face-remap"], "install_type": "git-clone", "description": "NODES: FaceRemap\nNOTE: The files in the repo are not organized."}, {"author": "trithemius", "title": "ComfyUI-SmolVLM [WIP]", "reference": "https://github.com/mamorett/ComfyUI-SmolVLM", "files": ["https://github.com/mamorett/ComfyUI-SmolVLM"], "install_type": "git-clone", "description": "Nodes to use SmolVLM for image tagging and captioning.\nNOTE: The files in the repo are not organized."}, {"author": "anze", "title": "ComfyUI-OIDN [WIP]", "reference": "https://github.com/Anze-/ComfyUI-OIDN", "files": ["https://github.com/Anze-/ComfyUI-OIDN"], "install_type": "git-clone", "description": "ComyUI wrapper for Intel OIDN image denoising\nWARNING! : this is a development repo, usage in production environments is not advised! Bugs are to be expected."}, {"author": "techzuhaib", "title": "ComfyUI-CacheImageNode", "reference": "https://github.com/techzuhaib/ComfyUI-CacheImageNode", "files": ["https://github.com/techzuhaib/ComfyUI-CacheImageNode"], "install_type": "git-clone", "description": "NODES: CacheImageNode"}, {"author": "hay86", "title": "ComfyUI AceNodes [UNSAFE]", "reference": "https://github.com/hay86/ComfyUI_AceNodes", "files": ["https://github.com/hay86/ComfyUI_AceNodes"], "install_type": "git-clone", "description": "Some useful custom nodes that are not included in ComfyUI core yet.\nNOTE: Vulnerability discovered. Not being managed."}, {"author": "<PERSON>wands", "title": "AddMaskForICLora", "reference": "https://github.com/dowands/ComfyUI-AddMaskForICLora", "files": ["https://github.com/dowands/ComfyUI-AddMaskForICLora"], "install_type": "git-clone", "description": "NODES: Add Mask For IC Lora x"}, {"author": "exectails", "title": "Scripting", "id": "et_scripting [UNSAFE]", "reference": "https://github.com/exectails/comfyui-et_scripting", "files": ["https://github.com/exectails/comfyui-et_scripting"], "install_type": "git-clone", "description": "Nodes that can be used to write Python scripts directly on a node. Useful for quick prototyping and testing, at the cost of security.[w/This extension allows the execution of arbitrary Python code from a workflow.]"}, {"author": "AIFSH", "title": "UltralightDigitalHuman-ComfyUI", "reference": "https://github.com/AIFSH/UltralightDigitalHuman-ComfyUI", "files": ["https://github.com/AIFSH/UltralightDigitalHuman-ComfyUI"], "install_type": "git-clone", "description": "a custom node for [a/Ultralight-Digital-Human](https://github.com/anliyuan/Ultralight-Digital-Human)\nNOTE: The files in the repo are not organized."}, {"author": "StartHua", "title": "Comfyui_Flux_Style_Ctr [WIP]", "reference": "https://github.com/StartHua/Comfyui_Flux_Style_Ctr", "files": ["https://github.com/StartHua/Comfyui_Flux_Style_Ctr"], "install_type": "git-clone", "description": "NODES:CXH_StyleModelApply\nNOTE: The files in the repo are not organized."}, {"author": "miragecoa", "title": "ComfyUI-LLM-Evaluation [WIP]", "reference": "https://github.com/miragecoa/ComfyUI-LLM-Evaluation", "files": ["https://github.com/miragecoa/ComfyUI-LLM-Evaluation"], "install_type": "git-clone", "description": "NODES:Load File, Select Item by Index, Select Item by Key, JSONToListNode, MathOperationNode, F1ScoreNode, AccuracyNode, ..."}, {"author": "WASasquatch", "title": "ASTERR [UNSAFE]", "id": "asterr", "reference": "https://github.com/WASasquatch/ASTERR", "files": ["https://github.com/WASasquatch/ASTERR"], "install_type": "git-clone", "description": "Abstract Syntax Trees Evaluated Restricted Run (ASTERR) is a Python Script executor for ComfyUI. [w/Warning:ASTERR runs Python Code from a Web Interface! It is highly recommended to run this in a closed-off environment, as it could have potential security risks.]"}, {"author": "BenjaMITM", "title": "ComfyUI_On_The_Fly_Wildcards [WIP]", "reference": "https://github.com/BenjaMITM/ComfyUI_On_The_Fly_Wildcards", "files": ["https://github.com/BenjaMITM/ComfyUI_On_The_Fly_Wildcards"], "install_type": "git-clone", "description": "NODES:<PERSON>card Creator, Wildcard Loader, Wildcard Selector, Display String.\nNOTE: The files in the repo are not organized."}, {"author": "celll1", "title": "cel_sampler [WIP]", "reference": "https://github.com/celll1/cel_sampler", "files": ["https://github.com/celll1/cel_sampler"], "install_type": "git-clone", "description": "NODES:Latent Value Tracker\nNOTE: The files in the repo are not organized."}, {"author": "DataCTE", "title": "ComfyUI-DataVoid-nodes [WIP]", "reference": "https://github.com/DataCTE/ComfyUI-DataVoid-nodes", "files": ["https://github.com/DataCTE/ComfyUI-DataVoid-nodes"], "install_type": "git-clone", "description": "A collection of custom nodes for ComfyUI focused on model merging and style adaptation.[w/It may cause a lot of node conflicts with comfyui_ipadapter_plus.]"}, {"author": "minhtuannhn", "title": "comfyui-gemini-studio [WIP]", "reference": "https://github.com/minhtuannhn/comfyui-gemini-studio", "files": ["https://github.com/minhtuannhn/comfyui-gemini-studio"], "install_type": "git-clone", "description": "comfyui-gemini-studio[w/This extension uses the legacy method of copying JS.]"}, {"author": "artem-k<PERSON>v<PERSON><PERSON>", "title": "ComfyUI Video Processing Nodes [WIP]", "reference": "https://github.com/artem-konevskikh/comfyui-split-merge-video", "files": ["https://github.com/artem-konevskikh/comfyui-split-merge-video"], "install_type": "git-clone", "description": "Custom nodes for ComfyUI that add video splitting and merging capabilities with crossfade transitions."}, {"author": "Poseidon-fan", "title": "ComfyUI-fileCleaner [UNSAFE]", "reference": "https://github.com/Poseidon-fan/ComfyUI-fileCleaner", "files": ["https://github.com/Poseidon-fan/ComfyUI-fileCleaner"], "install_type": "git-clone", "description": "In production environments, images are usually saved on storage servers such as S3, rather than local folders. So the method of placing images in local folders using ComfyUI's native LoadImage and SaveImage nodes cannot be used as a deployment service method, but can only be used as a temporary storage place for images. This requires a way to delete images from the input and output folders.\nThis node is used to delete images from the input and output folders. It is recommended to use this node through api call in the backend after the image processing is completed.[w/Users can use the file deletion feature through the workflow.]"}, {"author": "yorkane", "title": "Comfy UI Robe Nodes [UNSAFE]", "reference": "https://github.com/RobeSantoro/ComfyUI-RobeNodes", "files": ["https://github.com/RobeSantoro/ComfyUI-RobeNodes"], "install_type": "git-clone", "description": "NODES: List Video Path Node, List Image Path Node\nThis is a collection of utility nodes for the ComfyUI stable diffusion client that provides enhanced file path handling capabilities.[w/Users will be able to access images from arbitrary paths through the workflow.]"}, {"author": "Kimara.ai", "title": "Advanced Watermarking Tools [WIP]", "reference": "https://github.com/kimara-ai/ComfyUI-Kimara-AI-Advanced-Watermarks", "files": ["https://github.com/kimara-ai/ComfyUI-Kimara-AI-Advanced-Watermarks"], "install_type": "git-clone", "description": "The KimaraAIWatermarker custom node allows you to apply watermark text and logo overlays to images (or a batch of images). It provides features like customizable watermark movement, rotation, and opacity. You can also apply both text and logo watermarks simultaneously, with fine-tuned control over positioning and scaling."}, {"author": "<PERSON><PERSON><PERSON>", "title": "ComfyUI-FluxDeCLIP", "reference": "https://github.com/Clybius/ComfyUI-FluxDeCLIP", "files": ["https://github.com/Clybius/ComfyUI-FluxDeCLIP"], "install_type": "git-clone", "description": "NODES:FluxDeCLIPCheckpointLoader"}, {"author": "ZHO-ZHO-ZHO", "title": "ComfyUI-BiRefNet-ZHO [BROKEN]", "id": "birefnet", "reference": "https://github.com/ZHO-ZHO-ZHO/ComfyUI-BiRefNet-ZHO", "files": ["https://github.com/ZHO-ZHO-ZHO/ComfyUI-BiRefNet-ZHO"], "install_type": "git-clone", "description": "Better version for [a/BiRefNet](https://github.com/zhengpeng7/birefnet) in ComfyUI | Both img and video.\nNOTE: You need to do [a/manual patch](https://github.com/ZHO-ZHO-ZHO/ComfyUI-BiRefNet-ZHO/issues/20)"}, {"author": "trashgraphicard", "title": "Albedo-Sampler-for-ComfyUI", "reference": "https://github.com/trashgraphicard/Albedo-Sampler-for-ComfyUI", "files": ["https://github.com/trashgraphicard/Albedo-Sampler-for-ComfyUI"], "install_type": "git-clone", "description": "NODES:Sample Image, Make Seamless Tile"}, {"author": "Anze-", "title": "ComfyUI_deepDeband [WIP]", "reference": "https://github.com/Anze-/ComfyUI_deepDeband", "files": ["https://github.com/Anze-/ComfyUI_deepDeband"], "install_type": "git-clone", "description": "ComyUI wrapper for RaymondLZhou/deepDeband image and video debanding\nNOTE: The files in the repo are not organized."}, {"author": "bmad4ever", "title": "Bmad <PERSON> [UNSAFE]", "id": "bmad", "reference": "https://github.com/bmad4ever/comfyui_bmad_nodes", "files": ["https://github.com/bmad4ever/comfyui_bmad_nodes"], "install_type": "git-clone", "description": "This custom node offers the following functionalities: API support for setting up API requests, computer vision primarily for masking or collages, and general utility to streamline workflow setup or implement essential missing features.\nNOTE: Vulnerability discovered. Not being managed."}, {"author": "suncat2ps", "title": "ComfyUI-SaveImgNextcloud", "reference": "https://github.com/suncat2ps/ComfyUI-SaveImgNextcloud", "files": ["https://github.com/suncat2ps/ComfyUI-SaveImgNextcloud"], "install_type": "git-clone", "description": "NODES: Save Image to Nextcloud"}, {"author": "KoreTeknology", "title": "ComfyUI Production Nodes Pack [WIP]", "reference": "https://github.com/KoreTeknology/ComfyUI-Nai-Production-Nodes-Pack", "files": ["https://github.com/KoreTeknology/ComfyUI-Nai-Production-Nodes-Pack"], "description": "This is set of custom nodes for your ComfyUI1 production setup. It offers the very basic nodes that are missing in the official 'Vanilla' package. It is a research Node based project on Artificial Intelligence using ComfyUI visual editor. This repository also includes a set of workflows to test the nodes.", "install_type": "git-clone"}, {"author": "<PERSON><PERSON><PERSON><PERSON>", "title": "ComfyUI-Flashback", "reference": "https://github.com/DoctorDiffusion/ComfyUI-Flashback", "files": ["https://github.com/DoctorDiffusion/ComfyUI-Flashback"], "description": "NODES:Latent Export, Latent Import, Latent Loop", "install_type": "git-clone"}, {"author": "sswink", "title": "comfy<PERSON>-l<PERSON><PERSON>", "reference": "https://github.com/sswink/comfyui-lingshang", "files": ["https://github.com/sswink/comfyui-lingshang"], "description": "NODES:LS_SaveImageToOss, LS_LoadMaskFromUrl, LS_DigImageByMask, LS_ALY_Seg_Utils, LS_ALY_UploadToOssAndGetUrl, LS_GrowMaskWithBlur, LS_ALY_Seg_Body_Utils, LS_ALY_Seg_Common_Utils, LS_ALY_Seg_Clothes_Utils, LS_ALY_Seg_Body_Utils_Return_crop, ...", "install_type": "git-clone"}, {"author": "AICodeFactory", "title": "ComfyUI-Viva", "reference": "https://github.com/AICodeFactory/ComfyUI-Viva", "files": ["https://github.com/AICodeFactory/ComfyUI-Viva"], "description": "NODES:<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> (Viva), <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> (Image), <PERSON><PERSON><PERSON><PERSON>rigger (Common)", "install_type": "git-clone"}, {"author": "LogicAI", "title": "ComfyUI-MagicAI [UNSAFE]", "reference": "https://github.com/lcolok/ComfyUI-MagicAI", "files": ["https://github.com/lcolok/ComfyUI-MagicAI"], "install_type": "git-clone", "description": "NODES:Mask Size Calculator (MagicAI), Universal Mask Converter (MagicAI), Python Execution (MagicAI), Extract JSON From Text Node(MagicAI)\n[w/This extension allows the execution of arbitrary Python code from a workflow.]"}, {"author": "Laser-one", "title": "ComfyUI-align-pose", "reference": "https://github.com/Laser-one/ComfyUI-align-pose", "files": ["https://github.com/Laser-one/ComfyUI-align-pose"], "install_type": "git-clone", "description": "NODES:align pose"}, {"author": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "title": "ComfyUI_StepFun", "reference": "https://github.com/chenbaiyujason/ComfyUI_StepFun", "files": ["https://github.com/chenbaiyujason/ComfyUI_StepFun"], "install_type": "git-clone", "description": "To use stepfun's library, you need an official api that supports multimodal inputs such as video and pictures [a/https://platform.stepfun.com/request-restriction](https://platform.stepfun.com/request-restriction)"}, {"author": "aria1th", "title": "ComfyUI-SkipCFGSigmas", "reference": "https://github.com/aria1th/ComfyUI-SkipCFGSigmas", "files": ["https://github.com/aria1th/ComfyUI-SkipCFGSigmas"], "install_type": "git-clone", "description": "NODES: CFGControl_SKIPCFG"}, {"author": "Clelstyn", "title": "ComfyUI-Inpaint_with_Detailer", "reference": "https://github.com/Clelstyn/ComfyUI-Inpaint_with_<PERSON>ailer", "files": ["https://github.com/Clelstyn/ComfyUI-Inpaint_with_<PERSON>ailer"], "install_type": "git-clone", "description": "NODES:Masked Resize Image, Paste Masked Image, Filter And Blur Mask"}, {"author": "Looking-Glass", "title": "LKG-ComfyUI", "reference": "https://github.com/Looking-Glass/LKG-ComfyUI", "files": ["https://github.com/Looking-Glass/LKG-ComfyUI"], "install_type": "git-clone", "description": "NODES:Side by Side Node, Bridge Preview Node, <PERSON><PERSON>, Scale Maintain Aspect Ratio Node, "}, {"author": "<PERSON>ia<PERSON><PERSON><PERSON>", "title": "ComfyUI-XYNodes", "reference": "https://github.com/xiaoyumu/ComfyUI-XYNodes", "files": ["https://github.com/xiaoyumu/ComfyUI-XYNodes"], "install_type": "git-clone", "description": "Nodes:PrimitiveBBOX."}, {"author": "<PERSON><PERSON><PERSON>", "title": "etm_comfyui_nodes", "reference": "https://github.com/ainanoha/etm_comfyui_nodes", "files": ["https://github.com/ainanoha/etm_comfyui_nodes"], "install_type": "git-clone", "description": "NODES:LETM Save Image, ETM Load Image From Local"}, {"author": "m-ai-studio", "title": "mai-prompt-progress", "reference": "https://github.com/m-ai-studio/mai-prompt-progress", "files": ["https://github.com/m-ai-studio/mai-prompt-progress"], "install_type": "git-clone", "description": "ComfyUI extensions for sending prompt progress to webhook"}, {"author": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "title": "ComfyUI-TempFileDeleter [UNSAFE]", "reference": "https://github.com/neeltheninja/ComfyUI-TempFileDeleter", "files": ["https://github.com/neeltheninja/ComfyUI-TempFileDeleter"], "install_type": "git-clone", "description": "This node is designed to streamline your workflow in ComfyUI by efficiently cleaning up temporary files on each run. This node takes no input. You can specify 'on' or 'off' in the node itself, or just bypass to not use use it.[w/This node can delete any files in the folder mentioned in 'folder_path' in the node. Be aware of this and change the folder path correctly before running any workflow with this node. I will NOT be responsible for wrongly deleted files because you didn't read this beforehand.]"}, {"author": "kylegrover", "title": "comfyui-python-cowboy [UNSAFE]", "reference": "https://github.com/kylegrover/comfyui-python-cowboy", "files": ["https://github.com/kylegrover/comfyui-python-cowboy"], "install_type": "git-clone", "description": "run python code in comfyui\nuses codemirror for nice syntax highlighting\nNOTE: based on ComfyUI-nidefawl[w/This node is an unsafe node that includes the capability to execute arbitrary python script.]"}, {"author": "kijai", "title": "ComfyUI-MochiWrapper [WIP]", "reference": "https://github.com/kijai/ComfyUI-MochiWrapper", "files": ["https://github.com/kijai/ComfyUI-MochiWrapper"], "install_type": "git-clone", "description": "ComfyUI wrapper nodes for [a/<PERSON><PERSON>](https://github.com/genmoai/models) video generator"}, {"author": "kk8bit", "title": "KayTool", "reference": "https://github.com/kk8bit/KayTool", "files": ["https://github.com/kk8bit/KayTool"], "install_type": "git-clone", "description": "KayTool is a custom node utility package developed for ComfyUI. I plan to add more features in the future."}, {"author": "leadbreak", "title": "Face Aging [WIP]", "reference": "https://github.com/leadbreak/comfyui-faceaging", "files": ["https://github.com/leadbreak/comfyui-faceaging"], "install_type": "git-clone", "description": "This is a comfyui custom node version of [a/Age Transformation](https://github.com/yuval-alaluf/SAM).\nNOTE: The files in the repo are not organized."}, {"author": "downlifted", "title": "ComfyUI_BWiZ_Nodes [WIP]", "reference": "https://github.com/downlifted/ComfyUI_BWiZ_Nodes", "files": ["https://github.com/downlifted/ComfyUI_BWiZ_Nodes"], "install_type": "git-clone", "description": "NODES:Captain<PERSON><PERSON><PERSON><PERSON>, Captain<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, Captain<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>_AdvancedLoadImageBatch, B<PERSON><PERSON>_ErrorDetector, BWIZ_HFRepoBatchLoader, BWIZ_NotificationSound.\nNOTE: The files in the repo are not organized."}, {"author": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "title": "ComfyUI-FRED-Nodes [WIP]", "reference": "https://github.com/Poukpalaova/ComfyUI-FRED-Nodes", "files": ["https://github.com/Poukpalaova/ComfyUI-FRED-Nodes"], "install_type": "git-clone", "description": "Multiple nodes that ease the process.\nNOTE: The files in the repo are not organized."}, {"author": "<PERSON><PERSON><PERSON>", "title": "cozy-fireplace [WIP]", "reference": "https://github.com/blurymind/cozy-fireplace", "files": ["https://github.com/blurymind/cozy-fireplace"], "install_type": "git-clone", "description": "Cozy fireplace is a ComfyUI workflow prompter that brings a localhost server frontend for existing workflows created in ComfyUi. Just place your favorite or lovingly crafted workflows in a folder and cozy fireplace will let you select and run any of them (export them as API type in comfyui) It's a cozy UI that scales all the way down to mobile phone devices - to let you prompt your beefy pc at home with your smartphone."}, {"author": "<PERSON><PERSON><PERSON><PERSON>", "title": "xcpNodes [WIP]", "reference": "https://github.com/lordwedggie/xcpNodes", "files": ["https://github.com/lordwedggie/xcpNodes"], "install_type": "git-clone", "description": "Slider nodes based on Smirnov75's codes [a/https://github.com/Smirnov75/ComfyUI-mxToolkit](https://github.com/Smirnov75/ComfyUI-mxToolkit)\nNOTE: The files in the repo are not organized."}, {"author": "kxh", "title": "ComfyUI-ImageUpscaleWithModelMultipleTimes", "reference": "https://github.com/kxh/ComfyUI-ImageUpscaleWithModelMultipleTimes", "files": ["https://github.com/kxh/ComfyUI-ImageUpscaleWithModelMultipleTimes"], "install_type": "git-clone", "description": "Upscale image with model multiple times !"}, {"author": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "title": "comfyui-rxmt-nodes", "reference": "https://github.com/rouxianmantou/comfyui-rxmt-nodes", "files": ["https://github.com/rouxianmantou/comfyui-rxmt-nodes"], "install_type": "git-clone", "description": "NODES:Check Value Type, Why Prompt Text"}, {"author": "<PERSON><PERSON><PERSON><PERSON>", "title": "SirVeggie/Custom nodes for ComfyUI", "reference": "https://github.com/SirVeggie/comfyui-sv-nodes", "files": ["https://github.com/SirVeggie/comfyui-sv-nodes"], "install_type": "git-clone", "description": "NODES:SV-SimpleText, SV-PromptProcessing, SV-PromptProcessingRecursive, SV-PromptProcessingAdvanced, SV-PromptProcessingEncode,..."}, {"author": "artisanalcomputing", "title": "artcpu-custom-nodes", "reference": "https://github.com/artisanalcomputing/ComfyUI-Custom-Nodes", "files": ["https://github.com/artisanalcomputing/ComfyUI-Custom-Nodes"], "install_type": "git-clone", "description": "NODES:Random Video Mixer, Spotify Canvas Generator, Video Writer\ncustom comfyui nodes for audio/visual purposes# ComfyUI-Custom-Nodes"}, {"author": "kxh", "title": "ComfyUI-sam2", "reference": "https://github.com/kxh/ComfyUI-sam2", "files": ["https://github.com/kxh/ComfyUI-sam2"], "install_type": "git-clone", "description": "use semantic tag to segment any element in an image, output a mask.\nNOTE: Repo name is conflicting with neverbiasu/ComfyUI-SAM2"}, {"author": "AIFSH", "title": "UtilNodes-ComfyUI [WIP]", "reference": "https://github.com/AIFSH/UtilNodes-ComfyUI", "files": ["https://github.com/AIFSH/UtilNodes-ComfyUI"], "install_type": "git-clone", "description": "here put custom input nodes such as text,video...\nNOTE: The files in the repo are not organized."}, {"author": "fablestudio", "title": "ComfyUI-Showrunner-U<PERSON><PERSON>", "reference": "https://github.com/fablestudio/ComfyUI-Showrunner-Utils", "files": ["https://github.com/fablestudio/ComfyUI-Showrunner-Utils"], "install_type": "git-clone", "description": "NODES: Align Face, Generate Timestamp, GetMostCommonColors, Alpha Crop and Position Image, Shrink Image"}, {"author": "monate0615", "title": "ComfyUI-Simple-Image-Tools [WIP]", "reference": "https://github.com/gondar-software/ComfyUI-Simple-Image-Tools", "files": ["https://github.com/gondar-software/ComfyUI-Simple-Image-Tools"], "install_type": "git-clone", "description": "Get mask from image based on alpha (Get Mask From Alpha)\nNOTE: The files in the repo are not organized."}, {"author": "galoreware", "title": "ComfyUI-GaloreNodes [WIP]", "reference": "https://github.com/galoreware/ComfyUI-GaloreNodes", "files": ["https://github.com/galoreware/ComfyUI-GaloreNodes"], "install_type": "git-clone", "description": "Color and Image related nodes for ComfyUI."}, {"author": "lgldlk", "title": "ComfyUI-img-tiler", "reference": "https://github.com/lgldlk/ComfyUI-img-tiler", "files": ["https://github.com/lgldlk/ComfyUI-img-tiler"], "install_type": "git-clone", "description": "NODES:<PERSON><PERSON><PERSON><PERSON>, TilerSelect, TileMaker, ImageListTileMaker"}, {"author": "SSsnap", "title": "Snap Processing for Comfyui", "reference": "https://github.com/SS-snap/ComfyUI-Snap_Processing", "files": ["https://github.com/SS-snap/ComfyUI-Snap_Processing"], "install_type": "git-clone", "description": "for preprocessing images, presented in a visual way. It also calculates the corresponding image area."}, {"author": "cwebbi1", "title": "VoidCustomNodes", "reference": "https://github.com/cwebbi1/VoidCustomNodes", "files": ["https://github.com/cwebbi1/VoidCustomNodes"], "install_type": "git-clone", "description": "NODES:Prompt <PERSON>, String Combiner"}, {"author": "w<PERSON><PERSON><PERSON><PERSON><PERSON>", "title": "Comfyui-zZzZz [UNSAFE]", "reference": "https://github.com/wilzamguerrero/Comfyui-zZzZz", "files": ["https://github.com/wilzamguerrero/Comfyui-zZzZz"], "install_type": "git-clone", "description": "NODES:<PERSON><PERSON> <PERSON>, <PERSON><PERSON><PERSON> <PERSON>, <PERSON> Z, <PERSON><PERSON> Z, <PERSON><PERSON>, <PERSON><PERSON> <PERSON>, Infinite Z, Share Screen Z"}, {"author": "monate0615", "title": "Affine Transform ComfyUI Node [WIP]", "reference": "https://github.com/gondar-software/ComfyUI-Affine-Transform", "files": ["https://github.com/gondar-software/ComfyUI-Affine-Transform"], "install_type": "git-clone", "description": "This node output the image that are transfromed by affine matrix what is made according to 4 points of output.\nNOTE: The files in the repo are not organized."}, {"author": "ComfyUI-Workflow", "title": "ComfyUI OpenAI Nodes", "reference": "https://github.com/ComfyUI-Workflow/ComfyUI-OpenAI", "files": ["https://github.com/ComfyUI-Workflow/ComfyUI-OpenAI"], "install_type": "git-clone", "description": "By utilizing OpenAI's powerful vision models, this node enables you to incorporate state-of-the-art image understanding into your ComfyUI projects with minimal setup."}, {"author": "ruka-game", "title": "ComfyUI RukaLib [WIP]", "reference": "https://github.com/ruka-game/rukalib_comfyui", "files": ["https://github.com/ruka-game/rukalib_comfyui"], "install_type": "git-clone", "description": "NODES: <PERSON><PERSON> Prompt Enhancer, <PERSON><PERSON> Debug Probe.\nMy utilities for comfy (WIP / ollama is required for LLM nodes)"}, {"author": "<PERSON><PERSON><PERSON><PERSON>", "title": "ComfyUI-APG_ImYourCFGNow", "reference": "https://github.com/MythicalChu/ComfyUI-APG_ImYourCFGNow", "files": ["https://github.com/MythicalChu/ComfyUI-APG_ImYourCFGNow"], "install_type": "git-clone", "description": "Use this node like a RescaleCFG node, ... modelIn -> ThisNode -> ModelOut ... -> KSampler\n'scale' acts like your CFG, your CFG doesn't do anything anymore white this node is active. See paper [a/https://arxiv.org/pdf/2410.02416](https://arxiv.org/pdf/2410.02416) for instructions about the other parameters. (Pages 20-21)"}, {"author": "okg21", "title": "VLLMVisionChatNode", "reference": "https://github.com/okg21/VLLMVisionChatNode", "files": ["https://raw.githubusercontent.com/okg21/VLLMVisionChatNode/refs/heads/main/VLLMVisionChatNode.py"], "pip": ["openai", "numpy"], "install_type": "copy", "description": "This platform extension provides ZhipuAI nodes, enabling you to configure a workflow for online video generation."}, {"author": "netanelben", "title": "comfyui-photobooth-customnode", "reference": "https://github.com/netanelben/comfyui-photobooth-customnode", "files": ["https://github.com/netanelben/comfyui-photobooth-customnode"], "install_type": "git-clone", "description": "comfyui-photobooth-customnode"}, {"author": "netanelben", "title": "comfyui-text2image-customnode", "reference": "https://github.com/netanelben/comfyui-text2image-customnode", "files": ["https://github.com/netanelben/comfyui-text2image-customnode"], "install_type": "git-clone", "description": "comfyui-text2image-customnode"}, {"author": "netanelben", "title": "comfyui-camera2image-customnode", "reference": "https://github.com/netanelben/comfyui-camera2image-customnode", "files": ["https://github.com/netanelben/comfyui-camera2image-customnode"], "install_type": "git-clone", "description": "comfyui-camera2image-customnode"}, {"author": "netanelben", "title": "comfyui-image2image-customnode", "reference": "https://github.com/netanelben/comfyui-image2image-customnode", "files": ["https://github.com/netanelben/comfyui-image2image-customnode"], "install_type": "git-clone", "description": "comfyui-image2image-customnode"}, {"author": "<PERSON><PERSON><PERSON>", "title": "ComfyUI_BaiKong_Node", "id": "baikong", "reference": "https://github.com/JayLyu/ComfyUI_BaiKong_Node", "files": ["https://github.com/JayLyu/ComfyUI_BaiKong_Node"], "install_type": "git-clone", "description": "Nodes for advanced color manipulation and image processing: BK Img To Color, BK Color Selector, BK Color Contrast, BK Color Limit, BK Color Luminance, BK Gradient Image, and BK Image Aspect Filter.\n[w/requirements.txt is broken.]"}, {"author": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "title": "ComfyUI-FreeMemory", "reference": "https://github.com/ShmuelRonen/ComfyUI-FreeMemory", "files": ["https://github.com/ShmuelRonen/ComfyUI-FreeMemory"], "install_type": "git-clone", "description": "ComfyUI-FreeMemory is a custom node extension for ComfyUI that provides advanced memory management capabilities within your image generation workflows."}, {"author": "ZHO-ZHO-ZHO", "title": "ComfyUI Llama 3.1 [WIP]", "reference": "https://github.com/ZHO-ZHO-ZHO/ComfyUI-Llama-3-2", "files": ["https://github.com/ZHO-ZHO-ZHO/ComfyUI-Llama-3-2"], "install_type": "git-clone", "description": "Using Llama-3-1 in ComfyUI"}, {"author": "netanelben", "title": "comfyui-text2image-customnode [WIP]", "reference": "https://github.com/netanelben/comfyui-text2image-customnode", "files": ["https://github.com/netanelben/comfyui-text2image-customnode"], "install_type": "git-clone", "description": "text2image web extension"}, {"author": "leeguandong", "title": "ComfyUI_AliControlnetInpainting [WIP]", "reference": "https://github.com/leeguandong/ComfyUI_AliControlnetInpainting", "files": ["https://github.com/leeguandong/ComfyUI_AliControlnetInpainting"], "install_type": "git-clone", "description": "ComfyUI nodes to use AliControlnetInpainting"}, {"author": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "title": "ComfyUI_HelpfulNodes", "reference": "https://github.com/jordancoult/ComfyUI_HelpfulNodes", "files": ["https://github.com/jordancoult/ComfyUI_HelpfulNodes"], "install_type": "git-clone", "description": "NODES: Prep Crop Around Keypoints"}, {"author": "<PERSON><PERSON><PERSON><PERSON>", "title": "comfyui_segformer_b2_sleeves", "reference": "https://github.com/ashishsaini/comfyui-segment-clothing-sleeves", "files": ["https://github.com/ashishsaini/comfyui-segment-clothing-sleeves"], "install_type": "git-clone", "description": "NODES:segformer_b2_sleeves"}, {"author": "io-club", "title": "ComfyUI-LuminaNext [WIP]", "reference": "https://github.com/io-club/ComfyUI-LuminaNext", "files": ["https://github.com/io-club/ComfyUI-LuminaNext"], "install_type": "git-clone", "description": "NODES: Gemma<PERSON><PERSON><PERSON><PERSON><PERSON>"}, {"author": "shadowcz007", "title": "Comfyui-EzAudio [WIP]", "reference": "https://github.com/shadowcz007/Comfyui-EzAudio", "files": ["https://github.com/shadowcz007/Comfyui-EzAudio"], "install_type": "git-clone", "description": "NODES: EZ Generate Audio, EZ Load Model\nNOTE: The files in the repo are not organized."}, {"author": "neo0801", "title": "my-comfy-node", "reference": "https://github.com/neo0801/my-comfy-node", "files": ["https://github.com/neo0801/my-comfy-node"], "install_type": "git-clone", "description": "NODES:Deep Mosaic Get Image Mosaic Mask, Deep Mosaic Get Video Mosaic Mask, Deep Mosaic Remove Image Mosaic, Deep Mosaic Remove Video Mosaic"}, {"author": "nikkuexe", "title": "List Data Helper Nodes", "reference": "https://github.com/paulhoux/Smart-Prompting", "files": ["https://github.com/paulhoux/Smart-Prompting"], "install_type": "git-clone", "description": "Custom nodes for ComfyUI, allowing you to more easily manipulate text and create good prompts.[w/The use of outdated front extension techniques results in remnants being left behind during uninstallation.]"}, {"author": "nikkuexe", "title": "List Data Helper Nodes", "reference": "https://github.com/nikkuexe/ComfyUI-ListDataHelpers", "files": ["https://github.com/nikkuexe/ComfyUI-ListDataHelpers"], "install_type": "git-clone", "description": "A set of custom nodes for handling lists in ComfyUI."}, {"author": "Fannovel16", "title": "ComfyUI-AppIO", "reference": "https://github.com/Fannovel16/ComfyUI-AppIO", "files": ["https://github.com/Fannovel16/ComfyUI-AppIO"], "install_type": "git-clone", "description": "NODES:AppIO_StringInput, AppIO_ImageInput, AppIO_StringOutput, AppIO_ImageOutput"}, {"author": "SoftMeng", "title": "ComfyUI-PIL", "reference": "https://github.com/SoftMeng/ComfyUI-PIL", "files": ["https://github.com/SoftMeng/ComfyUI-PIL"], "install_type": "git-clone", "description": "PIL Nodes"}, {"author": "<PERSON><PERSON><PERSON><PERSON>", "title": "comfyui-creative-nodes", "reference": "https://github.com/seancheung/comfyui-creative-nodes", "files": ["https://github.com/seancheung/comfyui-creative-nodes"], "install_type": "git-clone", "description": "NODES:Stop Flow, Skip From Flow, Skip To Flow, Resolution Selector, ResolutionXL Selector"}, {"author": "AlexXi19", "title": "ComfyUI-OpenAINode", "reference": "https://github.com/AlexXi19/ComfyUI-OpenAINode", "files": ["https://github.com/AlexXi19/ComfyUI-OpenAINode"], "install_type": "git-clone", "description": "ComfyUI-OpenAINode is a user-friendly node that serves as an interface to the OpenAI Models.[w/Repo name conflict with Electrofried/ComfyUI-OpenAINode]"}, {"author": "IgPoly", "title": "ComfyUI-igTools", "reference": "https://github.com/IgPoly/ComfyUI-igTools", "files": ["https://github.com/IgPoly/ComfyUI-igTools"], "install_type": "git-clone", "description": "NODES:IGT Simple Tiles Calc"}, {"author": "<PERSON><PERSON><PERSON>", "title": "<PERSON><PERSON><PERSON>'s Nodes", "reference": "https://github.com/lichenhao/Comfyui_<PERSON><PERSON>ta", "files": ["https://github.com/lichenhao/Comfyui_<PERSON><PERSON>ta"], "install_type": "git-clone", "description": "NODES:CombineTexts, FontLoader, DrawText, TxtFileLoader, SaveTxtFile, SwitchModelClip, SwitchAnyInputs, Reroute2, Reroute3"}, {"author": "Soppatorsk", "title": "comfyui_img_to_ascii [WIP]", "reference": "https://github.com/Soppatorsk/comfyui_img_to_ascii", "files": ["https://github.com/Soppatorsk/comfyui_img_to_ascii"], "install_type": "git-clone", "description": "Basic functionality for converting an image to ASCII art returned as a png image based on [a/ascii_magic](https://github.com/LeandroBarone/python-ascii_magic)"}, {"author": "AIFSH", "title": "HivisionIDPhotos-ComfyUI", "reference": "https://github.com/AIFSH/HivisionIDPhotos-ComfyUI", "files": ["https://github.com/AIFSH/HivisionIDPhotos-ComfyUI"], "install_type": "git-clone", "description": "a custom node for [a/HivisionIDPhotos](https://github.com/<PERSON><PERSON><PERSON>-<PERSON>/HivisionIDPhotos).\nNOTE: Unsuitable for international users"}, {"author": "lu64k", "title": "SK-Nodes", "reference": "https://github.com/lu64k/SK-Nodes", "files": ["https://github.com/lu64k/SK-Nodes"], "install_type": "git-clone", "description": "NODES:image select, Load AnyLLM, Ask LLM, OpenAI DAlle Node, SK Text_String, SK Random File Name"}, {"author": "Lilien86", "title": "Comfyui_Latent_Interpolation [WIP]", "reference": "https://github.com/Lilien86/Comfyui_Latent_Interpolation", "files": ["https://github.com/Lilien86/Comfyui_Latent_Interpolation"], "install_type": "git-clone", "description": "Hey everyone it's my Custom ComfyUI Nodes Pack repository! This project contains a collection of custom nodes designed to extend the functionality of ComfyUI. These nodes offer capabilities and new creative possibilities, especially in the realms of latent space manipulation and interpolation.\nNOTE: The files in the repo are not organized."}, {"author": "<PERSON><PERSON><PERSON>", "title": "ComfyUI_Rain", "reference": "https://github.com/haodman/ComfyUI_Rain", "files": ["https://github.com/haodman/ComfyUI_Rain"], "install_type": "git-clone", "description": "NODES:Rain_ValueSwitch, Rain_Math, Rain_IntToFloat, Rain_ImageSize."}, {"author": "bananasss00", "title": "Comfyui-PyExec [UNSAFE]", "reference": "https://github.com/bananasss00/Comfyui-PyExec", "files": ["https://github.com/bananasss00/Comfyui-PyExec"], "install_type": "git-clone", "description": "Nodes:PyExec.[w/This node allows access to arbitrary files through the workflow, which could pose a security threat.]"}, {"author": "jgbrblmd", "title": "ComfyUI-ComfyFluxSize [WIP]", "reference": "https://github.com/jgbrblmd/ComfyUI-ComfyFluxSize", "files": ["https://github.com/jgbrblmd/ComfyUI-ComfyFluxSize"], "install_type": "git-clone", "description": "Nodes:ComfyFlux Size\nNOTE: The files in the repo are not organized."}, {"author": "yojimbodayne", "title": "ComfyUI-Dropbox-API [WIP]", "reference": "https://github.com/yojimbodayne/ComfyUI-Dropbox-API", "files": ["https://github.com/yojimbodayne/ComfyUI-Dropbox-API"], "install_type": "git-clone", "description": "This custom node package for ComfyUI allows users to interact with Dropbox API, enabling image, text, and video uploads, downloads, and management directly from ComfyUI workflows.\nNOTE: The files in the repo are not organized."}, {"author": "ilovejohnwhite", "title": "Ko<PERSON><PERSON> A<PERSON> Prompts [WIP]", "reference": "https://github.com/ilovejohnwhite/Tracer", "files": ["https://github.com/ilovejohnwhite/Tracer"], "install_type": "git-clone", "description": "Nodes:Image Load TTK, SuckerPunch, LinkMasterNode, PixelPerfectResolution, ImageGenResolutionFromImage, ImageGenResolutionFromLatent, HintImageEnchance\nNOTE: The files in the repo are not organized."}, {"author": "shuanshtalon468uan", "title": "ComfyUI-Rpg-Architect [WIP]", "reference": "https://github.com/talon468/ComfyUI-Rpg-Architect", "files": ["https://github.com/talon468/ComfyUI-Rpg-Architect"], "install_type": "git-clone", "description": "Custom Node for ComfyUI to create RPG Characters\nNOTE: The files in the repo are not organized."}, {"author": "shuanshuan", "title": "ComfyUI_CheckPointLoader_Ext [WIP]", "reference": "https://github.com/shuanshuan/ComfyUI_CheckPointLoader_Ext", "files": ["https://github.com/shuanshuan/ComfyUI_CheckPointLoader_Ext"], "install_type": "git-clone", "description": "NODES:Checkpoint Loader Ext"}, {"author": "123<PERSON><PERSON>", "title": "ComfyUI MobileForm [WIP]", "reference": "https://github.com/123jimin/ComfyUI-MobileForm", "files": ["https://github.com/123jimin/ComfyUI-MobileForm"], "install_type": "git-clone", "description": "MobileForm is an extension for ComfyUI, providing simple form for any workflows, suitable for use on mobile phones.[w/Currently MobileForm is in a PoC state; expect bugs and breaking changes.]"}, {"author": "go-package-lab", "title": "ComfyUI-Tools-Video-Combine [WIP]", "reference": "https://github.com/go-package-lab/ComfyUI-Tools-Video-Combine", "files": ["https://github.com/go-package-lab/ComfyUI-Tools-Video-Combine"], "install_type": "git-clone", "description": "NODES:LoadAudioUrl, VideoWatermark"}, {"author": "zhongpei", "title": "Comfyui_image2prompt", "id": "img2prompt", "reference": "https://github.com/zhongpei/Comfyui_image2prompt", "files": ["https://github.com/zhongpei/Comfyui_image2prompt"], "install_type": "git-clone", "description": "Nodes:Image to Text, Loader Image to Text Model.[w/This custom node may break dependencies by reinstalling the torch package.]"}, {"author": "APZmedia", "title": "comfyui-textools [WIP]", "reference": "https://github.com/APZmedia/comfyui-textools", "files": ["https://github.com/APZmedia/comfyui-textools"], "install_type": "git-clone", "description": "ComfyUI-textools is a collection of custom nodes designed for use with ComfyUI. These nodes enhance text processing capabilities, including applying rich text overlays on images and cleaning file names for safe and consistent file management.\nNOTE: The files in the repo are not organized."}, {"author": "Comfy Org", "title": "ComfyUI_devtools [WIP]", "reference": "https://github.com/Comfy-Org/ComfyUI_devtools", "files": ["https://github.com/Comfy-Org/ComfyUI_devtools"], "install_type": "git-clone", "description": "ComfyUI developer tools (Custom Node)"}, {"author": "<PERSON><PERSON><PERSON><PERSON>", "title": "ComfyUI_Save2Discord", "reference": "https://github.com/<PERSON>-nee/ComfyUI_Save2Discord", "files": ["https://github.com/<PERSON>-nee/ComfyUI_Save2Discord"], "install_type": "git-clone", "description": "Nodes:Send Generated Image To Discord Webhook.\nNOTE: The files in the repo are not organized."}, {"author": "ThisModernDay", "title": "ComfyUI Instructor Ollama", "reference": "https://github.com/ThisModernDay/ComfyUI-InstructorOllama", "files": ["https://github.com/ThisModernDay/ComfyUI-InstructorOllama"], "install_type": "git-clone", "description": "Custom ComfyUI Nodes for interacting with Ollama using the Instructor. Library to provide structured output from your LLM. To use this properly, you would need a running Ollama server reachable from the host that is running ComfyUI.\nNOTE: The files in the repo are not organized, which may lead to update issues."}, {"author": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "title": "ComfyUI-Molde-Utils", "reference": "https://github.com/gioferreira/ComfyUI-Molde-Utils", "files": ["https://github.com/gioferreira/ComfyUI-Molde-Utils"], "install_type": "git-clone", "description": "ComfyUI-Molde-Utils is a utility library designed to provide various helper functions for working with UI elements. This project includes modules for handling bezier curves and color conversions.\nNOTE: The files in the repo are not organized, which may lead to update issues."}, {"author": "kijai", "title": "ComfyUI nodes for VEnhancer [WIP]", "reference": "https://github.com/kijai/ComfyUI-VEnhancer", "files": ["https://github.com/kijai/ComfyUI-VEnhancer"], "install_type": "git-clone", "description": "Original repo: [a/https://github.com/Vchitect/VEnhancer](https://github.com/Vchitect/VEnhancer)"}, {"author": "<PERSON><PERSON><PERSON><PERSON>", "title": "Jim's ComfyUI Nodes [WIP]", "reference": "https://github.com/jimstudt/ComfyUI-Jims-Nodes", "files": ["https://github.com/jimstudt/ComfyUI-Jims-Nodes"], "install_type": "git-clone", "description": "NODES: <PERSON><PERSON> and <PERSON><PERSON><PERSON>, Text To String List, Choose String, Define Word, Lookup Word, Substitute Words, Dictionary to JSON, JSON file to Dictionary, JSON to Dictionary, Load Image And Info From Path, CubbyHack, Image to Solid Background"}, {"author": "<PERSON><PERSON><PERSON><PERSON>", "title": "node_dev - ComfyUI Node Development Helper", "reference": "https://github.com/hananbeer/node_dev", "files": ["https://github.com/hananbeer/node_dev"], "install_type": "git-clone", "description": "Browse to this endpoint to reload custom nodes for more streamlined development:\nhttp://127.0.0.1:8188/node_dev/reload/<module_name>"}, {"author": "ChrisColeTech", "title": "ComfyUI-Get-Random-File [UNSAFE]", "reference": "https://github.com/ChrisColeTech/ComfyUI-Get-Random-File", "files": ["https://github.com/ChrisColeTech/ComfyUI-Get-Random-File"], "install_type": "git-clone", "description": "Gets a random file from a directory. Returns the filpath as a STRING. [w/This node allows access to arbitrary files through the workflow, which could pose a security threat.]"}, {"author": "logtd", "title": "ComfyUI-Fluxtapoz [WIP]", "reference": "https://github.com/logtd/ComfyUI-Fluxtapoz", "files": ["https://github.com/logtd/ComfyUI-Fluxtapoz"], "install_type": "git-clone", "description": "A set of nodes for editing images using Flux in ComfyUI"}, {"author": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "title": "ComfyUI-ControlNeXt [WIP]", "reference": "https://github.com/neverbiasu/ComfyUI-ControlNeXt", "files": ["https://github.com/neverbiasu/ComfyUI-ControlNeXt"], "install_type": "git-clone", "description": "In progress🚧"}, {"author": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "title": "ComfyUI-TextOverlay", "reference": "https://github.com/neeltheninja/ComfyUI-TextOverlay", "files": ["https://github.com/neeltheninja/ComfyUI-TextOverlay"], "install_type": "git-clone", "description": "A custom node for ComfyUI that adds text overlay to images, with options for text and background color, opacity, vertical positioning, and custom font selection. [w/Name conflict with munkyfoot/ComfyUI-TextOverlay. Cannot install simulatenously.]"}, {"author": "comfyanonymous", "title": "ComfyUI_bitsandbytes_NF4 [EXPERIMENTAL]", "reference": "https://github.com/comfyanonymous/ComfyUI_bitsandbytes_NF4", "files": ["https://github.com/comfyanonymous/ComfyUI_bitsandbytes_NF4"], "install_type": "git-clone", "description": "A quickly written custom node that uses code from Forge to support the nf4 flux dev checkpoint and nf4 flux schnell checkpoint.\nRequires installing bitsandbytes.\nMake sure your ComfyUI is updated.\nThe node is: CheckpointLoaderNF4, just plug it in your flux workflow instead of the regular one.[w/NF4 checckpoint doesn't support LoRA.]"}, {"author": "kijai", "title": "ComfyUI-EasyAnimateWrapper [WIP]", "reference": "https://github.com/kijai/ComfyUI-EasyAnimateWrapper", "files": ["https://github.com/kijai/ComfyUI-EasyAnimateWrapper"], "install_type": "git-clone", "description": "EasyAnimateWrapper for ComfyUI"}, {"author": "logtd", "title": "ComfyUI-Veevee [WIP]", "reference": "https://github.com/logtd/ComfyUI-Veevee", "files": ["https://github.com/logtd/ComfyUI-Veevee"], "install_type": "git-clone", "description": "A Video2Video framework for text2image models in ComfyUI. Supports SD1.5 and SDXL."}, {"author": "IuvenisSapiens", "title": "ComfyUI_MiniCPM-V-2_6-int4", "id": "minicpm-v-2_6-int4", "reference": "https://github.com/IuvenisSapiens/ComfyUI_MiniCPM-V-2_6-int4", "files": ["https://github.com/IuvenisSapiens/ComfyUI_MiniCPM-V-2_6-int4"], "install_type": "git-clone", "description": "This is an implementation of [a/MiniCPM-V-2_6-int4](https://github.com/OpenBMB/MiniCPM-V) by [a/ComfyUI](https://github.com/comfyanonymous/ComfyUI), including support for text-based queries, video queries, single-image queries, and multi-image queries to generate captions or responses."}, {"author": "<PERSON><PERSON><PERSON><PERSON>", "title": "ComfyUI_EnvAutopsyAPI Debugger [UNSAFE]", "id": "<PERSON><PERSON><PERSON><PERSON>", "reference": "https://github.com/chrisdreid/ComfyUI_EnvAutopsyAPI", "files": ["https://github.com/chrisdreid/ComfyUI_EnvAutopsyAPI"], "install_type": "git-clone", "description": "A powerful debugging tool designed to provide in-depth analysis of your environment and dependencies by exposing API endpoints. This tool allows you to inspect environment variables, pip packages, python info and dependency trees, making it easier to diagnose and resolve issues in your ComfyUI setup.[w/This tool may expose sensitive system information if used on a public server]"}, {"author": "Futureversecom", "title": "ComfyUI-JEN", "reference": "https://github.com/futureversecom/ComfyUI-JEN", "files": ["https://github.com/futureversecom/ComfyUI-JEN"], "install_type": "git-clone", "description": "Comfy UI custom nodes for JEN music generation powered by Futureverse"}, {"author": "<PERSON><PERSON><PERSON>", "title": "Comfyui_AutoSurvey", "reference": "https://github.com/denislov/Comfyui_AutoSurvey", "files": ["https://github.com/denislov/Comfyui_AutoSurvey"], "install_type": "git-clone", "description": "Nodes:AutoSurvey, WriteOutline, WriteSection, ChatModel, QueryKnowledge, ManageDatabase, AddDoc2Knowledge"}, {"author": "leoleelxh", "title": "ComfyUI-MidjourneyNode-leoleexh", "reference": "https://github.com/leoleelxh/ComfyUI-MidjourneyNode-leoleexh", "files": ["https://github.com/leoleelxh/ComfyUI-MidjourneyNode-leoleexh"], "install_type": "git-clone", "description": "This node allows ComfyUI to easily integrate with Midjourney, utilizing the ultra-high quality of Midjourney and the powerful control of SD to provide more convenient capabilities for AIGC.\nNOTE: This node relies on the midjourney proxy project and requires API deployment in advance. For detailed installation, please refer to the instructions of the project. https://github.com/novicezk/midjourney-proxy"}, {"author": "kijai", "title": "ComfyUI-FollowYourEmojiWrapper [WIP]", "reference": "https://github.com/kijai/ComfyUI-FollowYourEmojiWrapper", "files": ["https://github.com/kijai/ComfyUI-FollowYourEmojiWrapper"], "install_type": "git-clone", "description": "Original repo: [a/https://github.com/mayuelala/FollowYourEmoji](https://github.com/mayuelala/FollowYourEmoji)"}, {"author": "haomole", "title": "Comfyui-<PERSON><PERSON><PERSON><PERSON>", "reference": "https://github.com/haomole/Comfyui-SadTalker", "files": ["https://github.com/haomole/Comfyui-SadTalker"], "install_type": "git-clone", "description": "[a/SadTalker](https://github.com/OpenTalker/SadTalker) for ComfyUI"}, {"author": "hotpizzatactics", "title": "ComfyUI-WaterMark-Detector", "id": "watermark-detector", "reference": "https://github.com/hotpizzatactics/ComfyUI-WaterMark-Detector", "files": ["https://github.com/hotpizzatactics/ComfyUI-WaterMark-Detector"], "install_type": "git-clone", "description": "Nodes:CLAHE Enhancement, High Pass Filter, Edge Detection, Combine Enhancements, Adaptive Thresholding, Morphological Operations, Gray Color Enhancement, Improved Gray Color Enhancement, Texture Enhancement, Denoising Filter, Flexible Combine Enhancements."}, {"author": "AIFSH", "title": "IMAGDressing-ComfyUI", "id": "imagdressing", "reference": "https://github.com/AIFSH/IMAGDressing-ComfyUI", "files": ["https://github.com/AIFSH/IMAGDressing-ComfyUI"], "install_type": "git-clone", "description": "Nodes:IMAGDressingNode, TextNode"}, {"author": "BetaDoggo", "title": "ComfyUI-LogicGates", "id": "logicgates", "reference": "https://github.com/BetaDoggo/ComfyUI-LogicGates", "files": ["https://github.com/BetaDoggo/ComfyUI-LogicGates"], "install_type": "git-clone", "description": "<PERSON><PERSON>, <PERSON><PERSON> Nodes, ..."}, {"author": "shadowcz007", "title": "comfyui-hydit", "reference": "https://github.com/shadowcz007/comfyui-hydit-lowvram", "files": ["https://github.com/shadowcz007/comfyui-hydit-lowvram"], "install_type": "git-clone", "description": "<PERSON><PERSON><PERSON><PERSON> Diffusers Nodes"}, {"author": "walter<PERSON>eng", "title": "ComfyUI-Image-Utils", "reference": "https://github.com/walterFeng/ComfyUI-Image-Utils", "files": ["https://github.com/walterFeng/ComfyUI-Image-Utils"], "install_type": "git-clone", "description": "Nodes: Calculate Image Brightness"}, {"author": "zml-ai", "title": "comfyui-hydit", "reference": "https://github.com/zml-ai/comfyui-hydit", "files": ["https://github.com/zml-ai/comfyui-hydit"], "install_type": "git-clone", "description": "The ComfyUI code is under review in the official repository. Meanwhile, a temporary version is available below for immediate community use. We welcome users to try our workflow and appreciate any inquiries or suggestions."}, {"author": "melMass", "title": "ComfyUI-Lygia", "id": "lygia", "reference": "https://github.com/melMass/ComfyUI-Lygia", "files": ["https://github.com/melMass/ComfyUI-Lygia"], "install_type": "git-clone", "description": "NODES: LygiaProgram, LygiaUniforms"}, {"author": "SpaceWarpStudio", "title": "ComfyUI_Remaker_FaceSwap", "reference": "https://github.com/SpaceWarpStudio/ComfyUI_Remaker_FaceSwap", "files": ["https://github.com/SpaceWarpStudio/ComfyUI_Remaker_FaceSwap"], "install_type": "git-clone", "description": "Nodes:<PERSON><PERSON> Face Swap"}, {"author": "VisionExp", "title": "ve_custom_comfyui_nodes", "reference": "https://github.com/VisionExp/ve_custom_comfyui_nodes", "files": ["https://github.com/VisionExp/ve_custom_comfyui_nodes"], "install_type": "git-clone", "description": "Nodes:LoadImgFromInputUrl"}, {"author": "StartHua", "title": "Comfyui_CXH_CRM", "id": "csdmt-cxh", "reference": "https://github.com/StartHua/Comfyui_CSDMT_CXH", "files": ["https://github.com/StartHua/Comfyui_CSDMT_CXH"], "install_type": "git-clone", "description": "Node:CSD_Makeup\nNOTE:You need to download [a/pre-trained model file](https://github.com/StartHua/Comfyui_CSDMT_CXH)."}, {"author": "ZHO-ZHO-ZHO", "title": "ComfyUI-AuraSR-ZHO", "reference": "https://github.com/ZHO-ZHO-ZHO/ComfyUI-AuraSR-ZHO", "files": ["https://github.com/ZHO-ZHO-ZHO/ComfyUI-AuraSR-ZHO"], "install_type": "git-clone", "description": "AuraSR in ComfyUI for img & video\n[w/If the custom_nodes path is not under ComfyUI, be careful as it may not install properly.]"}, {"author": "tom-doerr", "title": "<PERSON><PERSON><PERSON> [WIP]", "reference": "https://github.com/tom-doerr/dspy_nodes", "files": ["https://github.com/tom-doerr/dspy_nodes"], "install_type": "git-clone", "description": "This is an attempt to make all DSPy features available in ComfyUI. Using an UI to devlop DSPy programs should be way faster since it makes it easier to see what is happening and allows to quickly iterate on the DSPy program structure."}, {"author": "Grant-CP", "title": "ComfyUI-LivePortraitKJ-MPS", "reference": "https://github.com/Grant-CP/ComfyUI-LivePortraitKJ-MPS", "files": ["https://github.com/Grant-CP/ComfyUI-LivePortraitKJ-MPS"], "install_type": "git-clone", "description": "If you wish to incorporate these changes into your repo, feel free to open an issue and ask. The commits should be pretty clear, and I also label almost all changes with #HAC<PERSON> so a full text search will work too.\nPlease let me know if you decide to incorporate any of these changes into your LivePortrait implementation so I can direct people to you repository. I do not intend to maintain this repo.\nSome operations are simply not supported on MPS and I didn't rewrite them. Most of my changes are just to .cuda calls and that sort of thing. Many operations are still done on CPU, so don't expect awesome performance."}, {"author": "thderoo", "title": "_topfun_s_nodes", "reference": "https://github.com/thderoo/ComfyUI-_topfun_s_nodes", "files": ["https://github.com/thderoo/ComfyUI-_topfun_s_nodes"], "install_type": "git-clone", "description": "Nodes:Conditioning Perturbation"}, {"author": "<PERSON><PERSON><PERSON><PERSON>", "title": "ComfyUI-Unload-Models", "reference": "https://github.com/willblaschko/ComfyUI-Unload-Models", "files": ["https://github.com/willblaschko/ComfyUI-Unload-Models"], "install_type": "git-clone", "description": "This repository provides developers with a way to better manage their ComfyUI model memory. It includes nodes that allow developers to either unload all models or unload one model at a time. These nodes are designed as pass-through nodes, so they can be used anywhere in the flow. The nodes can be found in the 'Unload Model' section.[w/These are massive hammers, and it could be possible to break things, please don't use them if you need finesse.]"}, {"author": "AIFSH", "title": "ComfyUI-OpenDIT [WIP]", "id": "opendit", "reference": "https://github.com/AIFSH/ComfyUI-OpenDIT", "files": ["https://github.com/AIFSH/ComfyUI-OpenDIT"], "install_type": "git-clone", "description": "make [a/OpenDIT](https://github.com/NUS-HPC-AI-Lab/OpenDiT) avaliable in ComfyUI"}, {"author": "alexisrolland", "title": "alexisrolland/ComfyUI-AuraSR", "id": "aurasr-alexisrolland", "reference": "https://github.com/alexisrolland/ComfyUI-AuraSR", "files": ["https://github.com/alexisrolland/ComfyUI-AuraSR"], "install_type": "git-clone", "description": "Custom ComfyUI nodes to run [a/fal-ai/AuraSR](https://huggingface.co/fal-ai/AuraSR) model.[w/This node cannot be installed simultaneously with AIFSH/ComfyUI-AuraSR due to overlapping repository names.]"}, {"author": "lin<PERSON><PERSON>", "title": "ComfyUI Build and Train Your Network [WIP]", "id": "cfgpp", "reference": "https://github.com/linhusyung/comfyui-Build-and-train-your-network", "files": ["https://github.com/linhusyung/comfyui-Build-and-train-your-network"], "install_type": "git-clone", "description": "Stable Diffusion is an image generation technique based on diffusion models. Its core idea involves simulating diffusion processes by iteratively adding noise and using neural networks to predict and remove the noise, thereby generating high-quality images. This approach is not limited to image generation; with appropriate network architecture and training data, it can be adapted for various other tasks. The application of neural networks extends beyond image generation. By adjusting network structures and loss functions, neural networks can also perform tasks such as classification and regression. This flexibility makes neural networks a powerful tool for handling a wide range of machine learning tasks. This project aims to expand custom neural network layers (such as linear layers, convolutional layers, etc.) within ComfyUI and provide simplified task training functionalities. Through this project, users can easily construct custom neural network layers and perform training in ComfyUI using a graphical interface."}, {"author": "<PERSON><PERSON><PERSON><PERSON>", "title": "ComfyUI-Airtable [WIP]", "id": "airtable", "reference": "https://github.com/<PERSON><PERSON>-<PERSON>/ComfyUI-Airtable", "files": ["https://github.com/<PERSON><PERSON>-<PERSON>/ComfyUI-Airtable"], "install_type": "git-clone", "description": "A simple node to load image from local path or http url. You can find this node from 'image' category."}, {"author": "majorsauce", "title": "comfyui_indieTools [WIP]", "id": "indie-tools", "reference": "https://github.com/majorsauce/comfyui_indieTools", "files": ["https://github.com/majorsauce/comfyui_indieTools"], "install_type": "git-clone", "description": "Nodes:[Indie] Cut by Mask, [Indie] Paste Image, [Indie] Local Scale, [Indie] Solidify, [Indie] Yolo Detector.[w/Install may fail due to invliad requirements.txt file]"}, {"author": "AIFSH", "title": "ComfyUI-ViViD", "id": "vivid", "reference": "https://github.com/AIFSH/ComfyUI-ViViD", "files": ["https://github.com/AIFSH/ComfyUI-ViViD"], "install_type": "git-clone", "description": "a comfyui custom node for ViViD"}, {"author": "NeuralNotW0rk", "title": "ComfyUI-Waveform-Extensions", "reference": "https://github.com/NeuralNotW0rk/ComfyUI-Waveform-Extensions", "files": ["https://raw.githubusercontent.com/NeuralNotW0rk/ComfyUI-Waveform-Extensions/main/EXT_VariationUtils.py", "https://raw.githubusercontent.com/NeuralNotW0rk/ComfyUI-Waveform-Extensions/main/EXT_AudioManipulation.py"], "install_type": "copy", "description": "Some additional audio utilites for use on top of Sample Diffusion ComfyUI Extension"}, {"author": "nat-chan", "title": "comfyui-paint", "reference": "https://github.com/nat-chan/comfyui-paint", "files": ["https://github.com/nat-chan/comfyui-paint"], "install_type": "git-clone", "description": "comfyui-paint\n[w/You need to clone submodule manually after clone. There is permission issue.]"}, {"author": "prabinpebam", "title": "anyPython [UNSAFE]", "reference": "https://github.com/prabinpebam/anyPython", "files": ["https://github.com/prabinpebam/anyPython"], "install_type": "git-clone", "description": "This node was inspired by AnyNode. I wanted to have a node where I can paste any python script and execute it. That way I can use this node in combination with a Custom node like the Ollama node that can generate the python code and feed into this node. This also makes it much easier to debug or modify the code iteratively. As of the current version, I've created separate nodes for no input, 1 input and 2 inputs. The input also currently takes only sting as input. Let me know in the discussion how you would use this node.\n[w/This extension allows the execution of arbitrary Python code from a workflow.]"}, {"author": "kijai", "title": "ComfyUI DiffSynth wrapper nodes", "id": "diffsynth-wrapper", "reference": "https://github.com/kijai/ComfyUI-DiffSynthWrapper", "files": ["https://github.com/kijai/ComfyUI-DiffSynthWrapper"], "install_type": "git-clone", "description": "Currently only the new extended SVD model 'ExVideo' is supported.\nOriginal repo:[a/https://github.com/modelscope/DiffSynth-Studio](https://github.com/modelscope/DiffSynth-Studio)"}, {"author": "AllenEdgarPoe", "title": "ComfyUI-Xorbis-nodes [WIP]", "id": "xorbis", "reference": "https://github.com/AllenEdgarPoe/ComfyUI-Xorbis-nodes", "files": ["https://github.com/AllenEdgarPoe/ComfyUI-Xorbis-nodes"], "install_type": "git-clone", "description": "This repository is for MuseumX Update. We use ComfyUI as our framework, and the nodes are built for my comfort.\nNOTE: The files in the repo are not organized."}, {"author": "mikeymcfish", "title": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> and <PERSON><PERSON><PERSON><PERSON> Nodes", "id": "fishtools", "reference": "https://github.com/mikeymcfish/FishTools", "files": ["https://github.com/mikeymcfish/FishTools"], "install_type": "git-clone", "description": "This repository contains two custom nodes, LaserCutterFull and Deptherize, designed for use in image processing workflows. The LaserCutterFull node processes input images to generate layers for laser cutting, while the Deptherize node converts SVG data into a depth map image."}, {"author": "pzzmyc", "title": "comfyui-sd3-simple-simpletuner", "id": "simpletuner", "reference": "https://github.com/pzzmyc/comfyui-sd3-simple-simpletuner", "files": ["https://github.com/pzzmyc/comfyui-sd3-simple-simpletuner"], "install_type": "git-clone", "description": "Nodes:sd3 simple simpletuner by hhy."}, {"author": "horidream", "title": "ComfyUI-Horidream", "id": "horidream", "reference": "https://github.com/horidream/ComfyUI-Horidream", "files": ["https://github.com/horidream/ComfyUI-Horidream"], "install_type": "git-clone", "description": "Nodes:Pass Through With Sound."}, {"author": "kijai", "title": "ComfyUI-DiffusersSD3Wrapper", "id": "diffusers-sd3-wrapper", "reference": "https://github.com/kijai/ComfyUI-DiffusersSD3Wrapper", "files": ["https://github.com/kijai/ComfyUI-DiffusersSD3Wrapper"], "install_type": "git-clone", "description": "Nodes:Load SD3DiffusersPipeline, SD3 ControlNet Sampler"}, {"author": "AustinMroz", "title": "ComfyUI-SD3-Medium-CN-Diffusers [WIP]", "reference": "https://github.com/ZHO-ZHO-ZHO/ComfyUI-SD3-Medium-CN-Diffusers", "files": ["https://github.com/AustinMroz/ComfyUI-WorkflowCheckpointing"], "install_type": "git-clone", "description": "ComfyUI SD3-Medium ControlNet (Diffusers)"}, {"author": "redhottensors", "title": "ComfyUI-ODE", "id": "ode", "reference": "https://github.com/redhottensors/ComfyUI-ODE", "files": ["https://github.com/redhottensors/ComfyUI-ODE"], "install_type": "git-clone", "description": "ODE Solvers for ComfyUI\nThis node enables use of torchdiffeq ODE solvers with models.  Intended for use with Stable Diffusion 3 and similar flow models."}, {"author": "ma<PERSON><PERSON><PERSON>", "title": "Transparent Background for ComfyUI", "id": "transparent-bg", "reference": "https://github.com/maruhidd/ComfyUI_Transparent-Background", "files": ["https://github.com/maruhidd/ComfyUI_Transparent-Background"], "install_type": "git-clone", "description": "Nodes:<PERSON><PERSON><PERSON>, <PERSON>ll Transparent"}, {"author": "baicai99", "title": "ComfyUI-FrameSkipping", "id": "frame-skipping", "reference": "https://github.com/baicai99/ComfyUI-FrameSkipping", "files": ["https://github.com/baicai99/ComfyUI-FrameSkipping"], "install_type": "git-clone", "description": "This plugin can precisely control the rendering between frames, completing the synthesis of multiple frames in a single load. My homepage includes my attached workflow."}, {"author": "ejektaflex", "title": "ComfyUI - Ty", "id": "ty-nodes", "reference": "https://github.com/ejektaflex/ComfyUI-Ty", "files": ["https://github.com/ejektaflex/ComfyUI-Ty"], "install_type": "git-clone", "description": "Nodes:Lora Block Weight Regex Loader"}, {"author": "jtydhr88", "title": "ComfyUI-Unique3D [WIP]", "id": "unique3d", "reference": "https://github.com/jtydhr88/ComfyUI-Unique3D", "files": ["https://github.com/jtydhr88/ComfyUI-Unique3D"], "install_type": "git-clone", "description": "ComfyUI Unique3D is custom nodes that running [a/AiuniAI/Unique3D](https://github.com/AiuniAI/Unique3D) into ComfyUI."}, {"author": "kycg", "title": "comfyui-Kwtoolset", "id": "kwtoolset", "reference": "https://github.com/kycg/comfyui-Kwtoolset", "files": ["https://github.com/kycg/comfyui-Kwtoolset"], "install_type": "git-clone", "description": "Nodes:KwtoolsetLoraLoaderwithpreview, KwtoolsetCheckpointLoaderwithpreview, KwtoolsetLoadCheckpointsBatch, KwtoolsetGrowMaskPlus, KwtoolsetGetHipMask, KwtoolsetGetHipMasktest, KwtoolsetGetImageSize, KWPositiveString, KWNagetiveString, KWanywhereString, KwtoolsetChangeOpenpose, ..."}, {"author": "mashb1t", "title": "ComfyUI mashb1t nodes", "id": "mashb1t", "reference": "https://github.com/mashb1t/comfyui-nodes-mashb1t", "files": ["https://github.com/mashb1t/comfyui-nodes-mashb1t"], "install_type": "git-clone", "description": "This Python script is an optional add-on to the Comfy UI stable diffusion client."}, {"author": "immersiveexperience", "title": "ie-comfyui-color-nodes", "reference": "https://github.com/immersiveexperience/ie-comfyui-color-nodes", "files": ["https://github.com/immersiveexperience/ie-comfyui-color-nodes"], "install_type": "git-clone", "description": "Custom ComfyUI nodes for simple color correction."}, {"author": "LZpenguin", "title": "ComfyUI-Text", "id": "comfy-text", "reference": "https://github.com/LZpenguin/ComfyUI-Text", "files": ["https://github.com/LZpenguin/ComfyUI-Text"], "install_type": "git-clone", "description": "Nodes:Add_text_by_mask.[w/This custom node cannot be installed simultaneously as it has the same repository name as MarkoCa1/ComfyUI-Text.]"}, {"author": "norgeous", "title": "UI Builder [WIP]", "id": "norgeous", "reference": "https://github.com/norgeous/ComfyUI-UI-Builder", "files": ["https://github.com/norgeous/ComfyUI-UI-Builder"], "install_type": "git-clone", "description": "Alternative configurable React UI overlay for Comfy UI."}, {"author": "Shinsplat", "title": "ComfyUI-Shinsplat [UNSAFE]", "id": "shin<PERSON><PERSON>", "reference": "https://github.com/Shinsplat/ComfyUI-Shinsplat", "files": ["https://github.com/Shinsplat/ComfyUI-Shinsplat"], "install_type": "git-clone", "description": "Nodes: Clip Text Encode (Shinsplat), Clip Text Encode SDXL (Shinsplat), <PERSON><PERSON> (Shinsplat).\n[w/This extension poses a risk of executing arbitrary commands through workflow execution. Please be cautious.]"}, {"author": "hy134300", "title": "comfyui-hydit", "reference": "https://github.com/hy134300/comfyui-hydit", "files": ["https://github.com/hy134300/comfyui-hydit"], "install_type": "git-clone", "description": "This repository contains a customized node and workflow designed specifically for HunYuan DIT. The official tests conducted on DDPM, DDIM, and DPMMS have consistently yielded results that align with those obtained through the Diffusers library. However, it's important to note that we cannot assure the consistency of results from other ComfyUI native samplers with the Diffusers inference. We cordially invite users to explore our workflow and are open to receiving any inquiries or suggestions you may have."}, {"author": "corbin-hayden13", "title": "ComfyUI-Better-Dimensions", "id": "better-dim", "reference": "https://github.com/corbin-hayden13/ComfyUI-Better-Dimensions", "files": ["https://github.com/corbin-hayden13/ComfyUI-Better-Dimensions"], "install_type": "git-clone", "description": "Nodes:BetterImageDimensions, SDXLDimensions, PureRatio"}, {"author": "endman100", "title": "ComfyUI-augmentation", "id": "augmentation", "reference": "https://github.com/endman100/ComfyUI-augmentation", "files": ["https://github.com/endman100/ComfyUI-augmentation"], "install_type": "git-clone", "description": "Nodes:<PERSON>domFlipImage (endman100)"}, {"author": "endman100", "title": "ComfyUI Nodes: SaveConditioning and LoadConditioning", "id": "save-load-conditioning", "reference": "https://github.com/endman100/ComfyUI-SaveAndLoadPromptCondition", "files": ["https://github.com/endman100/ComfyUI-SaveAndLoadPromptCondition"], "install_type": "git-clone", "description": "The SaveConditioning node is designed to save conditioning data to binary files. This is useful for storing and reusing conditioning information across different sessions or applications.\n[w/This node can only handle very limited conditioning at the text prompt level.]"}, {"author": "marduk191", "title": "comfyui-marno<PERSON>", "id": "marnodes", "reference": "https://github.com/marduk191/comfyui-marnodes", "files": ["https://github.com/marduk191/comfyui-marnodes"], "install_type": "git-clone", "description": "Nodes:marduk191_workflow_settings"}, {"author": "kijai", "title": "ComfyUI-CV-VAE", "id": "cv-vae", "reference": "https://github.com/kijai/ComfyUI-CV-VAE", "files": ["https://github.com/kijai/ComfyUI-CV-VAE"], "install_type": "git-clone", "description": "Nodes:CV_VAE_Load, CV_VAE_Encode, CV_VAE_Decode"}, {"author": "Gentleman<PERSON><PERSON>", "title": "ComfyUI Notifier", "id": "notifier", "reference": "https://github.com/GentlemanHu/ComfyUI-Notifier", "files": ["https://github.com/GentlemanHu/ComfyUI-Notifier"], "install_type": "git-clone", "description": "Nodes:GentlemanHu_Notifier"}, {"author": "jimmm-ai", "title": "TimeUi a ComfyUI Timeline Node System [WIP]", "id": "timeline", "reference": "https://github.com/jimmm-ai/TimeUi-a-ComfyUi-Timeline-Node", "files": ["https://github.com/jimmm-ai/TimeUi-a-ComfyUi-Timeline-Node"], "install_type": "git-clone", "description": "I've been working on the UX/UI of a timeline custom node system for ComfyUI over the past two weeks. The goal is to create a timeline similar to video/animation editing tools, without relying on traditional timeframe code. You can effortlessly add, delete, or rearrange rows, providing a streamlined user experience."}, {"author": "StartHua", "title": "Comfyui_CXH_CRM", "id": "cxh-crm", "reference": "https://github.com/StartHua/Comfyui_CXH_CRM", "files": ["https://github.com/StartHua/Comfyui_CXH_CRM"], "install_type": "git-clone", "description": "Nodes:CRM"}, {"author": "comfypod", "title": "ComfyUI-Comflow", "id": "comflow", "reference": "https://github.com/comfypod/ComfyUI-Comflow", "files": ["https://github.com/comfypod/ComfyUI-Comflow"], "install_type": "git-clone", "description": "ComfyUI-Comflow."}, {"author": "pamparamm", "title": "ComfyUI-ppm", "id": "comfyui-ppm", "reference": "https://github.com/pamparamm/ComfyUI-ppm", "files": ["https://github.com/pamparamm/ComfyUI-ppm"], "install_type": "git-clone", "description": "Fixed AttentionCouple/NegPip(negative weights in prompts), more CFG++ samplers, etc."}, {"author": "FoundD-oka", "title": "ComfyUI KISEKAE-OOTD", "id": "kisekae-ootd", "reference": "https://github.com/FoundD-oka/ComfyUI-kisekae-OOTD", "files": ["https://github.com/FoundD-oka/ComfyUI-kisekae-OOTD"], "install_type": "git-clone", "description": "Nodes:LoadOOTDPipeline, LoadOOTDPipelineHub, LoadOOTDPipelineHub."}, {"author": "bruce007lee", "title": "comfyui-tiny-utils", "id": "tiny-utils", "reference": "https://github.com/bruce007lee/comfyui-tiny-utils", "files": ["https://github.com/bruce007lee/comfyui-tiny-utils"], "install_type": "git-clone", "description": "Nodes:FaceAlign, FaceAlignImageProcess, FaceAlignMaskProcess, ImageFillColorByMask, CropImageByMask, LoadImageAdvance, ImageTransposeAdvance, ImageSAMMask"}, {"author": "brycego<PERSON>", "title": "brycegoh/comfyui-custom-nodes", "reference": "https://github.com/brycegoh/comfyui-custom-nodes", "files": ["https://github.com/brycegoh/comfyui-custom-nodes"], "install_type": "git-clone", "description": "Nodes:MaskAreaComparisonSegment, FillMaskedArea, OCRAndMask, CombineTwoImageIntoOne"}, {"author": "LykosAI", "title": "ComfyUI Nodes for Inference.Core", "id": "inference-core", "reference": "https://github.com/LykosAI/ComfyUI-Inference-Core-Nodes", "files": ["https://github.com/LykosAI/ComfyUI-Inference-Core-Nodes"], "install_type": "git-clone", "description": "Primary Nodes for Inference.Core and Stability Matrix. With a focus on not impacting startup performance and using fully qualified Node names. [w/This custom node is likely to conflict with many other nodes.]"}, {"author": "<PERSON><PERSON><PERSON>", "title": "comfyui-p5js-node", "id": "p5js", "reference": "https://github.com/tracerstar/comfyui-p5js-node", "files": ["https://github.com/tracerstar/comfyui-p5js-node"], "install_type": "git-clone", "description": "A simple proof of concept node to pass a p5js canvas through ComfyUI for img2img generation use."}, {"author": "<PERSON>ao<PERSON><PERSON>", "title": "ComfyUI-mobvoi-openapi", "id": "mobvoi-openapi", "reference": "https://github.com/chaojie/ComfyUI-mobvoi-openapi", "files": ["https://github.com/chaojie/ComfyUI-mobvoi-openapi"], "install_type": "git-clone", "description": "Nodes:MobvoiOpenapiMetamanText, MobvoiOpenapiMetamanAudio, MobvoiOpenapiTts, HtmlViewer, OssUploadImage, OssUploadAudio"}, {"author": "immersiveexperience", "title": "ie-comfyui-color-nodes", "id": "ie-color-nodes", "reference": "https://github.com/immersiveexperience/ie-comfyui-color-nodes", "files": ["https://github.com/immersiveexperience/ie-comfyui-color-nodes"], "install_type": "git-clone", "description": "Custom ComfyUI nodes for simple color correction."}, {"author": "beyastard", "title": "ComfyUI_BeySoft", "reference": "https://github.com/beyastard/ComfyUI_BeySoft", "files": ["https://github.com/beyastard/ComfyUI_BeySoft"], "install_type": "git-clone", "description": "Nodes:BeySoft"}, {"author": "christian-byrne", "title": "🌌 Infinite Parallax Nodes [WIP]", "reference": "https://github.com/christian-byrne/infinite-zoom-parallax-nodes", "files": ["https://github.com/christian-byrne/infinite-zoom-parallax-nodes"], "install_type": "git-clone", "description": "Nodes:Parallax Config, Load Parallax Frame, Save Parallax Object Layers, <PERSON><PERSON> for Parallax Outpainting, Save Parallax Frame, <PERSON><PERSON> and Pa<PERSON> for Outpainting, Create Infinite Zoom Video"}, {"author": "flyingdogsoftware", "title": "<PERSON>yre for ComfyUI", "id": "gyre", "reference": "https://github.com/flyingdogsoftware/gyre_for_comfyui", "files": ["https://github.com/flyingdogsoftware/gyre_for_comfyui"], "install_type": "git-clone", "description": "Nodes:<PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>nd, Gyre<PERSON>f<PERSON><PERSON>e"}, {"author": "g<PERSON><PERSON><PERSON><PERSON><PERSON>", "title": "comfyui_median_filter", "id": "median-filter", "reference": "https://github.com/githubYiheng/comfyui_median_filter", "files": ["https://github.com/githubYiheng/comfyui_median_filter"], "install_type": "git-clone", "description": "Nodes:Apply Median Filter. [w/This has been updated to be equivalent to the comfyui_kmeans_filter node. Mistake?]"}, {"author": "nat-chan", "title": "comfyui-exec [UNSAFE]", "id": "evalnode", "reference": "https://github.com/nat-chan/comfyui-exec", "files": ["https://github.com/nat-chan/comfyui-exec"], "install_type": "git-clone", "description": "Nodes:EvalNode [w/Please do not use the node that executes arbitrary code and outputs in any type, as it is dangerous.]"}, {"author": "ha<PERSON><PERSON><PERSON>", "title": "ComfyUI-InstantStyle", "id": "instantstyle", "reference": "https://github.com/haofanwang/ComfyUI-InstantStyle", "files": ["https://github.com/haofanwang/ComfyUI-InstantStyle"], "install_type": "git-clone", "description": "Nodes:<PERSON><PERSON><PERSON><PERSON><PERSON>, BaseModel<PERSON>oader, InstantStyleLoader, InstantStyleGenerationNode"}, {"author": "jp0215", "title": "comfyUI_padding-resize_node", "reference": "https://github.com/jp0215/comfyUI_padding-resize_node", "files": ["https://raw.githubusercontent.com/jp0215/comfyUI_padding-resize_node/main/PaddingNode.py", "https://raw.githubusercontent.com/jp0215/comfyUI_padding-resize_node/main/ResizeNode.py"], "install_type": "copy", "description": "Padding image to 8x: input image and mask, if the side length is not an integer multiple of 8, expand the side length to the smallest multiple of 8 greater than the original side length. Output padding image and mask. Resize to the origin: input the generated image and the original image, crop the generated image to the size of the original image, output the cropped image."}, {"author": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "title": "ComfyUI-QuasimondoNodes [WIP]", "id": "quasimondo-nodes", "reference": "https://github.com/Quasimondo/ComfyUI-QuasimondoNodes", "files": ["https://github.com/Quasimondo/ComfyUI-QuasimondoNodes"], "install_type": "git-clone", "description": "Nodes:<PERSON> Shader, <PERSON>sh"}, {"author": "TSFSean", "title": "ComfyUI-TSFNodes", "id": "tsfnodes", "reference": "https://github.com/TSFSean/ComfyUI-TSFNodes", "files": ["https://github.com/TSFSean/ComfyUI-TSFNodes"], "install_type": "git-clone", "description": "Nodes:GyroOSC"}, {"author": "blib-la", "title": "ComfyUI-Captain-Extensions", "id": "captain", "reference": "https://github.com/blib-la/ComfyUI-Captain-Extensions", "files": ["https://github.com/blib-la/ComfyUI-Captain-Extensions"], "install_type": "git-clone", "description": "ComfyUI extensions for better [a/Captain](https://github.com/blib-la/captain) integration."}, {"author": "ejektaflex", "title": "ComfyUI-Ty", "reference": "https://github.com/ejektaflex/ComfyUI-Ty", "files": ["https://github.com/ejektaflex/ComfyUI-Ty"], "install_type": "git-clone", "description": "Nodes:Lora Block Weight Regex Loader"}, {"author": "christian-byrne", "title": "Python Interpreter ComfyUI Node [UNSAFE]", "reference": "https://github.com/christian-byrne/python-interpreter-node", "files": ["https://github.com/christian-byrne/python-interpreter-node"], "install_type": "git-clone", "description": "For debugging, parsing data, generating random values, converting types, testing custom nodes faster.\nReference and use variables in the code using the same names as the inputs in the UI\nWrapper class around tensors with operator overloading for doing common image manipulation tasks.I might remove this aspect\n[w/This extension allows you to run programs through Python code in your workflow, which may not be secure. Use with caution.]"}, {"author": "sofakid", "title": "dandy [UNSAFE]", "reference": "https://github.com/sofakid/dandy", "files": ["https://github.com/sofakid/dandy"], "install_type": "git-clone", "description": "Dandy is a JavaScript bridge for ComfyUI. It includes everything you need to make JavaScript enabled extensions, or just load and code in little editor nodes right in ComfyUI.[w/This code can cause security issues because it allows for the execution of arbitrary JavaScript input.]"}, {"author": "shadowcz007", "title": "ComfyUI-PuLID [TEST]", "reference": "https://github.com/shadowcz007/ComfyUI-PuLID-Test", "files": ["https://github.com/shadowcz007/ComfyUI-PuLID-Test"], "install_type": "git-clone", "description": "[a/PuLID](https://github.com/ToTheBeginning/PuLID) ComfyUI native implementation."}, {"author": "sangeet", "title": "Simple Frontend For ComfyUI workflow [TEST]", "reference": "https://github.com/sangeet/testui", "files": ["https://github.com/sangeet/testui"], "install_type": "git-clone", "description": "A simple base front-end for text-to-image workflow in ComfyUI. Meant to serve as a base to be modified for future complex workflows"}, {"author": "<PERSON><PERSON><PERSON><PERSON>", "title": "ComfyUI-MusicGen [WIP]", "id": "musicgen", "reference": "https://github.com/Elawphant/ComfyUI-MusicGen", "files": ["https://github.com/Elawphant/ComfyUI-MusicGen"], "install_type": "git-clone", "description": "ComfyUI for Meta MusicGen."}, {"author": "jtscmw01", "title": "ComfyUI-DiffBIR", "id": "diffbir", "reference": "https://github.com/jtscmw01/ComfyUI-DiffBIR", "files": ["https://github.com/jtscmw01/ComfyUI-DiffBIR"], "install_type": "git-clone", "description": "This extension provides [a/DiffBIR](https://github.com/XPixelGroup/DiffBIR) feature."}, {"author": "<PERSON><PERSON><PERSON><PERSON>", "title": "guidance_interval", "reference": "https://github.com/ericbeyer/guidance_interval", "files": ["https://github.com/ericbeyer/guidance_interval"], "install_type": "git-clone", "description": "Nodes:Guidance Interval\nNOTE: Because the sampling function is replaced, you must restart after executing this custom node to restore the original state."}, {"author": "oztrkoguz", "title": "Kosmos2_BBox_Cutter Models", "reference": "https://github.com/oztrkoguz/ComfyUI_Kosmos2_BBox_Cutter", "files": ["https://github.com/oztrkoguz/ComfyUI_Kosmos2_BBox_Cutter"], "install_type": "git-clone", "description": "Nodes:<PERSON><PERSON><PERSON><PERSON><PERSON>, Kosmos2Sampler<PERSON><PERSON><PERSON>, W<PERSON>"}, {"author": "ZHO-ZHO-ZHO", "title": "ComfyUI-PuLID-ZHO [WIP]", "reference": "https://github.com/ZHO-ZHO-ZHO/ComfyUI-PuLID-ZHO", "files": ["https://github.com/ZHO-ZHO-ZHO/ComfyUI-PuLID-ZHO"], "install_type": "git-clone", "description": "Unofficial implementation of [a/PuLID](https://github.com/ToTheBeginning/PuLID)（diffusers） for ComfyUI"}, {"author": "longgui0318", "title": "comfyui-one-more-step [WIP]", "reference": "https://github.com/longgui0318/comfyui-one-more-step", "files": ["https://github.com/longgui0318/comfyui-one-more-step"], "install_type": "git-clone", "description": "[a/(OMS)mhh0318/OneMoreStep](https://github.com/mhh0318/OneMoreStep) comfyui support ."}, {"author": "unknown", "title": "CLIPTextEncodeAndEnhancev4 (shirazdesigner)", "reference": "https://github.com/shirazdesigner/CLIPTextEncodeAndEnhancev4", "files": ["https://github.com/shirazdesigner/CLIPTextEncodeAndEnhancev4"], "install_type": "git-clone", "description": "Nodes:CLIPTextEncodeAndEnhance.\nNOTE:Translation:This is a wrapper that simply makes it easy to install an existing node via git."}, {"author": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "title": "comfyui_mergekit [WIP]", "reference": "https://github.com/umisetokikaze/comfyui_mergekit", "files": ["https://github.com/umisetokikaze/comfyui_mergekit"], "install_type": "git-clone", "description": "Nodes:DefineSaveName, SetModels, get_skip, LoadLR, LoadTarget, SetTokenizer, Merge, Set<PERSON>ayer, SetModels"}, {"author": "Video3DGenResearch", "title": "ComfyUI Batch Input Node", "reference": "https://github.com/Video3DGenResearch/comfyui-batch-input-node", "files": ["https://github.com/Video3DGenResearch/comfyui-batch-input-node"], "install_type": "git-clone", "description": "Nodes:BatchInputText"}, {"author": "kijai", "title": "ComfyUI nodes to use DeepSeek-VL", "reference": "https://github.com/kijai/ComfyUI-DeepSeek-VL", "files": ["https://github.com/kijai/ComfyUI-DeepSeek-VL"], "install_type": "git-clone", "description": "[a/https://huggingface.co/deepseek-ai](https://huggingface.co/deepseek-ai)"}, {"author": "Gentleman<PERSON><PERSON>", "title": "ComfyUI-Notifier", "reference": "https://github.com/GentlemanHu/ComfyUI-Notifier", "files": ["https://github.com/GentlemanHu/ComfyUI-Notifier"], "install_type": "git-clone", "description": "Nodes:GentlemanHu_Notifier"}, {"author": "nat-chan", "title": "Transceiver📡", "reference": "https://github.com/nat-chan/transceiver", "files": ["https://github.com/nat-chan/transceiver"], "install_type": "git-clone", "description": "Why? When processing a large number of requests, the SaveImage and LoadImage nodes may be IO-limited, and using shared memory improves performance by passing images only through memory access, not through IO."}, {"author": "DrMWeigand", "title": "ComfyUI_LineBreakInserter", "reference": "https://github.com/DrMWeigand/ComfyUI_LineBreakInserter", "files": ["https://github.com/DrMWeigand/ComfyUI_LineBreakInserter"], "install_type": "git-clone", "description": "Nodes:Line Break Inserter"}, {"author": "<PERSON><PERSON><PERSON><PERSON>", "title": "visuallabs_comfyui_nodes", "reference": "https://github.com/WilliamStanford/ComfyUI-VisualLabs", "files": ["https://github.com/WilliamStanford/ComfyUI-VisualLabs"], "install_type": "git-clone", "description": "Nodes:PointStringFromFloatArray"}, {"author": "bruce007lee", "title": "comfyui-cleaner", "reference": "https://github.com/bruce007lee/comfyui-cleaner", "files": ["https://github.com/bruce007lee/comfyui-cleaner"], "install_type": "git-clone", "description": "Nodes:cleaner"}, {"author": "ExponentialML", "title": "ComfyUI_LiveDirector (WIP)", "reference": "https://github.com/ExponentialML/ComfyUI_LiveDirector", "files": ["https://github.com/ExponentialML/ComfyUI_LiveDirector"], "install_type": "git-clone", "description": "Experimental method to use reference video to drive motion in generations without training in ComfyUI."}, {"author": "hy134300", "title": "comfyui-hb-node", "reference": "https://github.com/hy134300/comfyui-hb-node", "files": ["https://github.com/hy134300/comfyui-hb-node"], "install_type": "git-clone", "description": "Nodes:sound voice, text concat, latent to list, movie generate, movie batch, hy save image, generate story"}, {"author": "gameltb", "title": "io_comfyui", "reference": "https://github.com/gameltb/io_comfyui", "files": ["https://github.com/gameltb/io_comfyui"], "install_type": "git-clone", "description": "Let Blender work with ComfyUI by ComfyScript. This addon is still in development."}, {"author": "ALatentPlace", "title": "YANC- Yet Another Node Collection", "reference": "https://github.com/ALatentPlace/ComfyUI_yanc", "files": ["https://github.com/ALatentPlace/ComfyUI_yanc"], "install_type": "git-clone", "description": "This is another node collection for ComfyUI. It includes some basic nodes that I find useful, and I've also created them to meet my personal needs."}, {"author": "Jiffies-64", "title": "ComfyUI-SaveImagePlus", "reference": "https://github.com/Jiffies-64/ComfyUI-SaveImagePlus", "files": ["https://github.com/Jiffies-64/ComfyUI-SaveImagePlus"], "install_type": "git-clone", "description": "Nodes:SaveImagePlus"}, {"author": "<PERSON><PERSON><PERSON>", "title": "ComfyUI-Adapter [WIP]", "reference": "https://github.com/kadirnar/ComfyUI-Adapter", "files": ["https://github.com/kadirnar/ComfyUI-Adapter"], "install_type": "git-clone", "description": "WIP"}, {"author": "Be<PERSON>ez<PERSON>", "title": "comfyui-amd-go-fast", "reference": "https://github.com/Beinsezii/comfyui-amd-go-fast", "files": ["https://github.com/Beinsezii/comfyui-amd-go-fast"], "install_type": "git-clone", "description": "This contains all-in-one 'principled' nodes for T2I, I2I, refining, and scaling. Additionally it has many tools for directly manipulating the color of latents, high res fix math, and scripted image post-processing."}, {"author": "sugarkwork", "title": "comfyui_psd [WIP]", "reference": "https://github.com/sugarkwork/comfyui_psd", "files": ["https://github.com/sugarkwork/comfyui_psd"], "install_type": "git-clone", "description": "Not working yet."}, {"author": "SadaleNet", "title": "ComfyUI Port for Google's Prompt-to-Prompt", "reference": "https://github.com/SadaleNet/ComfyUI-Prompt-To-Prompt", "files": ["https://github.com/SadaleNet/ComfyUI-Prompt-To-Prompt"], "install_type": "git-clone", "description": "This is a PoC port of [a/Google's Prompt-to-Prompt](https://github.com/google/prompt-to-prompt/) to ComfyUI. It isn't feature complete. But it's good enough for evaluating if prompt-to-prompt is of any good."}, {"author": "stavsap", "title": "ComfyUI Ollama [WIP]", "reference": "https://github.com/stavsap/ComfyUI-React-SDK", "files": ["https://github.com/stavsap/ComfyUI-React-SDK"], "install_type": "git-clone", "description": "This project is for building React application as an overlay upon ComfyUI.\nProviding and ability to provide desired UI with ComfyUI API and workflows.\nInspired by: [a/https://github.com/cubiq/Comfy_Dungeon](https://github.com/cubiq/Comfy_Dungeon)"}, {"author": "<PERSON>ao<PERSON><PERSON>", "title": "ComfyUI DynamiCrafter", "reference": "https://github.com/chaojie/ComfyUI-DynamiCrafter", "files": ["https://github.com/chaojie/ComfyUI-DynamiCrafter"], "install_type": "git-clone", "description": "ComfyUI [a/DynamiCrafter](https://github.com/Doubiiu/DynamiCrafter)"}, {"author": "cubiq", "title": "Com<PERSON> Du<PERSON>on [WIP]", "reference": "https://github.com/cubiq/Comfy_Dungeon", "files": ["https://github.com/cubiq/Comfy_Dungeon"], "install_type": "git-clone", "description": "Build D&D Character Portraits with ComfyUI.\nIMPORTANT: At the moment this is mostly a tech demo to show how to build a web app on top of ComfyUI. The code is very messy and the application doesn't guaratee consistent results."}, {"author": "dfl", "title": "comfyui-stylegan", "reference": "https://github.com/dfl/comfyui-stylegan", "files": ["https://github.com/dfl/comfyui-stylegan"], "install_type": "git-clone", "description": "Generator for StyleGAN 3"}, {"author": "A719689614", "title": "ComfyUI_AC_FUNV8Beta1", "reference": "https://github.com/A719689614/ComfyUI_AC_FUNV8Beta1", "files": ["https://github.com/A719689614/ComfyUI_AC_FUNV8Beta1"], "install_type": "git-clone", "description": "Nodes:AC_Super_Controlnet/Checkpoint/Loras/Lora&LCM/KSampler/UpKSampler/SaveImage/PreviewImage/CKPT&LCM/CLIPEN/EmptLatent, AC_FUN_SUPER_LARGE, AC_Super_Come_Ckpt, AC_Super_Come_Lora"}, {"author": "<PERSON><PERSON><PERSON><PERSON>", "title": "comfy-magick [WIP]", "reference": "https://github.com/houdinii/comfy-magick", "files": ["https://github.com/houdinii/comfy-magick"], "install_type": "git-clone", "description": "This is a way to implement ImageMagick functionality in ComfyUI, which is generally PIL (pillow) based. I'm not sure the best way to handle this, as batch images make it a lot more complex, but the general idea will be two nodes to translate the IMAGE type, a torch.tensor of shape [batch, height, width, channels], or [1, 600, 800, 3] for a single 800x600 image, into/from a wand Image object."}, {"author": "tjo<PERSON><PERSON>rden", "title": "my-useful-comfyui-custom-nodes", "reference": "https://github.com/tjorbogarden/my-useful-comfyui-custom-nodes", "files": ["https://github.com/tjorbogarden/my-useful-comfyui-custom-nodes"], "install_type": "git-clone", "description": "Nodes:<PERSON>-<PERSON>, KSamplerSDXLAdvanced."}, {"author": "DeTK", "title": "ComfyUI Node Switcher", "reference": "https://github.com/DeTK/ComfyUI-Switch", "files": ["https://github.com/DeTK/ComfyUI-Switch"], "install_type": "git-clone", "description": "Nodes:NodeSwitch."}, {"author": "GrindHouse66", "title": "GH Tools for ComfyUI", "reference": "https://github.com/GrindHouse66/ComfyUI-GH_Tools", "files": ["https://github.com/GrindHouse66/ComfyUI-GH_Tools"], "install_type": "git-clone", "description": "Nodes:GH Tools Image Sizer, GH Tools Simple Scale. Simple quality of life Tools for ComfyUI. Basically, If it makes my life easier, it will be here. The list will grow over time."}, {"author": "sdfxai", "title": "SDFXBridgeForComfyUI - ComfyUI Custom Node for SDFX Integration", "reference": "https://github.com/sdfxai/SDFXBridgeForComfyUI", "files": ["https://github.com/sdfxai/SDFXBridgeForComfyUI"], "install_type": "git-clone", "description": "SDFXBridgeForComfyUI is a custom node designed for seamless integration between ComfyUI and the SDFX solution. This custom node allows users to make ComfyUI compatible with SDFX when running the ComfyUI instance on their local machines."}, {"author": "Be<PERSON>ez<PERSON>", "title": "comfyui-amd-go-fast", "reference": "https://github.com/Beinsezii/comfyui-amd-go-fast", "files": ["https://github.com/Beinsezii/comfyui-amd-go-fast"], "install_type": "git-clone", "description": "See details: [a/link](https://github.com/Beinsezii/comfyui-amd-go-fast?tab=readme-ov-file)"}, {"author": "SeedV", "title": "ComfyUI-SeedV-Nodes [UNSAFE]", "reference": "https://github.com/SeedV/ComfyUI-SeedV-Nodes", "files": ["https://github.com/SeedV/ComfyUI-SeedV-Nodes"], "install_type": "git-clone", "description": "Nodes:Script.\n[w/This extension poses a risk of executing arbitrary commands through workflow execution. Please be cautious.]"}, {"author": "mut-ex", "title": "ComfyUI GLIGEN GUI Node", "reference": "https://github.com/mut-ex/comfyui-gligengui-node", "files": ["https://github.com/mut-ex/comfyui-gligengui-node"], "install_type": "git-clone", "description": "This is a simple, straightforward ComfyUI node to be used along with the [a/GLIGEN GUI](https://github.com/mut-ex/gligen-gui) I developed.\nNOTE:[a/Make sure you have the GLIGEN GUI up and running](https://github.com/mut-ex/gligen-gui/tree/main)"}, {"author": "unanan", "title": "ComfyUI-Dist [WIP]", "reference": "https://github.com/unanan/ComfyUI-Dist", "files": ["https://github.com/unanan/ComfyUI-Dist"], "install_type": "git-clone", "description": "For distributed processing ComfyUI workflows within a local area network.\nNot Finished Yet."}, {"author": "NicholasKao1029", "title": "comfyui-hook", "reference": "https://github.com/NicholasKao1029/comfyui-hook", "files": ["https://github.com/NicholasKao1029/comfyui-hook"], "install_type": "git-clone", "description": "This extension provides additional API"}, {"author": "Extraltodeus", "title": "Conditioning-token-experiments-for-ComfyUI", "reference": "https://github.com/Extraltodeus/Conditioning-token-experiments-for-ComfyUI", "files": ["https://github.com/Extraltodeus/Conditioning-token-experiments-for-ComfyUI"], "install_type": "git-clone", "description": "I made these nodes for experimenting so it's far from perfect but at least it is entertaining!\nIt uses cosine similarities or smallest euclidean distances to find the closest tokens."}, {"author": "shadowcz007", "title": "comfyui-llamafile [WIP]", "reference": "https://github.com/shadowcz007/comfyui-sd-prompt-mixlab", "files": ["https://github.com/shadowcz007/comfyui-sd-prompt-mixlab"], "install_type": "git-clone", "description": "This node is an experimental node aimed at exploring the collaborative way of human-machine creation."}, {"author": "gameltb", "title": "ComfyUI paper playground", "reference": "https://github.com/gameltb/ComfyUI_paper_playground", "files": ["https://github.com/gameltb/ComfyUI_paper_playground"], "install_type": "git-clone", "description": "Evaluate some papers in ComfyUI, just playground.\nNOTE: Various models need to be installed, so please visit the repository to check."}, {"author": "huizhang0110", "title": "ComfyUI_Easy_Nodes_hui", "reference": "https://github.com/huizhang0110/ComfyUI_Easy_Nodes_hui", "files": ["https://github.com/huizhang0110/ComfyUI_Easy_Nodes_hui"], "install_type": "git-clone", "description": "Nodes:EasyEmptyLatentImage"}, {"author": "tuck<PERSON><PERSON><PERSON>", "title": "ComfyUI-TDNodes [WIP]", "reference": "https://github.com/tuckerdarby/ComfyUI-TDNodes", "files": ["https://github.com/tuckerdarby/ComfyUI-TDNodes"], "install_type": "git-clone", "description": "Nodes:<PERSON><PERSON><PERSON><PERSON> (RAVE), K<PERSON><PERSON>r (TF), Object Tracker, KSample<PERSON> Batched, Video Tracker Prompt, TemporalNet Preprocessor, Instance Tracker Prompt, Instance Diffusion Loader, Hand Tracker Node"}, {"author": "shadowcz007", "title": "comfyui-CLIPSeg", "reference": "https://github.com/shadowcz007/comfyui-CLIPSeg", "files": ["https://github.com/shadowcz007/comfyui-CLIPSeg"], "install_type": "git-clone", "description": "Download [a/CLIPSeg](https://huggingface.co/CIDAS/clipseg-rd64-refined/tree/main), move to : models/clipseg"}, {"author": "stutya", "title": "ComfyUI-Terminal [UNSAFE]", "reference": "https://github.com/stutya/ComfyUI-Terminal", "files": ["https://github.com/stutya/ComfyUI-Terminal"], "install_type": "git-clone", "description": "Run Terminal Commands from ComfyUI.\n[w/This extension poses a risk of executing arbitrary commands through workflow execution. Please be cautious.]"}, {"author": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "title": "ComfyUI-BuildPath", "reference": "https://github.com/marcueberall/ComfyUI-BuildPath", "files": ["https://github.com/marcueberall/ComfyUI-BuildPath"], "install_type": "git-clone", "description": "Nodes: Build Path Adv."}, {"author": "LotzF", "title": "ComfyUI simple ChatGPT completion [UNSAFE]", "reference": "https://github.com/LotzF/ComfyUI-Simple-Chat-GPT-completion", "files": ["https://github.com/LotzF/ComfyUI-Simple-Chat-GPT-completion"], "install_type": "git-clone", "description": "A simple node to request ChatGPT completions. [w/Do not share your workflows including the API key! I'll take no responsibility for your leaked keys.]"}, {"author": "kappa54m", "title": "ComfyUI_Usability (WIP)", "reference": "https://github.com/kappa54m/ComfyUI_Usability", "files": ["https://github.com/kappa54m/ComfyUI_Usability"], "install_type": "git-clone", "description": "Nodes: Load Image Dedup, Load Image By Path."}, {"author": "17Retoucher", "title": "ComfyUI_Fooocus", "reference": "https://github.com/17Retoucher/ComfyUI_Fooocus", "files": ["https://github.com/17Retoucher/ComfyUI_Fooocus"], "install_type": "git-clone", "description": "Custom nodes that help reproduce image generation in Fooocus."}, {"author": "nkchocoai", "title": "ComfyUI-PromptUtilities", "reference": "https://github.com/nkchocoai/ComfyUI-PromptUtilities", "files": ["https://github.com/nkchocoai/ComfyUI-PromptUtilities"], "install_type": "git-clone", "description": "Nodes: Format String, Join String List, Load Preset, Load Preset (Advanced), Const String, Const String (multi line). Add useful nodes related to prompt."}, {"author": "BadCafeCode", "title": "execution-inversion-demo-comfyui", "reference": "https://github.com/BadCafeCode/execution-inversion-demo-comfyui", "files": ["https://github.com/BadCafeCode/execution-inversion-demo-comfyui"], "install_type": "git-clone", "description": "execution-inversion-demo-comfyui"}, {"author": "prodogape", "title": "ComfyUI-clip-interrogator [WIP]", "reference": "https://github.com/prodogape/ComfyUI-clip-interrogator", "files": ["https://github.com/prodogape/ComfyUI-clip-interrogator"], "install_type": "git-clone", "description": "Unofficial ComfyUI extension of clip-interrogator"}, {"author": "poise<PERSON><PERSON>", "title": "NudeNet-Detector-Provider [WIP]", "reference": "https://github.com/poisenbery/NudeNet-Detector-Provider", "files": ["https://github.com/poisenbery/NudeNet-Detector-Provider"], "install_type": "git-clone", "description": "BBOX Detector Provider for NudeNet. Bethesda version of NudeNet V3 detector provider to work with Impact Pack ComfyUI."}, {"author": "LarryJane491", "title": "ComfyUI-ModelUnloader", "reference": "https://github.com/LarryJane491/ComfyUI-ModelUnloader", "files": ["https://github.com/LarryJane491/ComfyUI-ModelUnloader"], "install_type": "git-clone", "description": "A simple custom node that unloads all models. Useful for developers or users who want to free some memory."}, {"author": "MrAdamBlack", "title": "CheckProgress [WIP]", "reference": "https://github.com/MrAdamBlack/CheckProgress", "files": ["https://github.com/MrAdamBlack/CheckProgress"], "install_type": "git-clone", "description": "I was looking for a node to put in place to ensure my prompt etc where going as expected before the rest of the flow executed. To end the session, I just return the input image as None (see expected error). Recommend using it alongside PreviewImage, then output to the rest of the flow and Save Image."}, {"author": "birnam", "title": "Gen Data Tester [WIP]", "reference": "https://github.com/birnam/ComfyUI-GenData-Pack", "files": ["https://github.com/birnam/ComfyUI-GenData-Pack"], "install_type": "git-clone", "description": "This answers the itch for being able to easily paste [a/CivitAI.com](https://civitai.com/) generated data (or other simple metadata) into Comfy in a way that makes it easy to test with multiple checkpoints."}, {"author": "nidefawl", "title": "ComfyUI-nidefawl [UNSAFE]", "reference": "https://github.com/nidefawl/ComfyUI-nidefawl", "files": ["https://github.com/nidefawl/ComfyUI-nidefawl"], "install_type": "git-clone", "description": "Nodes:PythonScript, BlendImagesWithBoundedMasks, CropImagesWithMasks, VAELoaderDataType, ModelSamplerTonemapNoiseTest, gcLatentTunnel, ReferenceOnlySimple, EmptyImageWithColor, MaskFromColor, SetLatentCustomNoise, LatentToImage, ImageToLatent, LatentScaledNoise, DisplayAnyType, SamplerCustomCallback, CustomCallback, SplitCustomSigmas, SamplerDPMPP_2M_SDE_nidefawl, LatentPerlinNoise.<BR>[w/This node is an unsafe node that includes the capability to execute arbitrary python script.]"}, {"author": "fog<PERSON><PERSON>", "title": "comfyui-cem-tools", "reference": "https://github.com/foglerek/comfyui-cem-tools", "files": ["https://github.com/foglerek/comfyui-cem-tools"], "install_type": "git-clone", "description": "Nodes:ProcessImageBatch"}, {"author": "komojini", "title": "ComfyUI_Prompt_Template_CustomNodes", "reference": "https://github.com/komojini/ComfyUI_Prompt_Template_CustomNodes", "files": ["https://raw.githubusercontent.com/komojini/ComfyUI_Prompt_Template_CustomNodes/main/prompt_with_template.py"], "install_type": "copy", "description": "Nodes:Prompt with Template"}, {"author": "talesofai", "title": "comfyui-supersave [WIP]", "reference": "https://github.com/talesofai/comfyui-supersave", "files": ["https://github.com/talesofai/comfyui-supersave"], "install_type": "git-clone", "description": "WIP"}, {"author": "Sai-ComfyUI", "title": "ComfyUI-MS-Nodes [WIP]", "reference": "https://github.com/Sai-ComfyUI/ComfyUI-MS-Nodes", "files": ["https://github.com/Sai-ComfyUI/ComfyUI-MS-Nodes"], "install_type": "git-clone", "description": "WIP"}, {"author": "eigenpunk", "title": "ComfyUI-audio", "reference": "https://github.com/eigenpunk/ComfyUI-audio", "files": ["https://github.com/eigenpunk/ComfyUI-audio"], "install_type": "git-clone", "description": "generative audio tools for ComfyUI. highly experimental-expect things to break."}, {"author": "Jaxkr", "title": "comfyui-terminal-command [UNSAFE]", "reference": "https://github.com/Jaxkr/comfyui-terminal-command", "files": ["https://github.com/Jaxkr/comfyui-terminal-command"], "install_type": "git-clone", "description": "Nodes: Run Terminal Command. [w/This node is an unsafe node that includes the capability to execute terminal commands.]"}, {"author": "BlueDangerX", "title": "ComfyUI-BDXNodes [WIP]", "reference": "https://github.com/BlueDangerX/ComfyUI-BDXNodes", "files": ["https://github.com/BlueDangerX/ComfyUI-BDXNodes"], "install_type": "git-clone", "description": "Nodes: <PERSON><PERSON>. Various quality of life testing nodes"}, {"author": "IvanZhd", "title": "comfyui-codeformer [WIP]", "reference": "https://github.com/IvanZhd/comfyui-codeformer", "files": ["https://github.com/IvanZhd/comfyui-codeformer"], "install_type": "git-clone", "description": "Nodes:Image Inverter"}, {"author": "alt-key-project", "title": "Dream Project Video Batches [WIP]", "reference": "https://github.com/alt-key-project/comfyui-dream-video-batches", "files": ["https://github.com/alt-key-project/comfyui-dream-video-batches"], "install_type": "git-clone", "description": "NOTE: This is currently work in progress. Expect nodes to break (or be broken) until 1.0 release."}, {"author": "oyvindg", "title": "ComfyUI-TrollSuite", "reference": "https://github.com/oyvindg/ComfyUI-TrollSuite", "files": ["https://github.com/oyvindg/ComfyUI-TrollSuite"], "install_type": "git-clone", "description": "Nodes: BinaryImageMask, ImagePadding, LoadLastCreatedImage, RandomMask, TransparentImage."}, {"author": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "title": "ComfyUI-EZ-Pipes", "reference": "https://github.com/romeobuilderotti/ComfyUI-EZ-Pipes", "files": ["https://github.com/romeobuilderotti/ComfyUI-EZ-Pipes"], "install_type": "git-clone", "description": "ComfyUI-EZ-Pipes is a set of custom pipe nodes for ComfyUI. It provides a set of Input/Edit/Output nodes for each pipe type."}, {"author": "<PERSON><PERSON>", "title": "comfyui-wormley-nodes", "reference": "https://github.com/wormley/comfyui-wormley-nodes", "files": ["https://github.com/wormley/comfyui-wormley-nodes"], "install_type": "git-clone", "description": "Nodes: CheckpointVAELoaderSimpleText, CheckpointVAESelectorText, LoRA_Tag_To_Stack"}, {"author": "<PERSON><PERSON><PERSON>", "title": "ComfyUI_bd_customNodes", "reference": "https://github.com/Brandelan/ComfyUI_bd_customNodes", "files": ["https://github.com/Brandelan/ComfyUI_bd_customNodes"], "install_type": "git-clone", "description": "Nodes: BD Random Range, B<PERSON> Settings, BD Sequencer."}, {"author": "<PERSON><PERSON><PERSON>", "title": "comfy-consistency-vae", "reference": "https://github.com/Jordach/comfy-consistency-vae", "files": ["https://github.com/Jordach/comfy-consistency-vae"], "install_type": "git-clone", "description": "Nodes: Comfy_ConsistencyVAE"}, {"author": "gameltb", "title": "ComfyUI_stable_fast", "reference": "https://github.com/gameltb/ComfyUI_stable_fast", "files": ["https://github.com/gameltb/ComfyUI_stable_fast"], "install_type": "git-clone", "description": "Nodes:ApplyStableFastUnet. Experimental usage of stable-fast."}, {"author": "jn-j<PERSON>o", "title": "jn_node_suite_comfyui [WIP]", "reference": "https://github.com/jn-jairo/jn_node_suite_comfyui", "files": ["https://github.com/jn-jairo/jn_node_suite_comfyui"], "install_type": "git-clone", "description": "Image manipulation nodes, Temperature control nodes, Tiling nodes, Primitive and operation nodes, ..."}, {"author": "laksjdjf", "title": "ssd-1b-comfyui", "reference": "https://github.com/laksjdjf/ssd-1b-comfyui", "files": ["https://github.com/laksjdjf/ssd-1b-comfyui"], "install_type": "git-clone", "description": "Experimental node for SSD-1B. This node is not need for latest comfyui."}, {"author": "flowtyone", "title": "comfyui-flowty-lcm", "reference": "https://github.com/flowtyone/comfyui-flowty-lcm", "files": ["https://github.com/flowtyone/comfyui-flowty-lcm"], "install_type": "git-clone", "description": "This is a comfyui early testing node for LCM, adapted from [a/https://github.com/0xbitches/sd-webui-lcm](https://github.com/0xbitches/sd-webui-lcm). It uses the diffusers backend unfortunately and not comfy's model loading mechanism. But the intention here is just to be able to execute lcm inside comfy.\nNOTE: 0xbitches's 'Latent Consistency Model for ComfyUI' is original implementation."}, {"author": "do<PERSON><PERSON>", "title": "ComfyUI_WcpD_Utility_Kit", "reference": "https://github.com/doucx/ComfyUI_WcpD_Utility_Kit", "files": ["https://github.com/doucx/ComfyUI_WcpD_Utility_Kit"], "install_type": "git-clone", "description": "Nodes: MergeStrings, ExecStrAsCode, RandnLatentImage. [w/NOTE: This extension includes the ability to execute code as a string in nodes. Be cautious during installation, as it can pose a security risk.]"}, {"author": "WSJUSA", "title": "pre-comfyui-stablsr", "reference": "https://github.com/WSJUSA/Comfyui-StableSR", "files": ["https://github.com/WSJUSA/Comfyui-StableSR"], "install_type": "git-clone", "description": "This is a development respository for debugging migration of StableSR to Comfyui"}, {"author": "Dr.Lt.<PERSON>", "title": "ComfyUI-Workflow-Component [WIP]", "reference": "https://github.com/ltdrdata/ComfyUI-Workflow-Component", "files": ["https://github.com/ltdrdata/ComfyUI-Workflow-Component"], "install_type": "git-clone", "description": "This extension provides the capability to use ComfyUI Workflow as a component and the ability to use the Image Refiner functionality based on components. NOTE: This is an experimental extension feature with no consideration for backward compatibility and can be highly unstable."}]}